package lotto;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.LottoServerApplicationConfig;
import com.turbospaces.ups.KafkaServiceInfo;
import org.flywaydb.core.api.configuration.FluentConfiguration;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.util.ResourceUtils;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.google.common.collect.ImmutableMap;
import com.turbospaces.api.CommonTopics;
import com.turbospaces.api.Topic;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.healthcheck.KafkaHealthCheck;
import com.turbospaces.plugins.FlywayBootstrapInitializer;
import com.turbospaces.ups.UPSs;

import lotto.di.LottoAcceptorsDiModule;
import lotto.di.LottoServerDiModule;
import model.Schemas;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class LottoServerStarter {
    public static void main(String[] args) throws Throwable {
        run(new LottoServerApplicationConfig(), args);
    }
    public static SimpleBootstrap run(ApplicationConfig cfg, String[] args) throws Throwable {
        SimpleBootstrap bootstrap = bootstrap(cfg);
        bootstrap.run(args); // ~ start
        return bootstrap;
    }
    public static SimpleBootstrap bootstrap(ApplicationConfig cfg) throws Throwable {
        LottoServerProperties props = new LottoServerProperties(cfg.factory());

        cfg.loadDefaultPropsFromResource(ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "lotto-server.properties"));
        cfg.setDefaultProperty(props.QUARTZ_JOBSTORE_IS_CLUSTERED.getKey(), true);
        cfg.setDefaultProperty(props.APP_DB_MIGRATION_PATH.getKey(), "db/lotto-migration");

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, LottoAcceptorsDiModule.class, LottoServerDiModule.class);

        PostgresqlServiceInfo posi = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.POSTGRES_OWNER);

        var topics = createTopics(props);
        for (Topic topic : topics) {
            topic.configure(cfg);
        }

        registerHealthchecks(bootstrap, topics);

        bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, props.APP_DB_MIGRATION_PATH, posi, new LottoEntities(), Schemas.LOTTO) {
            @Override
            protected void configureMigration(FluentConfiguration config) {
                super.configureMigration(config);

                ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                m.put(props.PARTITIONING_LOTTO_TICKETS_PRECISION.getKey(), props.PARTITIONING_LOTTO_TICKETS_PRECISION.get());
                m.put(props.PARTITIONING_LOTTO_TICKETS_SCALE.getKey(), Integer.toString(props.PARTITIONING_LOTTO_TICKETS_SCALE.get()));

                config.placeholders(m.build());
            }
        });

        return bootstrap;
    }

    public static Collection<Topic> createTopics(ApplicationProperties props) {
        List<Topic> topics = new ArrayList<>();
        topics.add(LottoTopics.REQ);
        topics.add(LottoTopics.RESP);
        topics.add(LottoTopics.BACKGROUND);
        topics.addAll(CommonTopics.asNotifies(props));
        topics.add(CommonTopics.asEvents(props));
        return topics;
    }

    public static void registerHealthchecks(SimpleBootstrap bootstrap, Collection<Topic> topics) throws Exception {
        HealthCheckRegistry hltr = bootstrap.healthCheckRegistry();
        KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.KAFKA);
        hltr.register("kafka-check", new KafkaHealthCheck(ksi, bootstrap.keyStore(), topics));
    }
}
