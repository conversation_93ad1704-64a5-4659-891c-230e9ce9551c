package notification;

import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.GcpCloudConnector;
import org.springframework.cloud.SmartCloudConnector;
import org.springframework.cloud.WildcardUPSFilter;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.cloud.service.common.RedisServiceInfo;
import org.springframework.util.ResourceUtils;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.google.common.collect.ImmutableList;
import com.turbospaces.api.CommonTopics;
import com.turbospaces.api.Topic;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.NotificationApplicationConfig;
import com.turbospaces.healthcheck.KafkaHealthCheck;
import com.turbospaces.healthcheck.RedisHealthCheck;
import com.turbospaces.plugins.FlywayBootstrapInitializer;
import com.turbospaces.plugins.KafkaBootstrapInitializer;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import model.Schemas;
import notification.di.NotificationServerDiModule;
import notification.sink.InboxNotificationSink;

public class NotificationServerStarter {
    public static void main(String[] args) throws Throwable {
        run(new NotificationApplicationConfig(),
                new GcpCloudConnector(new WildcardUPSFilter(new NotificationUPSs().build(), new NotificationWildcardUPSs().build())), args);
    }
    public static SimpleBootstrap run(ApplicationConfig cfg, SmartCloudConnector connector, String[] args) throws Throwable {
        SimpleBootstrap bootstrap = bootstrap(cfg, connector);
        bootstrap.run(args); // ~ start
        return bootstrap;
    }
    public static SimpleBootstrap bootstrap(ApplicationConfig cfg, SmartCloudConnector connector) throws Throwable {
        var props = new NotificationServerProperties(cfg.factory());

        cfg.loadDefaultPropsFromResource(ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "notification-server.properties"));
        cfg.setDefaultProperty(props.HTTP_REQUEST_MAX_SIZE.getKey(), props.KAFKA_RECORD_MAX_REQUEST_SIZE.get());
        cfg.setDefaultProperty(props.QUARTZ_JOBSTORE_IS_CLUSTERED.getKey(), true);
        cfg.setDefaultProperty(props.KAFKA_MAX_POLL_CONCURRENCY.getKey(), 1);
        cfg.setDefaultProperty(props.KAFKA_COMPRESSION_TYPE.getKey(), "lz4");
        cfg.setDefaultProperty(props.KAFKA_DO_NOT_QUEUE_PER_READ_ONLY_ACTOR.getKey(), true);
        cfg.setDefaultProperty(props.APP_DB_MIGRATION_PATH.getKey(), "db/notification-migration");
        cfg.setDefaultProperty(props.APP_NOTIFY_TOPIC.getKey(), NotificationTopics.NOTIFY.name().toString());

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, connector, NotificationServerDiModule.class);
        DynamicCloud cloud = bootstrap.cloud();

        PostgresqlServiceInfo posi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.POSTGRES_OWNER);
        RedisServiceInfo rsi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.REDIS);
        KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);

        //
        // ~ auto create topics (configuration part)
        //
        var topics = createTopics(props);
        for (Topic topic : topics) {
            topic.configure(cfg);
        }

        //
        // ~ health checks
        //
        registerHealthchecks(bootstrap, ksi, rsi, topics);

        //
        // ~ Extensions
        //
        bootstrap.addBootstrapRegistryInitializer(new KafkaBootstrapInitializer(bootstrap.keyStore(), props, ksi, topics));
        bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, props.APP_DB_MIGRATION_PATH, posi, new NotificationEntities(), Schemas.NOTIFICATION));

        return bootstrap;
    }

    public static ImmutableList<Topic> createTopics(NotificationServerProperties props) {
        return ImmutableList.<Topic> builder()
                .add(NotificationTopics.REQ)
                .add(NotificationTopics.READ_REQ)
                .addAll(CommonTopics.asNotifies(props))
                .add(CommonTopics.asEvents(props))
                .add(InboxNotificationSink.DEAD_LETTER_TOPIC)
                .build();
    }

    public static void registerHealthchecks(SimpleBootstrap bootstrap, KafkaServiceInfo ksi, RedisServiceInfo rsi, ImmutableList<Topic> topics) throws Exception {
        HealthCheckRegistry hltr = bootstrap.healthCheckRegistry();
        hltr.register("kafka-check", new KafkaHealthCheck(ksi, bootstrap.keyStore(), topics));
        hltr.register("redis-check", new RedisHealthCheck(rsi));
    }
}
