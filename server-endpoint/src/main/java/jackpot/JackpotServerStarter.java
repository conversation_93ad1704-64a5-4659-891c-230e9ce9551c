package jackpot;

import org.flywaydb.core.api.configuration.FluentConfiguration;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.GcpCloudConnector;
import org.springframework.cloud.SmartCloudConnector;
import org.springframework.cloud.WildcardUPSFilter;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.cloud.service.common.RedisServiceInfo;
import org.springframework.util.ResourceUtils;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.turbospaces.api.CommonTopics;
import com.turbospaces.api.Topic;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.JackpotApplicationConfig;
import com.turbospaces.healthcheck.KafkaHealthCheck;
import com.turbospaces.healthcheck.RedisHealthCheck;
import com.turbospaces.plugins.FlywayBootstrapInitializer;
import com.turbospaces.plugins.KafkaBootstrapInitializer;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import jackpot.di.JackpotServerDiModule;
import model.Schemas;

public class JackpotServerStarter {
    public static void main(String[] args) throws Throwable {
        run(new JackpotApplicationConfig(), new GcpCloudConnector(new WildcardUPSFilter(new JackpotUPSs().build(), new JackpotWildcardUPSs().build())), args);
    }
    public static SimpleBootstrap run(ApplicationConfig cfg, SmartCloudConnector connector, String[] args) throws Throwable {
        SimpleBootstrap bootstrap = bootstrap(cfg, connector);
        bootstrap.run(args); // ~ start
        return bootstrap;
    }
    public static SimpleBootstrap bootstrap(ApplicationConfig cfg, SmartCloudConnector connector) throws Throwable {
        JackpotServerProperties props = new JackpotServerProperties(cfg.factory());
        cfg.loadDefaultPropsFromResource(ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "jackpot-server.properties"));
        cfg.setDefaultProperty(props.KAFKA_MAX_POLL_CONCURRENCY.getKey(), 1);
        cfg.setDefaultProperty(props.KAFKA_COMPRESSION_TYPE.getKey(), "lz4");
        cfg.setDefaultProperty(props.KAFKA_DO_NOT_QUEUE_PER_READ_ONLY_ACTOR.getKey(), true);
        cfg.setDefaultProperty(props.QUARTZ_JOBSTORE_IS_CLUSTERED.getKey(), true);
        cfg.setDefaultProperty(props.APP_DB_MIGRATION_PATH.getKey(), "db/jackpot-migration");
        cfg.setDefaultProperty(props.APP_NOTIFY_TOPIC.getKey(), JackpotTopics.NOTIFY.name().toString());

        SimpleBootstrap bootstrap = new SimpleBootstrap(props, connector, JackpotServerDiModule.class);
        DynamicCloud cloud = bootstrap.cloud();

        PostgresqlServiceInfo posi = UPSs.findRequiredServiceInfoByName(cloud, JackpotProto.UPS_JP_POSTGRES_OWNER);
        KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
        RedisServiceInfo rsi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.REDIS);

        //
        // ~ auto create topics (configuration part)
        //
        var topics = createTopics(props);
        for (Topic topic : topics) {
            topic.configure(cfg);
        }

        //
        // ~ health checks
        //
        registerHealthchecks(bootstrap, topics, rsi);

        //
        // ~ Extensions
        //
        bootstrap.addBootstrapRegistryInitializer(
                new FlywayBootstrapInitializer(props, props.APP_DB_MIGRATION_PATH, posi, new JackpotEntities(), Schemas.JACKPOT) {
                    @Override
                    protected void configureMigration(FluentConfiguration config) {
                        super.configureMigration(config);
                        ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                        m.put(props.PARTITIONING_JACKPOT_CONTRIBUTION_PRECISION.getKey(), props.PARTITIONING_JACKPOT_CONTRIBUTION_PRECISION.get());
                        m.put(props.PARTITIONING_JACKPOT_CONTRIBUTION_SCALE.getKey(), Integer.toString(props.PARTITIONING_JACKPOT_CONTRIBUTION_SCALE.get()));
                        m.put(props.PARTITIONING_JACKPOT_CONTRIBUTION_FROM_DATE.getKey(), props.PARTITIONING_JACKPOT_CONTRIBUTION_FROM_DATE.get().toString());
                        config.placeholders(m.build());
                    }
                });

        bootstrap.addBootstrapRegistryInitializer(new KafkaBootstrapInitializer(bootstrap.keyStore(), props, ksi, topics.toArray(new Topic[0])));

        return bootstrap;
    }

    public static ImmutableList<Topic> createTopics(JackpotServerProperties props) {
        return ImmutableList.<Topic> builder()
                .add(JackpotTopics.REQ)
                .add(JackpotTopics.READ_REQ)
                .addAll(CommonTopics.asNotifies(props))
                .add(CommonTopics.asEvents(props))
                .build();
    }

    public static void registerHealthchecks(SimpleBootstrap bootstrap, ImmutableList<Topic> topics, RedisServiceInfo rsi) throws Exception {
        KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.KAFKA);
        HealthCheckRegistry hltr = bootstrap.healthCheckRegistry();
        hltr.register("kafka-check", new KafkaHealthCheck(ksi, bootstrap.keyStore(), topics.toArray(new Topic[0])));
        hltr.register("redis-check", new RedisHealthCheck(rsi));
    }
}
