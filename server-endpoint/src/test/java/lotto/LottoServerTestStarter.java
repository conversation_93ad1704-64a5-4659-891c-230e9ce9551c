package lotto;

import java.io.File;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.Arrays;
import java.util.UUID;

import org.apache.commons.io.FileUtils;
import org.springframework.cloud.ConfigurableCloudConnector;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.util.ResourceUtils;

import com.turbospaces.boot.Bootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.CloudOptions;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.jdbc.JdbcPoolServiceConfig;
import com.turbospaces.ups.UPSs;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import io.ebean.ddlrunner.DdlRunner;

public class LottoServerTestStarter {
    public static void main(String[] args) throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        cfg.loadLocalDevProperties();
        cfg.setDefaultProperty(CloudOptions.CLOUD_APP_PORT, PlatformUtil.findAvailableTcpPort()); // ~ any
        cfg.setDefaultProperty(CloudOptions.CLOUD_APP_INSTANCE_INDEX, Long.toString(ProcessHandle.current().pid())); // ~ any

        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        Bootstrap bootstrap = LottoServerStarter.bootstrap(cfg);

        PostgresqlServiceInfo info = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.POSTGRES_OWNER);
        JdbcPoolServiceConfig poolCfg = new JdbcPoolServiceConfig(props, false);

        HikariConfig hikariCfg = poolCfg.toHikariConfig(info);
        hikariCfg.setPoolName(PlatformUtil.randomUUID().toString());
        hikariCfg.setAutoCommit(false);

        bootstrap.run(args);

        try (HikariDataSource ds = new HikariDataSource(hikariCfg)) {
            try (Connection connection = ds.getConnection()) {
                for (String it : Arrays.asList("dev-lotto-data.sql")) {
                    try {
                        URL sqlResource = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + it);
                        String sqlScript = FileUtils.readFileToString(new File(sqlResource.getFile()), StandardCharsets.UTF_8);

                        DdlRunner runner = new DdlRunner(false, "dev");
                        runner.runAll(sqlScript, connection);
                        connection.commit();
                    } catch (Exception err) {
                        connection.rollback();
                        throw err;
                    }
                }
            }
        }

        bootstrap.squashLogging(); // ~ reset logging level if necessary globally
    }
}
