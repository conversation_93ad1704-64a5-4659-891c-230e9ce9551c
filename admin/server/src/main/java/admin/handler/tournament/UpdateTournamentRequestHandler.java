package admin.handler.tournament;

import static api.v1.Code.ERR_BAD_REQUEST;
import static api.v1.Reason.BAD_REQUEST;

import java.io.IOException;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.rpc.ApiResponse;
import com.turbospaces.rpc.SafeJaxrsResponseConsumer;

import admin.TournamentAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.handler.AbstractAdminHandler;
import admin.mapper.RewardRequestMapper;
import admin.mapper.TournamentConfigMapper;
import admin.mapper.TournamentRewardName;
import admin.models.tournament.CreateOrUpdateTournamentResponse;
import admin.models.tournament.TournamentPrize;
import admin.models.tournament.TournamentStatus;
import admin.models.tournament.UpdateTournamentRequest;
import api.v1.ApiFactory;
import api.v1.EnhancedApplicationException;
import common.proto.TTournamentIcon;
import common.proto.TTournamentPrize;
import common.proto.TTournamentReward;
import common.proto.TTournamentRewardType;
import common.proto.TTournamentStatus;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import reward.RewardServiceApi;
import reward.api.v1.CoinRewardApiModel;
import reward.api.v1.CreateOrUpdateRewardsRequest;
import reward.api.v1.CreateOrUpdateRewardsResponse;
import reward.api.v1.RewardApiModel;
import tournament.TournamentServiceApi;
import tournament.api.v1.TCreateOrUpdateTournamentResponse;
import tournament.api.v1.TUpdateTournamentRequest;
import uam.api.UamServiceApi;

@Slf4j
@Service
public class UpdateTournamentRequestHandler extends AbstractAdminHandler<UpdateTournamentRequest> {
    private final TournamentServiceApi tournamentApi;
    private final RewardServiceApi rewardService;

    public UpdateTournamentRequestHandler(TournamentAdminServerProperties props, DynamicCloud cloud,
            MeterRegistry meterRegistry, UamServiceApi uamServiceApi,
            TournamentServiceApi tournamentApi, ApiFactory apiFactory,
            RewardServiceApi rewardService) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
        this.tournamentApi = tournamentApi;
        this.rewardService = rewardService;
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, UpdateTournamentRequest request) throws EnhancedApplicationException {
        MDC.put("tournamentId", String.valueOf(request.getId()));

        var prizeErrors = validatePrizes(request);
        if (!prizeErrors.isEmpty()) {
            var response = new AdminErrorResponse(Response.Status.BAD_REQUEST.getReasonPhrase(),
                    prizeErrors.toString());
            async.resume(Response.status(Response.Status.BAD_REQUEST).entity(response).build());
            return;
        }

        String adminEmail = adminEmail(header);
        if (request.getTournamentPrizes() == null || request.getTournamentPrizes().isEmpty()) {
            // No prizes - directly call updateTournament
            updateTournament(request, adminEmail, async, CreateOrUpdateRewardsResponse.newBuilder().build());
        } else {
            // Prizes exist - first create/update rewards
            CreateOrUpdateRewardsRequest rewardRequest = RewardRequestMapper.mapRewardRequest(request, header);
            ApiResponse<CreateOrUpdateRewardsResponse> rewardCall = rewardService.createOrUpdateRewards(rewardRequest);

            rewardCall.addListener(new SafeJaxrsResponseConsumer(rewardCall, async, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws IOException, EnhancedApplicationException {
                    if (!srespw.status().isOK()) {
                        var status = srespw.status();
                        async.resume(Response.status(Response.Status.BAD_REQUEST)
                                .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText()))
                                .build());
                        return;
                    }
                    CreateOrUpdateRewardsResponse rewardsResponse = srespw.unpack(CreateOrUpdateRewardsResponse.class);

                    // Now call updateTournament after rewards are processed
                    updateTournament(request, adminEmail, async, rewardsResponse);
                }
            });
        }
    }

    private void updateTournament(UpdateTournamentRequest request, String adminEmail, AsyncResponse async,
            CreateOrUpdateRewardsResponse rewardsResponse) throws EnhancedApplicationException {
        TUpdateTournamentRequest req = mapToRequest(request, adminEmail, rewardsResponse);
        ApiResponse<TCreateOrUpdateTournamentResponse> updateTournamentCall = tournamentApi.updateTournament(req,
                AsciiString.cached(adminEmail));

        updateTournamentCall.addListener(new SafeJaxrsResponseConsumer(updateTournamentCall, async, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade tournamentRespw) throws IOException {
                if (tournamentRespw.status().isOK() && tournamentRespw.is(TCreateOrUpdateTournamentResponse.class)) {
                    TCreateOrUpdateTournamentResponse resp = tournamentRespw.unpack(
                            TCreateOrUpdateTournamentResponse.class);
                    CreateOrUpdateTournamentResponse out = TournamentConfigMapper.mapToReply(resp);
                    async.resume(Response.ok(out).build());
                } else {
                    var status = tournamentRespw.status();
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText()))
                            .build());
                }
            }
        });
    }

    private static TUpdateTournamentRequest mapToRequest(UpdateTournamentRequest request, String adminEmail,
            CreateOrUpdateRewardsResponse rewardsResponse) throws EnhancedApplicationException {
        return TUpdateTournamentRequest.newBuilder()
                .setId(request.getId())
                .setStatus(TTournamentStatus.valueOf(request.getStatus().name()))
                .setStartDate(request.getStartDate().toInstant(ZoneOffset.UTC).toEpochMilli())
                .setEndDate(request.getEndDate().toInstant(ZoneOffset.UTC).toEpochMilli())
                .setTitle(request.getTitle())
                .setIcon(TTournamentIcon.newBuilder().setIconUrl(request.getIconUrl()).build())
                .setConfig(TournamentConfigMapper.mapToConfig(request.getConfig()))
                .addAllBrands(request.getBrands())
                .addAllGameCodes(request.getGameCodes())
                .setOwnerBrand(request.getOwnerBrand())
                .addAllPrizes(mapPrizes(request.getTournamentPrizes(), rewardsResponse))
                .setAdminId(adminEmail)
                .setUpcomingDate(request.getUpcomingDate().toInstant(ZoneOffset.UTC).toEpochMilli())
                .build();
    }

    private List<String> validatePrizes(UpdateTournamentRequest request) {
        var tournamentPrizes = request.getTournamentPrizes();
        if (tournamentPrizes.isEmpty()) if (request.getStatus() == TournamentStatus.DRAFT) {
            return List.of();
        } else {
            return List.of("At least one prize must be defined for tournament in status different from DRAFT.");
        }

        var errors = new ArrayList<String>();

        var prizes = new ArrayList<>(tournamentPrizes);
        prizes.sort(Comparator.comparing(TournamentPrize::getStartPosition));

        for (int i = 0; i < prizes.size(); i++) {
            var currentRange = prizes.get(i);
            if (currentRange.getStartPosition() > currentRange.getEndPosition()) {
                errors.add("Invalid range: %s".formatted(currentRange));
            }

            if (i < prizes.size() - 1) {
                var nextRange = prizes.get(i + 1);
                if (nextRange.getStartPosition() > nextRange.getEndPosition()) {
                    errors.add("Invalid range: %s".formatted(nextRange));
                }

                if (currentRange.getEndPosition() >= nextRange.getStartPosition()) {
                    errors.add("Range overlap: current range: %s, next range: %s".formatted(currentRange, nextRange));
                }

                if (nextRange.getStartPosition() - currentRange.getEndPosition() > 1) {
                    errors.add("There's a gap in the ranges: current range: %s, next range: %s".formatted(currentRange,
                            nextRange));
                }
            }
        }
        return errors;
    }

    private static List<TTournamentPrize> mapPrizes(List<TournamentPrize> prizeRequests,
            CreateOrUpdateRewardsResponse rewardsResponse) throws EnhancedApplicationException {
        var rewardGroupedByPosition = rewardsResponse.getRewardsList()
                .stream()
                .collect(
                        Collectors.groupingBy(reward -> TournamentRewardName.fromString(reward.getName()).positions()));
        List<TTournamentPrize> result = new LinkedList<>();
        for (var prizeRequest : prizeRequests) {
            TTournamentPrize prize = mapTournamentPrize(prizeRequest);
            String name = new TournamentRewardName(null, prize.getStartPosition(), prize.getEndPosition()).positions();
            var rewards = rewardGroupedByPosition.getOrDefault(name, Collections.emptyList());
            List<TTournamentReward> ttRewards = new java.util.ArrayList<>();
            for (RewardApiModel reward : rewards) {
                TTournamentRewardType type = getRewardType(reward);
                ttRewards.add(TTournamentReward.newBuilder()
                        .setRewardCode(reward.getRewardCode())
                        .setType(type)
                        .setBrandName(reward.getBrandName())
                        .build());
            }
            result.add(prize.toBuilder().addAllReward(ttRewards).build());
        }
        return result;
    }

    private static TTournamentPrize mapTournamentPrize(TournamentPrize prize) {
        TTournamentPrize.Builder builder = TTournamentPrize.newBuilder()
                .setStartPosition(prize.getStartPosition())
                .setEndPosition(prize.getEndPosition())
                .setScAmount(prize.getScAmount().doubleValue());
        if (prize.getId() != null) {
            builder.setId(prize.getId());
        }
        return builder.build();
    }

    private static TTournamentRewardType getRewardType(RewardApiModel reward) throws EnhancedApplicationException {
        if (reward.hasCoinReward()) {
            return TTournamentRewardType.COIN;
        } else if (reward.hasFreeSpinReward()) {
            return TTournamentRewardType.FREE_SPIN;
        } else if (reward.hasPurchaseOfferReward()) {
            return TTournamentRewardType.PURCHASE_OFFER;
        }
        var msg = "Unknow type of reward: %s".formatted(reward.getRewardCode());
        throw EnhancedApplicationException.of(msg, ERR_BAD_REQUEST, BAD_REQUEST);
    }
}
