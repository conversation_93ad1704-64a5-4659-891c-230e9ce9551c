package admin.api;

import admin.models.EmailPlaceholderMetadataRequestBody;
import admin.models.EmailPlaceholderMetadataResponseBody;
import admin.models.GetAccountOtpLimitRequestBody;
import admin.models.GetUploadDocumentTypesRequestBody;
import admin.models.GetUploadDocumentTypesResponseBody;
import admin.models.SendEmailWithPlaceholdersRequestBody;
import admin.models.SendEmailWithPlaceholdersResponseBody;
import admin.models.SetAccountOtpLimitRequestBody;
import admin.models.GetAccountOtpLimitResponseBody;
import admin.models.DeleteKycRiskSpendPolicyRequestBody;
import admin.models.SaveEthocaSettingsRequestBody;
import admin.models.SetDocUploadStatusRequestBody;
import admin.models.SetPaymentMethodVerificationRequestBody;
import admin.models.GetKycRiskSpendPolicyRequestBody;
import admin.models.GetKycRiskSpendPolicyResponseBody;
import admin.models.SaveKycRiskSpendPolicyRequestBody;
import admin.models.SetMassChargebackStatusRequestBody;
import admin.models.GetMassChargebackStatusRequestBody;
import admin.models.SetMassChargebackStatusResponseBody;
import admin.models.GetMassChargebackStatusResponseBody;
import admin.models.SaveOTPTriggerRulesRequest;
import admin.models.kyc.CreateKycIdVerificationRequestBody;
import admin.models.kyc.CreateKycPoaVerificationRequestBody;
import admin.models.kyc.NotifyDocUploadRequiredRequestBody;
import admin.models.kyc.SetKycAttemptsRequestBody;
import com.turbospaces.http.HttpProto;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.*;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import org.jboss.resteasy.spi.HttpRequest;

@Path(HttpProto.V2 + "/admin/fraud")
@Tag(name = "Fraud", description = "Admin - Fraud API Endpoints")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityScheme(type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, name = "ApiKeyAuth", paramName = HttpHeaders.AUTHORIZATION)
@SecurityRequirement(name = "ApiKeyAuth")
@ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = AdminOkResponse.class)))
@ApiResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = AdminErrorResponse.class)))
public interface FraudAdminApiEndpoint {

    @GET
    @Path("/getKycRiskPolicy")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = GetKycRiskSpendPolicyResponseBody.class)))
    void getKycRiskSpendPolicy(@Suspended AsyncResponse async,
                               @Context HttpRequest httpReq,
                               @BeanParam AdminHeader header,
                               @NotNull @Valid GetKycRiskSpendPolicyRequestBody request);

    @POST
    @Path("/saveKycRiskPolicy")
    void saveKycRiskSpendPolicy(@Suspended AsyncResponse async,
                                  @Context HttpRequest httpReq,
                                  @BeanParam AdminHeader header,
                                  @NotNull @Valid SaveKycRiskSpendPolicyRequestBody request);

    @POST
    @Path("/deleteKycRiskPolicy")
    void deleteKycRiskSpendPolicy(@Suspended AsyncResponse async,
                                  @Context HttpRequest httpReq,
                                  @BeanParam AdminHeader header,
                                  @NotNull @Valid DeleteKycRiskSpendPolicyRequestBody request);

    @POST
    @Path("/setPaymentMethodVerificationStatus")
    void setPaymentMethodVerificationStatus(@Suspended AsyncResponse async,
                                  @Context HttpRequest httpReq,
                                  @BeanParam AdminHeader header,
                                  @NotNull @Valid SetPaymentMethodVerificationRequestBody request);

    @POST
    @Path("/setMassChargebackStatus")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = SetMassChargebackStatusResponseBody.class)))
    void setMassChargebackStatus(@Suspended AsyncResponse async,
                                            @Context HttpRequest httpReq,
                                            @BeanParam AdminHeader header,
                                            @NotNull @Valid SetMassChargebackStatusRequestBody request);


    @POST
    @Path("/getMassChargebackStatus")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = GetMassChargebackStatusResponseBody.class)))
    void getMassChargebackStatus(@Suspended AsyncResponse async,
                                            @Context HttpRequest httpReq,
                                            @BeanParam AdminHeader header,
                                            @NotNull @Valid GetMassChargebackStatusRequestBody request);

    @POST
    @Path("/SaveOTPTriggerRules")
    void saveOTPTriggerRulesRequest(@Suspended AsyncResponse async,
                                    @Context HttpRequest httpReq,
                                    @BeanParam AdminHeader header,
                                    @NotNull @Valid SaveOTPTriggerRulesRequest request);

    @POST
    @Path("/getAccountOtpLimit")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = GetAccountOtpLimitResponseBody.class)))
    void getAccountOtpLimitRequest(@Suspended AsyncResponse async,
                               @Context HttpRequest httpReq,
                               @BeanParam AdminHeader header,
                               @NotNull @Valid GetAccountOtpLimitRequestBody request);

    @POST
    @Path("/setAccountOtpLimit")
    void setAccountOtpLimitRequest(@Suspended AsyncResponse async,
                                @Context HttpRequest httpReq,
                                @BeanParam AdminHeader header,
                                @NotNull @Valid SetAccountOtpLimitRequestBody request);

    @POST
    @Path("/saveEthocaSettings")
    void saveEthocaSettings(@Suspended AsyncResponse async,
                            @Context HttpRequest httpReq,
                            @BeanParam AdminHeader header,
                            @NotNull @Valid SaveEthocaSettingsRequestBody request);

    @POST
    @Path("/CreateKycIdVerification")
    void createKycIdVerification(@Suspended AsyncResponse async,
                                 @Context HttpRequest httpReq,
                                 @BeanParam AdminHeader header,
                                 @NotNull @Valid CreateKycIdVerificationRequestBody request);

    @POST
    @Path("/CreateKycPoaVerification")
    void createKycPoaVerification(@Suspended AsyncResponse async,
                                  @Context HttpRequest httpReq,
                                  @BeanParam AdminHeader header,
                                  @NotNull @Valid CreateKycPoaVerificationRequestBody request);

    @POST
    @Path("/SetKycAttempts")
    void setKycAttempts(@Suspended AsyncResponse async,
                        @Context HttpRequest httpReq,
                        @BeanParam AdminHeader header,
                        @NotNull @Valid SetKycAttemptsRequestBody request);

    @POST
    @Path("/getUploadSupportedDocuments")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = GetUploadDocumentTypesResponseBody.class)))
    void getUploadSupportedDocuments(@Suspended AsyncResponse async,
                               @Context HttpRequest httpReq,
                               @BeanParam AdminHeader header,
                               @NotNull @Valid GetUploadDocumentTypesRequestBody request);

    @POST
    @Path("/notifyDocUploadRequired")
    void notifyDocUploadRequired(@Suspended AsyncResponse async,
                                 @Context HttpRequest httpReq,
                                 @BeanParam AdminHeader header,
                                 @NotNull @Valid NotifyDocUploadRequiredRequestBody request);

    @POST
    @Path("/setDocUploadStatus")
    void setDocUploadStatus(@Suspended AsyncResponse async,
                                 @Context HttpRequest httpReq,
                                 @BeanParam AdminHeader header,
                                 @NotNull @Valid SetDocUploadStatusRequestBody request);

    @POST
    @Path("/getEmailPlaceholdersMetadata")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = EmailPlaceholderMetadataResponseBody.class)))
    void resolveTemplatePlaceholders(@Suspended AsyncResponse async,
                                     @Context HttpRequest httpReq,
                                     @BeanParam AdminHeader header,
                                     @NotNull @Valid EmailPlaceholderMetadataRequestBody request);

    @POST
    @Path("/sendFraudEmail")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = SendEmailWithPlaceholdersResponseBody.class)))
    void sendFraudEmail(@Suspended AsyncResponse async,
                        @Context HttpRequest httpReq,
                        @BeanParam AdminHeader header,
                        @NotNull @Valid SendEmailWithPlaceholdersRequestBody request);

}
