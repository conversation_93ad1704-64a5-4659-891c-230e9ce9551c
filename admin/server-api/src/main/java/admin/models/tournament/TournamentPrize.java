package admin.models.tournament;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

import jakarta.validation.constraints.Positive;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TournamentPrize {
    private Long id;
    @Positive
    private int startPosition;
    @Positive
    private int endPosition;
    @Positive
    private BigDecimal scAmount;
    private List<TournamentReward> rewards = new LinkedList<>();

    @Override
    public String toString() {
        return "TournamentPrize(id=%d, startPosition=%d, endPosition=%d)".formatted(id, startPosition, endPosition);
    }
}
