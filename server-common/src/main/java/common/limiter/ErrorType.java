package common.limiter;

import java.io.EOFException;
import java.net.ConnectException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.ConnectionClosedException;
import org.apache.http.NoHttpResponseException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.turbospaces.http.UnexpectedJaxrsException;

public enum ErrorType {
    EOF_EXCEPTION("SSL peer shut down incorrectly", EOFException.class),
    JSON_EOI_EXCEPTION("Unexpected end-of-input", JsonProcessingException.class),
    FAILED_TO_RESPOND_443("443 failed to respond", NoHttpResponseException.class),
    READ_TIMED_OUT("Read timed out", SocketTimeoutException.class),
    CONNECT_TIMED_OUT("Connect timed out", SocketTimeoutException.class),
    CONNECTION_RESET("Connection reset", SocketException.class),
    CONNECT_EXCEPTION("Timeout connecting to", ConnectException.class),
    CONNECTION_CLOSED("Connection is closed", ConnectionClosedException.class),
    CONNECTION_TIMED_OUT("Connection timed out", UnexpectedJaxrsException.class),
    WEB_SERVER_IS_DOWN("521: Web server is down", UnexpectedJaxrsException.class),
    WEB_SERVER_UNKNOWN_ERROR("520: Web server is returning an unknown error", UnexpectedJaxrsException.class),
    BAD_GATEWAY("Bad Gateway", UnexpectedJaxrsException.class),
    GATEWAY_TIME_OUT("504: Gateway time-out", UnexpectedJaxrsException.class);

    private static final Map<Class<?>, List<ErrorType>> types = new HashMap<>();

    static {
        for (ErrorType errorType : ErrorType.values()) {
            var key = errorType.type();
            var existingValues = types.computeIfAbsent(key, k -> new ArrayList<>());
            existingValues.add(errorType);
        }
    }

    private final String code;
    private final String message;
    private final Class<?> type;

    ErrorType(String message, Class<?> type) {
        this.code = name().toLowerCase();
        this.message = message;
        this.type = type;
    }

    public static ErrorType valueOf(Throwable throwable) {
        var typesByName = types.entrySet().stream()
                .filter(entry -> entry.getKey().isInstance(throwable))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(new ArrayList<>());
        for (ErrorType errorType : typesByName) {
            if (StringUtils.contains(throwable.getMessage(), errorType.message)) {
                return errorType;
            }
        }
        return null;
    }

    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    public Class<?> type() {
        return type;
    }
}
