package dataactivation.cookies;

import static org.patrianna.utils.ui.WaitsUtil.waitForLoadingUntilAsserted;
import static org.patrianna.workflow.assertions.ui.dataactivation.AssertUnexpectedCookies.assertActualCookies;

import com.epam.reportportal.annotations.TestCaseId;
import com.epam.reportportal.annotations.attribute.Attribute;
import com.epam.reportportal.annotations.attribute.Attributes;
import com.microsoft.playwright.options.Cookie;
import dataactivation.CookiesDiscoveryBase;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.patrianna.common.enums.Domain;
import org.patrianna.common.tags.brands.ysi.Pulszbingo;
import org.patrianna.utils.common.UserTestDataFactory;

@Attributes(attributes = {@Attribute(key = "epic", value = "Web Security"),
    @Attribute(key = "feature", value = "Cookie Management"),
    @Attribute(key = "story", value = "Verify absence of unexpected cookies on web pages")})

@Pulszbingo
class AllContextCookieTest extends CookiesDiscoveryBase {

    @BeforeAll
    static void setTestUp() {
        user = UserTestDataFactory.buildWithDomain(Domain.TEST_ACCOUNT);

        dataActivationFlow
            .openLandingPage()
            .checkCookiesPromptVisible()
            .trustAllCookies()
            .performRegistration(user)
            .homePage()
            .waitPageLoaded()
            .waitOneOfModalsVisible();
    }

    @ParameterizedTest(name = "Verify all cookies in browser context for URL: {0}")
    @MethodSource("pathProvider")
    @TestCaseId(value = "76160", parametrized = true)
    @DisplayName("Retrieves all cookies in the browser context and verifies no unexpected cookies are present.")
    @Attributes(attributes = {@Attribute(key = "priority", value = "high")})
    void testAllCookiesInBrowser(String path) {
        dataActivationFlow.navigateTo(path).makeSureUrlOpened();

        List<Cookie> actualCookies = dataActivationFlow.getContextCookies();

        waitForLoadingUntilAsserted(() -> assertActualCookies(actualCookies)
            .locatedUnderUrl(baseUrl + path)
            .matchesToExpected(expectedCookieNames).assertNow());
    }


    @SneakyThrows
    protected static Stream<String> pathProvider() {
        return loadPaths().stream();
    }

    @SneakyThrows
    protected static List<String> loadPaths() {
        var testPathFile = "/cookies/path.txt";
        try (InputStream is = AllContextCookieTest.class.getResourceAsStream(testPathFile)) {
            if (is == null) {
                throw new IOException(testPathFile + " not found");
            }

            try (BufferedReader r = new BufferedReader(new InputStreamReader(is))) {
                return r.lines()
                    .map(String::trim)
                    .filter(l -> !l.isEmpty())
                    .map(p -> p.startsWith("/") ? p : "/" + p)
                    .toList();
            }
        }
    }
}