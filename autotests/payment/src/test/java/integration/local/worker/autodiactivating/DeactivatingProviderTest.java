package integration.local.worker.autodiactivating;

import static java.util.concurrent.TimeUnit.SECONDS;
import static org.patrianna.utils.api.dbmanager.DatabaseManager.dbManager;
import static org.patrianna.workflow.assertions.api.payment.PaymentAssertionsFlow.paymentAssertionsFlow;
import static org.patrianna.workflow.flow.payment.PaymentIntegrationFlow.paymentFlow;

import bots.payment.ResponseWrapper;
import com.epam.reportportal.annotations.TestCaseId;
import com.epam.reportportal.annotations.attribute.Attribute;
import com.epam.reportportal.annotations.attribute.Attributes;
import com.spreedly.sdk.gateway.model.SpreedlyPaymentMethod;
import fe.api.payment.model.CreateOrderResponseBody;
import integration.PaymentGeneralBaseClass;
import java.time.Duration;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.patrianna.common.enums.BigDecimalValue;
import org.patrianna.common.enums.payment.PaymentProvider;
import org.patrianna.common.tags.payment.PaymentTag;
import org.patrianna.core.bot.ExtendedEmbeddedCompositeBot;
import org.patrianna.dataproviders.testdata.payment.CardService;
import org.patrianna.dataproviders.testdata.payment.PaymentProviderUtils;
import org.patrianna.utils.api.dbmanager.payment.PaymentDatabaseManager;
import payment.PaymentJpaManager;
import payment.model.PaymentOrder;
import payment.model.Provider;
import payment.model.ProviderIntegrationTypeSpec;
import payment.type.PurchaseProviderSpec;
import uam.UamServerProperties;

@Attributes(attributes = {
    @Attribute(key = "epic", value = "Payment Integration"),
    @Attribute(key = "feature", value = "Purchase ::: Spreedly ::: Provider Deactivation Job"),
    @Attribute(key = "story", value = "Automatically deactivate and enable payment providers due to failed orders")
})
@Tag(PaymentTag.PAYMENT_WORKER)
@Tag(PaymentTag.PURCHASE)
@Tag(PaymentTag.SPREEDLY)
@Tag(PaymentTag.PROVIDER_DEACTIVATION_JOB)
class DeactivatingProviderTest extends PaymentGeneralBaseClass {

    private static final String SPREEDLY_FISERV_CODE = PaymentProvider.SPREEDLY_FISERV.getCode();
    private static final String SPREEDLY_EMERCHANTPAY_CODE = PaymentProvider.SPREEDLY_EMERCHANTPAY.getCode();
    private static final String SPREEDLY_PAYNEARME_CODE = PaymentProvider.SPREEDLY_PAYNEARME.getCode();
    private static final String SPREEDLY_WORLDPAY_CODE = PaymentProvider.SPREEDLY_WORLDPAY.getCode();
    private static final String SPREEDLY_RAPYD_CODE = PaymentProvider.SPREEDLY_RAPYD.getCode();

    private static final int THIRTY_SECONDS = 30;
    private static final int ONE_MINUTE = 60;
    private static final int TWENTY_SECONDS = 20;
    private static final int PERCENTAGE_THRESHOLD = 51;
    private static final String HTTP_NOT_FOUND_CODE = "404";
    private static final String RAPYD_PROVIDER_CODE = PaymentProvider.SPREEDLY_RAPYD.getCode();
    private static final Duration DURATION_ONE_MINUTE = Duration.ofSeconds(ONE_MINUTE);
    private static final Duration DEACTIVATION_DURATION = Duration.ofSeconds(50);
    private static final Duration DURATION_THIRTY_SECONDS = Duration.ofSeconds(THIRTY_SECONDS);
    private UamServerProperties properties;
    private PaymentJpaManager jpaManager;
    private PaymentDatabaseManager dbManager;

    static Stream<Arguments> provideArguments() {
        return Stream.of(
            Arguments.of(PaymentProvider.SPREEDLY_RAPYD,
                CardService.SPREEDLY_4200,
                CardService.SPREEDLY_DECLINED_CARD,
                SPREEDLY_RAPYD_CODE),
            Arguments.of(PaymentProvider.SPREEDLY_FISERV,
                CardService.SPREEDLY_FISERV,
                CardService.SPREEDLY_FISERV_HIGHER_300_DECLINE,
                SPREEDLY_FISERV_CODE),
            Arguments.of(PaymentProvider.SPREEDLY_EMERCHANTPAY,
                CardService.SPREEDLY_EMERCHANT_PAY,
                CardService.SPREEDLY_4111,
                SPREEDLY_EMERCHANTPAY_CODE),
            Arguments.of(PaymentProvider.SPREEDLY_PAYNEARME,
                CardService.SPREEDLY_4111,
                CardService.PAYNEARME_INVALID_ZIP,
                SPREEDLY_PAYNEARME_CODE),
            Arguments.of(PaymentProvider.SPREEDLY_WORLDPAY,
                CardService.SPREEDLY_WORLDPAY,
                CardService.SPREEDLY_WORLDPAY_EXPIRED_CARD,
                SPREEDLY_WORLDPAY_CODE)
        );
    }

    @BeforeEach
    void setUp() {
        // Initializing properties and payment managers
        properties = crmCtx.getCrmServer().getProperties();
        jpaManager = payment.getPaymentServer().getCtx().getBean(PaymentJpaManager.class);
        dbManager = dbManager().payment(jpaManager);
        // Setting up payment flow and providers
        paymentFlow()
            .providerSetup(payment)
            .setupPaymentServer(PaymentProviderUtils.SPREEDLY_RAPYD)
            .addPaymentServerProviders(PaymentProviderUtils.SPREEDLY_FISERV, PaymentProviderUtils.SPREEDLY_EMERCHANTPAY,
                PaymentProviderUtils.SPREEDLY_PAYNEARME, PaymentProviderUtils.SPREEDLY_WORLDPAY)
            .setupPurchaseProviders(PurchaseProviderSpec.SPREEDLY_RAPYD, ProviderIntegrationTypeSpec.SPREEDLY_RAPYD)
            .addPurchaseProviders(PurchaseProviderSpec.SPREEDLY_FISERV, ProviderIntegrationTypeSpec.SPREEDLY_FISERV)
            .addPurchaseProviders(PurchaseProviderSpec.SPREEDLY_EMERCHANTPAY,
                ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY)
            .addPurchaseProviders(PurchaseProviderSpec.SPREEDLY_PAYNEARME,
                ProviderIntegrationTypeSpec.SPREEDLY_PAYNEARME)
            .addPurchaseProviders(PurchaseProviderSpec.SPREEDLY_WORLDPAY,
                ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY);

        // Setting up payment worker server properties
        payment.getPaymentWorkerServer().setProperty(
            payment.getPaymentWorkerServer().getProperties().DEACTIVATE_PAYMENT_PROVIDER_JOB_TEST_MODE.getKey(), false);
        payment.getPaymentWorkerServer().setProperty(payment.getPaymentWorkerServer().getProperties()
            .DEACTIVATE_PAYMENT_PROVIDER_JOB_ENABLED.getKey(), true);
        payment.getPaymentWorkerServer().setProperty(payment.getPaymentWorkerServer().getProperties()
            .DEACTIVATE_PAYMENT_PROVIDER_JOB_ORDERS_PERIOD.getKey(), "PT30S"); //period of time to check orders
        payment.getPaymentWorkerServer().setProperty(payment.getPaymentWorkerServer().getProperties()
            .DEACTIVATE_PAYMENT_PROVIDER_JOB_FREQUENCY.getKey(), "PT30S");
    }

    @Test()
    @Attributes(attributes = {
        @Attribute(key = "priority", value = "medium")
    })
    @TestCaseId("69658")
    @DisplayName("[Spreedly Rapyd] Provider Deactivation on Custom Error Code")
    @SneakyThrows
    void testRapydProviderDeactivationOnCustomErrorCode() {
        var paymentProvider = PaymentProvider.SPREEDLY_RAPYD;
        try (ExtendedEmbeddedCompositeBot embeddedCompositeBot = createWebBotWithSeonMock()) {
            // Set up deactivation policy based on a custom error code
            List<String> customErrorCodes = List.of("ERROR_PROCESS_PAYMENT_SETTLEMENT_DECLINED");
            var deactivationJobActions = paymentFlow().automationJobActions(jpaManager)
                .getProviderDeactivationJobAction(paymentProvider);
            deactivationJobActions.setUpPolicyByError(customErrorCodes);

            paymentFlow().getCrmActions(embeddedCompositeBot).signUpAndConnectBot(properties);

            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);

            // Wait for the deactivation policy to process (e.g., scheduled job execution) and check provider
            waitForDelay(THIRTY_SECONDS);
            var provider = dbManager.getProvider(RAPYD_PROVIDER_CODE);
            paymentAssertionsFlow(payment).spreedlyAssertions(embeddedCompositeBot).assertProviderActive(provider);

            // Attempt a transaction that will trigger the custom error code
            makeFailRapydPayment(embeddedCompositeBot);

            // Wait for the provider to deactivate and then reactivate based on the policy settings
            deactivationJobActions.waitForProviderToDeactivate(DURATION_ONE_MINUTE);

            //Verify that order is failing due to provider deactivation
            makeFailRapydPaymentDueToProviderDeactivation(embeddedCompositeBot);

            //Verify provider is auto reactivated after time is passed
            deactivationJobActions.ensureProviderRemainsInState(DEACTIVATION_DURATION, Provider::isInactive)
                .waitForProviderToActivate(DURATION_ONE_MINUTE);

            // Verify that provider was modified
            var modifiedProvider = dbManager.getProvider(RAPYD_PROVIDER_CODE);
            paymentAssertionsFlow(payment).spreedlyAssertions(embeddedCompositeBot)
                .assertProviderModified(provider, modifiedProvider);

            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);
        }
    }

    @Test()
    @Attributes(attributes = {
        @Attribute(key = "priority", value = "medium")
    })
    @TestCaseId("71382")
    @DisplayName("[Spreedly Emerchantpay] Provider deactivation by http_error_code")
    @SneakyThrows
    void testEmerchantProviderDeactivationOnHttpErrorCode() {
        var paymentProvider = PaymentProvider.SPREEDLY_EMERCHANTPAY;
        try (ExtendedEmbeddedCompositeBot embeddedCompositeBot = createWebBotWithSeonMock()) {
            // Set up deactivation policy based on a custom error code
            List<Integer> httpCode = List.of(Integer.valueOf(HTTP_NOT_FOUND_CODE));
            var deactivationJobActions = paymentFlow().automationJobActions(jpaManager)
                .getProviderDeactivationJobAction(paymentProvider);
            deactivationJobActions.setUpPolicyByHttpError(httpCode);

            paymentFlow().getCrmActions(embeddedCompositeBot).signUpAndConnectBot(properties);

            makeSuccessfulEmerchantpayPayment(embeddedCompositeBot);
            waitForDelay(TWENTY_SECONDS);
            var provider = dbManager.getProvider(RAPYD_PROVIDER_CODE);
            paymentAssertionsFlow(payment).spreedlyAssertions(embeddedCompositeBot).assertProviderActive(provider);

            // Attempt a transaction that will fail and update http_code for the transaction
            updateHttpCodeOfFailedOrder(paymentProvider, embeddedCompositeBot);

            // Wait for the provider to deactivate and then reactivate based on the policy settings
            deactivationJobActions.waitForProviderToDeactivate(DURATION_ONE_MINUTE);
            makeFailEmerchantpayPaymentDueToProviderDeactivation(embeddedCompositeBot);
            deactivationJobActions
                .ensureProviderRemainsInState(DEACTIVATION_DURATION, Provider::isInactive)
                .waitForProviderToActivate(DURATION_ONE_MINUTE);

            // Verify that provider was modified
            var modifiedProvider = dbManager.getProvider(SPREEDLY_EMERCHANTPAY_CODE);
            paymentAssertionsFlow(payment).spreedlyAssertions(embeddedCompositeBot)
                .assertProviderModified(provider, modifiedProvider);

            makeSuccessfulEmerchantpayPayment(embeddedCompositeBot);
        }
    }

    @ParameterizedTest
    @Attributes(attributes = {
        @Attribute(key = "priority", value = "medium")
    })
    @TestCaseId(value = "71005", parametrized = true)
    @DisplayName("[Spreedly] Provider Deactivation on Exception")
    @MethodSource("provideArguments")
    @SneakyThrows
    void testProvidersDeactivationOnException(
        PaymentProvider paymentProvider,
        SpreedlyPaymentMethod validCard,
        SpreedlyPaymentMethod invalidCard,
        String providerCode
    ) {
        try (ExtendedEmbeddedCompositeBot embeddedCompositeBot = createWebBotWithSeonMock()) {
            // Set up deactivation policy based on exceptions
            var deactivationJobActions = paymentFlow().automationJobActions(jpaManager)
                .getProviderDeactivationJobAction(paymentProvider);
            deactivationJobActions.setUpPolicyByException();

            // Prepare payment actions and generate an offer of $20
            var paymentBotActions = paymentFlow().getPaymentActions(embeddedCompositeBot);
            var offer = paymentFlow().offerSetup(paymentUtil).generateOffer(BigDecimalValue.TWENTY.value());
            var cardToken = paymentBotActions.tokenizeCard(payment, validCard);
            paymentFlow().getCrmActions(embeddedCompositeBot).signUpAndConnectBot(properties);

            // Send a create order request and assert that the payment is successful
            var orderRequest = paymentBotActions.sendCreateOrderWithProvider(offer, cardToken, paymentProvider);
            paymentAssertionsFlow(payment)
                .purchaseAssertions(embeddedCompositeBot)
                .withSuccessOrderResponse(orderRequest)
                .assertSuccessfulPaymentByProvider(paymentProvider);
            // Wait for the deactivation policy to process
            waitForDelay(THIRTY_SECONDS);

            // Verify that the provider is still active after the successful transaction
            var provider = dbManager.getProvider(providerCode);
            paymentAssertionsFlow(payment)
                .spreedlyAssertions(embeddedCompositeBot)
                .assertProviderActive(provider);

            // Tokenize a card that will trigger an exception (e.g., invalid card number)
            cardToken = paymentBotActions.tokenizeCard(payment, invalidCard);

            // Attempt a transaction that will trigger an exception and assert payment failure
            var failedOffer = paymentFlow().offerSetup(paymentUtil).generateOffer(BigDecimalValue.TWO_THOUSAND.value());
            orderRequest = paymentBotActions.sendCreateOrderWithProvider(failedOffer, cardToken, paymentProvider);
            paymentAssertionsFlow(payment)
                .purchaseAssertions(embeddedCompositeBot).withFailedOrderResponse(orderRequest)
                .assertPaymentFailed().assertOrderContainsExceptionClass();

            // Wait for the provider to deactivate and then reactivate based on the policy settings
            deactivationJobActions
                .waitForProviderToDeactivate(DURATION_ONE_MINUTE)
                .ensureProviderRemainsInState(DEACTIVATION_DURATION, Provider::isInactive)
                .waitForProviderToActivate(DURATION_ONE_MINUTE);

            // Verify that provider was modified
            var modifiedProvider = dbManager.getProvider(providerCode);
            paymentAssertionsFlow(payment).spreedlyAssertions(embeddedCompositeBot)
                .assertProviderModified(provider, modifiedProvider);
        }
    }

    @Test
    @Attributes(attributes = {
        @Attribute(key = "priority", value = "high")
    })
    @TestCaseId("171334")
    @DisplayName("Deactivate provider by default rule without provider_code specified")
    @SneakyThrows
    void testProvidersDeactivationPolicyWithoutProvider() {
        try (ExtendedEmbeddedCompositeBot embeddedCompositeBot = createWebBotWithSeonMock()) {
            // Set up deactivation policy based on exception_class without provider (30 second deactivate interval)
            var deactivationJobActions = paymentFlow().automationJobActions(jpaManager)
                .getProviderDeactivationJobAction(PaymentProvider.SPREEDLY_RAPYD);
            deactivationJobActions.setUpPolicyByExceptionWithoutProvider(ONE_MINUTE);
            paymentFlow().getCrmActions(embeddedCompositeBot).signUpAndConnectBot(properties);

            // Wait for the deactivation policy to process (e.g., scheduled job execution) and check provider
            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);
            waitForDelay(THIRTY_SECONDS);
            var provider = dbManager.getProvider(RAPYD_PROVIDER_CODE);
            paymentAssertionsFlow(payment).spreedlyAssertions(embeddedCompositeBot).assertProviderActive(provider);

            // Make a failed order to trigger DeactivateProviderJob
            makeFailRapydPayment(embeddedCompositeBot);

            // Wait for the provider to deactivate and then reactivate based on the policy settings
            deactivationJobActions.waitForProviderToDeactivate(DURATION_ONE_MINUTE);

            //Verify provider remains deactivated for a minute after successful order
            makeSuccessfulEmerchantpayPayment(embeddedCompositeBot);
            deactivationJobActions.ensureProviderRemainsInState(DURATION_THIRTY_SECONDS, Provider::isInactive)
                .waitForProviderToActivate(DURATION_ONE_MINUTE);
            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);

            // Wait for the deactivation policy to process (e.g., scheduled job execution) and check provider
            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);
            waitForDelay(THIRTY_SECONDS);
            provider = dbManager.getProvider(RAPYD_PROVIDER_CODE);
            paymentAssertionsFlow(payment).spreedlyAssertions(embeddedCompositeBot).assertProviderActive(provider);

            //Make a failed Worldpay order
            makeFailedOrderToEmerchantPay(embeddedCompositeBot);

            // Wait for the provider to deactivate and then reactivate based on the policy settings
            paymentFlow().automationJobActions(jpaManager)
                .getProviderDeactivationJobAction(PaymentProvider.SPREEDLY_EMERCHANTPAY)
                .waitForProviderToDeactivate(DURATION_ONE_MINUTE);
            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);
        }
    }

    @Test
    @Attributes(attributes = {
        @Attribute(key = "priority", value = "medium")
    })
    @TestCaseId("70748")
    @DisplayName("Verify that default configuration skipped when per provider is present")
    @SneakyThrows
    void testProvidersDeactivationSkipDefaultWhenProviderPolicyPresent() {
        var paymentProvider = PaymentProvider.SPREEDLY_RAPYD;
        try (ExtendedEmbeddedCompositeBot embeddedCompositeBot = createWebBotWithSeonMock()) {
            // Set up deactivation policy based on exception_class without provider (30 second deactivate interval)
            // and with provider (60 second deactivate interval)
            var deactivationJobActions = paymentFlow().automationJobActions(jpaManager)
                .getProviderDeactivationJobAction(paymentProvider);
            deactivationJobActions.setUpPolicyByException();
            deactivationJobActions.setUpPolicyByExceptionWithoutProvider(THIRTY_SECONDS);
            paymentFlow().getCrmActions(embeddedCompositeBot).signUpAndConnectBot(properties);

            // Wait for the deactivation policy to process (e.g., scheduled job execution) and check provider
            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);
            waitForDelay(THIRTY_SECONDS);
            var provider = dbManager.getProvider(RAPYD_PROVIDER_CODE);
            paymentAssertionsFlow(payment).spreedlyAssertions(embeddedCompositeBot).assertProviderActive(provider);

            // Make a failed order to trigger DeactivateProviderJob
            makeFailRapydPayment(embeddedCompositeBot);

            // Wait for the provider to deactivate and then reactivate based on the policy settings
            deactivationJobActions.waitForProviderToDeactivate(DURATION_ONE_MINUTE);

            //Verify provider remains deactivated for a minute after failed order
            makeFailRapydPaymentDueToProviderDeactivation(embeddedCompositeBot);
            deactivationJobActions.ensureProviderRemainsInState(DEACTIVATION_DURATION, Provider::isInactive)
                .waitForProviderToActivate(DURATION_THIRTY_SECONDS);

            // Make successful order to check that provider is active
            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);
        }
    }

    @Test()
    @Attributes(attributes = {
        @Attribute(key = "priority", value = "medium")
    })
    @TestCaseId("71421")
    @DisplayName("[Spreedly Rapyd] Provider deactivation after re-activation")
    @SneakyThrows
    void testRapydProviderDeactivationAfterAutoReActivation() {
        var paymentProvider = PaymentProvider.SPREEDLY_RAPYD;
        try (ExtendedEmbeddedCompositeBot embeddedCompositeBot = createWebBotWithSeonMock()) {
            // Set up deactivation policy based on a custom error code
            List<String> customErrorCodes = List.of("ERROR_PROCESS_PAYMENT_SETTLEMENT_DECLINED");
            var deactivationJobActions = paymentFlow().automationJobActions(jpaManager)
                .getProviderDeactivationJobAction(paymentProvider);
            deactivationJobActions.setUpPolicyByError(customErrorCodes);

            paymentFlow().getCrmActions(embeddedCompositeBot).signUpAndConnectBot(properties);

            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);

            // Wait for the deactivation policy to process (e.g., scheduled job execution) and check provider
            waitForDelay(ONE_MINUTE);
            var provider = dbManager.getProvider(RAPYD_PROVIDER_CODE);
            paymentAssertionsFlow(payment).spreedlyAssertions(embeddedCompositeBot).assertProviderActive(provider);

            makeFailRapydPayment(embeddedCompositeBot);

            // Wait for the provider to deactivate and then reactivate based on the policy settings
            deactivationJobActions.waitForProviderToDeactivate(DURATION_ONE_MINUTE);

            //Verify that order is failing due to provider deactivation
            makeFailRapydPaymentDueToProviderDeactivation(embeddedCompositeBot);

            //Verify provider is auto reactivated after time is passed
            deactivationJobActions.ensureProviderRemainsInState(DEACTIVATION_DURATION, Provider::isInactive)
                .waitForProviderToActivate(DURATION_ONE_MINUTE);

            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);

            // Wait for the deactivation policy to process (e.g., scheduled job execution) and check provider
            waitForDelay(THIRTY_SECONDS);
            provider = dbManager.getProvider(RAPYD_PROVIDER_CODE);
            paymentAssertionsFlow(payment).spreedlyAssertions(embeddedCompositeBot).assertProviderActive(provider);

            // Attempt a transaction that will trigger the custom error code
            makeFailRapydPayment(embeddedCompositeBot);

            // Wait for the provider to deactivate and then reactivate based on the policy settings
            deactivationJobActions.waitForProviderToDeactivate(DURATION_ONE_MINUTE);
            makeFailRapydPaymentDueToProviderDeactivation(embeddedCompositeBot);
            deactivationJobActions.ensureProviderRemainsInState(DEACTIVATION_DURATION, Provider::isInactive)
                .waitForProviderToActivate(DURATION_ONE_MINUTE);
            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);
        }
    }

    @Test()
    @Attributes(attributes = {
        @Attribute(key = "priority", value = "medium")
    })
    @TestCaseId("123745")
    @DisplayName("Verify that failure percentage counted correctly")
    @SneakyThrows
    void testRapydProviderDeactivationBasedOnFailurePercentage() {
        var paymentProvider = PaymentProvider.SPREEDLY_RAPYD;
        try (ExtendedEmbeddedCompositeBot embeddedCompositeBot = createWebBotWithSeonMock()) {
            // Set up deactivation policy with custom failure percentage
            var deactivationJobActions = paymentFlow().automationJobActions(jpaManager)
                .getProviderDeactivationJobAction(paymentProvider);
            deactivationJobActions.setUpPolicyByCustomFailurePercentage(PERCENTAGE_THRESHOLD);

            // Sign up and connect the bot
            paymentFlow().getCrmActions(embeddedCompositeBot).signUpAndConnectBot(properties);

            // Make order and wait for the deactivation policy to process (e.g., scheduled job execution)
            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);
            waitForDelay(THIRTY_SECONDS);
            var provider = dbManager.getProvider(RAPYD_PROVIDER_CODE);
            paymentAssertionsFlow(payment).spreedlyAssertions(embeddedCompositeBot).assertProviderActive(provider);

            // Attempt two failed and one successful payment to trigger the deactivation policy
            makeFailRapydPayment(embeddedCompositeBot);
            makeFailRapydPayment(embeddedCompositeBot);
            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);

            // Wait for the provider to deactivate and then reactivate based on the policy settings
            deactivationJobActions
                .waitForProviderToDeactivate(DURATION_ONE_MINUTE)
                .ensureProviderRemainsInState(DEACTIVATION_DURATION, Provider::isInactive)
                .waitForProviderToActivate(DURATION_ONE_MINUTE);

            // Wait for the deactivation job to apply the deactivation policy
            waitForDelay(THIRTY_SECONDS);

            //make one failed order and one successful order
            makeFailRapydPayment(embeddedCompositeBot);
            makeSuccessfulRapydPaymentAndAssert(embeddedCompositeBot);

            // Check provider remains active for 60 seconds
            deactivationJobActions
                .ensureProviderRemainsInState(DURATION_ONE_MINUTE, Provider::isActive);
        }
    }

    @SneakyThrows
    public void waitForDelay(int seconds) {
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
        CountDownLatch latch = new CountDownLatch(1);

        // Schedule a task to count down the latch after one minute
        scheduler.schedule(latch::countDown, seconds, SECONDS);

        // Wait until the latch is counted down
        latch.await();

        scheduler.shutdown();
    }

    @SneakyThrows
    private void makeSuccessfulRapydPaymentAndAssert(ExtendedEmbeddedCompositeBot embeddedCompositeBot) {
        UUID transactionId = UUID.randomUUID();
        var orderRequest = makeRapidOrder(embeddedCompositeBot,
            BigDecimalValue.TWENTY, transactionId);
        paymentAssertionsFlow(payment).purchaseAssertions(embeddedCompositeBot)
            .withSuccessOrderResponse(orderRequest).assertSuccessfulPaymentToRapyd();
    }

    @SneakyThrows
    private void makeSuccessfulEmerchantpayPayment(ExtendedEmbeddedCompositeBot embeddedCompositeBot) {
        UUID transactionId = UUID.randomUUID();
        var paymentBotActions = paymentFlow().getPaymentActions(embeddedCompositeBot);
        var offer = paymentFlow().offerSetup(paymentUtil).generateOffer(BigDecimalValue.TWENTY.value());
        var cardToken = paymentBotActions.tokenizeCard(payment, CardService.SPREEDLY_4200);
        var orderRequest = paymentBotActions.sendCreateOrderWithProvider(offer, cardToken, transactionId,
            PaymentProvider.SPREEDLY_EMERCHANTPAY);
        paymentAssertionsFlow(payment).purchaseAssertions(embeddedCompositeBot)
            .withSuccessOrderResponse(orderRequest).assertSuccessfulPaymentToEmerchantPay();
    }

    @SneakyThrows
    private void makeFailedOrderToEmerchantPay(ExtendedEmbeddedCompositeBot embeddedCompositeBot) {
        var transactionId = UUID.randomUUID();
        var offer = paymentFlow().offerSetup(paymentUtil).generateOffer(BigDecimalValue.TWO_HUNDRED.value());
        var paymentBotActions = paymentFlow().getPaymentActions(embeddedCompositeBot);
        var cardToken = paymentBotActions.tokenizeCard(payment, CardService.SPREEDLY_4111);
        var orderRequest = paymentBotActions.sendCreateOrderWithProvider(offer, cardToken, transactionId,
            PaymentProvider.SPREEDLY_EMERCHANTPAY);
        paymentAssertionsFlow(payment)
            .purchaseAssertions(embeddedCompositeBot)
            .withFailedOrderResponse(orderRequest).assertOrderContainsExceptionClass();
    }

    @SneakyThrows
    private void updateHttpCodeOfFailedOrder(PaymentProvider paymentProvider,
        ExtendedEmbeddedCompositeBot embeddedCompositeBot) {
        // Attempt a transaction that will fail and update http_code for the transaction
        UUID transactionId = UUID.randomUUID();
        var failedOffer = paymentFlow().offerSetup(paymentUtil).generateOffer(BigDecimalValue.TWO_THOUSAND.value());
        var paymentBotActions = paymentFlow().getPaymentActions(embeddedCompositeBot);
        var cardToken = paymentBotActions.tokenizeCard(payment, CardService.SPREEDLY_4111);
        var orderRequest = paymentBotActions.sendCreateOrderWithProvider(failedOffer, cardToken, transactionId,
            paymentProvider);
        //Set 404 http_error for the order
        dbManager.updateOrder(HTTP_NOT_FOUND_CODE, transactionId);
        PaymentOrder order = dbManager.getOrder(transactionId);
        var failedOrderAssert = paymentAssertionsFlow(payment)
            .purchaseAssertions(embeddedCompositeBot)
            .withFailedOrderResponse(orderRequest);
        failedOrderAssert.assertPaymentFailed().assertOrderContainsHttpError(order, HTTP_NOT_FOUND_CODE);
    }

    @SneakyThrows
    private void makeFailEmerchantpayPaymentDueToProviderDeactivation(ExtendedEmbeddedCompositeBot
        embeddedCompositeBot) {
        var transactionId = UUID.randomUUID();
        var offer = paymentFlow().offerSetup(paymentUtil).generateOffer(BigDecimalValue.TWO_HUNDRED.value());
        var paymentBotActions = paymentFlow().getPaymentActions(embeddedCompositeBot);
        var cardToken = paymentBotActions.tokenizeCard(payment, CardService.SPREEDLY_4200);
        var orderRequest = paymentBotActions.sendCreateOrderWithProvider(offer, cardToken, transactionId,
            PaymentProvider.SPREEDLY_EMERCHANTPAY);
        paymentAssertionsFlow(payment)
            .purchaseAssertions(embeddedCompositeBot)
            .withFailedOrderResponse(orderRequest)
            .assertFailedDueToInactiveSpreedlyEmerchantPayProvider(transactionId);
    }

    @SneakyThrows
    private void makeFailRapydPayment(ExtendedEmbeddedCompositeBot embeddedCompositeBot) {
        UUID transactionId = UUID.randomUUID();
        var orderRequest = makeRapidOrder(embeddedCompositeBot,
            BigDecimalValue.TWO_THOUSAND, transactionId);
        paymentAssertionsFlow(payment).purchaseAssertions(embeddedCompositeBot).withFailedOrderResponse(orderRequest)
            .assertPaymentFailed().assertOrderContainsExceptionClass();
    }

    @SneakyThrows
    private void makeFailRapydPaymentDueToProviderDeactivation(ExtendedEmbeddedCompositeBot embeddedCompositeBot) {
        UUID transactionId = UUID.randomUUID();
        var orderRequest = makeRapidOrder(embeddedCompositeBot,
            BigDecimalValue.TWO_HUNDRED, transactionId);
        paymentAssertionsFlow(payment).purchaseAssertions(embeddedCompositeBot).withFailedOrderResponse(orderRequest)
            .assertFailedDueToInactiveSpreedlyRapydProvider(transactionId);
    }

    @SneakyThrows
    private ResponseWrapper<CreateOrderResponseBody> makeRapidOrder(ExtendedEmbeddedCompositeBot embeddedCompositeBot,
        BigDecimalValue amount, UUID transactionId) {
        var paymentBotActions = paymentFlow().getPaymentActions(embeddedCompositeBot);
        var offer = paymentFlow().offerSetup(paymentUtil).generateOffer(amount.value());
        var cardToken = paymentBotActions.tokenizeCard(payment, CardService.SPREEDLY_4200);
        return paymentBotActions.sendCreateOrderWithProvider(offer, cardToken, transactionId,
            PaymentProvider.SPREEDLY_RAPYD);
    }
}
