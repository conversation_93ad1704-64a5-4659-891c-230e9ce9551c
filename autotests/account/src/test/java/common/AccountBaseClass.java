package common;

import static org.patrianna.utils.api.console.TestAnnotationLogger.logTestDetails;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInfo;
import org.patrianna.common.tags.type.cases.Regression;
import org.patrianna.common.tags.type.test.BackEnd;
import org.patrianna.testcase.BaseJunitTestCase;
import org.slf4j.MDC;

@BackEnd
@Regression
@SuppressWarnings("java:S5786")
public class AccountBaseClass extends BaseJunitTestCase {

    @BeforeEach
    void combinedBeforeEach(TestInfo testInfo) {
        initializeTestNameInMdc(testInfo);
        logTestDetails(testInfo);
    }

    private void initializeTestNameInMdc(TestInfo testInfo) {
        MDC.put("testName", testInfo.getDisplayName());
    }
}



