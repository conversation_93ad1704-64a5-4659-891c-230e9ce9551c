<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.turbospaces.uam</groupId>
        <artifactId>frontend-parent</artifactId>
        <version>25.07.2-SNAPSHOT</version>
    </parent>
    <artifactId>frontend-server</artifactId>
    <name>frontend ::: ${project.artifactId}</name>
    <dependencies>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.inject</groupId>
            <artifactId>jakarta.inject-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-healthchecks</artifactId>
        </dependency>
        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.temporal</groupId>
            <artifactId>temporal-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius2-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.ws.rs</groupId>
            <artifactId>jakarta.ws.rs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-frontend-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-frontend</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>auth-frontend</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-gateway-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-openapi-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>jackpot-frontend</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>game-hub-frontend</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>notification-frontend</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>notification-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>payment-frontend-server</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>payment-frontend-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-auth</artifactId>
        </dependency>
        <!--        fraud-->
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>fraud-frontend</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>fraud-frontend-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>jackpot-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>auth-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>lotto-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>bingo-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>fraud-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-api-facade-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-api-identity-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-api-tags-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-api-types-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-api-spec-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>gateway-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>frontend-server-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>frontend-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-protobuf-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-temporal-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-api-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-spanner-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-resteasy-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-jaxrs2-servlet-initializer-v2-jakarta</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-server-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>payment-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>game-hub-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>lotto-frontend</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>bingo-frontend</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <!-- -->
        <!-- runtime -->
        <!-- -->
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- -->
        <!-- provided -->
        <!-- -->
        <dependency>
            <artifactId>lombok</artifactId>
            <groupId>org.projectlombok</groupId>
            <scope>provided</scope>
        </dependency>
        <!-- -->
        <!-- test dependencies -->
        <!-- -->
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.glassfish.expressly</groupId>
            <artifactId>expressly</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
