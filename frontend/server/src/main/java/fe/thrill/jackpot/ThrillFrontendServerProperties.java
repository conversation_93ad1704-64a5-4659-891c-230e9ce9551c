package fe.thrill.jackpot;

import com.turbospaces.cfg.DynamicPropertyFactory;

import com.turbospaces.cfg.FragmentProperties;
import fe.RenameMeLaterFrontendServerProperties;
import frontend.ThrillFrontendPropertiesFragment;

import java.util.List;

public class ThrillFrontendServerProperties extends RenameMeLaterFrontendServerProperties {
    public final ThrillFrontendPropertiesFragment thrillFragment;

    public ThrillFrontendServerProperties(DynamicPropertyFactory pf) {
        super(pf);
        thrillFragment = new ThrillFrontendPropertiesFragment(pf);
    }

    @Override
    public List<FragmentProperties> fragments() {
        return List.of(thrillFragment);
    }
}
