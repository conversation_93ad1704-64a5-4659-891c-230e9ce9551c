package fe.handlers.influencer;

import static fe.api.influencer.openapi.model.FinalizeGiveawayRejectionReason.GIVEAWAY_ALREADY_CANCELED_OR_CLOSED;
import static fe.api.influencer.openapi.model.FinalizeGiveawayRejectionReason.NOT_AN_OWNER;
import static influencer.api.v1.FinalizeGiveawayRejectionReason.FINALIZE_GIVEAWAY_REJECTION_REASON_GIVEAWAY_DOES_NOT_EXIST_OR_INACTIVE;
import static influencer.api.v1.FinalizeGiveawayRejectionReason.FINALIZE_GIVEAWAY_REJECTION_REASON_NOT_AN_OWNER;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import influencer.api.v1.CoinGiveawayWinner;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.influencer.FinalizeCoinGiveawayRequest;
import fe.api.influencer.FinalizeCoinGiveawayResponse;
import fe.api.influencer.openapi.model.FinalizeGiveawayRejectionReason;
import fe.api.influencer.openapi.model.Giveaway;
import fe.api.influencer.openapi.model.GiveawayPrize;
import fe.api.influencer.openapi.model.GiveawayPrizeType;
import fe.api.influencer.openapi.model.GiveawayStatus;
import fe.handlers.AbstractFrontendRequestHandler;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import influencer.InfluencerServiceApi;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;
import uam.api.UamServiceApi;

@Service
@Slf4j
public class FinalizeCoinGiveawayRequestHandler extends AbstractFrontendRequestHandler<FinalizeCoinGiveawayRequest> {

    private static final String GIVEAWAY_INTERNAL_PRODUCT_CODE = "giveaway";

    private final InfluencerServiceApi influencerServiceApi;
    private final UamServiceApi uamServiceApi;
    private final Map<influencer.api.v1.GiveawayStatus, GiveawayStatus> giveawayStatusMappings;
    private final Map<influencer.api.v1.FinalizeGiveawayRejectionReason, FinalizeGiveawayRejectionReason> rejectionReasonMappings;

    public FinalizeCoinGiveawayRequestHandler(
            ApplicationProperties props,
            InfluencerServiceApi influencerServiceApi,
            UamServiceApi uamServiceApi,
            Map<influencer.api.v1.GiveawayStatus, GiveawayStatus> giveawayStatusMappings) {
        super(props, FinalizeCoinGiveawayRequest.class);
        this.influencerServiceApi = influencerServiceApi;
        this.uamServiceApi = uamServiceApi;
        this.giveawayStatusMappings = giveawayStatusMappings;
        this.rejectionReasonMappings = Map.of(
                FINALIZE_GIVEAWAY_REJECTION_REASON_NOT_AN_OWNER, NOT_AN_OWNER,
                FINALIZE_GIVEAWAY_REJECTION_REASON_GIVEAWAY_DOES_NOT_EXIST_OR_INACTIVE, GIVEAWAY_ALREADY_CANCELED_OR_CLOSED);
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack)
            throws Exception {
        var opt = client.jwt();
        var req = (FinalizeCoinGiveawayRequest) reqw.body();
        var giveawayCode = req.getGiveawayCode();
        var when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            var auth = opt.get();

            var requestBuilder = influencer.api.v1.FinalizeCoinGiveawayRequest.newBuilder();
            requestBuilder.setIdentity(toIdentity(client, auth));
            requestBuilder.setCode(giveawayCode.toString());

            var finalizeGiveawayResponse = influencerServiceApi.finalizeCoinGiveaway(
                    requestBuilder.build(),
                    toRoutingKey(auth));

            finalizeGiveawayResponse
                    .addListener(new SafeSocketResponseConsumer(finalizeGiveawayResponse, client, reqw, ack) {

                        @Override
                        public void accept(ResponseWrapperFacade rwf) throws Throwable {
                            var reply = (GenericOutboundWrapper) reqw.toReply();
                            var response = (FinalizeCoinGiveawayResponse) reply.getBody();
                            var responseBody = rwf.body();
                            setStatusAndMeta(reply, rwf, when);
                            var status = rwf.status();

                            if (status.isOK()) {
                                var finalizeGiveawayResponse = responseBody
                                        .unpack(influencer.api.v1.FinalizeCoinGiveawayResponse.class);

                                if (finalizeGiveawayResponse.hasGiveaway()
                                        && !finalizeGiveawayResponse.hasRejectionReason()) {
                                    var giveaway = finalizeGiveawayResponse.getGiveaway();
                                    var winners = finalizeGiveawayResponse.getWinnersList();

                                    if (!winners.isEmpty()) {
                                        var coinPrize = giveaway.getCoinPrize();
                                        var accountIds = winners.stream()
                                                .map(CoinGiveawayWinner::getAccountId)
                                                .toList();

                                        var batchRewardRequest = uam.api.v1.RewardWithCoinsBatchRequest.newBuilder()
                                                .addAllAccountIds(accountIds)
                                                .setGcAmount(coinPrize.getGcPrize())
                                                .setScAmount(coinPrize.getScPrize())
                                                .setInternalProduct(GIVEAWAY_INTERNAL_PRODUCT_CODE)
                                                .build();

                                        var routingKey = AsciiString.cached(giveawayCode.toString());
                                        var batchRewardResponse = uamServiceApi.rewardWithCoinsBatch(batchRewardRequest, routingKey);

                                        batchRewardResponse.addListener(new SafeSocketResponseConsumer(batchRewardResponse, client, reqw, ack) {
                                            @Override
                                            public void accept(ResponseWrapperFacade rwf) throws Throwable {
                                                var status = rwf.status();

                                                if (!status.isOK()) {
                                                    log.warn("[{}] Failed to reward coin giveaway winners in batch. Winners count: {}",
                                                            giveawayCode, winners.size());
                                                } else {
                                                    var batchResponse = rwf.body().unpack(uam.api.v1.RewardWithCoinsBatchResponse.class);
                                                    if (!batchResponse.getApplied()) {
                                                        log.warn("[{}] Batch reward was not fully applied. Winners count: {}",
                                                                giveawayCode, winners.size());
                                                    } else {
                                                        log.info("[{}] Successfully rewarded all {} winners",
                                                                giveawayCode, winners.size());
                                                    }

                                                }
                                            }
                                        });
                                    }
                                }
                                convert(finalizeGiveawayResponse, response);
                            }
                            client.writeAndFlush(reply, api.v1.CacheControl.getDefaultInstance());
                        }
                    });
        }
    }

    private FinalizeCoinGiveawayResponse convert(
            influencer.api.v1.FinalizeCoinGiveawayResponse src,
            FinalizeCoinGiveawayResponse target) {
        if (src.hasRejectionReason()) {
            target.setRejectionReason(rejectionReasonMappings
                    .getOrDefault(src.getRejectionReason(), FinalizeGiveawayRejectionReason.UNRECOGNIZED));
        } else if (src.hasGiveaway()) {
            var payload = src.getGiveaway();
            var giveawayStatus = giveawayStatusMappings.getOrDefault(
                    payload.getStatus(),
                    GiveawayStatus.UNRECOGNIZED);

            var giveawayBuilder = Giveaway.builder()
                    .code(UUID.fromString(payload.getCode()))
                    .status(giveawayStatus)
                    .winnersNumber(payload.getWinnersNumber())
                    .createdAt(payload.getCreatedAt())
                    .endedAt(payload.getEndedAt());

            if (payload.hasCoinPrize()) {
                var prizeSrc = payload.getCoinPrize();
                var prizeTarget = new HashMap<String, GiveawayPrize>();
                prizeTarget.put(GiveawayPrizeType.GC.name(), new GiveawayPrize().amount(new BigDecimal(prizeSrc.getGcPrize())));
                prizeTarget.put(GiveawayPrizeType.SC.name(), new GiveawayPrize().amount(new BigDecimal(prizeSrc.getScPrize())));
                giveawayBuilder.prize(prizeTarget);
            }
            target.setGiveaway(giveawayBuilder.build());
        }

        return target;
    }
}
