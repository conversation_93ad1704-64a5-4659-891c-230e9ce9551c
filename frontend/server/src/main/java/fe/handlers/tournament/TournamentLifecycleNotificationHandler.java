package fe.handlers.tournament;

import org.springframework.stereotype.Service;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.NotificationWrapperFacade;

import common.proto.TTournamentStatus;
import fe.api.SocketServer;
import fe.api.tournament.TournamentLifecycleNotification;
import fe.api.tournament.model.TournamentStatus;
import fe.handlers.AbstractNotificationMessageHandler;
import tournament.api.v1.TTournamentLifecycleEvent;

@Service
public class TournamentLifecycleNotificationHandler
        extends AbstractNotificationMessageHandler<TTournamentLifecycleEvent> {
    public TournamentLifecycleNotificationHandler() {
        super(TTournamentLifecycleEvent.class);
    }

    @Override
    public void accept(SocketServer server, NotificationWrapperFacade notfw) throws Exception {
        Any body = notfw.body();

        TTournamentLifecycleEvent notification = body.unpack(TTournamentLifecycleEvent.class);

        var n = new TournamentLifecycleNotification()
                .setTournamentCode(notification.getTournamentCode())
                .setOldStatus(map(notification.getOldStatus()))
                .setNewStatus(map(notification.getNewStatus()));

        for (var brand : notification.getBrandsList()) {
            server.broadcast(brand, wrapOutbound(n));
        }
    }

    private TournamentStatus map(TTournamentStatus notification) {
        var statusName = switch (notification) {
            case PENDING_SETTLEMENT -> "PROCESSING_REWARDS";
            case SETTLED -> "ENDED";
            default -> notification.name();
        };

        return TournamentStatus.valueOf(statusName);
    }
}
