package fe.handlers.payment;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import api.v1.Code;
import api.v1.Reason;
import api.v1.Status;
import fe.PaymentMappers;
import fe.SafeSocketResponseConsumer;
import fe.api.OtpCheck;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.payment.RedeemMoneyRequest;
import fe.api.payment.model.Currency;
import fe.api.payment.model.KycStatus;
import fe.api.payment.model.NuveiMazoomaWithdrawMethod;
import fe.api.payment.model.PayperWithdrawMethod;
import fe.api.payment.model.RedeemMoneyRequestBody;
import fe.api.payment.model.RedeemMoneyResponseBody;
import fe.api.payment.model.SkrillWithdrawMethod;
import fe.api.payment.model.StandardAchWithdrawMethod;
import fe.api.payment.model.TrustlyWithdrawMethod;
import fe.api.payment.model.WithdrawMethodObject;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fe.services.otp.OtpService;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import payment.api.PaymentServiceApi;
import uam.api.v1.AeroPayWithdrawMethod;
import uam.api.v1.BankAccountType;
import uam.api.v1.CryptoCurrency;
import uam.api.v1.CryptoNetwork;

@Service
@Slf4j
public class RedeemMoneyRequestHandler extends AbstractFrontendRequestHandler<RedeemMoneyRequest> {

    private final PaymentServiceApi paymentServiceApi;
    private final OtpService otpService;

    @Inject
    public RedeemMoneyRequestHandler(ApplicationProperties props, PaymentServiceApi paymentServiceApi, OtpService otpService) {
        super(props, RedeemMoneyRequest.class);
        this.paymentServiceApi = Objects.requireNonNull(paymentServiceApi);
        this.otpService = otpService;
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        RedeemMoneyRequestBody req = (RedeemMoneyRequestBody) reqw.body();

        var validationStatus = validateRequest(req);
        if (validationStatus.getErrorCode().equals(Code.ERR_BAD_REQUEST)) {
            replyWithBadRequest(client, reqw, validationStatus.getErrorText());
            return;
        }

        Optional<Jwt> opt = client.jwt();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();
            var identity = toIdentity(client, auth);
            AsciiString routingKey = toRoutingKey(auth);
            otpService.checkOtp(identity.build(), routingKey).addCallback(new FutureCallback<>() {
                @Override
                public void onSuccess(OtpCheck otpCheck) {
                    try {
                        if (otpCheck.showOtp() && otpCheck.isMandatory()) {
                            replyWithErrorOtpRequired(client, reqw);
                        } else {
                            var sreqb = prepareRedeemMoneyRequest(req, client).setIdentity(identity);
                            createRedeemMoney(client, sreqb, routingKey, reqw, ack, when);
                        }
                    } catch (RuntimeException e) {
                        log.error(e.getMessage());
                        replyWithBadRequest(client, reqw);
                    }
                }
                @Override
                public void onFailure(Throwable t) {
                    log.error(t.getMessage());
                }
            }, MoreExecutors.directExecutor());
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }

    private uam.api.v1.RedeemMoneyRequest.Builder prepareRedeemMoneyRequest(RedeemMoneyRequestBody req, RestEasyClient client) {
        uam.api.v1.RedeemMoneyRequest.Builder sreqb = uam.api.v1.RedeemMoneyRequest.newBuilder();

        sreqb.setCurrency(req.getCurrency());
        sreqb.setAmount(req.getAmount().toString());
        sreqb.setEmail(req.getEmail());
        sreqb.setLocked(req.getLocked());
        sreqb.setSession(req.getSession());
        sreqb.setMetadata(PaymentMappers.toAppMetadata(client));
        sreqb.setIsMobileApp(PaymentMappers.isMobileApp(client));

        // ~ deprecated
        if (StringUtils.isNotEmpty(req.getCode())) {
            sreqb.setCode(req.getCode());
        }

        if (req.getMethod() != null) {
            WithdrawMethodObject method = req.getMethod();

            uam.api.v1.WithdrawMethod.Builder wmb = uam.api.v1.WithdrawMethod.newBuilder();
            if (Objects.nonNull(method.getCode())) {
                wmb.setCode(method.getCode().toString());
            }
            if (method.getWithdrawMethod() instanceof NuveiMazoomaWithdrawMethod) {

                NuveiMazoomaWithdrawMethod nmach = (NuveiMazoomaWithdrawMethod) method.getWithdrawMethod();

                uam.api.v1.NuveiMazoomaACHWithdrawMethod.Builder nmachb = uam.api.v1.NuveiMazoomaACHWithdrawMethod.newBuilder();

                if (StringUtils.isNotEmpty(nmach.getFiAccountNumber())) {
                    nmachb.setFiAccountNumber(nmach.getFiAccountNumber());
                }
                if (StringUtils.isNotEmpty(nmach.getFiAccountType())) {
                    nmachb.setFiAccountType(nmach.getFiAccountType());
                }
                if (StringUtils.isNotEmpty(nmach.getFiRouting())) {
                    nmachb.setFiRouting(nmach.getFiRouting());
                }
                if (StringUtils.isNotEmpty(nmach.getUserPaymentOptionId())) {
                    nmachb.setUserPaymentOptionId(nmach.getUserPaymentOptionId());
                }

                nmachb.setCountryCode("US");
                nmachb.setCurrency(req.getCurrency());
                nmachb.setAmount(req.getAmount().toString());
                nmachb.setEmail(req.getEmail());

                if (StringUtils.isNotEmpty(nmach.getFirstName())) {
                    nmachb.setFirstName(nmach.getFirstName());
                }
                if (StringUtils.isNotEmpty(nmach.getLastName())) {
                    nmachb.setLastName(nmach.getLastName());
                }
                if (StringUtils.isNotEmpty(nmach.getAddr())) {
                    nmachb.setAddress(nmach.getAddr());
                }
                if (StringUtils.isNotEmpty(nmach.getCity())) {
                    nmachb.setCity(nmach.getCity());
                }
                if (StringUtils.isNotEmpty(nmach.getState())) {
                    nmachb.setState(nmach.getState());
                }
                if (StringUtils.isNotEmpty(nmach.getZip())) {
                    nmachb.setZip(nmach.getZip());
                }
                if (StringUtils.isNotEmpty(nmach.getDobDay()) && StringUtils.isNotEmpty(nmach.getDobMonth()) && StringUtils.isNotEmpty(nmach.getDobYear())) {
                    nmachb.setDateOfBirth(
                            LocalDate.of(Integer.parseInt(nmach.getDobYear()), Integer.parseInt(nmach.getDobMonth()), Integer.parseInt(nmach.getDobDay()))
                                    .toString());
                }

                wmb.setMazoomaAch(nmachb.build());
            } else if (method.getWithdrawMethod() instanceof TrustlyWithdrawMethod) {
                // TrustlyWithdrawMethod trustly = (TrustlyWithdrawMethod) method.getWithdrawMethod();
                uam.api.v1.TrustlyWithdrawMethod.Builder trustlyBuilder = uam.api.v1.TrustlyWithdrawMethod.newBuilder();
                wmb.setTrustly(trustlyBuilder);
            } else if (method.getWithdrawMethod() instanceof SkrillWithdrawMethod) {
                SkrillWithdrawMethod skr = (SkrillWithdrawMethod) method.getWithdrawMethod();

                uam.api.v1.SkrillWithdrawMethod.Builder skrill = uam.api.v1.SkrillWithdrawMethod.newBuilder();
                skrill.setEmail(skr.getEmail().trim());

                wmb.setSkrill(skrill.build());
            } else if (method.getWithdrawMethod() instanceof PayperWithdrawMethod) {
                if (StringUtils.isNotEmpty(method.getCode())) {
                    sreqb.setCode(method.getCode());
                }
                PayperWithdrawMethod pwm = (PayperWithdrawMethod) method.getWithdrawMethod();
                uam.api.v1.PayperWithdrawMethod.Builder payper = uam.api.v1.PayperWithdrawMethod.newBuilder();
                if (StringUtils.isNotEmpty(pwm.getEmail())) {
                    payper.setEmail(pwm.getEmail());
                }
                if (StringUtils.isNotEmpty(pwm.getPhone())) {
                    payper.setPhone(pwm.getPhone());
                }
                wmb.setPayper(payper);
            } else if (method.getWithdrawMethod() instanceof StandardAchWithdrawMethod) {
                // ~ ignore deprecated field
                sreqb.setCode("");
                var sawm = (StandardAchWithdrawMethod) method.getWithdrawMethod();
                var standardAch = uam.api.v1.StandardAchWithdrawMethod.newBuilder();
                if (StringUtils.isNotEmpty(sawm.getBankAccountType())) {
                    standardAch.setBankAccountType(BankAccountType.valueOf(sawm.getBankAccountType().toUpperCase()));
                }
                if (StringUtils.isNotEmpty(sawm.getBankAccountNumber())) {
                    standardAch.setBankAccountNumber(sawm.getBankAccountNumber());
                }
                if (StringUtils.isNotEmpty(sawm.getBankAccountRoutingNumber())) {
                    standardAch.setBankAccountRouting(sawm.getBankAccountRoutingNumber());
                }
                wmb.setStandardAch(standardAch);
            } else if (method.getWithdrawMethod() instanceof fe.api.payment.model.StandardCanadaAchWithdrawMethod sawm) {
                // ~ ignore deprecated field
                sreqb.setCode("");
                var standardCaAch = uam.api.v1.StandardCanadaAchWithdrawMethod.newBuilder();
                if (StringUtils.isNotEmpty(sawm.getBankAccountNumber())) {
                    standardCaAch.setBankAccountNumber(sawm.getBankAccountNumber());
                }
                if (StringUtils.isNotEmpty(sawm.getTransitNumber())) {
                    standardCaAch.setTransitNumber(sawm.getTransitNumber());
                }
                if (StringUtils.isNotEmpty(sawm.getInstitutionNumber())) {
                    standardCaAch.setInstitutionNumber(sawm.getInstitutionNumber());
                }
                wmb.setStandardCanadaAch(standardCaAch);
            } else if (method.getWithdrawMethod() instanceof fe.api.payment.model.StandardBsbAchWithdrawMethod sawm) {
                // ~ ignore deprecated field
                sreqb.setCode("");
                var standardBsbAch = uam.api.v1.StandardBsbAchWithdrawMethod.newBuilder();
                if (StringUtils.isNotEmpty(sawm.getBankAccountNumber())) {
                    standardBsbAch.setBankAccountNumber(sawm.getBankAccountNumber());
                }
                if (StringUtils.isNotEmpty(sawm.getBsbRoutingNumber())) {
                    standardBsbAch.setBsbRoutingNumber(sawm.getBsbRoutingNumber());
                }
                wmb.setStandardBsbAch(standardBsbAch);
            } else if (method.getWithdrawMethod() instanceof fe.api.payment.model.CryptoWithdrawMethod cwm) {
                // ~ ignore deprecated field
                sreqb.setCode("");
                var met = uam.api.v1.CryptoWithdrawMethod.newBuilder();
                met.setCurrency(CryptoCurrency.Enum.valueOf(cwm.getCurrency().name().toUpperCase()));
                met.setNetwork(CryptoNetwork.Enum.valueOf(cwm.getNetwork().name().toUpperCase()));
                met.setWallet(cwm.getWallet());
                wmb.setCrypto(met);
            } else if (method.getWithdrawMethod() instanceof fe.api.payment.model.AeroPayWithdrawMethod awm) {
                wmb.setAeroPay(AeroPayWithdrawMethod.newBuilder()
                        .setPhone(awm.getPhone())
                        .setBankAccountName(awm.getBankAccountName())
                        .setBankName(awm.getBankName())
                        .setBankAccountNumber(awm.getBankAccountNumber())
                );
            }

            sreqb.setMethod(wmb.build());
        }
        return sreqb;
    }

    private void createRedeemMoney(RestEasyClient client,
                                   uam.api.v1.RedeemMoneyRequest.Builder sreqb,
                                   AsciiString routingKey,
                                   RequestMessageWrapper reqw,
                                   RequestAck ack,
                                   long when) {
        var wrapped = paymentServiceApi.createRedeemMoney(sreqb.build(), routingKey);
        wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
            @Override
            public void accept(ResponseWrapperFacade srespw) throws Exception {
                GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                RedeemMoneyResponseBody resp = (RedeemMoneyResponseBody) respw.getBody();

                Any any = srespw.body();
                setStatusAndMeta(respw, srespw, when);
                var status = srespw.status();

                if (status.isOK()) {
                    uam.api.v1.RedeemMoneyResponse sresp = any.unpack(uam.api.v1.RedeemMoneyResponse.class);

                    resp.setId(sresp.getId());
                    resp.setKyc(sresp.getKyc());
                    resp.setProvider(sresp.getProvider());
                    resp.setKycStatus(KycStatus.fromString(sresp.getKycStatus()));
                }

                client.writeAndFlush(respw, srespw.cacheControl());
            }
        });
    }

    private static Status validateRequest(RedeemMoneyRequestBody req) {
        var invalidFields = new ArrayList<String>();

        if (!isCurrencyValid(req.getCurrency())) {
            invalidFields.add("currency");
        }

        if (invalidFields.isEmpty()) {
            return Status.newBuilder().build();
        }

        return Status.newBuilder()
                .setErrorCode(Code.ERR_BAD_REQUEST)
                .setReason(Reason.BAD_REQUEST)
                .setErrorText("Invalid field(s): %s".formatted(String.join(", ", invalidFields)))
                .build();
    }

    private static boolean isCurrencyValid(String requestCurrency) {
        return Arrays.stream(Currency.values()).anyMatch(currency -> currency.toString().equals(requestCurrency));
    }
}
