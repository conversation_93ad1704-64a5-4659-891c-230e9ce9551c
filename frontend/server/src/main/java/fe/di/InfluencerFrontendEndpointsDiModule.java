package fe.di;

import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.cfg.ApplicationProperties;

import api.v1.ApiFactory;
import fe.MessageDispatcher;
import fe.api.influencer.openapi.InfluencerApi;
import fe.api.influencer.openapi.RafMilestoneApi;
import fe.endpoints.DefaultInfluencerApiEndpoint;
import fe.endpoints.DefaultInfluencerDocumentationEndpoint;
import fe.endpoints.DefaultRafMilestoneApiEndpoint;
import fe.endpoints.InfluencerDocumentationEndpoint;
import fe.handlers.influencer.InfluencerHandlersPackageMarker;
import fe.services.CookieStorage;
import io.micrometer.core.instrument.MeterRegistry;

@Configuration
@ComponentScan(basePackageClasses = { InfluencerHandlersPackageMarker.class })
public class InfluencerFrontendEndpointsDiModule {

    @Bean
    public InfluencerApi influencerApiEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            MessageDispatcher dispatcher,
            CookieStorage cookieStorage,
            ApiFactory apiFactory) {
        return new DefaultInfluencerApiEndpoint(props, cloud, meterRegistry, dispatcher, cookieStorage, apiFactory);
    }

    @Bean
    public RafMilestoneApi rafMilestoneApiEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            MessageDispatcher dispatcher,
            CookieStorage cookieStorage,
            ApiFactory apiFactory) {
        return new DefaultRafMilestoneApiEndpoint(props, cloud, meterRegistry, dispatcher, cookieStorage, apiFactory);
    }

    @Bean
    public InfluencerDocumentationEndpoint influencerDocumentationEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            ApiFactory apiFactory) {
        return new DefaultInfluencerDocumentationEndpoint(props, apiFactory, cloud, meterRegistry);
    }
}
