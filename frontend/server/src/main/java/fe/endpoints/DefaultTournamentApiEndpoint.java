package fe.endpoints;

import org.jboss.resteasy.spi.HttpRequest;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.annotations.Tag;
import com.turbospaces.cfg.ApplicationProperties;

import api.v1.ApiFactory;
import fe.AbstractOpenApiEndpoint;
import fe.MessageDispatcher;
import fe.api.tournament.GetFeatureFlagRequest;
import fe.api.tournament.GetLeaderboardRequest;
import fe.api.tournament.GetTournamentsRequest;
import fe.api.tournament.GetTournamentDetailsByGameRequest;
import fe.api.tournament.GetTournamentGamesRequest;
import fe.api.tournament.JoinTournamentRequest;
import fe.api.tournament.TournamentsTournamentApi;
import fe.services.CookieStorage;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.channel.ChannelHandlerContext;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import org.springframework.cloud.DynamicCloud;

@ApiEndpoint(metricTags = { @Tag(key = "domain", value = "tournament") })
public class DefaultTournamentApiEndpoint extends AbstractOpenApiEndpoint implements TournamentsTournamentApi {
    @Inject
    public DefaultTournamentApiEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            MessageDispatcher dispatcher,
            CookieStorage cookieStorage,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, dispatcher, cookieStorage, apiFactory);
    }

    @Override
    public void getTournamentGames(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse asyncResponse)
            throws Throwable {
        send(asyncResponse, ctx, httpReq, GetTournamentGamesRequest.class.getSimpleName());
    }

    @Override
    public void joinTournament(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse asyncResponse)
            throws Throwable {
        send(asyncResponse, ctx, httpReq, JoinTournamentRequest.class.getSimpleName());
    }

    @Override
    public void getTournaments(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse asyncResponse)
            throws Throwable {
        send(asyncResponse, ctx, httpReq, GetTournamentsRequest.class.getSimpleName());
    }

    @Override
    public void getTournamentDetailsByGame(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse asyncResponse)
            throws Throwable {
        send(asyncResponse, ctx, httpReq, GetTournamentDetailsByGameRequest.class.getSimpleName());
    }

    @Override
    public void getFeatureFlag(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse asyncResponse) throws Throwable {
        send(asyncResponse, ctx, httpReq, GetFeatureFlagRequest.class.getSimpleName());
    }

    @Override
    public void getLeaderboard(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse asyncResponse)
            throws Throwable {
        send(asyncResponse, ctx, httpReq, GetLeaderboardRequest.class.getSimpleName());
    }
}
