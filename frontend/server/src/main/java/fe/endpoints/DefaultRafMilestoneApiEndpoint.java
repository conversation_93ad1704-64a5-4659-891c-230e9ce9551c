package fe.endpoints;

import org.jboss.resteasy.spi.HttpRequest;
import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.annotations.Tag;
import com.turbospaces.cfg.ApplicationProperties;

import api.v1.ApiFactory;
import fe.AbstractOpenApiEndpoint;
import fe.MessageDispatcher;
import fe.api.influencer.ClaimMilestoneStepRewardRequest;
import fe.api.influencer.GetPersonalizedMilestoneSchemaRequest;
import fe.api.influencer.openapi.RafMilestoneApi;
import fe.services.CookieStorage;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.channel.ChannelHandlerContext;
import jakarta.ws.rs.container.AsyncResponse;
import org.springframework.cloud.DynamicCloud;

@ApiEndpoint(metricTags = {@Tag(key = "domain", value = "raf-milestone")})
public class DefaultRafMilestoneApiEndpoint extends AbstractOpenApiEndpoint implements RafMilestoneApi {
    
    public DefaultRafMilestoneApiEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            MessageDispatcher dispatcher,
            CookieStorage cookieStorage,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, dispatcher, cookieStorage, apiFactory);
    }

    @Override
    public void getPersonalizedMilestoneSchema(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, GetPersonalizedMilestoneSchemaRequest.class.getSimpleName());
    }

    @Override
    public void claimMilestoneStepReward(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, ClaimMilestoneStepRewardRequest.class.getSimpleName());
    }
}