package fe.endpoints;

import org.jboss.resteasy.spi.HttpRequest;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.annotations.Tag;
import com.turbospaces.cfg.ApplicationProperties;

import api.v1.ApiFactory;
import fe.AbstractOpenApiEndpoint;
import fe.MessageDispatcher;
import fe.api.influencer.CancelGiveawayRequest;
import fe.api.influencer.ClaimMilestoneStepRewardRequest;
import fe.api.influencer.CreateCoinGiveawayRequest;
import fe.api.influencer.CreateFreeSpinGiveawayRequest;
import fe.api.influencer.FinalizeCoinGiveawayRequest;
import fe.api.influencer.FinalizeFreeSpinGiveawayRequest;
import fe.api.influencer.GetActiveGiveawayByAccountRequest;
import fe.api.influencer.GetActiveGiveawayRequest;
import fe.api.influencer.GetGiveawayRequest;
import fe.api.influencer.GetGiveawaySubscribersNumberRequest;
import fe.api.influencer.GetInfluencerInfoRequest;
import fe.api.influencer.OptInGiveawayRequest;
import fe.api.influencer.openapi.InfluencerApi;
import fe.services.CookieStorage;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.channel.ChannelHandlerContext;
import jakarta.ws.rs.container.AsyncResponse;
import org.springframework.cloud.DynamicCloud;

@ApiEndpoint(metricTags = {@Tag(key = "domain", value = "influencer")})
public class DefaultInfluencerApiEndpoint extends AbstractOpenApiEndpoint implements InfluencerApi {
    
    public DefaultInfluencerApiEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            MessageDispatcher dispatcher,
            CookieStorage cookieStorage,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, dispatcher, cookieStorage, apiFactory);
    }
    
    @Override
    public void getGiveaway(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, GetGiveawayRequest.class.getSimpleName());
    }
    
    @Override
    public void getActiveGiveaway(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, GetActiveGiveawayRequest.class.getSimpleName());
    }

    @Override
    public void getActiveGiveawayByAccount(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable { 
        send(async, ctx, httpReq, GetActiveGiveawayByAccountRequest.class.getSimpleName());
    }

    @Override
    public void getInfluencerInfo(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, GetInfluencerInfoRequest.class.getSimpleName());
    }

    @Override
    public void optInGiveaway(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, OptInGiveawayRequest.class.getSimpleName());
    }

    @Override
    public void getGiveawaySubscribersNumber(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, GetGiveawaySubscribersNumberRequest.class.getSimpleName());
    }

    @Override
    public void cancelGiveaway(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, CancelGiveawayRequest.class.getSimpleName());
    }

    @Override
    public void createCoinGiveaway(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, CreateCoinGiveawayRequest.class.getSimpleName());
    }

    @Override
    public void createFreeSpinGiveaway(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, CreateFreeSpinGiveawayRequest.class.getSimpleName());   
    }
    
    @Override
    public void finalizeCoinGiveaway(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, FinalizeCoinGiveawayRequest.class.getSimpleName());
    }

    @Override
    public void finalizeFreeSpinGiveaway(HttpRequest httpReq, ChannelHandlerContext ctx, AsyncResponse async) throws Throwable {
        send(async, ctx, httpReq, FinalizeFreeSpinGiveawayRequest.class.getSimpleName());
    }
}