package fe;

import static frontend.PaymentFrontendConstants.IS_MOBILE_APP_COOKIE;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import fe.api.SocketClient;
import fe.api.payment.model.AeroPayPaymentMethod;
import fe.api.payment.model.AeroPayWithdrawMethod;
import fe.api.payment.model.LinkBankAccountResponseBody;
import fe.api.payment.model.OfferPlatform;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import api.v1.CommonMappers;
import api.v1.PagingInfo;
import common.utils.ProtoUtils;
import fe.api.payment.model.AcceptedPaymentTerm;
import fe.api.payment.model.BalanceChange;
import fe.api.payment.model.CardPaymentMethod;
import fe.api.payment.model.CreatePayperPaymentMethodRequestBody;
import fe.api.payment.model.CryptoCurrency;
import fe.api.payment.model.CryptoCurrencyData;
import fe.api.payment.model.CryptoNetwork;
import fe.api.payment.model.CryptoNetworkData;
import fe.api.payment.model.CryptoPaymentMethod;
import fe.api.payment.model.CryptoWithdrawMethod;
import fe.api.payment.model.Currency;
import fe.api.payment.model.CurrencyType;
import fe.api.payment.model.GetOffersResponseBody;
import fe.api.payment.model.GetPaymentMethodsResponseBodyMethodsInner;
import fe.api.payment.model.GetPaymentOrderResponseBody;
import fe.api.payment.model.GetWithdrawMethodsResponseBody;
import fe.api.payment.model.MassPayWithdrawMethod;
import fe.api.payment.model.NuveiMazoomaPaymentMethod;
import fe.api.payment.model.NuveiMazoomaWithdrawMethod;
import fe.api.payment.model.OfferInfo;
import fe.api.payment.model.OrderError;
import fe.api.payment.model.OrderStatus;
import fe.api.payment.model.PageRequest;
import fe.api.payment.model.Pagination;
import fe.api.payment.model.PaymentOperation;
import fe.api.payment.model.PayperPaymentMethod;
import fe.api.payment.model.PayperWithdrawMethod;
import fe.api.payment.model.Price;
import fe.api.payment.model.PrizeoutWithdrawMethod;
import fe.api.payment.model.PurchaseItem;
import fe.api.payment.model.PurchaseMethod;
import fe.api.payment.model.RedeemItem;
import fe.api.payment.model.RedeemLimitPolicy;
import fe.api.payment.model.RedeemStatus;
import fe.api.payment.model.RedeemUserStatus;
import fe.api.payment.model.SkrillPaymentMethod;
import fe.api.payment.model.SkrillWithdrawMethod;
import fe.api.payment.model.SpecialEventOffer;
import fe.api.payment.model.StandardAchWithdrawMethod;
import fe.api.payment.model.StandardBsbAchWithdrawMethod;
import fe.api.payment.model.StandardCanadaAchWithdrawMethod;
import fe.api.payment.model.TransactionDetails;
import fe.api.payment.model.TransactionLimit;
import fe.api.payment.model.TrustlyPaymentMethod;
import fe.api.payment.model.TrustlyWithdrawMethod;
import fe.api.payment.model.UserIntegrationType;
import fe.api.payment.model.UserPaymentMode;
import fe.api.payment.model.WithdrawMethod;
import fe.api.payment.model.WithdrawMethodObject;
import fe.api.payment.model.WithdrawMethodType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import payment.api.v1.GetAccountPurchaseLimitResponse;
import payment.api.v1.Pageable;
import payment.api.v1.Purchase;
import payment.api.v1.RedeemMoneyPolicy;
import payment.api.v1.internal.AccountPaymentInfo;
import payment.model.RedeemStatusSpec;
import uam.api.v1.AppMetadata;
import uam.api.v1.BillingAddress;
import uam.api.v1.CreatePayperPaymentMethodRequest;
import uam.api.v1.Identity;
import uam.api.v1.PaymentMethodInfo;
import uam.api.v1.PaymentOrderError;
import uam.api.v1.RedeemMoneyInfo;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PaymentMappers {
    public static final String PLATFORM_COOKIE = "platform";
    public static final String APP_NAME_COOKIE = "appName";
    public static final String APP_VERSION_COOKIE = "appVersion";

    public static List<CurrencyType> mapCurrencies(List<payment.api.v1.CurrencyType.Enum> types) {
        return types.stream().map(t -> CurrencyType.valueOf(t.name().toUpperCase())).toList();
    }

    public static void setRedeemInfo(RedeemItem redeem, uam.api.v1.RedeemMoneyInfo info) {
        redeem.setId(info.getId());
        redeem.setCurrency(Currency.valueOf(info.getBaseCurrency().toUpperCase()));
        redeem.setStatus(RedeemStatus.valueOf(info.getStatus().toUpperCase()));
        RedeemStatusSpec.valueOf(info.getStatus().toUpperCase()).getUserStatus()
                .ifPresent(userStatus -> redeem.setRedeemStatus(RedeemUserStatus.fromString(userStatus.code())));
        if (StringUtils.isNotEmpty(info.getBaseAmount())) {
            redeem.setAmount(new BigDecimal(info.getBaseAmount()));
        }
        if (info.getCreatedAt() > 0) {
            redeem.setCreatedAt(new Date(info.getCreatedAt()));
        }
        if (info.getLockedAt() > 0) {
            redeem.setLockedAt(new Date(info.getLockedAt()));
        }
        if (info.getPreConfirmedAt() > 0) {
            redeem.setPreConfirmedAt(new Date(info.getPreConfirmedAt()));
        }
        redeem.setProvider(info.getProvider());

        ProtoUtils.getOptionalValue(info.getPaymentDetails()).ifPresent(redeem::setPaymentDetails);

        if (info.hasCryptoPaymentData()) {
            var data = info.getCryptoPaymentData();
            redeem.setCryptoPaymentDetails(fromCryptoPurchaseData(data));
        }
    }

    public static void setPaymentOrderInfo(GetPaymentOrderResponseBody order, uam.api.v1.PaymentOrderInfo info) {
        order.setId(info.getId());
        order.setTransactionId(UUID.fromString(info.getTransactionId()));
        order.setProvider(info.getProvider());
        order.setCurrency(info.getCurrency());
        order.setAmount(new BigDecimal(info.getAmount()));
        order.setSuccess(info.getSuccess());
        if (StringUtils.isNotEmpty(info.getStatus())) {
            order.setStatus(info.getStatus());
        }
        if (StringUtils.isNotEmpty(info.getCode())) {
            order.setCode(info.getCode());
        }
        if (info.getCreatedAt() > 0) {
            order.setCreatedAt(new Date(info.getCreatedAt()));
        }
        if (info.hasOffer()) {
            order.setOffer(toOffer(info.getOffer()));
        }
        order.setInternalStatus(OrderStatus.valueOf(info.getStatusSpec().name().toUpperCase()));
        order.setIsFirstDeposit(info.getIsFistDeposit());
        order.setFailReason(info.getFailReason());
        if (info.hasCryptoPaymentData()) {
            var data = info.getCryptoPaymentData();
            order.setCryptoPurchaseDetails(fromCryptoPurchaseData(data));
        }
        if (info.hasSweepsTakeCoins()) {
            order.setSweepsTakeCoins(new BigDecimal(info.getSweepsTakeCoins()));
        }
        if (info.hasGoldCoins()) {
            order.setGoldCoins(new BigDecimal(info.getGoldCoins()));
        }
    }

    public static void setPaymentMethodInfo(GetPaymentOrderResponseBody order, PaymentMethodInfo info) {
        order.setCity(info.getCity());
        order.setZip(info.getZip());
    }

    public static Optional<OrderError> toOrderBodyError(PaymentOrderError orderError, boolean exposeCardNetworkError) {
        String cardNetworkError = orderError.getCardNetworkError();
        OrderError error = new OrderError();
        if (exposeCardNetworkError) {
            error.setCardNetworkError(cardNetworkError);
        }
        error.setMessage(orderError.getMessage());
        if (!orderError.getRescueProvidersList().isEmpty()) {
            error.setRescueProviders(orderError.getRescueProvidersList().stream().map(pp -> pp.name().toLowerCase()).toList());
        }
        if (StringUtils.isNotEmpty(error.getMessage()) || StringUtils.isNotEmpty(error.getCardNetworkError()) || (error.getRescueProviders() != null && !error.getRescueProviders().isEmpty())) {
            return Optional.of(error);
        }
        return Optional.empty();
    }

    public static fe.api.payment.model.OfferInfo toOffer(uam.api.v1.OfferTemplateInfo offer) {
        fe.api.payment.model.OfferInfo info = new fe.api.payment.model.OfferInfo();
        SpecialEventOffer specialEvent = new SpecialEventOffer();
        info.setCode(offer.getCode());
        info.setTitle(offer.getTitle());
        info.setVipLevel(offer.getVipLevel());
        info.setPrice(new BigDecimal(offer.getPrice()));
        info.setCurrency(offer.getCurrency());
        info.setTags(offer.getTagsList());
        info.setBannerImageUrl(offer.getBannerImageUrl());
        info.setPopUpImageUrl(offer.getPopUpImageUrl());
        info.setSpecialOfferUrl(offer.getSpecialOfferImageURL());
        info.setPriority(offer.getPriority());
        info.setShowStickybar(offer.getShowStickybar());
        info.setShowTimeLeft(offer.getShowTimeLeft());
        info.setFreeSpins(offer.getFreeSpins());
        if (StringUtils.isNotEmpty(offer.getPlatform())) {
            info.setPlatform(OfferPlatform.fromString(offer.getPlatform()));
        }
        if (StringUtils.isNotEmpty(offer.getOfferType())) {
            info.setOfferType(OfferInfo.OfferTypeEnum.fromString(offer.getOfferType()));
        }
        if (StringUtils.isNotEmpty(offer.getGoldAmount())) {
            info.setGoldMoney(new BigDecimal(offer.getGoldAmount()));
        }
        if (StringUtils.isNotEmpty(offer.getGoldFirstAmount())) {
            info.setGoldFistOffer(new BigDecimal(offer.getGoldFirstAmount()));
        }
        if (StringUtils.isNotEmpty(offer.getSweepstakeAmount())) {
            info.setSweepstakeMoney(new BigDecimal(offer.getSweepstakeAmount()));
        }
        if (StringUtils.isNotEmpty(offer.getSweepstakeFirstAmount())) {
            info.setSweepstakeFirstOffer(new BigDecimal(offer.getSweepstakeFirstAmount()));
        }
        if (StringUtils.isNotEmpty(offer.getVipPoints())) {
            info.setVipPoints(new BigDecimal(offer.getVipPoints()));
        }
        if (StringUtils.isNotEmpty(offer.getOldPrice())) {
            info.setOldPrice(new BigDecimal(offer.getOldPrice()));
        }
        if (StringUtils.isNotEmpty(offer.getBaseOldPrice())) {
            info.setBaseOldPrice(new BigDecimal(offer.getBaseOldPrice()));
        }
        if (offer.getEndAt() != 0) {
            info.setEndAt(offer.getEndAt());
        }
        if (StringUtils.isNotEmpty(offer.getBasePrice())) {
            info.setBasePrice(new BigDecimal(offer.getBasePrice()));
        }
        if (StringUtils.isNotEmpty(offer.getBaseCurrency())) {
            info.setBaseCurrency(offer.getBaseCurrency());
        }
        if (StringUtils.isNotEmpty(offer.getUpgradeOffer().getCode())) {
            info.setUpgradeOffer(toOffer(offer.getUpgradeOffer()));
        }
        if (StringUtils.isNotEmpty(offer.getExternalRewardCode())) {
            info.setExternalRewardCode(offer.getExternalRewardCode());
        }
        specialEvent.setIconImageUrl(offer.getIconImageURL());
        specialEvent.setBackgroundImageUrl(offer.getBackgroundImageURL());
        specialEvent.setBackgroundBorder(offer.getBackgroundBorder());
        info.setHomepageBannerImageUrl(offer.getHomepageBannerImageURL());
        info.setSpecialEventOffer(specialEvent);
        return info;
    }

    public static uam.api.v1.WithdrawMethod toWithdrawMethod(WithdrawMethod wm) {
        if (wm.getType() == WithdrawMethodType.AEROPAY) {
            AeroPayWithdrawMethod method = (AeroPayWithdrawMethod) wm;
            return uam.api.v1.WithdrawMethod.newBuilder()
                    .setAeroPay(uam.api.v1.AeroPayWithdrawMethod.newBuilder()
                            .setBankName(method.getBankName())
                            .setBankAccountNumber(method.getBankAccountNumber())
                            .setBankAccountName(method.getBankAccountName())
                            .setPhone(method.getPhone())
                            .setAccountId(method.getAccountId()))
                    .build();
        }
        throw new UnsupportedOperationException("Unsupported withdraw method type: " + wm.getType());
    }

    public static WithdrawMethodObject toWithdrawMethod(uam.api.v1.WithdrawMethod wm) {
        WithdrawMethodObject info = new WithdrawMethodObject();
        info.setCode(wm.getCode());

        if (wm.hasTrustly()) {
            uam.api.v1.TrustlyWithdrawMethod methodInfo = wm.getTrustly();
            TrustlyWithdrawMethod paymentMethod = new TrustlyWithdrawMethod();
            paymentMethod.setReference(methodInfo.getMerchantReference());
            info.setWithdrawMethod(paymentMethod);
        } else if (wm.hasSkrill()) {
            uam.api.v1.SkrillWithdrawMethod methodInfo = wm.getSkrill();

            SkrillWithdrawMethod paymentMethod = new SkrillWithdrawMethod();
            paymentMethod.setEmail(methodInfo.getEmail());

            info.setWithdrawMethod(paymentMethod);
        } else if (wm.hasPrizeout()) {
            uam.api.v1.PrizeoutWithdrawMethod methodInfo = wm.getPrizeout();

            PrizeoutWithdrawMethod paymentMethod = new PrizeoutWithdrawMethod();
            paymentMethod.setEmail(methodInfo.getEmail());

            info.setWithdrawMethod(paymentMethod);
        } else if (wm.hasMazoomaAch()) {
            uam.api.v1.NuveiMazoomaACHWithdrawMethod methodInfo = wm.getMazoomaAch();

            NuveiMazoomaWithdrawMethod paymentMethod = new NuveiMazoomaWithdrawMethod();
            paymentMethod.setUserPaymentOptionId(methodInfo.getUserPaymentOptionId());
            paymentMethod.setFiAccountNumber(methodInfo.getFiAccountNumber());
            paymentMethod.setFiAccountType(methodInfo.getFiAccountType());
            paymentMethod.setFiRouting(methodInfo.getFiRouting());
            paymentMethod.setFiName(methodInfo.getFiName());

            info.setWithdrawMethod(paymentMethod);
        } else if (wm.hasMassPayAch()) {
            uam.api.v1.MassPayACHWithdrawMethod methodInfo = wm.getMassPayAch();

            MassPayWithdrawMethod paymentMethod = new MassPayWithdrawMethod();
            paymentMethod.setBankAccountNumber(methodInfo.getBankAccountNumber());
            paymentMethod.setBankAccountRoutingNumber(methodInfo.getBankAccountRouting());
            paymentMethod.setBankAccountType(methodInfo.getBankAccountType().name().toLowerCase());

            info.setWithdrawMethod(paymentMethod);
        } else if (wm.hasPayper()) {
            uam.api.v1.PayperWithdrawMethod methodInfo = wm.getPayper();

            PayperWithdrawMethod paymentMethod = new PayperWithdrawMethod();
            paymentMethod.setEmail(methodInfo.getEmail());
            paymentMethod.setPhone(methodInfo.getPhone());

            info.setWithdrawMethod(paymentMethod);
        } else if (wm.hasCrypto()) {
            var methodInfo = wm.getCrypto();

            var cryptoMethod = new CryptoWithdrawMethod();
            cryptoMethod.setWallet(methodInfo.getWallet());
            cryptoMethod.setCurrency(toCryptoCurrency(methodInfo.getCurrency()));
            cryptoMethod.setNetwork(toCryptoNetwork(methodInfo.getNetwork()));
            info.setWithdrawMethod(cryptoMethod);
        } else if (wm.hasAeroPay()) {
            var methodInfo = wm.getAeroPay();
            var aeroPayMethod = new AeroPayWithdrawMethod();
            aeroPayMethod.setBankName(methodInfo.getBankName());
            aeroPayMethod.setBankAccountNumber(methodInfo.getBankAccountNumber());
            aeroPayMethod.setBankAccountName(methodInfo.getBankAccountName());
            aeroPayMethod.setPhone(methodInfo.getPhone());
            aeroPayMethod.setAccountId(methodInfo.getAccountId());
            info.setWithdrawMethod(aeroPayMethod);
        } else if (wm.hasStandardAch()) {
            uam.api.v1.StandardAchWithdrawMethod methodInfo = wm.getStandardAch();
            StandardAchWithdrawMethod paymentMethod = new StandardAchWithdrawMethod();
            paymentMethod.setBankAccountType(methodInfo.getBankAccountType().name().toLowerCase());
            paymentMethod.setBankAccountNumber(methodInfo.getBankAccountNumber());
            paymentMethod.setBankAccountRoutingNumber(methodInfo.getBankAccountRouting());
            info.setWithdrawMethod(paymentMethod);
        } else if (wm.hasStandardBsbAch()) {
            uam.api.v1.StandardBsbAchWithdrawMethod methodInfo = wm.getStandardBsbAch();
            StandardBsbAchWithdrawMethod paymentMethod = new StandardBsbAchWithdrawMethod();
            paymentMethod.setBankAccountNumber(methodInfo.getBankAccountNumber());
            paymentMethod.setBsbRoutingNumber(methodInfo.getBsbRoutingNumber());
            info.setWithdrawMethod(paymentMethod);
        } else if (wm.hasStandardCanadaAch()) {
            uam.api.v1.StandardCanadaAchWithdrawMethod methodInfo = wm.getStandardCanadaAch();
            StandardCanadaAchWithdrawMethod paymentMethod = new StandardCanadaAchWithdrawMethod();
            paymentMethod.setBankAccountNumber(methodInfo.getBankAccountNumber());
            paymentMethod.setInstitutionNumber(methodInfo.getInstitutionNumber());
            paymentMethod.setTransitNumber(methodInfo.getTransitNumber());
            info.setWithdrawMethod(paymentMethod);
        }

        return info;
    }

    public static uam.api.v1.PaymentMethod toPaymentMethod(fe.api.payment.model.PaymentMethod pm) {
        if ("AeroPayPaymentMethod".equals(pm.getType())) {
            AeroPayPaymentMethod method = (AeroPayPaymentMethod) pm;
            return uam.api.v1.PaymentMethod.newBuilder()
                    .setAeroPay(uam.api.v1.AeroPayPaymentMethod.newBuilder()
                            .setBankName(method.getBankName())
                            .setBankAccountNumber(method.getBankAccountNumber())
                            .setBankAccountName(method.getBankAccountName())
                            .setPhone(method.getPhone())
                            .setAccountId(method.getAccountId()))
                    .build();
        }
        throw new UnsupportedOperationException("Unsupported withdraw method type: " + pm.getType());
    }

    public static GetPaymentMethodsResponseBodyMethodsInner toPaymentMethod(uam.api.v1.PaymentMethod pm) {
        GetPaymentMethodsResponseBodyMethodsInner info = new GetPaymentMethodsResponseBodyMethodsInner();
        info.setCode(pm.getCode());
        info.setProvider(pm.getProvider());
        info.setTokenize(pm.getTokenize());
        info.setDate(new Date(pm.getCreatedAt()));

        info.setId((long) Math.abs(pm.getCode().hashCode())); // TODO remove
        info.setToken(pm.getCode()); // TODO remove

        if (pm.hasCard()) {
            uam.api.v1.CardPaymentMethod methodInfo = pm.getCard();

            CardPaymentMethod paymentMethod = new CardPaymentMethod();
            paymentMethod.setName(methodInfo.getName());
            paymentMethod.setExpiryMonth(methodInfo.getExpiryMonth());
            paymentMethod.setExpiryYear(methodInfo.getExpiryYear());
            paymentMethod.setCardType(methodInfo.getCardType());
            paymentMethod.setMaskedCardNumber(methodInfo.getMaskedCardNumber());
            paymentMethod.setVerificationStatus(methodInfo.getVerificationStatus());
            paymentMethod.setFingerprint(methodInfo.getFingerprint());

            info.setPaymentMethod(paymentMethod);
        } else if (pm.hasTrustly()) {
            uam.api.v1.TrustlyPaymentMethod methodInfo = pm.getTrustly();
            TrustlyPaymentMethod paymentMethod = new TrustlyPaymentMethod();
            paymentMethod.setCode(methodInfo.getCode());
            paymentMethod.setBankName(methodInfo.getBankName());
            paymentMethod.setPaymentProviderId(methodInfo.getPaymentProviderId());
            paymentMethod.setAccountName(methodInfo.getAccountName());
            paymentMethod.setAccountNumber(methodInfo.getAccountNumberLast4());
            paymentMethod.setLastUsageAt(methodInfo.getLastUsageAt());

            info.setPaymentMethod(paymentMethod);
        } else if (pm.hasSkrill()) {
            uam.api.v1.SkrillPaymentMethod methodInfo = pm.getSkrill();

            SkrillPaymentMethod paymentMethod = new SkrillPaymentMethod();
            paymentMethod.setEmail(methodInfo.getEmail());
            paymentMethod.setLastUsageAt(methodInfo.getLastUsageAt());

            info.setPaymentMethod(paymentMethod);
        } else if (pm.hasMazooma()) {
            uam.api.v1.NuveiMazoomaPaymentMethod methodInfo = pm.getMazooma();

            NuveiMazoomaPaymentMethod paymentMethod = new NuveiMazoomaPaymentMethod();
            paymentMethod.setFiAccType(methodInfo.getFiAccType());
            paymentMethod.setFiAccLabel(methodInfo.getFiAccLabel());
            paymentMethod.setFiName(methodInfo.getFiName());

            info.setPaymentMethod(paymentMethod);
        } else if (pm.hasPayper()) {
            var methodInfo = pm.getPayper();
            var payperMethod = new PayperPaymentMethod();
            payperMethod.setEmail(methodInfo.getEmail());
            payperMethod.setPhone(methodInfo.getPhone());
            info.setPaymentMethod(payperMethod);
        } else if (pm.hasCrypto()) {
            var methodInfo = pm.getCrypto();
            var cryptoPaymentMethod = new CryptoPaymentMethod();
            cryptoPaymentMethod.setWallet(methodInfo.getWallet());
            cryptoPaymentMethod.setCurrency(toCryptoCurrency(methodInfo.getCurrency()));
            cryptoPaymentMethod.setNetwork(toCryptoNetwork(methodInfo.getNetwork()));
            info.setPaymentMethod(cryptoPaymentMethod);
        } else if (pm.hasAeroPay()) {
            var methodInfo = pm.getAeroPay();
            var aeroPayMethod = new AeroPayPaymentMethod();
            aeroPayMethod.setBankName(methodInfo.getBankName());
            aeroPayMethod.setBankAccountNumber(methodInfo.getBankAccountNumber());
            aeroPayMethod.setBankAccountName(methodInfo.getBankAccountName());
            aeroPayMethod.setAccountId(methodInfo.getAccountId());
            aeroPayMethod.setPhone(methodInfo.getPhone());
            info.setPaymentMethod(aeroPayMethod);
        }

        return info;
    }

    public static void addPurchaseLimits(GetAccountPurchaseLimitResponse sresp, GetOffersResponseBody resp) {
        ProtoUtils.getOptionalValue(sresp.getLimitAmount()).ifPresent(resp::setLimitAmount);
        ProtoUtils.getOptionalValue(sresp.getLimitPeriod()).ifPresent(resp::setLimitPeriod);
        ProtoUtils.getOptionalValue(sresp.getLimitAvailable()).ifPresent(resp::setLimitAvailable);
        if (sresp.getLimitEnd().getMonth() != 0) {
            resp.setLimitEnd(CommonMappers.toLocalDate(sresp.getLimitEnd()));
        }
    }

    public static void setPolicies(GetWithdrawMethodsResponseBody resp, uam.api.v1.WithdrawMethodPolicy policy) {
        if (policy.hasSkrill()) {
            resp.getPolicies().setSkrill(toWithdrawMethodPolicy(policy.getSkrill()));
        } else if (policy.hasPrizeout()) {
            resp.getPolicies().setPrizeout(toWithdrawMethodPolicy(policy.getPrizeout()));
        } else if (policy.hasMazoomaAch()) {
            resp.getPolicies().setMazoomaAch(toWithdrawMethodPolicy(policy.getMazoomaAch()));
        } else if (policy.hasTrustly()) {
            resp.getPolicies().setTrustly(toWithdrawMethodPolicy(policy.getTrustly()));
        } else if (policy.hasPayper()) {
            resp.getPolicies().setPayper(toWithdrawMethodPolicy(policy.getPayper()));
        } else if (policy.hasCrypto()) {
            resp.getPolicies().setPayper(toWithdrawMethodPolicy(policy.getCrypto()));
        } else if (policy.hasAeroPay()) {
            resp.getPolicies().setAeroPay(toWithdrawMethodPolicy(policy.getAeroPay()));
        } else if (policy.hasStandardAch()) {
            resp.getPolicies().setStandardAch(toWithdrawMethodPolicy(policy.getStandardAch()));
        } else if (policy.hasStandardCanadaAch()) {
            resp.getPolicies().setStandardCanadaAch(toWithdrawMethodPolicy(policy.getStandardCanadaAch()));
        } else if (policy.hasStandardBsbAch()) {
            resp.getPolicies().setStandardBsbAch(toWithdrawMethodPolicy(policy.getStandardBsbAch()));
        }
    }

    public static fe.api.payment.model.WithdrawMethodPolicy toWithdrawMethodPolicy(uam.api.v1.WithdrawMethodPolicyDropdown info) {
        fe.api.payment.model.WithdrawMethodPolicy policy = new fe.api.payment.model.WithdrawMethodPolicy();

        if (info.hasFiatRedeemPolicy()) {
            policy.setFiatRedeemPolicy(toRedeemPolicy(info.getFiatRedeemPolicy()));
        }
        if (info.hasSweepstakeRedeemPolicy()) {
            policy.setSweepstakeRedeemPolicy(toRedeemPolicy(info.getSweepstakeRedeemPolicy()));
        }
        if (info.hasNonMonetaryRedeemPolicy()) {
            policy.setNonMonetaryRedeemPolicy(toRedeemPolicy(info.getNonMonetaryRedeemPolicy()));
        }

        return policy;
    }

    public static fe.api.payment.model.RedeemMoneyPolicy toRedeemPolicy(uam.api.v1.RedeemMoneyPolicy info) {
        fe.api.payment.model.RedeemMoneyPolicy policy = new fe.api.payment.model.RedeemMoneyPolicy();

        policy.setMinAmount(new BigDecimal(info.getMinAmount()));
        policy.setMaxAmount(new BigDecimal(info.getMaxAmount()));
        if (info.hasAvailableAmount()) {
            policy.setAvailableToRedeem(new BigDecimal(info.getAvailableAmount()));
        }

        return policy;
    }

    public static uam.api.v1.BillingAddress.Builder toBillingAddress(fe.api.payment.model.BillingAddress billingAddress) {
        BillingAddress.Builder bab = BillingAddress.newBuilder();

        if (StringUtils.isNotEmpty(billingAddress.getFirstName())) {
            bab.setFirstName(billingAddress.getFirstName());
        }
        if (StringUtils.isNotEmpty(billingAddress.getLastName())) {
            bab.setLastName(billingAddress.getLastName());
        }
        if (StringUtils.isNotEmpty(billingAddress.getCountry())) {
            bab.setCountry(billingAddress.getCountry());
        }
        if (StringUtils.isNotEmpty(billingAddress.getCity())) {
            bab.setCity(billingAddress.getCity());
        }
        if (StringUtils.isNotEmpty(billingAddress.getStateOrProvince())) {
            bab.setStateOrProvince(billingAddress.getStateOrProvince());
        }
        if (StringUtils.isNotEmpty(billingAddress.getPostalCode())) {
            bab.setPostalCode(billingAddress.getPostalCode());
        }
        if (StringUtils.isNotEmpty(billingAddress.getStreet())) {
            bab.setStreet(billingAddress.getStreet());
        }
        if (StringUtils.isNotEmpty(billingAddress.getHouseNumberOrName())) {
            bab.setHouseNumberOrName(billingAddress.getHouseNumberOrName());
        }
        if (StringUtils.isNotEmpty(billingAddress.getDateOfBirth())) {
            bab.setDateOfBirth(billingAddress.getDateOfBirth());
        }

        return bab;
    }

    public static CreatePayperPaymentMethodRequest toCreatePayperPaymentMethodRequest(CreatePayperPaymentMethodRequestBody req,
                                                                                      Identity.Builder identity) {
        var builder = CreatePayperPaymentMethodRequest.newBuilder();
        builder.setIdentity(identity);
        if (StringUtils.isNotEmpty(req.getEmail())) {
            builder.setEmail(req.getEmail());
        }
        if (StringUtils.isNotEmpty(req.getPhone())) {
            builder.setPhone(req.getPhone());
        }
        return builder.build();
    }

    public static RedeemLimitPolicy toRedeemLimitPolicy(RedeemMoneyPolicy policy) {
        var redeemLimits = new RedeemLimitPolicy();
        if (StringUtils.isNotEmpty(policy.getMinAmount())) {
            redeemLimits.setMin(new BigDecimal(policy.getMinAmount()));
        }
        if (StringUtils.isNotEmpty(policy.getMaxAmount())) {
            redeemLimits.setMax(new BigDecimal(policy.getMaxAmount()));
        }
        if (StringUtils.isNotEmpty(policy.getAvailableAmount())) {
            redeemLimits.setAvailable(new BigDecimal(policy.getAvailableAmount()));
        }
        if (StringUtils.isNotEmpty(policy.getState())) {
            redeemLimits.setState(policy.getState());
        }
        return redeemLimits;
    }

    public static List<RedeemItem> toRedeemItems(List<RedeemMoneyInfo> redeems) {
        return redeems.stream().map(rmi -> {
            var item = new RedeemItem();
            PaymentMappers.setRedeemInfo(item, rmi);
            return item;
        }).toList();
    }
    
    public static List<PurchaseItem> toPurchaseItems(List<Purchase> dataList) {
        return dataList.stream().map(PaymentMappers::toPurchaseItem).toList();
    }

    public static PurchaseItem toPurchaseItem(Purchase purchase) {
        var item = new PurchaseItem();
        item.setTransactionId(UUID.fromString(purchase.getTransactionId()));
        item.setDateTime(Instant.ofEpochMilli(purchase.getCompletedAt()).atOffset(ZoneOffset.UTC));
        item.setBalanceChange(purchase.getBalanceChangeList().stream().map(PaymentMappers::toBalanceChange).toList());
        item.setPrice(purchase.getPriceList().stream().map(PaymentMappers::toPrice).toList());
        if (purchase.hasMethod()) {
            item.setMethod(toPurchaseMethod(purchase.getMethod()));
        }
        if (purchase.hasCryptoPurchaseData()) {
            var data = purchase.getCryptoPurchaseData();
            item.setCryptoPurchaseData(fromCryptoPurchaseData(data));
        }
        item.setInternalStatus(OrderStatus.valueOf(purchase.getStatusSpec().name().toUpperCase()));
        return item;
    }

    private static fe.api.payment.model.CryptoPurchaseData fromCryptoPurchaseData(uam.api.v1.CryptoPurchaseData data) {
        fe.api.payment.model.CryptoPurchaseData cr = new fe.api.payment.model.CryptoPurchaseData();
        if (data.hasAmount()) {
            cr.setAmount(new BigDecimal(data.getAmount()));
        }
        if (data.hasCurrency()) {
            cr.setCurrency(toCryptoCurrencyData(data.getCurrency()));
        }
        if (data.hasNetwork()) {
            cr.setNetwork(toCryptoNetworkData(data.getNetwork()));
        }
        if (data.hasWallet()) {
            cr.setWallet(data.getWallet());
        }
        if (data.hasTxHash()) {
            cr.setTxHash(data.getTxHash());
        }
        return cr;
    }

    public static PurchaseMethod toPurchaseMethod(payment.api.v1.PurchaseMethod purchaseMethod) {
        var method = new PurchaseMethod();
        method.setCardBrand(purchaseMethod.getCardBrand());
        method.setCardNumberMask(purchaseMethod.getCardNumberMask());
        method.setAccountNumberMask(purchaseMethod.getAccountNumberMask());
        method.setEmail(purchaseMethod.getEmail());
        method.setPaymentMode(mapPaymentMode(purchaseMethod.getPaymentMode()));
        method.setIntegrationType(mapIntegrationType(purchaseMethod.getIntegrationType()));
        return method;
    }

    public static TransactionDetails toTransactionDetails(AccountPaymentInfo paymentInfo) {
        var method = new TransactionDetails();
        method.setIsFirstOfferPurchase(paymentInfo.getOfferPurchaseInfo().getIsFirstPurchase());
        method.setFirstOfferPurchaseDate(new Date(paymentInfo.getOfferPurchaseInfo().getFirstPurchaseDate()));
        method.setIsFirstFiatDeposit(paymentInfo.getDepositInfo().getIsFirstPurchase());
        method.setFirstFiatDepositDate(new Date(paymentInfo.getDepositInfo().getFirstPurchaseDate()));
        return method;
    }

    private static UserPaymentMode mapPaymentMode(String paymentMode) {
        try {
            return UserPaymentMode.fromValue(paymentMode);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    public static UserIntegrationType mapIntegrationType(String integrationType) {
        if (integrationType.equals("pay_with_my_bank")) {
            return UserIntegrationType.TRUSTLY;
        }
        try {
            return UserIntegrationType.fromValue(integrationType);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    private static BalanceChange toBalanceChange(payment.api.v1.BalanceChange balanceChange) {
        var change = new BalanceChange();
        change.setAmount(balanceChange.getAmount());
        change.setCurrency(balanceChange.getCurrency());
        return change;
    }

    private static Price toPrice(payment.api.v1.Price price) {
        var p = new Price();
        p.setAmount(price.getAmount());
        p.setCurrency(price.getCurrency());
        p.setBaseAmount(price.getBaseAmount());
        p.setBaseCurrency(price.getBaseCurrency());
        return p;
    }
    
    public static Pagination toPagination(Pageable pageable) {
        var pagination = new Pagination();
        pagination.setTotalRecords(pageable.getTotalRecords());
        pagination.setCurrentPage(pageable.getCurrentPage());
        pagination.setTotalPages(pageable.getTotalPages());
        pagination.setHasNextPage(pageable.getHasNextPage());
        pagination.setHasPrevPage(pageable.getHasPrevPage());
        return pagination;
    }

    public static PagingInfo toPagingInfo(PageRequest pageRequest) {
        if (pageRequest == null) {
            return PagingInfo.getDefaultInstance();
        }
        var builder = PagingInfo.newBuilder();
        if (pageRequest.getPageNumber() != null) {
            builder.setOffset((pageRequest.getPageNumber() - 1) * pageRequest.getPageSize());
        }
        if (pageRequest.getPageSize() != null) {
            builder.setLimit(pageRequest.getPageSize());
        }
        return builder.build();
    }
    
    public static api.v1.Date toDate(LocalDate date) {
        return api.v1.Date.newBuilder()
                .setYear(date.getYear())
                .setMonth(date.getMonthValue())
                .setDay(date.getDayOfMonth())
                .build();
    }

    public static List<TransactionLimit> toLimits(List<payment.api.v1.TransactionLimit> limitsList) {
        return limitsList.stream().map(PaymentMappers::toLimit).toList();
    }

    private static TransactionLimit toLimit(payment.api.v1.TransactionLimit limit) {
        var txLimit = new TransactionLimit();
        txLimit.setOperation(PaymentOperation.fromString(limit.getOperation().toUpperCase()));
        txLimit.setCurrency(Currency.valueOf(limit.getCurrency().toUpperCase()));
        txLimit.setMin(new BigDecimal(limit.getMin()));
        txLimit.setMax(new BigDecimal(limit.getMax()));
        return txLimit;
    }

    public static CryptoCurrency toCryptoCurrency(uam.api.v1.CryptoCurrency.Enum currency) {
        return CryptoCurrency.fromString(currency.name().toUpperCase());
    }
    public static CurrencyType toCurrencyType(uam.api.v1.CurrencyType.Enum currency) {
        return CurrencyType.fromString(currency.name().toUpperCase());
    }
    public static uam.api.v1.CurrencyType.Enum toCurrencyTypeProto(CurrencyType currency) {
        return uam.api.v1.CurrencyType.Enum.valueOf(currency.name().toUpperCase());
    }
    public static CryptoNetwork toCryptoNetwork(uam.api.v1.CryptoNetwork.Enum network) {
        return CryptoNetwork.fromString(network.name().toUpperCase());
    }

    public static CryptoCurrencyData toCryptoCurrencyData(uam.api.v1.CryptoCurrency.Enum currency) {
        var data = new CryptoCurrencyData();
        data.setCode(toCryptoCurrency(currency));
        data.setLabel(label(data.getCode()));
        return data;
    }
    public static CryptoNetworkData toCryptoNetworkData(uam.api.v1.CryptoNetwork.Enum network) {
        var data = new CryptoNetworkData();
        data.setCode(toCryptoNetwork(network));
        data.setLabel(label(data.getCode()));
        return data;
    }

    public static String label(CryptoCurrency cryptoCurrency) {
       return switch (cryptoCurrency) {
            case USDT ->  "USD Tether";
            case USDC -> "USD Coin";
            case ETH -> "Ethereum";
            case BTC -> "Bitcoin";
            case LTC -> "Litecoin";
            case BCH -> "Bitcoin cash";
            case TST -> "Test USD";
            case CRO -> "Cronos";
            case APE -> "ApeCoin";
            case UNI -> "Uniswap";
            case POL -> "Proof Of Liquidity";
            case XRP -> "Ripple";
            default -> cryptoCurrency.name();
        };
    }

    public static String label(CryptoNetwork cryptoNetwork) {
       return switch (cryptoNetwork) {
            case BTC ->  "Bitcoin";
            case ETH ->  "Ethereum";
            case BNB ->  "Binance Coin";
            case LTC ->  "Litecoin";
            case TRX ->  "Tron";
            case POLYGON ->  "Polygon";
            case SOL ->  "Solana";
            case BCH ->  "Bitcoin cash";
            case AVAX ->  "Avax";
            case TETH ->  "Test Ethereum";
            case TON ->  "The Open Network";
            default -> cryptoNetwork.name() + " network";
        };
    }

    public static AcceptedPaymentTerm toAcceptedPaymentTerm(
            payment.api.v1.AcceptedPaymentTerm term) {

        AcceptedPaymentTerm acceptedTerm = new AcceptedPaymentTerm();
        acceptedTerm.setCode(term.getCode());
        acceptedTerm.setAcceptedAt(new Date(term.getAcceptedAt()));

        return acceptedTerm;
    }

    public static AppMetadata toAppMetadata(SocketClient client) {
        var metadata = AppMetadata.newBuilder();
        client.getCookie(PLATFORM_COOKIE).ifPresent(metadata::setPlatform);
        client.getCookie(APP_NAME_COOKIE).ifPresent(metadata::setAppName);
        client.getCookie(APP_VERSION_COOKIE).ifPresent(metadata::setAppVersion);
        return metadata.build();
    }

    public static List<LinkBankAccountResponseBody> toLinkBankAccountResponseBody(List<uam.api.v1.LinkBankAccountResponse> response) {
        return CollectionUtils.emptyIfNull(response).stream()
                .map(proto -> new LinkBankAccountResponseBody()
                        .accountId(proto.getAccountId())
                        .bankName(proto.getBankName())
                        .accountNumber(proto.getAccountNumber())
                        .selected(proto.getSelected())
                )
                .toList();
    }

    public static boolean isMobileApp(SocketClient client) {
        return client.getCookie(IS_MOBILE_APP_COOKIE).map(Boolean::parseBoolean).orElse(false);
    }
}
