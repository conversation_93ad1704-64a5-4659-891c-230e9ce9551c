package tools;

import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.DynamicPropertyFactory;
import com.turbospaces.cfg.FragmentProperties;
import fe.FrontendServerProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Slf4j
public class FrontendCleanupProperties {

    private static final List<Class<?>> PROPERTY_CLASSES = List.of(FrontendServerProperties.class);
    private static final String APPLICATION_PROPERTIES = "frontend-server/appplication.properties";

    public static void main(String[] args) throws Exception {
        var programArgs = parseProgramArgs(args);
        log.info("Mode: {}", programArgs.mode());
        var files = new ArrayList<String>();
        switch (programArgs.mode()) {
            case SINGLE_FILE -> files.add(programArgs.path());
            case CFG_DIR -> {
                try (var dirs = Files.list(Path.of(programArgs.path()))) {
                    dirs.filter(Files::isDirectory).forEach(dir -> {
                        var propertyFilePath = dir.resolve(APPLICATION_PROPERTIES);
                        log.info("Property file {} checking existence...", propertyFilePath);
                        if (Files.exists(propertyFilePath)) {
                            files.add(propertyFilePath.toString());
                            log.info("Property file found in directory: {}", dir);
                        } else {
                            log.info("Property file not found in directory: {}", dir);
                        }
                    });
                }
            }
        }
        var defaultValues = extractDefaultValuesFromPropertiesInCode();
        for (String file : files) {
            log.info("Cleaning up property file: {}", file);
            cleanUpApplicationProperties(file, defaultValues);
        }
    }

    private static ProgramArgs parseProgramArgs(String[] args) {
        if (args.length < 1) {
            log.info("Usage: CleanupProperties <path-to-application.properties>|<path-to-cfg-dir>");
            System.exit(1);
        }

        var p = Path.of(args[0]);
        if (p.toString().endsWith(".properties")) {
            if (!Files.exists(p)) {
                log.error("Error: {} does not exist", p);
                System.exit(1);
            }
            return new ProgramArgs(Mode.SINGLE_FILE, p.toString());
        } else {
            if (!Files.exists(p) || !Files.isDirectory(p)) {
                log.error("Error: {} does not exist or is not a directory", p);
                System.exit(1);
            }
            return new ProgramArgs(Mode.CFG_DIR, p.toString());
        }
    }

    private static void cleanUpApplicationProperties(String propertiesFilePath, Map<String, String> defaultValues) throws IOException {
        var properties = loadApplicationProperties(propertiesFilePath);
        log.info("Loaded {} properties from {}", properties.size(), propertiesFilePath);

        var toRemove = determinePropertiesToRemove(properties, defaultValues);

        updateApplicationProperties(propertiesFilePath, toRemove);
    }

    record ProgramArgs(Mode mode, String path) {
    }

    enum Mode {
        SINGLE_FILE,
        CFG_DIR
    }

    private static Properties loadApplicationProperties(String propertiesFilePath) throws IOException {
        var propertiesFile = new File(propertiesFilePath);

        if (!propertiesFile.exists() || !propertiesFile.isFile()) {
            log.error("Error: {} does not exist or is not a file", propertiesFilePath);
            System.exit(1);
        }

        var properties = new Properties();
        try (FileInputStream fis = new FileInputStream(propertiesFile)) {
            properties.load(fis);
        }
        return properties;
    }

    private static Map<String, String> extractDefaultValuesFromPropertiesInCode() throws ReflectiveOperationException {
        var result = new HashMap<String, String>();
        var propertyClasses = new ArrayList<>(PROPERTY_CLASSES);
        for (Class<?> propertyClass : new ArrayList<>(propertyClasses)) {
            if (ApplicationProperties.class.isAssignableFrom(propertyClass)) {
                var instance = (ApplicationProperties) propertyClass.getConstructor(DynamicPropertyFactory.class)
                        .newInstance(ApplicationConfig.mock().factory());
                for (FragmentProperties fragmentProperties : instance.fragments()) {
                    var e = fragmentProperties.getClass();
                    log.info("Found fragment: {}", e.getSimpleName());
                    propertyClasses.add(e);
                }
            }
        }

        for (Class<?> propertyClass : propertyClasses) {
            log.debug("Reading default properties from class {}...", propertyClass.getSimpleName());
            var props = propertyClass.getConstructor(DynamicPropertyFactory.class).newInstance(ApplicationConfig.mock().factory());
            for (Field field : propertyClass.getFields()) {
                Object fieldValue = field.get(props);

                if (fieldValue instanceof Property<?> p) {
                    String pKey = p.getKey();
                    String pValue = convertValueToString(p.get());
                    var oldValue = result.put(pKey, pValue);
                    if (oldValue != null) {
                        log.warn("MULTIPLE PROPERTIES DEFINITION! property={} value={} oldValue={}", pKey, pValue, oldValue);
                    }
                    log.debug("Read property={} value={}", pKey, pValue);
                }
            }
        }

        return result;
    }

    private static String convertValueToString(Object propertyValue) {
        if (propertyValue instanceof Map<?, ?> map) {
            return map.entrySet().stream()
                    .map(e -> e.getKey() + "=" + e.getValue())
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
        } else if (propertyValue instanceof Iterable<?> collection) {
            return StreamSupport.stream(collection.spliterator(), false)
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
        } else {
            return String.valueOf(propertyValue);
        }
    }

    private static Set<String> determinePropertiesToRemove(Properties properties, Map<String, String> defaultValues) {
        var toRemove = new HashSet<String>();
        for (String propertyName : properties.stringPropertyNames()) {
            var actualPropertyName = getActualPropertyName(defaultValues, propertyName);
            if (!defaultValues.containsKey(actualPropertyName)) {
                log.info("to remove: property='{}' not in code configuration", propertyName);
                toRemove.add(propertyName);
                continue;
            }

            var propertyValue = properties.getProperty(propertyName);
            String defaultValue = defaultValues.get(actualPropertyName);
            if (Objects.equals(propertyValue, defaultValue)) {
                toRemove.add(propertyName);
                log.info("to remove: property='{}' value='{}' (matches default value)",
                        propertyName, propertyValue);
            }
        }
        log.info("To remove {} properties", toRemove.size());
        return toRemove;
    }

    private static String getActualPropertyName(Map<String, String> defaultValues, String propertyName) {
        if (defaultValues.containsKey(propertyName)) {
            return propertyName;
        }
        // Find the first dot and check if what's before it is a brand name
        int firstDotIndex = propertyName.indexOf('.');
        if (firstDotIndex > 0) {
            var potentialPropertyName = propertyName.substring(firstDotIndex + 1);
            if (defaultValues.containsKey(potentialPropertyName)) {
                return potentialPropertyName;
            }
        }
        return propertyName;
    }

    private static void updateApplicationProperties(String propertiesFilePath, Set<String> toRemove) throws IOException {
        var propertiesPath = new File(propertiesFilePath).toPath();
        var lines = Files.readAllLines(propertiesPath, StandardCharsets.UTF_8);
        var updatedLines = excludePropertiesToRemoval(lines, toRemove);
        updatedLines = removeDanglingComments(updatedLines);
        updatedLines = removeConsecutiveEmptyLines(updatedLines);

        Files.write(propertiesPath, updatedLines, StandardCharsets.UTF_8, StandardOpenOption.TRUNCATE_EXISTING);
        log.info("Updated properties file saved to {}", propertiesFilePath);
    }

    private static List<String> excludePropertiesToRemoval(List<String> lines, Set<String> toRemove) {
        return lines.stream()
                .filter(line -> isNotForRemoval(toRemove, line))
                .toList();
    }

    private static boolean isNotForRemoval(Set<String> toRemove, String line) {
        for (String key : toRemove) {
            if (line.trim().startsWith(key)) {
                return false;
            }
        }
        return true;
    }

    private static List<String> removeDanglingComments(List<String> lines) {
        var result = new ArrayList<String>();
        int i = 0;

        while (i < lines.size()) {
            var currentLine = lines.get(i);
            if (currentLine.trim().startsWith("#")) {
                // Collect all consecutive comment lines
                var commentBlock = new ArrayList<String>();
                commentBlock.add(currentLine);

                int j = i + 1;
                while (j < lines.size() && lines.get(j).trim().startsWith("#")) {
                    commentBlock.add(lines.get(j));
                    j++;
                }

                if (j < lines.size() && StringUtils.isBlank(lines.get(j).trim())) {
                    i = j + 1;
                } else {
                    result.addAll(commentBlock);
                    i = j;
                }
            } else {
                result.add(currentLine);
                i++;
            }
        }

        return result;
    }

    private static List<String> removeConsecutiveEmptyLines(List<String> lines) {
        var result = new ArrayList<String>();
        var previousLineWasEmpty = false;

        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                if (!previousLineWasEmpty) {
                    result.add(line);
                    previousLineWasEmpty = true;
                }
            } else {
                result.add(line);
                previousLineWasEmpty = false;
            }
        }

        return result;
    }
}
