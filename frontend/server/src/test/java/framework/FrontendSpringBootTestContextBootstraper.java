package framework;

import com.turbospaces.boot.test.AbstractSpringBootTestContextBootstrapper;
import com.turbospaces.cfg.ApplicationConfig;

import fe.FrontendServerProperties;

public class FrontendSpringBootTestContextBootstraper extends AbstractSpringBootTestContextBootstrapper<FrontendServerProperties> {
    @Override
    protected FrontendServerProperties createProps() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        return new FrontendServerProperties(cfg.factory());
    }
}
