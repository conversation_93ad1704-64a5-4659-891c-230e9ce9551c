package fe.handlers;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.Futures;
import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;

import api.DefaultApiFactory;
import api.v1.ApiFactory;
import api.v1.Code;
import api.v1.PlatformSpec;
import fe.FrontendServerProperties;
import fe.NoOpClient;
import fe.NoOpRequestAckWrapper;
import fe.api.OtpCheck;
import fe.services.otp.OtpService;
import gateway.api.GenericInboundWrapper;
import gateway.api.GenericOutboundWrapper;
import gateway.api.MessageHeaders;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.netty.util.AsciiString;
import payment.api.PaymentServiceApi;
import payment.api.v1.GetLastPaymentDateResponse;
import uam.api.UamServiceApi;
import uam.api.v1.DailyBonusInfo;
import uam.api.v1.GetAccountDailyBonusesResponse;
import uam.api.v1.Identity;

@ExtendWith(SpringExtension.class)
public class GetAccountDailyBonusesRequestHandlerTest {

    private final String brandName = "bluedream";
    private final String platform = PlatformSpec.WEB.code();
    private final long accountId = System.currentTimeMillis();
    private final AsciiString routingKey = AsciiString.cached(PlatformUtil.randomUUID().toString());

    @Mock
    private OtpService otpService;

    @Mock
    private PaymentServiceApi paymentServiceApi;

    @Mock
    private UamServiceApi uamServiceApi;

    private FrontendServerProperties props;
    private ApiFactory apiFactory;
    private MockUtil mockUtil;
    private GetAccountDailyBonusesRequestHandler handler;

    @BeforeEach
    void setUp() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        props = new FrontendServerProperties(cfg.factory());
        apiFactory = new DefaultApiFactory(props, new CommonObjectMapper());
        mockUtil = new MockUtil(props, apiFactory);
        handler = Mockito.spy(new fe.handlers.GetAccountDailyBonusesRequestHandler(props, new SimpleMeterRegistry(), otpService, uamServiceApi, paymentServiceApi));
    }

    @Test
    void testHappyPath() throws Throwable {
        NoOpClient client = Mockito.spy(new NoOpClient(apiFactory));
        client.brandName(brandName);
        client.platform(platform);
        client.withJwt(brandName, accountId, routingKey);

        GenericInboundWrapper wrapper = new GenericInboundWrapper();
        wrapper.setHeaders(new MessageHeaders());
        wrapper.getHeaders().messageId = PlatformUtil.randomUUID();
        wrapper.getHeaders().timestamp = new Date();
        wrapper.setBody(new gateway.api.GetAccountDailyBonusesRequest());

        FluentFuture<OtpCheck> from = FluentFuture.from(Futures.immediateFuture(OtpCheck.builder().showOtp(false).mandatory(false).build()));
        when(otpService.checkOtp(any(Identity.class), any(AsciiString.class))).thenReturn(from);

        var getAccountDailyBonuses = mockUtil.mockServiceCall(
                uamServiceApi,
                (api) -> api.getAccountDailyBonuses(any(), any()),
                GetAccountDailyBonusesResponse.newBuilder().addDaily(DailyBonusInfo.newBuilder().setCode("test").setStatus("active").build()).setInitialSegment("new").setSegment("new").build());
        var getPaymentDate = mockUtil.mockServiceCall(
                paymentServiceApi,
                (api) -> api.getLastPaymentDate(any(), any()),
                GetLastPaymentDateResponse.newBuilder().setLastPaymentDate(System.currentTimeMillis()).build());

        handler.accept(client, wrapper, new NoOpRequestAckWrapper());

        GenericOutboundWrapper response = (GenericOutboundWrapper) client.asFlux().blockFirst().getEntity();

        Awaitility.await().untilAsserted(() -> verify(client, times(1)).writeAndFlush(any(GenericOutboundWrapper.class), any()));
        var reply = (gateway.api.GetAccountDailyBonusesResponse) response.getBody();
        var expected = gateway.api.GetAccountDailyBonusesResponse.builder()
                .daily(List.of(gateway.api.DailyBonusInfo.builder().code("test").status("active").offset(0L).at(LocalDate.now(ZoneOffset.UTC).plusDays(2)).time("").build()))
                .segment("new")
                .initialSegment("new")
                .showCaptcha(false)
                .build();
        Assertions.assertEquals(expected, reply);
        Assertions.assertEquals(Code.ERR_OK.name().toLowerCase(), response.getStatus().errorCode);
        getAccountDailyBonuses.verify();
        getPaymentDate.verify();
    }

    @Test
    void testTimeoutOnPayments() throws Throwable {
        NoOpClient client = Mockito.spy(new NoOpClient(apiFactory));
        client.brandName(brandName);
        client.platform(platform);
        client.withJwt(brandName, accountId, routingKey);

        GenericInboundWrapper wrapper = new GenericInboundWrapper();
        wrapper.setHeaders(new MessageHeaders());
        wrapper.getHeaders().messageId = PlatformUtil.randomUUID();
        wrapper.getHeaders().timestamp = new Date();
        wrapper.setBody(new gateway.api.GetAccountDailyBonusesRequest());

        FluentFuture<OtpCheck> from = FluentFuture.from(Futures.immediateFuture(OtpCheck.builder().showOtp(false).mandatory(false).build()));
        when(otpService.checkOtp(any(Identity.class), any(AsciiString.class))).thenReturn(from);

        var getAccountDailyBonuses = mockUtil.mockServiceCall(
                uamServiceApi,
                (api) -> api.getAccountDailyBonuses(any(), any()),
                GetAccountDailyBonusesResponse.newBuilder().addDaily(DailyBonusInfo.newBuilder().setCode("test").setStatus("active").build()).setInitialSegment("new").setSegment("good").build());
        var getPaymentDate = mockUtil.mockServiceCallWithTimeout(
                paymentServiceApi,
                (api) -> api.getLastPaymentDate(any(), any()),
                GetLastPaymentDateResponse.class);

        handler.accept(client, wrapper, new NoOpRequestAckWrapper());

        GenericOutboundWrapper response = (GenericOutboundWrapper) client.asFlux().blockFirst().getEntity();

        Awaitility.await().untilAsserted(() -> verify(client, times(1)).writeAndFlush(any(GenericOutboundWrapper.class), any()));
        var reply = (gateway.api.GetAccountDailyBonusesResponse) response.getBody();
        var expected = gateway.api.GetAccountDailyBonusesResponse.builder()
                .daily(List.of(gateway.api.DailyBonusInfo.builder().code("test").status("active").offset(0L).at(LocalDate.now(ZoneOffset.UTC).plusDays(2)).time("").build()))
                .segment("good")
                .initialSegment("new")
                .showCaptcha(false)
                .build();
        Assertions.assertEquals(expected, reply);
        Assertions.assertEquals(Code.ERR_OK.name().toLowerCase(), response.getStatus().errorCode);
        getAccountDailyBonuses.verify();
        getPaymentDate.verify();
    }
}
