package fe.testutil;

import gateway.api.AccountNotificationInfo;
import gateway.api.FreeSpinAccountNotification;
import gateway.api.JackpotFreeContributionAccountNotification;
import gateway.api.JackpotFreeContributionInfo;
import gateway.api.ManualBonusAccountNotification;
import gateway.api.MilestoneStatus;
import gateway.api.MilestoneUpdateInstanceAccountNotification;
import gateway.api.MilestoneUpdateInstanceInfo;
import gateway.api.OfferChainInstanceAccountNotification;
import gateway.api.OfferChainInstanceInfo;
import gateway.api.PickemReward;
import gateway.api.PickemRewardAccountNotification;
import gateway.api.PromotionAccountNotification;
import gateway.api.QuestlineInstanceAccountNotification;
import gateway.api.QuestlineInstanceInfo;
import gateway.api.QuestlineStatus;
import notification.api.v1.FreeSpinInfo;
import notification.api.v1.FreeSpinsNotification;
import notification.api.v1.FreeSpinsNotificationCategory;
import notification.api.v1.JackpotAccountFreeContribution;
import notification.api.v1.JackpotContributionsNotification;
import notification.api.v1.JackpotContributionsNotificationCategory;
import notification.api.v1.ManualBonusInfo;
import notification.api.v1.ManualBonusNotification;
import notification.api.v1.ManualBonusNotificationCategory;
import notification.api.v1.MilestoneUpdateNotification;
import notification.api.v1.MilestoneUpdateNotificationCategory;
import notification.api.v1.MilestoneUpdateNotificationInfo;
import notification.api.v1.NotificationCategoryInfo;
import notification.api.v1.OfferChainInstanceNotification;
import notification.api.v1.OfferChainInstanceNotificationCategory;
import notification.api.v1.OfferChainInstanceNotificationInfo;
import notification.api.v1.PickemRewardInfo;
import notification.api.v1.PickemRewardsNotification;
import notification.api.v1.PickemRewardsNotificationCategory;
import notification.api.v1.ProductInfo;
import notification.api.v1.PromotionNotification;
import notification.api.v1.PromotionNotificationCategory;
import notification.api.v1.QuestlineInstanceNotification;
import notification.api.v1.QuestlineInstanceNotificationCategory;
import notification.api.v1.QuestlineInstanceNotificationInfo;
import notification.model.NotificationCategoryCodeSpec;
import quest.api.v1.MilestoneStatusEnum;
import quest.api.v1.QuestlineOverallStatusEnum;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

public class NotificationGenerator {

    private static final Instant BASIC_CREATED_AT = Instant.parse("2024-02-19T18:35:24.00Z");

    private static final Instant EXPIRE_AT = Instant.parse("2024-10-19T18:35:24.00Z");

    public static final String TEST_CODE = "promo code";

    public static List<AccountNotificationInfo> buildAccountNotificationInfoList(NotificationCategoryCodeSpec code) {
        return List.of(buildNotificationDto(code, 1 + code.ordinal() * 2),
                buildNotificationDto(code, 2 + code.ordinal() * 2));
    }

    public static JackpotContributionsNotificationCategory buildJackpotContributionsNotificationCategory() {
        return JackpotContributionsNotificationCategory.newBuilder()
                .setCategoryInfo(buildNotificationCategoryInfo(NotificationCategoryCodeSpec.JACKPOT_CONTRIBUTIONS))
                .addJackpotContributionsNotifications(buildJackpotContributionsNotification(1L))
                .addJackpotContributionsNotifications(buildJackpotContributionsNotification(2L))
                .build();
    }

    public static PromotionNotificationCategory buildPromotionNotificationCategory() {
        return PromotionNotificationCategory.newBuilder()
                .setCategoryInfo(buildNotificationCategoryInfo(NotificationCategoryCodeSpec.PROMOTIONS))
                .addPromotionNotifications(buildPromotionNotification(5L))
                .addPromotionNotifications(buildPromotionNotification(6L))
                .build();
    }

    public static FreeSpinsNotificationCategory buildFreeSpinsNotificationCategory() {
        return FreeSpinsNotificationCategory.newBuilder()
                .setCategoryInfo(buildNotificationCategoryInfo(NotificationCategoryCodeSpec.FREE_SPINS))
                .addFreeSpinsNotifications(buildFreeSpinsNotification(3L))
                .addFreeSpinsNotifications(buildFreeSpinsNotification(4L))
                .build();
    }

    public static ManualBonusNotificationCategory buildManualBonusNotificationCategory() {
        return ManualBonusNotificationCategory.newBuilder()
                .setCategoryInfo(buildNotificationCategoryInfo(NotificationCategoryCodeSpec.COINS_DROP))
                .addBonusNotifications(buildBonusRewardNotification(9L))
                .addBonusNotifications(buildBonusRewardNotification(10L))
                .build();
    }

    public static OfferChainInstanceNotificationCategory buildOfferChainInstanceNotificationCategory() {
        return OfferChainInstanceNotificationCategory.newBuilder()
                .setCategoryInfo(buildNotificationCategoryInfo(NotificationCategoryCodeSpec.OFFER_CHAIN_INSTANCES))
                .addOfferChainInstanceNotifications(buildOfferChainInstanceNotification(13L))
                .addOfferChainInstanceNotifications(buildOfferChainInstanceNotification(14L))
                .build();
    }

    public static QuestlineInstanceNotificationCategory buildQuestlineInstanceNotificationCategory() {
        return QuestlineInstanceNotificationCategory.newBuilder()
                .setCategoryInfo(buildNotificationCategoryInfo(NotificationCategoryCodeSpec.QUESTLINES))
                .addQuestlineNotifications(buildQuestlineInstanceNotification(15L))
                .addQuestlineNotifications(buildQuestlineInstanceNotification(16L))
                .build();
    }

    public static MilestoneUpdateNotificationCategory buildMilestoneUpdateNotificationCategory() {
        return MilestoneUpdateNotificationCategory.newBuilder()
                .setCategoryInfo(buildNotificationCategoryInfo(NotificationCategoryCodeSpec.MILESTONES))
                .addMilestoneNotification(buildMilestoneUpdateNotification(17L))
                .addMilestoneNotification(buildMilestoneUpdateNotification(18L))
                .build();
    }

    public static PickemRewardsNotificationCategory buildPickemRewardsNotificationCategory() {
        return PickemRewardsNotificationCategory.newBuilder()
                .setCategoryInfo(buildNotificationCategoryInfo(NotificationCategoryCodeSpec.PICKEM_REWARDS))
                .addPickemRewardsNotifications(buildPickemRewardsNotification(21L))
                .addPickemRewardsNotifications(buildPickemRewardsNotification(22L))
                .build();
    }

    private static ManualBonusNotification buildBonusRewardNotification(long id) {
        return ManualBonusNotification.newBuilder()
                .setAccountNotification(buildAccountNotification(id))
                .setBonusInfo(buildManualBonusInfo(id))
                .build();
    }

    private static ManualBonusInfo buildManualBonusInfo(long id) {
        return ManualBonusInfo.newBuilder()
                .setCode("bonus code" + id)
                .setGoldAmount(BigDecimal.TEN.toString())
                .setSweepstakeAmount(BigDecimal.ONE.toString())
                .build();
    }

    private static JackpotContributionsNotification buildJackpotContributionsNotification(long id) {
        return JackpotContributionsNotification.newBuilder()
                .setAccountNotification(buildAccountNotification(id).toBuilder().setExpireAt(EXPIRE_AT.toEpochMilli()).build())
                .setJackpotAccountFreeContribution(buildJackpotAccountFreeContribution(id))
                .build();
    }

    private static FreeSpinsNotification buildFreeSpinsNotification(long id) {
        return FreeSpinsNotification.newBuilder()
                .setAccountNotification(buildAccountNotification(id).toBuilder().setExpireAt(EXPIRE_AT.toEpochMilli()).build())
                .setFreeSpinInfo(buildFreeSpinInfo(id))
                .build();
    }

    private static FreeSpinInfo buildFreeSpinInfo(long id) {
        return FreeSpinInfo.newBuilder()
                .setRounds(12)
                .setCurrency("SC")
                .setRoundsPlayed(0)
                .addProducts(ProductInfo.newBuilder()
                        .setCode("game code" + id)
                        .setTitle("Title game code" + id)
                        .setRoute("/route")
                        .build())
                .setFreeSpinId("free spin id" + id)
                .build();
    }

    private static NotificationCategoryInfo buildNotificationCategoryInfo(NotificationCategoryCodeSpec code) {
        return NotificationCategoryInfo.newBuilder()
                .setIcon(code.getValue() + ".ico")
                .setCode(code.getValue())
                .setTitle(code.getValue().toUpperCase())
                .build();
    }

    private static AccountNotificationInfo buildNotificationDto(NotificationCategoryCodeSpec code, long id) {
        AccountNotificationInfo accountNotificationInfo;
        switch (code) {
            case NotificationCategoryCodeSpec.FREE_SPINS -> accountNotificationInfo = buildFreeSpinAccountNotification(id);
            case NotificationCategoryCodeSpec.JACKPOT_CONTRIBUTIONS -> accountNotificationInfo = buildJackpotAccountNotification(id);
            case NotificationCategoryCodeSpec.COINS_DROP -> accountNotificationInfo = buildManualBonusAccountNotification(id);
            case NotificationCategoryCodeSpec.PROMOTIONS -> accountNotificationInfo = buildPromotionAccountNotification(id);
            case NotificationCategoryCodeSpec.OFFER_CHAIN_INSTANCES -> accountNotificationInfo = buildOfferChainInstanceAccountNotification(id);
            case NotificationCategoryCodeSpec.QUESTLINES -> accountNotificationInfo = buildQuestlineInstanceAccountNotification(id);
            case NotificationCategoryCodeSpec.MILESTONES -> accountNotificationInfo = buildMilestoneUpdateAccountNotification(id);
            case NotificationCategoryCodeSpec.PICKEM_REWARDS -> accountNotificationInfo = buildPickemRewardAccountNotification(id);
            default -> throw new RuntimeException(String.format("type '%s' is not supported in tests", code));
        }
        accountNotificationInfo.id = id;
        accountNotificationInfo.status = "unread";
        accountNotificationInfo.categoryCode = code.getValue();
        accountNotificationInfo.createdAt = Instant.ofEpochMilli(
                Date.from(BASIC_CREATED_AT.minus(id, ChronoUnit.DAYS)).getTime()).atZone(ZoneOffset.UTC).toLocalDateTime();
        return accountNotificationInfo;

    }

    private static AccountNotificationInfo buildManualBonusAccountNotification(long id) {
        ManualBonusAccountNotification notification = new ManualBonusAccountNotification();
        gateway.api.ManualBonusInfo manualBonusDto = new gateway.api.ManualBonusInfo();
        manualBonusDto.code = "bonus code" + id;
        manualBonusDto.sweepstakeAmount = BigDecimal.ONE;
        manualBonusDto.goldAmount = BigDecimal.TEN;
        notification.data = manualBonusDto;
        return notification;
    }

    private static FreeSpinAccountNotification buildFreeSpinAccountNotification(long id) {
        FreeSpinAccountNotification freeSpinAccountNotification = new FreeSpinAccountNotification();
        gateway.api.ProductInfo productInfo = new gateway.api.ProductInfo();
        productInfo.code = "game code" + id;
        productInfo.title = "Title game code" + id;
        productInfo.route = "/route";

        gateway.api.FreeSpinInfo freeSpinInfo = new gateway.api.FreeSpinInfo();
        freeSpinInfo.currency = "SC";
        freeSpinInfo.rounds = 12L;
        freeSpinInfo.roundsPlayed = 0;
        freeSpinInfo.products.add(productInfo);
        freeSpinInfo.freeSpinId = "free spin id" + id;
        freeSpinAccountNotification.data = freeSpinInfo;
        freeSpinAccountNotification.expireAt = EXPIRE_AT.atZone(ZoneOffset.UTC).toLocalDateTime();
        return freeSpinAccountNotification;
    }

    private static AccountNotificationInfo buildJackpotAccountNotification(long id) {
        JackpotFreeContributionAccountNotification notification = new JackpotFreeContributionAccountNotification();
        JackpotFreeContributionInfo item = new JackpotFreeContributionInfo();
        item.amount = "1" + id;
        item.usedAmount = "0";
        notification.data = item;
        notification.expireAt = EXPIRE_AT.atZone(ZoneOffset.UTC).toLocalDateTime();
        return notification;
    }

    private static notification.api.v1.AccountNotification buildAccountNotification(long id) {
        return notification.api.v1.AccountNotification.newBuilder()
                .setStatus("unread")
                .setCreatedAt(Date.from(BASIC_CREATED_AT.minus(id, ChronoUnit.DAYS)).getTime())
                .setId(id)
                .build();
    }

    private static JackpotAccountFreeContribution buildJackpotAccountFreeContribution(long id) {
        return JackpotAccountFreeContribution.newBuilder()
                .setAmount("1" + id)
                .setUsedAmount("0")
                .build();
    }

    private static PromotionNotification buildPromotionNotification(long id) {
        return PromotionNotification.newBuilder()
                .setAccountNotification(buildAccountNotification(id).toBuilder().setExpireAt(EXPIRE_AT.toEpochMilli()).build())
                .setPromotionInfo(genPromotionInfo(id))
                .build();
    }


    private static OfferChainInstanceNotification buildOfferChainInstanceNotification(long id) {
        return OfferChainInstanceNotification.newBuilder()
                .setAccountNotification(buildAccountNotification(id).toBuilder().setExpireAt(EXPIRE_AT.toEpochMilli()).build())
                .setOfferChainInstanceNotificationInfo(buildOfferChainInstanceNotificationInfo(id))
                .build();
    }


    private static QuestlineInstanceNotification buildQuestlineInstanceNotification(long id) {
        return QuestlineInstanceNotification.newBuilder()
                .setAccountNotification(buildAccountNotification(id).toBuilder().setExpireAt(EXPIRE_AT.toEpochMilli()).build())
                .setQuestlineNotificationInfo(buildQuestlineInstanceNotificationInfo(id))
                .build();
    }

    private static MilestoneUpdateNotification buildMilestoneUpdateNotification(long id) {
        return MilestoneUpdateNotification.newBuilder()
                .setAccountNotification(buildAccountNotification(id).toBuilder().setExpireAt(EXPIRE_AT.toEpochMilli()).build())
                .setMilestoneNotificationInfo(buildMilestoneUpdateNotificationInfo(id))
                .build();
    }

    private static PickemRewardsNotification buildPickemRewardsNotification(long id) {
        return PickemRewardsNotification.newBuilder()
                .setAccountNotification(buildAccountNotification(id).toBuilder().setExpireAt(EXPIRE_AT.toEpochMilli()).build())
                .setPickemRewardInfo(buildPickemRewardInfo(id))
                .build();
    }

    private static QuestlineInstanceNotificationInfo buildQuestlineInstanceNotificationInfo(long id) {
        return QuestlineInstanceNotificationInfo.newBuilder()
                .setCode(String.valueOf(id))
                .setStatus(QuestlineOverallStatusEnum.QUESTLINE_COMPLETED)
                .setTitle("title-" + id)
                .setDescription("description-" + id)
                .setIcon("icon-" + id)
                .setExpirationTimestamp(Date.from(EXPIRE_AT).getTime())
                .build();
    }

    private static MilestoneUpdateNotificationInfo buildMilestoneUpdateNotificationInfo(long id) {
        return MilestoneUpdateNotificationInfo.newBuilder()
                .setMilestoneCode(String.valueOf(id))
                .setQuestCode(String.valueOf(id))
                .setQuestlineCode(String.valueOf(id))
                .setStatus(MilestoneStatusEnum.MILESTONE_COMPLETED)
                .build();
    }

    private static PickemRewardInfo buildPickemRewardInfo(long id) {
        PickemRewardInfo.Builder builder = PickemRewardInfo.newBuilder()
                .setId(id)
                .setTitle("Pickem Reward " + id)
                .setDescription("Description for pickem reward " + id)
                .setExpireAt(EXPIRE_AT.toEpochMilli())
                .setType("ENTRY");

        if (id % 2 == 0) {
            builder.setEntryAmount("10.0");
        } else {
            builder.setMultiplier("2.0");
        }

        return builder.build();
    }

    private static OfferChainInstanceNotificationInfo buildOfferChainInstanceNotificationInfo(long id) {
        return OfferChainInstanceNotificationInfo.newBuilder()
                .setDisplayName("Display-name-" + id)
                .setDisplayTagline("Display-tagline-" + id)
                .setDisplayDescription("Display-description-" + id)
                .setIconInbox("Icon-inbox-" + id)
                .setExpiresAt(Date.from(EXPIRE_AT).getTime())
                .setStatus("Assigned")
                .build();
    }

    private static AccountNotificationInfo buildPromotionAccountNotification(long id) {
        PromotionAccountNotification notification = new PromotionAccountNotification();
        notification.data = genPromotionInfoDto(id);
        notification.expireAt = EXPIRE_AT.atZone(ZoneOffset.UTC).toLocalDateTime();
        return notification;
    }

    private static AccountNotificationInfo buildOfferChainInstanceAccountNotification(long id) {
        OfferChainInstanceAccountNotification notification = new OfferChainInstanceAccountNotification();
        OfferChainInstanceInfo info = new OfferChainInstanceInfo();
        info.displayName = "Display-name-" + id;
        info.displayTagline = "Display-tagline-" + id;
        info.displayDescription = "Display-description-" + id;
        info.iconInbox = "Icon-inbox-" + id;
        info.expiresAt = Date.from(EXPIRE_AT);
        info.status = "Assigned";
        notification.data = info;
        notification.expireAt = EXPIRE_AT.atZone(ZoneOffset.UTC).toLocalDateTime();
        return notification;
    }

    private static AccountNotificationInfo buildQuestlineInstanceAccountNotification(long id) {
        QuestlineInstanceAccountNotification notification = new QuestlineInstanceAccountNotification();
        QuestlineInstanceInfo info = new QuestlineInstanceInfo();
        info.questlineCode = String.valueOf(id);
        info.status = QuestlineStatus.COMPLETED;
        info.title = "title-" + id;
        info.description =  "description-" + id;
        info.image = "icon-" + id;
        info.expirationTimestamp = Date.from(EXPIRE_AT);

        notification.data = info;
        notification.expireAt = EXPIRE_AT.atZone(ZoneOffset.UTC).toLocalDateTime();
        return notification;
    }

    private static AccountNotificationInfo buildMilestoneUpdateAccountNotification(long id) {
        MilestoneUpdateInstanceAccountNotification notification = new MilestoneUpdateInstanceAccountNotification();
        MilestoneUpdateInstanceInfo info = new MilestoneUpdateInstanceInfo();
        info.code = String.valueOf(id);
        info.questCode = String.valueOf(id);
        info.questlineCode = String.valueOf(id);
        info.status = MilestoneStatus.COMPLETED;
        notification.data = info;
        notification.expireAt = EXPIRE_AT.atZone(ZoneOffset.UTC).toLocalDateTime();
        return notification;
    }

    private static AccountNotificationInfo buildPickemRewardAccountNotification(long id) {
        PickemRewardAccountNotification notification = new PickemRewardAccountNotification();
        PickemReward reward = new PickemReward();
        reward.id = id;
        reward.title = "Pickem Reward " + id;
        reward.description = "Description for pickem reward " + id;
        reward.expireAt = EXPIRE_AT.toEpochMilli();
        reward.type = "ENTRY";

        if (id % 2 == 0) {
            reward.entryAmount = "10.0";
        } else {
            reward.multiplier = "2.0";
        }

        notification.data = reward;
        notification.expireAt = EXPIRE_AT.atZone(ZoneOffset.UTC).toLocalDateTime();
        return notification;
    }

    public static gateway.api.PromotionInfo genPromotionInfoDto(long id) {
        gateway.api.PromotionInfo promotionRequest = new gateway.api.PromotionInfo();
        promotionRequest.title = "Title " + TEST_CODE + id;
        promotionRequest.code = TEST_CODE + id;
        promotionRequest.url = "http://localhost/promo.html";
        promotionRequest.description = "Description " + TEST_CODE + id;
        return promotionRequest;
    }

    public static notification.api.v1.PromotionInfo genPromotionInfo(long id) {
        return notification.api.v1.PromotionInfo.newBuilder()
                .setCode(TEST_CODE + id)
                .setTitle("Title " + TEST_CODE + id)
                .setDescription("Description " + TEST_CODE + id)
                .setUrl("http://localhost/promo.html")
                .build();
    }

}
