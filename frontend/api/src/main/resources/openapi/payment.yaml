openapi: 3.0.1
info:
  title: Payment Endpoints API Documentation
  description: Endpoints provided by payment team
  termsOfService: https://patrianna.com/terms/
  contact:
    email: <EMAIL>
  version: '1.0'
  license:
    name: MIT
tags:
  - name: 'payment-core'
  - name: 'capture'
  - name: 'purchase'
  - name: 'redeem'
  - name: 'payper'
  - name: 'applepay'
  - name: 'googlepay'
paths:
  #
  #  Payment core API
  #
  /v1/dispatch/GetCryptoPaymentServiceInfoRequest:
    post:
      tags:
        - 'payment-core'
      summary: 'Get basic payment settings.'
      description: 'Returns payment setting and list of enabled purchase and redeem providers.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getCryptoPaymentServiceInfo'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCryptoPaymentServiceInfoRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetCryptoPaymentServiceInfoResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCryptoPaymentServiceInfoResponseBody'
  /v1/dispatch/GetPaymentServiceBankDetailsRequest:
    post:
      tags:
        - 'payment-core'
      summary: 'Get information about bank accounts.'
      description: 'Returns payment setting and list of enabled purchase and redeem providers.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getPaymentServiceBankDetails'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPaymentServiceBankDetailsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetPaymentServiceBankDetailsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPaymentServiceBankDetailsResponseBody'
  /v1/dispatch/GetPaymentServiceInfoRequest:
    post:
      tags:
        - 'payment-core'
      summary: 'Get basic payment settings.'
      description: 'Returns payment setting and list of enabled purchase and redeem providers.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getPaymentServiceInfo'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPaymentServiceInfoRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetPaymentServiceInfoResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPaymentServiceInfoResponseBody'
  /v1/dispatch/GetPaymentMetaInfoRequest:
    post:
      tags:
        - 'payment-core'
      summary: 'Get basic account payment info.'
      description: 'Returns information on player purchase/redeem activity. E.G Check if player is first time purchaser.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getPaymentMetaInfo'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPaymentMetaInfoRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetPaymentMetaInfoResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPaymentMetaInfoResponseBody'
  /v1/dispatch/GetWidgetRequest:
    post:
      tags:
        - 'payment-core'
      summary: 'Get widget information for registration.'
      description: 'Returns widget URL, token, and username needed for registration.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getWidget'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWidgetRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetWidgetResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWidgetResponseBody'
  /v1/dispatch/AcceptPaymentTermRequest:
    post:
      tags:
        - 'payment-core'
      summary: 'Accept payment term per account'
      description: 'Save shown payment terms per account'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'acceptPaymentTerm'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AcceptPaymentTermRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'AcceptPaymentTermResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AcceptPaymentTermResponseBody'
  /v1/dispatch/GetAcceptedPaymentTermsRequest:
    post:
      tags:
        - 'payment-core'
      summary: 'Get accepted payment terms per account'
      description: 'Return accepted payment terms per account and the time, when they were accepted'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getAcceptedPaymentTerms'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAcceptedPaymentTermsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetAcceptedPaymentTermsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAcceptedPaymentTermsResponseBody'
  /v1/dispatch/RegisterPaymentUserAccountRequest:
    post:
      tags:
        - 'payment-core'
      summary: 'Register payment user account.'
      description: 'Register payment user account in third party system.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'registerPaymentUserAccount'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterPaymentUserAccountRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'RegisterPaymentUserAccountResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterPaymentUserAccountResponseBody'
  /v1/dispatch/ConfirmPaymentUserAccountRequest:
    post:
      tags:
        - 'payment-core'
      summary: 'Confirm payment user account.'
      description: 'Confirm payment user account in third party system.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'confirmPaymentUserAccount'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfirmPaymentUserAccountRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'ConfirmPaymentUserAccountResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmPaymentUserAccountResponseBody'
  /v1/dispatch/LinkBankAccountRequest:
    post:
      tags:
        - 'payment-core'
      summary: 'Link bank account to user payment methods.'
      description: 'Link bank account to user payment methods.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'linkBankAccount'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LinkBankAccountRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'LinkBankAccountResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LinkBankAccountResponseBody'
  /v1/dispatch/CreateNewAeroPayMethodRequest:
    post:
      tags:
        - 'payment-core'
      summary: 'Create new AeroPay payment and withdraw methods.'
      description: 'Create new payment and withdraw methods.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'createNewMethod'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateNewAeroPayMethodRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'CreateNewMethodResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateNewAeroPayMethodResponseBody'
  #
  #  Purchase API
  #
  /v1/dispatch/Capture3DsPaymentOrderRequest:
    post:
      tags:
        - 'capture'
      summary: 'Start capture 3ds payment.'
      description: 'Capturing of 3ds payment is made when 3ds challenge is completed'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'capture3DSPayment'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Capture3DsPaymentOrderRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
              description: |
                Possible statuses:                                                         
                * [common statuses](#components/schema/Status)
                * `err_payment` - Error on provider side.
                * `err_payment_purchase_limit` - Player can't purchase due to his purchase limit. Actual limits can be found in reply. 
                Make sure you validate player purchase limits from [get offers call](#operation/getOffers)
                * `err_payment_input_cvv` - Occurs during routing of spreedly card purchase, that's mean you need to ask user for cvv recache.
                [Spreeedly documentation](https://docs.spreedly.com/reference/iframe/v1/recache/). After cvv is recached resend transaction with cvvEntered=true.
          description: 'Capture3DSPaymentOrderResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Capture3DsPaymentOrderResponseBody'
  /v1/dispatch/GetOffersRequest:
    post:
      tags:
        - 'purchase'
      summary: 'Returns list of offers available for purchase.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getOffers'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetOffersRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetOffersResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetOffersResponseBody'
  /v1/dispatch/UpdateInboxNotificationRequest:
    post:
      tags:
        - 'notification'
      summary: 'Updates inbox notification status.'
      description: >
        FE should call this method to update status of notification returned in [Get inbox notifications](#operation/getInboxNotifications).
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'updateInboxNotification'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInboxNotificationRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'DeletePaymentMethodResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateInboxNotificationResponseBody'
  /v1/dispatch/GetInboxNotificationsRequest:
    post:
      tags:
        - 'notification'
      summary: 'Returns list of inbox notifications available'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getInboxNotifications'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetInboxNotificationsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetInboxNotificationsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetInboxNotificationsResponseBody'
  /v1/dispatch/CreateAccountPaymentMethodRequest:
    post:
      tags:
        - 'purchase'
      summary: 'Create account payment method.'
      description: >
        This call is used for creating purchase methods, right now used only for spreedly cards. 
        After tokenization of card in spreedly iframe pass all card data and pay with token returned in reply.
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'createAccountPaymentMethod'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAccountPaymentMethodRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'CreateAccountPaymentMethodResponseBody'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateAccountPaymentMethodResponseBody'
  /v1/dispatch/CreatePayperPaymentMethodRequest:
    post:
      tags:
        - 'payper'
      summary: 'Creates a Payper payment method.'
      description: 'Create a payment method for Payper and receive a token for subsequent purchases or redeems.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'createPayperPaymentMethod'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePayperPaymentMethodRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'CreatePayperPaymentMethodResponseBody'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePayperPaymentMethodResponseBody'
  /v1/dispatch/GetPaymentMethodsRequest:
    post:
      tags:
        - 'purchase'
      summary: 'Returns list purchase methods.'
      description: 'Returns list of saved purchase methods that can be reused for subsequent purchases.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getPaymentMethods'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPaymentMethodsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetPaymentMethodsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPaymentMethodsResponseBody'

  /v1/dispatch/GetIframeSecurityDataRequest:
    post:
      tags:
        - 'purchase'
      summary: 'Get iFrame security payload'
      description: >
        Generates a security payload required to initialize a secure Spreedly iFrame. 
        This includes a cryptographic signature, nonce, timestamp, and the certificate token. 
        The frontend must call this endpoint before each iFrame initialization.
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getIframeSecurityData'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetIframeSecurityDataRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetPaymentMethodsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetIframeSecurityDataResponseBody'

  /v1/dispatch/DeletePaymentMethodRequest:
    post:
      tags:
        - 'purchase'
      summary: 'Delete purchase method.'
      description: >
        Users may want to remove some of their saved methods return in [Get payment methods](#operation/getPaymentMethods).
        Use this API call for such scenarios. Pass either code or fingerprint.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'deletePaymentMethod'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeletePaymentMethodRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'DeletePaymentMethodResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeletePaymentMethodResponseBody'
  /v1/dispatch/CreateOrderRequest:
    post:
      tags:
        - 'purchase'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      summary: 'Make a purchase.'
      description: >
        This resource is used to create a payment order. **Important:** 
        after receiving an error or first player successful purchase, payment providers must be updated: [Get payment providers](#operation/getPaymentServiceInfo)'
      operationId: 'createOrder'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              description: |
                Possible statuses:                                                         
                * [common statuses](#components/schema/Status)
                * `err_payment` - Error on provider side.
                * `err_duplicate` - Error because transaction id is already processed.
                * `err_payment_3ds_required` - Include sca input data in [create order](#operation/createOrder).
                * `err_payment_purchase_limit` - Player can't purchase due to his purchase limit. Actual limits can be found in reply. 
                Make sure you validate player purchase limits from [get offers call](#operation/getOffers)
                * `err_payment_input_cvv` - Occurs during routing of spreedly card purchase, that's mean you need to ask user for cvv recache.
                [Spreeedly documentation](https://docs.spreedly.com/reference/iframe/v1/recache/). After cvv is recached resend transaction with cvvEntered=true.
              schema:
                type: string
          description: 'CreateOrderResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOrderResponseBody'
  /v1/dispatch/ConfirmOrderRequest:
    post:
      tags:
        - 'purchase'
      summary: 'Confirm a purchase.'
      description: 'Used only for in app purchases and direct fiserv applepay, googlepay.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'confirmOrder'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfirmOrderRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'ConfirmOrderResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmOrderResponseBody'
  /v1/dispatch/RefreshOrderRequest:
    post:
      tags:
        - 'purchase'
      summary: 'Refresh a purchase.'
      description: 'Used when it is necessary to continue processing of early created transaction. It could be only for specific payment providers (f.e. AeroPay).'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'refreshOrder'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshOrderRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'RefreshOrderResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshOrderResponseBody'
  /v1/dispatch/GetPaymentOrderRequest:
    post:
      tags:
        - 'purchase'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      summary: 'Get order info.'
      description: >
        For some integration, in case of redirecting back to our website this call can be used 
        to check transaction status using transaction id in query parameters. E.G skrill
      operationId: 'getPaymentOrder'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPaymentOrderRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetPaymentOrderResponse'
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetPaymentOrderResponseBody"
  #
  #  Redeem API
  #
  /v1/dispatch/GetWithdrawMethodsRequest:
    post:
      tags:
        - 'redeem'
      summary: 'Returns list redeem methods.'
      description: 'Returns list of saved redeem methods that can be reused for redemptions purchases.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getWithdrawMethods'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWithdrawMethodsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetWithdrawMethodsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWithdrawMethodsResponseBody'
  /v1/dispatch/CaptureRedeemOptionRequest:
    post:
      tags:
        - 'redeem'
      summary: 'Start redeem creation.'
      description: 'Capturing of redeem option is used for trustly only.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'captureRedeemOption'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CaptureRedeemOptionRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'CaptureRedeemOptionResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CaptureRedeemOptionResponseBody'
  /v1/dispatch/RedeemMoneyRequest:
    post:
      tags:
        - 'redeem'
      summary: 'Create redeem.'
      description: >
        This call is used for all subsequent redemptions using saved methods 
        or for those provider where redeem is initiated on our web page
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'redeemMoney'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RedeemMoneyRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'RedeemMoneyResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RedeemMoneyResponseBody'
  /v1/dispatch/CancelRedeemMoneyRequest:
    post:
      tags:
        - 'redeem'
      summary: 'Cancel redeem.'
      description: "After redeem creation if it wasn't locked by customer support user can cancel it."
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'cancelRedeemMoney'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelRedeemMoneyRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'CancelRedeemMoneyResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelRedeemMoneyResponseBody'
  /v1/dispatch/CancelAllRedeemMoneyRequest:
    post:
      tags:
        - 'redeem'
      summary: 'Cancel all pending redeems.'
      description: "Cancels all pending redeems for the authenticated user."
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'cancelAllRedeemMoney'
      x-async-enabled: true
      responses:
        '202':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'Cancel all pending redeems request accepted'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelAllRedeemMoneyResponseBody'
  /v1/dispatch/GetRedeemMoneyHistoryRequest:
    post:
      deprecated: true
      tags:
        - 'redeem'
      summary: 'Get redeem money history.'
      description: 'Returns redemption money history based on some filters. Please migrate to GetRedeemsRequest'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getRedeemMoneyHistory'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetRedeemMoneyHistoryRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetRedeemMoneyHistoryResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRedeemMoneyHistoryResponseBody'

  /v1/dispatch/GetRedeemHistoryRequest:
    post:
      deprecated: true
      tags:
        - 'redeem'
      summary: 'Get redeem history.'
      description: 'Returns redemption history based on some filters with pagination. Please migrate to GetRedeemsRequest.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getRedeemHistory'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetRedeemHistoryRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetRedeemHistoryResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRedeemHistoryResponseBody'

  /v1/dispatch/GetWithdrawSettingsRequest:
    post:
      tags:
        - 'redeem'
      summary: 'Returns list of available redeem providers and limit policy.'
      description: 'Returns list of redeem providers and redeem limit policy(per day, daily per state) by provider configured in DB.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getWithdrawSettings'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWithdrawSettingsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetWithdrawSettingsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWithdrawSettingsResponseBody'

  /v1/dispatch/GetPendingRedeemCountRequest:
    post:
      tags:
        - 'redeem'
      summary: 'Returns data about pending redeems.'
      description: 'Returns the number of pending redeems and their total amount.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getPendingRedeemCount'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPendingRedeemCountRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetPendingRedeemCountResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPendingRedeemCountResponseBody'

  /v1/dispatch/GetCurrencyRateRequest:
    post:
      tags:
        - 'redeem'
      summary: 'Returns FX currency rate'
      description: 'Returns FX rate for player account currency'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getCurrencyRate'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCurrencyRateRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetCurrencyRateResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCurrencyRateResponseBody'
  #
  #  Applepay API
  #
  /v1/dispatch/ValidateApplePayMerchantRequest:
    post:
      tags:
        - 'applepay'
      summary: 'Validate applepay merhcant.'
      description: >
        This call is part of applepay flow, when tokenizing card using apple iframe.
        You need validation validationResponse from reply to call completeMerchantValidation 
        on validate merchant callback in iframe.
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'validateApplePayMerchant'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateApplePayMerchantRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'ValidateApplePayMerchantResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateApplePayMerchantResponseBody'
  /v1/dispatch/TokenizeApplePayRequest:
    post:
      tags:
        - 'applepay'
      summary: 'Tokenizing of apple pay method.'
      description: 'After validating merchant in payment authorized callback make tokenize call with applepayToken.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'tokenizeApplePay'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenizeApplePayRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'TokenizeApplePayResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenizeApplePayResponseBody'
  #
  #  Googlepay API
  #
  /v1/dispatch/TokenizeGooglePayRequest:
    post:
      tags:
        - 'googlepay'
      summary: 'Googlepay method tokenization.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'tokenizeGooglePay'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenizeGooglePayRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'TokenizeGooglePayResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenizeGooglePayResponseBody'
  /v1/dispatch/GetPurchaseHistoryRequest:
    post:
      tags:
        - 'purchase'
      summary: 'Get purchase history.'
      description: 'Returns purchase history based on some filters with pagination.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getPurchaseHistory'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPurchaseHistoryRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetPurchaseHistoryResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPurchaseHistoryResponseBody'
  /v1/dispatch/GetRedeemsRequest:
    post:
      tags:
        - 'redeem'
      summary: 'Get redeem history.'
      description: 'Returns redeem history based on some filters with pagination.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getRedeemsRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetRedeemsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetRedeemsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRedeemsResponseBody'

  /v1/dispatch/GetTransactionLimitRequest:
    post:
      tags:
        - 'limit'
      summary: 'Get Transaction limits.'
      description: 'Returns transaction limits for a given operation (deposit or redeem).'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getTransactionLimitRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetTransactionLimitRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetTransactionLimitResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTransactionLimitResponseBody'
                
  /v1/dispatch/SetPurchaseLimitRequest:
    post:
      tags:
        - 'limit'
      summary: 'Set Purchase limits.'
      description: 'Set deposit limits for a user (deposit).'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'setPurchaseLimitRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetPurchaseLimitRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'SetPurchaseLimitResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetPurchaseLimitResponseBody'
                
  /v1/dispatch/GetPurchaseLimitsRequest:
    post:
      tags:
        - 'limit'
      summary: 'Returns Purchase limits.'
      description: 'Return deposit limits for a user (deposit).'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getPurchaseLimitRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPurchaseLimitsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetPurchaseLimitsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPurchaseLimitsResponseBody'
  /v1/dispatch/ResetAccountPaymentMethodsRequest:
    post:
      summary: 'Reset payment methods.'
      description: 'Reset payment methods for a user.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'resetPaymentMethodsRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetAccountPaymentMethodsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetPurchaseLimitsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResetAccountPaymentMethodsResponseBody'
  /v1/dispatch/ResetPurchaseLimitsRequest:
    post:
      tags:
        - 'limit'
      summary: 'Reset Purchase limits.'
      description: 'Reset deposit limits for a user (deposit).'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'resetPurchaseLimitRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPurchaseLimitsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: 'GetPurchaseLimitsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResetPurchaseLimitsResponseBody'
components:
  headers:
    X-Message-Id:
      description: UUID of original request
      schema:
        type: string
        format: uuid
    X-Took:
      description: Processing time
      schema:
        type: integer
        example: 42
    X-Status:
      description: |
        Operation status with [possible values](#components/schema/Status)
      schema:
        $ref: '#/components/schemas/Status'
    X-Status-Text:
      description: Operation text
      schema:
        type: string
  parameters:
    X-Message-Id:
      in: header
      name: X-Message-Id
      description: UUID of original request
      required: true
      schema:
        type: string
        format: uuid
    X-Timestamp:
      name: X-Timestamp
      in: header
      description: Unix request timestamp
      required: true
      schema:
        type: integer
        format: timestamp
        example: 1694794233734
    brandName:
      name: brandName
      description: Brand name
      required: true
      in: query
      schema:
        type: string
    platform:
      name: platform
      description: Platform
      required: true
      in: query
      schema:
        type: string
        enum: [ web, android, ios ]
  schemas:
    # ~~~ Common
    Status:
      type: string
      enum: [ err_ok, err_auth, err_system, err_too_many_request, err_bad_request, err_duplicate, err_not_found, err_denied ]
      description: >
        Statuses:
          * err_ok                    - No error.
          * err_auth                  - Unauthorized access to resource.
          * err_system                - Unexpected server error.
          * err_too_many_request      - You were rate limited.
          * err_bad_request           - Incorrect parameter check X-Status-Text for more details.
          * err_duplicate             - Conflict with the current state of the target resource.
          * err_not_found             - The requested resource could not be found.
          * err_denied                - Access denied.
    PageRequest:
      type: object
      required:
        - pageSize
        - pageNumber
      properties:
        pageSize:
          type: integer
          format: int32
        pageNumber:
          type: integer
          format: int32
    Pagination:
      type: object
      properties:
        totalRecords:
          type: integer
          format: int32
        currentPage:
          type: integer
          format: int32
        totalPages:
          type: integer
          format: int32
        hasNextPage:
          type: 'boolean'
        hasPrevPage:
          type: 'boolean'
    DateRange:
      type: object
      properties:
        startDate:
          type: "string"
          format: "date"
          description: "The optional inclusive start date for the query. Cannot be after endDate."
          example: "2017-07-21"
        endDate:
          type: "string"
          format: "date"
          description: "The optional inclusive end date for the query. Cannot be before startDate."
          example: "2017-07-21"
    # ~~~ Notifications
    PurchaseSuccessNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      description: >
        This websocket notification is sent after offer is marked as successful [Create order](#operation/createOrder), in most cases CreateOrderResponse is enough to understand status of order.
        In most cases it is used in cases when player is redirected back from provider checkout page (e.g Nuvei, Skrill) to get notification if order was successful.
        In cases when transaction is initiated on our website this notification can be ignored.
      required:
        - transactionId
        - provider
        - currency
        - amount
        - firstPurchaseDate
        - isFirstPurchase
      properties:
        transactionId:
          type: 'string'
          format: 'uuid'
          description: 'Unique transaction identifier.'
        provider:
          type: 'string'
          description: 'Payment provider that has processed this transaction.'
        currency:
          type: 'string'
          description: 'Transaction currency.'
        amount:
          type: 'number'
          description: 'Transaction amount.'
        balance:
          type: 'number'
          description: 'Current user balance for this currency after this purchase.'
        firstPurchaseDate:
          type: 'string'
          format: 'Date'
          example: '2023-06-29T12:35:51.410+00:00'
          description: 'Use from transactionDetails'
          deprecated: true
        isFirstPurchase:
          type: 'boolean'
          default: 'false'
          description: 'Use from transactionDetails'
          deprecated: true
        city:
          type: 'string'
          description: 'City that was used for transaction processing.'
        zip:
          type: 'string'
          description: 'Zip code that was used for transaction processing.'
        billingDescriptor:
          type: 'string'
          description: 'Transaction billing descriptor.'
        method:
          $ref: '#/components/schemas/PurchaseMethod'
        transactionDetails:
          $ref: '#/components/schemas/TransactionDetails'
    PurchaseDeclineNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      description: >
        This websocket notification is sent after offer is marked as declined after [Create order](#operation/createOrder), in most cases CreateOrderResponse is enough to understand status of order.
        In most cases it is used in cases when player is redirected back from provider checkout page (e.g Nuvei, Skrill) to get notification if order was declined.
        In cases when transaction is initiated on our website this notification can be ignored.'
      required:
        - provider
        - transactionId
      properties:
        provider:
          type: 'string'
          description: 'Payment provider that has processed this transaction.'
        reason:
          type: 'string'
          description: 'Decline reason for this purchase.'
        code:
          type: 'string'
          description: 'External transaction reference on the payment provider.'
        transactionId:
          type: 'string'
          description: 'Unique transaction identifier.'
        amount:
          type: 'number'
          description: 'Transaction amount for this purchase.'
        currency:
          type: 'string'
          description: 'Transaction currency for this purchase.'
        tempToken:
          type: 'string'
          description: 'Token used for payment processing.'
        errCode:
          type: 'string'
          description: 'Transaction error code.'
        description:
          type: 'string'
          description: 'Order description.'
        method:
          $ref: '#/components/schemas/PurchaseMethod'
          description: 'Payment method for a given order.'
        totalDepositAmount:
          type: 'string'
          description: 'Total deposited amount, offer purchases not included.'
        rescueProviders:
          type: 'array'
          description: 'Rescue providers to use for failed purchase'
          items:
            type: 'string'
    OfferPurchaseNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      description: >
        This websocket notification is sent after offer is marked as successful [Create order](#operation/createOrder), in most cases CreateOrderResponse is enough to understand status of order.
        In most cases it is used in cases when player is redirected back from provider checkout page (e.g Nuvei, Skrill) to get notification if order was successful.
        In cases when transaction is initiated on our website this notification can be ignored.
      required:
        - offer
        - transactionId
        - provider
        - currency
        - firstDeposit
        - amount
        - isFirstDeposit
      properties:
        offer:
          $ref: '#/components/schemas/OfferInfo'
        transactionId:
          type: 'string'
          format: 'uuid'
        provider:
          type: 'string'
        currency:
          type: 'string'
        firstDeposit:
          type: 'string'
          format: 'Date'
          example: '2023-06-29T12:35:51.410+00:00'
        amount:
          type: 'number'
        isFirstDeposit:
          type: 'boolean'
          default: 'false'
        city:
          type: 'string'
        zip:
          type: 'string'
        billingDescriptor:
          type: 'string'
        method:
          $ref: '#/components/schemas/PurchaseMethod'
    RewardOfferUpdateNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      description: >
        This websocket notification is when a reward offer is assigned to a user.
      required:
        - offer
        - status
        - count
        - expireAt
      properties:
        offer:
          $ref: '#/components/schemas/OfferInfo'
        status:
          description: 'Offer reward status'
          type: 'string'
          enum:
            - 'created'
        maxPurchaseCount:
          type: 'integer'
          format: 'int32'
        expireAt:
          type: 'integer'
          format: 'int64'
    InboxNotificationsRefreshedNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      description: >
        This websocket notification is sent when user's inbox notifications are updated. For more details see [Get inbox notifications](#operation/getInboxNotifications),
      required:
        - notifications
      properties:
        notifications:
          type: 'array'
          items:
            $ref: '#/components/schemas/InboxNotificationInfo'
    PurchaseRefreshNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      description: >
        This websocket notification is sent after purchase state refreshes.
      required:
        - provider
        - transactionId
      properties:
        offer:
          $ref: '#/components/schemas/OfferInfo'
        provider:
          type: 'string'
        code:
          type: 'string'
        transactionId:
          type: 'string'
        tempToken:
          type: 'string'
    OfferDeclineNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      description: >
        This websocket notification is sent after offer is marked as declined after [Create order](#operation/createOrder), in most cases CreateOrderResponse is enough to understand status of order.
        In most cases it is used in cases when player is redirected back from provider checkout page (e.g Nuvei, Skrill) to get notification if order was declined.
        In cases when transaction is initiated on our website this notification can be ignored.'
      required:
        - offer
        - provider
        - transactionId
      properties:
        offer:
          description: present only if offer was used for purchase
          $ref: '#/components/schemas/OfferInfo'
        provider:
          type: 'string'
        reason:
          type: 'string'
        code:
          type: 'string'
        transactionId:
          type: 'string'
        tempToken:
          type: 'string'
        errCode:
          type: 'string'
        internalErrorCode:
          type: 'string'
        rescueProviders:
          description: 'Rescue providers to use for failed purchase'
          type: 'array'
          items:
            type: 'string'
        errorDetails:
          $ref: '#/components/schemas/OrderError'
    #   todo  May be deprecated double check
    OfferRefundNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      required:
        - offer
        - provider
        - transactionId
        - currency
        - amount
      properties:
        offer:
          $ref: '#/components/schemas/OfferInfo'
        transactionId:
          type: 'string'
          format: 'uuid'
        provider:
          type: 'string'
        currency:
          type: 'string'
        amount:
          type: 'number'
    RedeemAuthorizedNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      description: 'This notification is triggered after redeem creation [Create redeem](#operation/redeemMoney)'
      type: object
      required:
        - id
        - provider
      properties:
        id:
          type: 'integer'
          format: 'int64'
        provider:
          type: 'string'
        kyc:
          type: 'boolean'
          default: 'false'
        kycStatus:
          type: string
    UpdatePaymentMethodsNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      description: 'This notification is triggered as an instruction to ui to update list of purchase and reedem providers by calling [Get payment providers](#operation/getPaymentServiceInfo)'
      type: object
      properties:
        updatedType:
          description: 'Provider type updated. purchase or redeem'
          type: 'string'
          enum:
            - purchase
            - redeem
      allOf:
        - $ref: '#/components/schemas/GetPaymentServiceInfoResponseBody'
    # ~~~ Request-Response
    ValidateApplePayMerchantRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - validationUrl
      properties:
        validationUrl:
          description: 'Apple pay validation url is coming after apple pay tokenization using apple native iframe.'
          type: 'string'
        homePageUrl:
          description: 'Apple pay domain url for validation. Optional. Can be set if there are several domains for brand'
          type: 'string'
    ValidateApplePayMerchantResponseBody:
      type: 'object'
      required:
        - validationResponse
      properties:
        validationResponse:
          description: 'Raw validation response pass it to [Tokenize applepay call](#operation/tokenizeApplePayRequestBody)'
          type: 'string'
    TokenizeApplePayRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - applePayToken
      properties:
        applePayToken:
          description: 'Apple pay token from payment authorized callback in iframe.'
          type: 'string'
        address1:
          description: 'Billing address that comes from apple pay.'
          type: 'string'
        address2:
          description: 'Billing address that comes from apple pay.'
          type: 'string'
        city:
          description: 'City that comes from apple pay.'
          type: 'string'
        country:
          description: 'Country that comes from apple pay.'
          type: 'string'
        firstName:
          description: 'Cardholder first name that comes from apple pay.'
          type: 'string'
        lastName:
          description: 'Cardholder last name that comes from apple pay.'
          type: 'string'
        postalCode:
          description: 'Postal code that comes from apple pay.'
          type: 'string'
        stateOrProvince:
          description: 'Stare or province that comes from apple pay.'
          type: 'string'
    TokenizeApplePayResponseBody:
      type: 'object'
      properties:
        token:
          type: 'string'
        type:
          type: 'string'
    TokenizeGooglePayRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      properties:
        googlePayToken:
          description: 'Google pay token from google pay lib(on load payment data).'
          type: 'string'
        type:
          type: 'string'
        zip:
          type: 'string'
        city:
          type: 'string'
        country:
          type: 'string'
        stateOrProvince:
          type: 'string'
        address1:
          type: 'string'
        address2:
          type: 'string'
        fullName:
          type: 'string'
    TokenizeGooglePayResponseBody:
      type: 'object'
      properties:
        token:
          description: 'Payment method token.'
          type: 'string'
    GetWidgetRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    GetWidgetResponseBody:
      type: 'object'
      properties:
        widgetFastlinkURL:
          type: 'string'
          description: "Widget URL for registration"
        widgetToken:
          type: 'string'
          description: "Widget token for registration"
        widgetUsername:
          type: 'string'
          description: "Widget username for registration"
    GetIframeSecurityDataRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      description: >
        Optional request body for initializing  iframe security data.
      allOf: [ ]

    GetIframeSecurityDataResponseBody:
      type: object
      required:
        - secureDataEnabled
      properties:
        nonce:
          type: string
          description: "A unique one-time UUID to prevent replay attacks."
          example: "dd6b70a5-e070-4fa2-a3ca-0db0b45f78d0"

        timestamp:
          type: integer
          format: int64
          description: "Unix timestamp in seconds at the moment of generation."
          example: "**********"

        certificateToken:
          type: string
          description: "Token of the signed certificate obtained from Provider."
          example: "6Q15M5VFJZ9QS9PEFTNP622W1T"

        signature:
          type: string
          description: >
            RSA SHA-256 signature (base64-encoded) .Used by Provider iframe to verify authenticity of request.
          example: "CchzX5noiMcEpFlrz6ImWuzLXaUAvLLCpd6F3Y7gd7..."

        secureDataEnabled:
          type: "boolean"
          description: " Indicates whether enhanced iFrame security is enabled. 
                          When true, the secure tokenization process using signed certificate data is active."

    ConfirmOrderRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      properties:
        provider:
          type: 'string'
          example: 'fiserv'
        session:
          type: 'string'
        token:
          type: 'string'
        transactionId:
          type: 'string'
          format: 'uuid'
        type:
          type: 'string'
        amount:
          type: 'number'
    ConfirmOrderResponseBody:
      type: 'object'
      properties:
        errorMsg:
          type: 'string'
        mode:
          type: 'string'
        status:
          type: 'string'
        token:
          type: 'string'
        type:
          type: 'string'
        offer:
          $ref: '#/components/schemas/OfferInfo'
        price:
          type: 'string'
        billingDescriptor:
          type: 'string'
        isFirstDeposit:
          type: 'boolean'
          default: 'false'
        firstDepositDate:
          type: 'string'
          format: 'Date'
        city:
          type: 'string'
        zip:
          type: 'string'
        amount:
          type: 'number'
        currency:
          type: 'string'
        provider:
          type: 'string'
          example: 'fiserv'
    CreateOrderRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - provider
      properties:
        at:
          type: "string"
          format: "Date"
          description: "Purchase date. May be applicable to some of integrations where purchase created after redirect."
          example: "2023-07-19T10:50:54.650+00:00"
        billingAddress:
          $ref: "#/components/schemas/BillingAddress"
          description: "Billing user details."
        cvvEntered:
          type: "boolean"
          description: "Applicable to spreedly card routing purchases only. 
                        If new card tokenization or user has entered cvv after receiving `err_payment_input_cvv`."
          default: "false"
        offer:
          type: 'string'
          description: 'Offer code to purchase. Amount will be taken from the offer.'
        cryptoData:
          description: 'Pass data for crypto purchses'
          type: object
          required:
            - network
            - currency
          properties:
            network:
              $ref: "#/components/schemas/CryptoNetwork"
            currency:
              $ref: "#/components/schemas/CryptoCurrency"
        amount:
          type: 'number'
          minimum: 0.01
          description: 'Order amount in fiat currency. Must be a greater than 0. Required for deposits, optional for offer purchases.'
        rewardCodes:
          type: 'array'
          items:
            type: 'string'
            format: 'uuid'
          description: 'Codes of deposit bonus reward.'
        provider:
          type: 'string'
          description: 'Provider used for purchase.'
          example: 'fiserv'
        referer:
          type: 'string'
          description: 'Referer URL.'
        session:
          type: "string"
          description: "SEON device fingerprint."
        sourceId:
          type: 'string'
          externalDocs:
            description: 'Spreedly recache flow.'
            url: https://docs.spreedly.com/reference/iframe/v1/recache/
          description: >
            Routing chain identifier to connect purchases within same routing. 
            It may happen that during routing of purchase user will be asked to enter cvv. You will get errorCode err_payment_input_cvv.
            That a signal for you to recache cvv for payment method using spreedly iframe 
            and resend [Create order](#operation/createOrder) with the same sourceId you've got in response with errorCode err_payment_input_cvv'
        token:
          type: 'string'
          description: >
            Pass payment method token for first time purchases. E.G cardToken, ach. 
            In case of subsequent purchase pass code from [Get payment methods](#operation/getPaymentMethods)'
        transactionId:
          type: 'string'
          description: 'Unique identifier of transaction id.'
          format: 'uuid'
        #       todo remove rework
        validationUrl:
          type: 'string'
          description: 'Used for direct fiserv apple pay only.'
        quickPurchaseSupportedType:
          $ref: '#/components/schemas/QuickPurchaseSupportedType'
        scaAuthenticateData:
            $ref: "#/components/schemas/ScaAuthenticateData"
    RefreshOrderRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - transactionId
        - provider
      properties:
        transactionId:
          type: 'string'
          format: 'uuid'
          description: 'Unique identifier of transaction id. Should be used the same value as in CreateOrderRequest.'
        provider:
          type: 'string'
          description: 'Provider used for purchase.'
          example: 'aeropay'
        token:
            type: 'string'
            description: 'Payment method token for subsequent purchases. E.G bankID for aeropay.'
    RefreshOrderResponseBody:
      type: 'object'
      allOf: [ ]
    Capture3DsPaymentOrderResponseBody:
      type: 'object'
      properties:
        error:
          $ref: '#/components/schemas/OrderError'
    CreateOrderResponseBody:
      type: 'object'
      properties:
        at:
          type: "string"
          format: "Date"
        clientToken:
          type: 'string'
 #       todo remove rework
        completeMerchantValidation:
          description: 'Used for direct fiserv apple pay only.'
          type: 'string'
        cvvEntered:
          type: "boolean"
          description: "Echo back request `cvvEntered` parameter."
          default: "false"
        error:
          $ref: '#/components/schemas/OrderError'
        limitAmount:
          type: 'string'
        limitAvailable:
          type: 'string'
        limitEnd:
          type: 'string'
          format: 'date'
        limitPeriod:
          type: 'string'
        orderCode:
          type: 'string'
        paymentData:
          type: 'string'
        publicKeyBase64:
          type: 'string'
        redirectUrl:
          type: "string"
          description: "URL to redirect. Applicable to integrations where redirect to provider page is needed."
        requestKyc:
          type: 'boolean'
          default: 'false'
        secure3D:
          type: 'boolean'
          default: 'false'
        sourceId:
          type: 'string'
        status:
          type: 'string'
        supplier:
          type: 'string'
        tempToken:
          type: 'string'
        transactionId:
          type: 'string'
          format: 'uuid'
        type:
          type: 'string'
        offer:
          $ref: '#/components/schemas/OfferInfo'
        price:
          type: 'string'
          deprecated: true
          description: 'Use price from offer (offer.price)'
        billingDescriptor:
          type: 'string'
        isFirstDeposit:
          type: 'boolean'
          default: 'false'
        firstDepositDate:
          type: 'string'
          format: 'Date'
        city:
          type: 'string'
        zip:
          type: 'string'
        amount:
          type: 'number'
        currency:
          type: 'string'
        provider:
          type: 'string'
          example: 'fiserv'
        internalStatus:
          $ref: '#/components/schemas/OrderStatus'
        threeDsAuthorizeData:
          type: object
          properties:
             authenticateTxId:
               type: 'string'
               description: >
                 SCA authenticate API call result.
             state:
               type: 'string'
               enum:
                 - pending
                 - succeeded
               description: >
                  SCA authentication state. If 'pending' user should complete 3ds challenge.
        description:
          type: 'string'
        method:
          $ref: '#/components/schemas/PurchaseMethod'
    CardPaymentMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/PaymentMethod'
      properties:
        name:
          type: 'string'
          description: "Cardholder name."
          example: "John Doe"
        verificationStatus:
          type: 'string'
          description: 'UNVERIFIED,NEEDS_VERIFICATION,VERIFICATION_IN_PROGRESS,VERIFIED'
        expiryMonth:
          type: 'integer'
          description: 'Card expiration month'
          example: 7
        expiryYear:
          type: 'integer'
          description: 'Card expiration year.'
        maskedCardNumber:
          type: 'string'
          description: 'Card last four.'
        cardType:
          type: 'string'
          description: 'Card brand: visa, master, amex, discover.'
        cardIssuer:
          type: 'string'
          deprecated: true
          default: ''
        fingerprint:
          type: 'string'
          description: 'Unique card number identifier, same card will have same fingerprint after any tokenization.'
    TrustlyPaymentMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/PaymentMethod'
      properties:
        code:
          type: 'string'
        bankName:
          type: 'string'
        paymentProviderId:
          type: 'string'
        accountName:
          type: 'string'
        accountNumber:
          type: 'string'
        lastUsageAt:
          type: 'integer'
          format: 'int64'
    SkrillPaymentMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/PaymentMethod'
      properties:
        email:
          type: 'string'
        lastUsageAt:
          type: 'integer'
          format: 'int64'
    AeroPayPaymentMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/PaymentMethod'
      properties:
        bankAccountName:
          type: 'string'
        bankAccountNumber:
          type: 'string'
        bankName:
          type: 'string'
        phone:
          type: 'string'
        accountId:
          type: 'string'
    NuveiMazoomaPaymentMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/PaymentMethod'
      properties:
        fiAccType:
          type: 'string'
        fiAccLabel:
          type: 'string'
        fiName:
          type: 'string'
    PayperPaymentMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/PaymentMethod'
      properties:
        email:
          type: 'string'
        phone:
          type: 'string'
    CryptoPaymentMethod:
      type: object
      description: 'Saved crypto payment method'
      allOf:
        - $ref: '#/components/schemas/PaymentMethod'
      properties:
        wallet:
          type: 'string'
        currency:
          $ref: '#/components/schemas/CryptoCurrency'
        network:
          $ref: '#/components/schemas/CryptoNetwork'
    PaymentMethod:
      required:
        - type
      type: object
      properties:
        type:
          type: string
      discriminator:
        propertyName: type
        mapping:
          CardPaymentMethod: '#/components/schemas/CardPaymentMethod'
          NuveiMazoomaPaymentMethod: '#/components/schemas/NuveiMazoomaPaymentMethod'
          SkrillPaymentMethod: '#/components/schemas/SkrillPaymentMethod'
          TrustlyPaymentMethod: '#/components/schemas/TrustlyPaymentMethod'
          PayperPaymentMethod: '#/components/schemas/PayperPaymentMethod'
          CryptoPaymentMethod: '#/components/schemas/CryptoPaymentMethod'
          AeroPayPaymentMethod: '#/components/schemas/AeroPayPaymentMethod'
    GetPaymentMethodsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: "object"
      allOf: [ ]
    GetPaymentMethodsResponseBody:
      type: 'object'
      required:
        - provider
        - token
        - paymentMethod
      properties:
        methods:
          type: 'array'
          items:
            type: 'object'
            properties:
              code:
                type: "string"
                deprecated: true
              date:
                type: "string"
                format: "Date"
                description: "Date of payment method creation."
                example: "2023-07-19T10:50:54.650+00:00"
              id:
                type: "integer"
                description: "Internal payment method identifier."
                format: "int64"
                deprecated: true
              paymentMethod:
                $ref: '#/components/schemas/PaymentMethod'
              provider:
                type: "string"
                description: "Provider name used to match payment methods with list of active providers from [Get payment methods](#operation/getPaymentMethods) by `providerName` field."
                example: "fiserv"
              token:
                type: "string"
                description: "Payment method token for using subsequent purchase. Use this it as `token` field in subsequent calls of [Create order](#operation/createOrder)."
              tokenize:
                type: "boolean"
                description: >
                  Flag for spreedly card methods which indicates whether user must be asked for cvv before purchase.
                  [Spreedly recache flow](https://docs.spreedly.com/reference/iframe/v1/recache/)
                default: "false"
        quickPurchaseSupportedType:
          $ref: '#/components/schemas/QuickPurchaseSupportedType'
        verificationRequired:
          type: 'boolean'
          description: 'Indicates whether card verification is required or not'
    GetWithdrawMethodsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    RedeemMoneyPolicy:
      required:
        - minAmount
        - maxAmount
      type: 'object'
      properties:
        minAmount:
          type: 'number'
        maxAmount:
          type: 'number'
        availableToRedeem:
          type: 'number'
    WithdrawMethodPolicy:
      type: 'object'
      properties:
        fiatRedeemPolicy:
          $ref: '#/components/schemas/RedeemMoneyPolicy'
        nonMonetaryRedeemPolicy:
          $ref: '#/components/schemas/RedeemMoneyPolicy'
        sweepstakeRedeemPolicy:
          $ref: '#/components/schemas/RedeemMoneyPolicy'
    GetWithdrawMethodsResponseBody:
      type: 'object'
      required:
        - methods
      properties:
        methods:
          type: 'array'
          items:
            $ref: '#/components/schemas/WithdrawMethodObject'
        policies:
          type: 'object'
          properties:
            mazoomaAch:
              $ref: '#/components/schemas/WithdrawMethodPolicy'
            prizeout:
              $ref: '#/components/schemas/WithdrawMethodPolicy'
            trustly:
              $ref: '#/components/schemas/WithdrawMethodPolicy'
            skrill:
              $ref: '#/components/schemas/WithdrawMethodPolicy'
            payper:
              $ref: '#/components/schemas/WithdrawMethodPolicy'
            aeroPay:
              $ref: '#/components/schemas/WithdrawMethodPolicy'
            crypto:
              $ref: '#/components/schemas/WithdrawMethodPolicy'
            standardAch:
              $ref: '#/components/schemas/WithdrawMethodPolicy'
            standardCanadaAch:
              $ref: '#/components/schemas/WithdrawMethodPolicy'
            standardBsbAch:
              $ref: '#/components/schemas/WithdrawMethodPolicy'
    GetWithdrawSettingsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    GetWithdrawSettingsResponseBody:
      type: 'object'
      properties:
        type:
          type: 'string'
        supportedCurrencies:
          type: 'array'
          items:
            $ref: '#/components/schemas/CurrencyType'
        providerSettings:
          type: 'array'
          items:
            type: 'object'
            $ref: '#/components/schemas/RedeemProviderSettings'
        isNewRedeemPolicyEnabled:
          type: 'boolean'
          default: 'false'
    RedeemLimitPolicy:
      type: 'object'
      properties:
        min:
          description: 'Min redeem amount per request'
          type: 'number'
        max:
          description: 'Max redeem amount per request'
          type: 'number'
        available:
          description: 'Available redeem amount per day. Not empty if daily redeem limit by state applicable for player '
          type: 'number'
        state:
          description: 'Not empty if daily redeem limit by state applicable for player'
          type: 'string'
    RedeemProviderSettings:
      type: 'object'
      properties:
        currencyType:
          $ref: '#/components/schemas/CurrencyType'
        providerName:
          type: "string"
        withdrawMethodName:
          type: "string"
          description: > 
            Name of withdraw method related to provider, matches the type in response 
            [Get withdraw methods](#operation/getWithdrawMethods)'
        merchantName:
          type: "string"
          description: "Access ID used for iframe initialization. Provider specific field."
        merchantId:
          type: "string"
          description: "Merchant ID used for iframe initialization. Provider specific field."
        apiKey:
          type: 'string'
          description : "API key to pass to widget. Provider specific field."
        notificationUrl:
          type: "string"
          description: "Notification URL used for iframe initialization. Provider specific field."
        limitPolicy:
          $ref: '#/components/schemas/RedeemLimitPolicy'
    GetOffersRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    UpdateInboxNotificationResponseBody:
      type: 'object'
      description: 'Returns updated notification'
      required:
        - notification
      properties:
        notification:
          $ref: '#/components/schemas/InboxNotificationInfo'
    UpdateInboxNotificationRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - notificationToken
        - status
      properties:
        notificationToken:
          description: 'Token of notification returned in [Get inbox notifications](#operation/getInboxNotifications)'
          type: 'string'
        status:
          description: 'New status'
          type: 'string'
          enum:
            - 'read'
            - 'removed'
    GetInboxNotificationsRequestBody:
        x-implements: [ 'gateway.api.RequestBody' ]
        type: 'object'
        allOf: [ ]
    GetInboxNotificationsResponseBody:
      type: 'object'
      properties:
        notifications:
          type: 'array'
          items:
            $ref: '#/components/schemas/InboxNotificationInfo'
    OrderError:
      type: "object"
      description: "Details on purchase error."
      properties:
        message:
          type: "string"
        cardNetworkError:
          type: 'string'
        rescueProviders:
          description: 'Rescue providers to use for failed purchase'
          type: 'array'
          items:
            type: 'string'
    CryptoPurchaseInboxNotificationInfo:
      description: "Details on crypto purchase inbox notification event"
      type: 'object'
      required:
        - purchase
      allOf:
        - $ref: '#/components/schemas/InboxNotificationInfo'
      properties:
        purchase:
          $ref: '#/components/schemas/GetPaymentOrderResponseBody'
    OfferInboxNotificationInfo:
      description: "Details on offer inbox notification event"
      type: 'object'
      required:
        - offer
      allOf:
        - $ref: '#/components/schemas/InboxNotificationInfo'
      properties:
        offer:
          $ref: '#/components/schemas/OfferInfo'
    InboxNotificationType:
      type: string
      enum:
        - 'offer'
        - 'cryptoPurchase'
    InboxNotificationInfo:
      description: "Details on inbox notification event"
      type: 'object'
      required:
        - type
        - notificationToken
        - createdAt
        - status
      discriminator:
        propertyName: type
        mapping:
          offer: '#/components/schemas/OfferInboxNotificationInfo'
          cryptoPurchase: '#/components/schemas/CryptoPurchaseInboxNotificationInfo'
      properties:
        type:
          $ref: '#/components/schemas/InboxNotificationType'
        notificationToken:
          description: 'Unique identifier. Used in [Update notification](#operation/updateInboxNotification)'
          type: 'string'
        status:
          type: 'string'
          enum:
            - 'unread'
            - 'read'
            - 'claimed'
            - 'expired'
            - 'removed'
        createdAt:
          type: 'integer'
          format: 'int64'
        expiresAt:
          type: 'integer'
          format: 'int64'
    OrderStatus:
      type: 'string'
      enum:
        - 'CREATED'
        - 'PENDING'
        - 'SUCCESS'
        - 'FAILED'
    OfferInfo:
      type: 'object'
      properties:
        platform:
          $ref: '#/components/schemas/OfferPlatform'
        bannerImageUrl:
          type: 'string'
        code:
          type: 'string'
        goldFistOffer:
          type: 'number'
        goldMoney:
          type: 'number'
        offerType:
          type: 'string'
          enum:
            - 'one_time'
            - 'subscription'
            - 'permanent'
            - 'personalized'
            - 'weekly'
            - 'daily'
            - 'fixed_daily'
            - 'fixed_weekly'
            - 'reward'
        popUpImageUrl:
          type: 'string'
        specialOfferUrl:
          type: 'string'
        price:
          type: 'number'
        oldPrice:
          type: 'number'
        baseOldPrice:
          type: 'number'
        currency:
          type: 'string'
        basePrice:
          type: 'number'
        baseCurrency:
          type: 'string'
        priority:
          type: 'integer'
        showStickybar:
          type: 'boolean'
          default: 'false'
        showTimeLeft:
          type: 'boolean'
          default: 'false'
        endAt:
          type: 'integer'
          format: 'int64'
        sweepstakeFirstOffer:
          type: 'number'
        sweepstakeMoney:
          type: 'number'
        freeSpins:
          type: 'integer'
          format: 'int32'
        tags:
          type: 'array'
          items:
            type: 'string'
        title:
          type: 'string'
        vipLevel:
          type: 'integer'
        vipPoints:
          type: 'number'
        upgradeOffer:
          $ref: '#/components/schemas/OfferInfo'
        externalRewardCode:
          type: 'string'
        homepageBannerImageUrl:
          type: 'string'
        specialEventOffer:
          $ref: '#/components/schemas/SpecialEventOffer'
    OfferPlatform:
      type: 'string'
      enum:
        - 'web'
        - 'android'
        - 'ios'
        - 'native'
    SpecialEventOffer:
      type: 'object'
      properties:
        iconImageUrl:
          type: 'string'
        backgroundImageUrl:
          type: 'string'
        backgroundBorder:
          type: 'string'
    GetOffersResponseBody:
      type: 'object'
      properties:
        applied:
          type: 'array'
          items:
            $ref: '#/components/schemas/OfferInfo'
        limitAmount:
          type: 'string'
        limitAvailable:
          type: 'string'
        limitEnd:
          type: 'string'
          format: 'date'
        limitPeriod:
          type: 'string'
        offers:
          type: 'array'
          items:
            $ref: '#/components/schemas/OfferInfo'
        type:
          type: 'string'
        lastPurchasedOffer:
          $ref: '#/components/schemas/OfferInfo'
        coinStoreViewExperimentGroup:
          type: 'boolean'
    GetCurrencyRateRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    GetCurrencyRateResponseBody:
      type: 'object'
      required:
        - rate
        - currency
      properties:
        rate:
          type: 'number'
        currency:
          type: 'string'
    DeletePaymentMethodRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      properties:
        code:
          description: 'Payment method code.'
          type: 'string'
        fingerpint:
          description: 'Payment method fingerprint.'
          type: 'string'
    DeletePaymentMethodResponseBody:
      type: 'object'
      properties:
        code:
          type: 'string'
        fingerprint:
          type: 'string'
        type:
          type: 'string'
    Capture3DsPaymentOrderRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - orderTransactionId
        - cvvEntered
        - session
      properties:
        orderTransactionId:
          type: 'string'
          format: 'uuid'
        session:
          description: 'SEON device fingerprint'
          type: 'string'
        cvvEntered:
          type: "boolean"
          description: "Applicable to spreedly card routing purchases only. 
                        If new card tokenization or user has entered cvv after receiving `err_payment_input_cvv`."
          default: "false"
    CaptureRedeemOptionRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - amount
        - currency
        - provider
        - referer
      properties:
        at:
          type: 'string'
          format: 'Date'
        amount:
          type: 'number'
        currency:
          type: 'string'
        provider:
          type: 'string'
          example: 'fiserv'
        referer:
          type: 'string'
        type:
          type: 'string'
    CaptureRedeemOptionResponseBody:
      type: 'object'
      properties:
        id:
          type: 'integer'
          format: 'int64'
        kyc:
          type: 'boolean'
          default: 'false'
        kycStatus:
          $ref: '#/components/schemas/KycStatus'
        provider:
          type: 'string'
          example: 'fiserv'
        redirectUrl:
          type: 'string'
        type:
          type: 'string'
        tmpToken:
          type: 'string'
          description: 'Provider specific metadata, e.g. request signature.'
        method:
          $ref: '#/components/schemas/WithdrawMethodObject'
    SkrillWithdrawMethod:
      type: object
      required:
        - email
      allOf:
        - $ref: '#/components/schemas/WithdrawMethod'
      properties:
        email:
          type: 'string'
        sweepstakeRedeemPolicy:
          $ref: '#/components/schemas/RedeemMoneyPolicy'
        fiatRedeemPolicy:
          $ref: '#/components/schemas/RedeemMoneyPolicy'
    NuveiMazoomaWithdrawMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/WithdrawMethod'
      properties:
        userPaymentOptionId:
          type: 'string'
        fiAccountNumber:
          type: 'string'
        fiAccountType:
          type: 'string'
        fiRouting:
          type: 'string'
        fiName:
          type: 'string'
        firstName:
          type: 'string'
        lastName:
          type: 'string'
        addr:
          type: 'string'
        city:
          type: 'string'
        state:
          type: 'string'
        zip:
          type: 'string'
        country:
          type: 'string'
        dobMonth:
          type: 'string'
        dobDay:
          type: 'string'
        dobYear:
          type: 'string'
    MassPayWithdrawMethod:
      type: object
      required:
        - bankAccountNumber
        - bankAccountRoutingNumber
        - bankAccountType
      allOf:
        - $ref: '#/components/schemas/WithdrawMethod'
      properties:
        bankAccountType:
          type: 'string'
        bankAccountNumber:
          type: 'string'
        bankAccountRoutingNumber:
          type: 'string'
    TrustlyWithdrawMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/WithdrawMethod'
      properties:
        reference:
          type: 'string'
        sweepstakeRedeemPolicy:
          $ref: '#/components/schemas/RedeemMoneyPolicy'
        fiatRedeemPolicy:
          $ref: '#/components/schemas/RedeemMoneyPolicy'
    PrizeoutWithdrawMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/WithdrawMethod'
      properties:
        email:
          type: 'string'
        nonMonetaryRedeemPolicy:
          $ref: '#/components/schemas/RedeemMoneyPolicy'
    PayperWithdrawMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/WithdrawMethod'
      properties:
        email:
          type: 'string'
        phone:
          type: 'string'
    AeroPayWithdrawMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/WithdrawMethod'
      properties:
        phone:
          type: 'string'
        bankAccountName:
          type: 'string'
        bankName:
          type: 'string'
        bankAccountNumber:
          type: 'string'
        accountId:
          type: 'string'
    CryptoWithdrawMethod:
      type: object
      required:
        - wallet
        - currency
        - network
      allOf:
        - $ref: '#/components/schemas/WithdrawMethod'
      properties:
        wallet:
          type: 'string'
        currency:
          $ref: '#/components/schemas/CryptoCurrency'
        network:
          $ref: '#/components/schemas/CryptoNetwork'
    StandardAchWithdrawMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/WithdrawMethod'
      properties:
        bankAccountType:
          type: 'string'
        bankAccountNumber:
          type: 'string'
        bankAccountRoutingNumber:
          type: 'string'
      required:
        - bankAccountNumber
        - bankAccountRoutingNumber
        - bankAccountType
    StandardCanadaAchWithdrawMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/WithdrawMethod'
      properties:
        bankAccountNumber:
          type: 'string'
        institutionNumber:
          type: 'string'
        transitNumber:
          type: 'string'
      required:
        - bankAccountNumber
        - institutionNumber
        - transitNumber
    StandardBsbAchWithdrawMethod:
      type: object
      allOf:
        - $ref: '#/components/schemas/WithdrawMethod'
      properties:
        bankAccountNumber:
          type: 'string'
        bsbRoutingNumber:
          type: 'string'
      required:
        - bankAccountNumber
        - bsbRoutingNumber
    WithdrawMethod:
      description: |
        One of objects depending of provider find in 'components/schemas': <br>
        <b>skrill</b>: SkrillWithdrawMethod <br>
        <b>nuvei_mazooma_ach</b>: NuveiMazoomaWithdrawMethod<br>
        <b>masspay_ach</b>: MassPayWithdrawMethod<br>
        <b>prizeout</b>: PrizeoutWithdrawMethod<br>
        <b>trustly</b>: TrustlyWithdrawMethod<br>
        <b>crypto</b>: CryptoWithdrawMethod<br>
        <b>payper</b>: PayperWithdrawMethod<br>
        <b>aeropay</b>: AeroPayWithdrawMethod<br>
        <b>standard_ach</b>: StandardAchWithdrawMethod<br>
        <b>standard_ca_ach</b>: StandardCanadaAchWithdrawMethod<br>
        <b>standard_bsb_ach</b>: StandardBsbAchWithdrawMethod<br>
      required:
          - type
      type: object
      properties:
        type:
          $ref: '#/components/schemas/WithdrawMethodType'
      discriminator:
        propertyName: type
        mapping:
          skrill: '#/components/schemas/SkrillWithdrawMethod'
          nuvei_mazooma_ach: '#/components/schemas/NuveiMazoomaWithdrawMethod'
          masspay_ach: '#/components/schemas/MassPayWithdrawMethod'
          prizeout: '#/components/schemas/PrizeoutWithdrawMethod'
          trustly: '#/components/schemas/TrustlyWithdrawMethod'
          payper: '#/components/schemas/PayperWithdrawMethod'
          crypto: '#/components/schemas/CryptoWithdrawMethod'
          aeropay: '#/components/schemas/AeroPayWithdrawMethod'
          standard_ach: '#/components/schemas/StandardAchWithdrawMethod'
          standard_ca_ach: '#/components/schemas/StandardCanadaAchWithdrawMethod'
          standard_bsb_ach: '#/components/schemas/StandardBsbAchWithdrawMethod'
    RedeemMoneyRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - amount
        - currency
        - email
        - method
        - session
      properties:
        amount:
          description: Requested amount of redeems
          type: 'number'
        code:
          deprecated: true
          type: 'string'
        currency:
          description: Currency of redeems
          example: 'SC, USD'
          type: 'string'
        email:
          description: 'User email'
          type: 'string'
        locked:
          deprecated: true
          type: 'boolean'
          default: 'false'
        method:
          $ref: '#/components/schemas/WithdrawMethodObject'
        session:
          description: 'SEON device fingerprint'
          type: 'string'
        type:
          type: 'string'
    RedeemMoneyResponseBody:
      type: 'object'
      required:
        - kyc
      properties:
        id:
          type: 'integer'
          format: 'int64'
        kyc:
          type: 'boolean'
          default: 'false'
        kycStatus:
          $ref: '#/components/schemas/KycStatus'
        provider:
          type: 'string'
          example: 'fiserv'
        type:
          type: 'string'
    GetRedeemMoneyHistoryRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      properties:
        firstResult:
          type: 'integer'
        maxResults:
          type: 'integer'
    GetRedeemMoneyHistoryResponseBody:
      type: 'object'
      required:
        - redeems
      properties:
        redeems:
          type: 'array'
          items:
            $ref: '#/components/schemas/RedeemItem'
    GetPendingRedeemCountRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      properties:
        type:
          type: 'string'
    GetPendingRedeemCountResponseBody:
      type: 'object'
      required:
        - count
        - totalAmount
      properties:
        count:
          type: 'integer'
          format: 'int64'
          description: "Pending SC redeem count"
        totalAmount:
          type: 'number'
          description: "Pending SC redeem amount in local currency"
        totalBaseAmount:
          type: 'number'
          description: "Pending SC redeem amount in base currency used during redeem request"
        fiatRedeemCount:
          type: 'integer'
          format: 'int64'
          description: "Pending fiat redeem count"
        fiatRedeemTotalAmount:
          type: 'number'
          description: "Pending fiat redeem amount in local currency"
        fiatRedeemTotalBaseAmount:
          type: 'number'
          description: "Pending fiat redeem amount in base currency used during redeem request"
    GetPaymentOrderRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      properties:
        transactionId:
          type: 'string'
          format: 'uuid'
        type:
          type: 'string'
    GetPaymentOrderResponseBody:
      type: 'object'
      properties:
        amount:
          type: 'number'
        code:
          type: 'string'
        createdAt:
          type: 'string'
          format: 'Date'
        currency:
          type: 'string'
        id:
          type: 'integer'
          format: 'int64'
        offer:
          $ref: '#/components/schemas/OfferInfo'
        internalStatus:
          $ref: '#/components/schemas/OrderStatus'
        provider:
          type: 'string'
          example: 'fiserv'
        status:
          type: 'string'
        success:
          type: 'boolean'
          default: 'false'
        transactionId:
          type: 'string'
          format: 'uuid'
        type:
          type: 'string'
        isFirstDeposit:
          type: 'boolean'
          default: 'false'
          description: 'Use from transactionDetails'
          deprecated: true
        zip:
          type: 'string'
        city:
          type: 'string'
        billingDescriptor:
          type: 'string'
        failReason:
          type: 'string'
        method:
          $ref: '#/components/schemas/PurchaseMethod'
        error:
          $ref: '#/components/schemas/OrderError'
        transactionDetails:
          $ref: '#/components/schemas/TransactionDetails'
        cryptoPurchaseDetails:
          $ref: '#/components/schemas/CryptoPurchaseData'
        goldCoins:
          type: number
        sweepsTakeCoins:
          type: number
    GetPaymentServiceInfoRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    GetCryptoPaymentServiceInfoRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    GetPaymentServiceBankDetailsRequestBody:
        x-implements: [ 'gateway.api.RequestBody' ]
        type: 'object'
        allOf: [ ]
    GetPaymentServiceBankDetailsResponseBody:
        type: 'object'
        properties:
          success:
            type: 'boolean'
            description: 'Indicates whether the user data was successfully retrieved'
          errorMsg:
            type: 'string'
            description: 'Error message if success is false'
          bankAccounts:
            type: 'array'
            items:
              $ref: '#/components/schemas/LinkBankAccountResponseBody'

    Nuvei:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: >
        ACH provider. Iframe initialization not needed. 
        On purchase just use redirect url from [Create order](#operation/createOrder).
        On withdraw just pass ACH data to [Redeem money](#operation/redeemMoney)
      properties:
        id:
          type: "string"
          deprecated: true
    Trustly:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: "ACH provider. If you have this object in reply that's mean trustly is enabled on backend side."
      externalDocs:
        description: >
          For more information, you can read this documentation.
          For Trustly initialization we need to pass fields from response to the trustly initialization object and javascript library:
          {
          ……
            accessId: trustly.id,
            merchantId: trustly.merchant,
          notificationUrl: trustly.notificationUrl
          }
          Javascript library: https://trustly.one/start/scripts/trustly.js?accessId={trustly.id}'
        url: https://amer.developers.trustly.com/payments/docs/sdk
      properties:
        id:
          type: "string"
          description: "Access ID used for iframe initialization."
        merchant:
          type: "string"
          description: "Merchant ID used for iframe initialization."
        notificationUrl:
          type: "string"
          description: "Notification URL used for iframe initialization."
    Payper:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: "E-Transfer provider. If you have this object in reply that's mean payper is enabled on backend side."
    Crypto:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: "Crypto provider. If you have this object in reply that's mean crypto is enabled on backend side."
    Skrill:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: >
        Digital wallet. If you have this object in reply that's mean skrill processing is enable on backend side.
        On purchase just use redirect url from [Create order](#operation/createOrder)'
      properties:
        id:
          type: "string"
          deprecated: true
    #    todo add description on initialization
    AppleInApp:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: "If you have this object in reply that's mean apple application purchase processing is enable on backend side."
      required:
        - providerName
      properties:
        id:
          type: "string"
          deprecated: true
    AndroidInApp:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: "If you have this object in reply that's mean android application purchase processing is enable on backend side."
      required:
        - providerName
    Fiserv:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: "If you have this object in reply that's mean fiserv card processing is enable on backend side."
      required:
        - id
        - providerName
      properties:
        id:
          description: "Fiserv client id, not used for iframe initialization"
          type: "string"
    FiservApplePay:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: "If you have this object in reply that's mean fiserv apple pay card processing is enable on backend side."
      externalDocs:
        description: 'For more information, you can refer to the Apple Pay documentation.'
        url: https://applepaydemo.apple.com
      required:
        - merchantId
        - providerName
      properties:
        merchantId:
          description: "For the initialization of Apple Pay, we need to pass the merchantId to the initialization function: window.ApplePaySession.canMakePayments(merchantId)."
          type: "string"
    FiservGooglePay:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: >
        If you have this object in reply that's mean fiserv google pay card processing is enable on backend side.
        For the initialization of Google Pay, we need to pass fields from the response to the Google Pay react component. 
        In our case, we use '@google-pay/button-react'. Data from the response we provide to these fields: 
        tokenizationSpecification: {
                      type: 'PAYMENT_GATEWAY',
                      parameters: {
                        gateway: config?.gateway as string,
                        gatewayMerchantId: config?.merchant as string,
                      },
                    },
        merchantInfo: {
                  merchantId: config?.merchantId as string,
                  merchantName: config?.merchantName?.replace(/_/g, ' '),
                },
        For more information about initialization Google Pay button you can get from this resources:
        https://www.npmjs.com/package/@google-pay/button-react'
      externalDocs:
        description: 'For more information, you can refer to the Google Pay documentation.'
        url: https://developers.google.com/pay/api/web/overview
      required:
        - gateway
        - merchant
        - merchantId
        - merchantName
        - providerName
      properties:
        gateway:
          type: 'string'
        merchant:
          type: 'string'
        merchantId:
          type: 'string'
        merchantName:
          type: "string"
    Spreedly:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: "If you have this object in reply that's mean spreedly card processing is enable on backend side."
      externalDocs:
        description: 'For more information, you can refer to the Spreedly documentation.'
        url: https://docs.spreedly.com/guides/adding-payment-methods/iframe/
      properties:
        id:
          description: >
            For the initialization of Spreedly, we need to pass the id to the initialization function: 
              Spreedly.init('id', {
                  'numberEl': 'spreedly-number',
                   'cvvEl' 'spreedly-cvv'
              });
          type: "string"
    SpreedlyApplePay:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: "If you have this object in reply that's mean spreedly apple pay card processing is enable on backend side."
      externalDocs:
        description: 'For more information, you can refer to the Apple Pay documentation.'
        url: https://applepaydemo.apple.com
      properties:
        merchantId:
          description: "For the initialization of Apple Pay, we need to pass the merchantId to the initialization function: window.ApplePaySession.canMakePayments(merchantId)."
          type: "string"
    SpreedlyGooglePay:
      type: 'object'
      description: >
        If you have this object in reply that's mean spreedly google pay card processing is enable on backend side.
        For the initialization of Google Pay, we need to pass fields from the response to the Google Pay react component. 
        In our case, we use '@google-pay/button-react'. Data from the response we provide to these fields: 
        tokenizationSpecification: {
                      type: 'PAYMENT_GATEWAY',
                      parameters: {
                        gateway: config?.gateway as string,
                        gatewayMerchantId: config?.merchant as string,
                      },
                    },
        merchantInfo: {
                  merchantId: config?.merchantId as string,
                  merchantName: config?.merchantName?.replace(/_/g, ' '),
                },
        [For more information about initialization Google Pay button](https://www.npmjs.com/package/@google-pay/button-react),
        [Spreedly documentation regarding google pay](https://docs.spreedly.com/guides/google-pay/).'
      externalDocs:
        description: 'For more information, you can refer to the Google Pay documentation.'
        url: https://developers.google.com/pay/api/web/overview
      allOf:
          - $ref: '#/components/schemas/PaymentProvider'
      properties:
        gateway:
          type: 'string'
          description: 'Gateway name configuration. For now it is spreedly.'
        merchant:
          type: 'string'
          description: 'Merchant identifier from google pay admin panel.'
        merchantId:
          type: 'string'
          description: 'Gateway merchant id. Spreedly environment key.'
        merchantName:
          type: "string"
          description: "Merchant name from google pay admin panel."
    Masspay:
      description: "ACH provider. If you have this object in reply that's mean masspay is enabled on backend side."
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      properties:
        id:
          type: "string"
          deprecated: true
    Prizeout:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: >
        Non-monetary redeem provider. If you have this object in reply that's mean prizeout is enabled on backend side.
        For initialization prizeout widget we need to pass id and key to the 
        window.prizeoutSDK.init({...., publisher: {..., id: prizeoutId: id, apikey:key},})
      properties:
        id:
          type: 'string'
          description: 'ID to pass to prizeout widget'
        key:
          type: "string"
          description: "API key to pass to prizeout widget"
    StandardAch:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: "Ach provider. If you have this object in reply that's mean standard ach option is enabled on backend side."
    AeroPay:
      type: 'object'
      allOf:
        - $ref: '#/components/schemas/PaymentProvider'
      description: "AeroPay provider. If you have this object in reply that's mean aeropay is enabled on backend side."
    PaymentProvider:
      type: 'object'
      required:
        - currencyType
      properties:
        providerName:
          type: "string"
        currencyType:
          $ref: '#/components/schemas/CurrencyType'
    CurrencyType:
      type: string
      enum:
        - 'FIAT'
        - 'CRYPTO'
    CryptoServiceInfoItem:
      type: 'object'
      required:
        - currency
        - networks
      properties:
        currency:
          $ref: '#/components/schemas/CryptoCurrencyData'
        minLimitInBaseCurrency:
          description: 'Target offer price should be more or equal to this value'
          type: number
        rateToBaseCurrency:
          description: 'crypto to base currency approximate rate'
          type: number
        networks:
          type: 'array'
          items:
            $ref: '#/components/schemas/CryptoNetworkData'
    GetCryptoPaymentServiceInfoResponseBody:
      type: 'object'
      required:
        - purchaseData
        - withdrawData
      properties:
        purchaseData:
          description: 'service items used for purchases'
          type: 'array'
          items:
            $ref: '#/components/schemas/CryptoServiceInfoItem'
        withdrawData:
          description: 'service items used for redeems'
          type: 'array'
          items:
            $ref: '#/components/schemas/CryptoServiceInfoItem'
    GetPaymentServiceInfoResponseBody:
      type: 'object'
      properties:
        thirdPartyCheckEnabled:
          type: "boolean"
          description: >
            AB testing. Indicates whether 3rd part card check is enabled. 
            If enabled verify cardholder name matches with account first last name from personal info.
        softKycRequiredEnabled:
          type: "boolean"
          description: "AB testing. Indicates whether user need to complete soft kyc data before any purchase or redeem"
        cardRegistrationModify:
          type: 'boolean'
          description: 'AB testing. Indicates for modify card registration first time and Subsequent Purchasers'
        softKycAutoCompleteAddress:
          type: 'boolean'
          description: 'AB testing. Indicates whether to apply soft kyc autocomplete address.'
        supportedCurrencies:
          type: 'array'
          items:
            $ref: '#/components/schemas/CurrencyType'
        purchaseProviders:
          type: 'object'
          properties:
            appleInApp:
              $ref: '#/components/schemas/AppleInApp'
            androidInApp:
              $ref: '#/components/schemas/AndroidInApp'
            fiserv:
              $ref: '#/components/schemas/Fiserv'
            fiservApplePay:
              $ref: '#/components/schemas/FiservApplePay'
            fiservGooglePay:
              $ref: '#/components/schemas/FiservGooglePay'
            nuvei:
              $ref: '#/components/schemas/Nuvei'
            trustly:
              $ref: '#/components/schemas/Trustly'
            skrill:
              $ref: '#/components/schemas/Skrill'
            spreedly:
              $ref: '#/components/schemas/Spreedly'
            spreedlyApplePay:
              $ref: '#/components/schemas/SpreedlyApplePay'
            spreedlyGooglePay:
              $ref: '#/components/schemas/SpreedlyGooglePay'
            payper:
              $ref: '#/components/schemas/Payper'
            aeropay:
              $ref: '#/components/schemas/AeroPay'
            crypto:
              $ref: '#/components/schemas/Crypto'
        withdrawProviders:
          type: 'object'
          properties:
            nuvei:
              $ref: '#/components/schemas/Nuvei'
            trustly:
              $ref: '#/components/schemas/Trustly'
            prizeout:
              $ref: '#/components/schemas/Prizeout'
            skrill:
              $ref: '#/components/schemas/Skrill'
            payper:
              $ref: '#/components/schemas/Payper'
            standardAch:
              $ref: '#/components/schemas/StandardAch'
            aeropay:
              $ref: '#/components/schemas/AeroPay'
        type:
          type: 'string'
    GetPaymentMetaInfoRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    GetPaymentMetaInfoResponseBody:
      type: 'object'
      properties:
        firstDeposit:
          type: 'string'
          format: 'Date'
          description: 'Date of first successful purchase or deposit.'
          example: '2023-06-29T12:36:02.185+00:00'
        lastDeposit:
          type: 'string'
          format: 'Date'
          description: 'Date of last successful purchase or deposit.'
          example: '2023-06-29T12:36:02.185+00:00'
        withdrawCount:
          type: 'integer'
          description: 'Number of successfully confirmed user redemptions.'
          format: 'int64'
        withdrawTotalAmount:
          type: 'number'
          description: 'Sum of all successfully confirmed user redemptions.'
        firstWithdraw:
          type: 'string'
          description: 'Date of first successfully confirmed user redemption.'
          example: '2023-06-29T12:36:02.185+00:00'
          format: 'Date'
        firstSuccessfulDeposit:
          type: 'string'
          description: 'Date of first successful deposit.'
          example: '2023-06-29T12:39:02.185+00:00'
          format: 'Date'
        firstSuccessfulPurchase:
          type: 'string'
          description: 'Date of first successful purchase.'
          example: '2023-06-29T12:46:02.185+00:00'
          format: 'Date'
        lastSuccessPurchaseOfferCode:
          type: 'string'
          description: 'Offer code of the last successful purchase.'
        lastSuccessPurchasePrice:
          type: 'string'
          description: 'Price of the last successful purchased offer.'
        lastSuccessPurchaseDate:
          type: 'string'
          description: 'Date of the last successful purchased offer.'
          example: '2023-06-29T12:46:02.185+00:00'
          format: 'Date'
    AcceptPaymentTermRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - code
      properties:
        code:
          type: 'string'
          description: "code of accepted payment term"
    AcceptPaymentTermResponseBody:
      type: 'object'
      allOf: [ ]
    GetAcceptedPaymentTermsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    GetAcceptedPaymentTermsResponseBody:
      type: 'object'
      properties:
        terms:
          type: 'array'
          items:
            $ref: '#/components/schemas/AcceptedPaymentTerm'
    AcceptedPaymentTerm:
      type: 'object'
      properties:
        code:
          type: 'string'
          description: "code of accepted payment term"
        acceptedAt:
          type: 'string'
          format: "Date"
          description: "the time when the term was accepted"
    CancelRedeemMoneyRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      properties:
        id:
          type: "integer"
          format: "int64"
          description: "Redeem id to cancel."
    CancelRedeemMoneyResponseBody:
      type: object
      allOf:
        - $ref: '#/components/schemas/RedeemItem'
    CancelAllRedeemMoneyResponseBody:
      type: object
      properties:
        success:
          type: boolean
          description: 'Indicates whether the request data was successfully retrieved'
    CancelAllRedeemMoneyNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      description: 'This websocket notification is sent when user pending redeems are updated cancelled'
      required:
        - redeemIds
        - cancelledIds
        - eventType
        - eventMessage
      properties:
        redeemIds:
          type: array
          items:
            type: "integer"
            format: "int64"
        cancelledIds:
          type: array
          items:
            type: "integer"
            format: "int64"
        eventType:
          type: string
          enum:
            - 'ALL_CANCELLED'
            - 'PARTIAL_CANCELLED'
            - 'NONE_CANCELLED'
            - 'ERROR'
        eventMessage:
          type: string
    CreateAccountPaymentMethodRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      required:
        - token
      properties:
        token:
          type: string
        cardData:
          $ref: '#/components/schemas/CardData'
    CreateAccountPaymentMethodResponseBody:
      type: object
      allOf: [ ]
    CreatePayperPaymentMethodRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      required:
        - email
        - phone
      properties:
        email:
          type: string
        phone:
          type: string
    CreatePayperPaymentMethodResponseBody:
      type: object
      properties:
        token:
          type: "string"
          description: "Payment method token for using subsequent purchase. Use this it as `token` field in subsequent calls of [Create order](#operation/createOrder)."
    RegisterPaymentUserAccountRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - phone
      properties:
        phone:
          type: 'string'
          description: "User's phone number"
    RegisterPaymentUserAccountResponseBody:
      type: 'object'
      properties:
        otpRequired:
          type: 'boolean'
          description: "Indicates whether OTP is required for registration"
        widgetFastlinkURL:
          type: 'string'
          description: "Widget URL for registration"
        widgetToken:
          type: 'string'
          description: "Widget token for registration"
        widgetUsername:
          type: 'string'
          description: "Widget username for registration"
    ConfirmPaymentUserAccountRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - code
      properties:
        code:
          type: 'string'
          description: "User's OTP code"
    LinkBankAccountRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - userName
        - userPassword
        - phone
        - transactionId
      properties:
          phone:
              type: 'string'
              description: "User's phone number used for registration of Payment method"
          userId:
              type: 'string'
              description: "User ID received from widget for linking bank account"
          userPassword:
              type: 'string'
              description: "User password received from widget for linking bank account"
          transactionId:
              type: 'string'
              description: "Transaction ID received when CreateOrderRequest was sent "
    CreateNewAeroPayMethodRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - bankAccountId
      properties:
        accountId:
          type: 'string'
          description: "Bank account ID in payment provider system."
    CreateNewAeroPayMethodResponseBody:
      type: 'object'
      properties:
        withdrawMethod:
          $ref: '#/components/schemas/WithdrawMethodObject'
        paymentMethod:
          $ref: '#/components/schemas/PaymentMethod'
    ConfirmPaymentUserAccountResponseBody:
      type: 'object'
      properties:
        success:
          type: 'boolean'
          description: 'Indicates the user was successfully confirmed'
        errorMsg:
          type: 'string'
          description: 'Error message if success is false'
        bankAccounts:
          type: 'array'
          items:
            $ref: '#/components/schemas/LinkBankAccountResponseBody'
    LinkBankAccountResponseBody:
      type: 'object'
      properties:
        accountId:
          type: 'string'
          description: "Bank account ID in payment provider system."
        bankName:
          type: 'string'
          description: "Name of bank"
        accountNumber:
            type: 'string'
            description: "Account number"
        selected:
            type: 'boolean'
            description: "Indicates whether this bank account is selected for payment"
    RedeemItem:
      type: 'object'
      required:
        - currency
        - id
        - provider
        - status
      properties:
        amount:
          type: 'number'
        createdAt:
          type: 'string'
          format: 'Date'
        lockedAt:
          type: 'string'
          format: 'Date'
        preConfirmedAt:
          type: 'string'
          format: 'Date'
        currency:
          $ref: '#/components/schemas/Currency'
        id:
          type: 'integer'
          format: 'int64'
        provider:
          type: 'string'
          example: 'fiserv'
        status:
          $ref: '#/components/schemas/RedeemStatus'
        redeemStatus:
          $ref: '#/components/schemas/RedeemUserStatus'
        paymentDetails:
          type: 'string'
        cryptoPaymentDetails:
          $ref: '#/components/schemas/CryptoPurchaseData'
    BalanceChange:
      type: 'object'
      properties:
        amount:
          type: 'string'
        currency:
          type: 'string'
    CryptoNetwork:
      type: string
      enum:
        - 'BTC'
        - 'ETH'
        - 'BNB'
        - 'LTC'
        - 'TRX'
        - 'POLYGON'
        - 'SOL'
        - 'BCH'
        - 'AVAX'
        - 'TETH'
        - 'TON'
    CryptoNetworkData:
      type: object
      required:
        - code
        - label
      properties:
        preferred:
          description: 'Tag item'
          type: 'boolean'
        code:
          $ref: '#/components/schemas/CryptoNetwork'
        label:
          description: 'UI label in human format'
          type: string
        walletValidationRegexp:
          description: 'Used for wallet validation. Present in redeems service info'
          type: string
    CryptoCurrency:
      type: string
      enum:
        - 'USDT'
        - 'USDC'
        - 'ETH'
        - 'BTC'
        - 'LTC'
        - 'BCH'
        - 'TST'
        - 'EOS'
        - 'CRO'
        - 'UNI'
        - 'APE'
        - 'POL'
        - 'XRP'
    CryptoCurrencyData:
      type: object
      required:
        - code
        - label
      properties:
        code:
          $ref: '#/components/schemas/CryptoCurrency'
        label:
          description: 'UI label in human format'
          type: string
    Price:
      type: 'object'
      properties:
        amount:
          type: 'string'
          description: "Transaction amount in local currency."
        currency:
          type: 'string'
          description: "Local user currency."
        baseAmount:
          type: 'string'
          description: "Transaction amount in base currency."
        baseCurrency:
          type: 'string'
          description: "Original currency used for this transaction."
    UserPaymentMode:
      type: 'string'
      enum:
        - 'card'
        - 'skrill'
        - 'wire_transfer'
        - 'virtual_wallet_apple_pay'
        - 'virtual_wallet_google_pay'
        - 'in_app_purchase'
        - 'crypto'
        - 'aeropay'
    UserIntegrationType:
      type: 'string'
      enum:
        - 'skrill'
        - 'nuvei_mazooma_ach'
        - 'apple_in_app'
        - 'trustly'
        - 'payper'
        - 'crypto'
        - 'aeropay'
    PurchaseMethod:
      type: 'object'
      properties:
        paymentMode:
          $ref: '#/components/schemas/UserPaymentMode'
        integrationType:
          $ref: '#/components/schemas/UserIntegrationType'
        cardBrand:
          type: "string"
          description: "Relevant only for card payments."
        cardNumberMask:
          type: "string"
          description: "Optional field that is relevant for card payments."
        accountNumberMask:
          type: "string"
          description: "Optional field that is relevant for ach payments."
        email:
          type: "string"
          description: "Optional field that is relevant for e-transfer payments."
    PurchaseItem:
      type: 'object'
      required:
        - transactionId
        - status
        - dateTime
      properties:
        transactionId:
          type: 'string'
          format: 'uuid'
        dateTime:
          type: "string"
          format: "date-time"
          description: "The creation or completion date depending on the purchase status."
          example: "2023-07-19T10:50:54.650+00:00"
        balanceChange:
          type: 'array'
          items:
            $ref: '#/components/schemas/BalanceChange'
        price:
          type: 'array'
          items:
            $ref: '#/components/schemas/Price'
        method:
          $ref: '#/components/schemas/PurchaseMethod'
        cryptoPurchaseData:
          $ref: '#/components/schemas/CryptoPurchaseData'
        internalStatus:
          $ref: '#/components/schemas/OrderStatus'
    CryptoPurchaseData:
      type: 'object'
      properties:
        amount:
          type: number
        wallet:
          type: string
        txHash:
          type: string
        currency:
          $ref: '#/components/schemas/CryptoCurrencyData'
        network:
          $ref: '#/components/schemas/CryptoNetworkData'
    TransactionLimit:
      type: 'object'
      required:
        - operation
        - currency
      properties:
        operation:
          description: "Type of the payment operation"
          $ref: '#/components/schemas/PaymentOperation'
        currency:
          description: "Transaction currency"
          $ref: '#/components/schemas/Currency'
        min:
          type: 'number'
        max:
          type: 'number'
    TransactionDetails:
      type: 'object'
      properties:
        isFirstOfferPurchase:
          type: 'boolean'
          description: "Indicates whether this order is a first offer purchase for this account."
        firstOfferPurchaseDate:
          type: 'string'
          format: 'Date'
          example: '2023-06-29T12:35:51.410+00:00'
          description: 'Date of the first offer purchase.'
        isFirstFiatDeposit:
          type: 'boolean'
          description: "Indicates whether this order is a first fiat deposit for this account."
        firstFiatDepositDate:
          type: 'string'
          format: 'Date'
          example: '2023-06-29T12:35:51.410+00:00'
          description: 'Date of the first deposit.'
    GetRedeemHistoryRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      properties:
        statuses:
          type: 'array'
          items:
            $ref: '#/components/schemas/RedeemUserStatus'
        firstResult:
          type: 'integer'
        maxResults:
          type: 'integer'
    GetRedeemHistoryResponseBody:
      type: 'object'
      required:
        - redeems
        - totalCount
      properties:
        redeems:
          type: 'array'
          items:
            $ref: '#/components/schemas/RedeemItem'
        totalCount:
          type: 'integer'
          format: 'int64'
    ScaAuthenticateData:
      type: 'object'
      required:
        - type
      description: >
        SCA authenticate data. Include if you have 'ERR_PAYMENT_3DS_REQUIRED' error in [#operation/createOrder].
        For spreedly use SpreedlyScaAuthenticateRequest.
      properties:
        type:
          type: string
      discriminator:
        propertyName: type
        mapping:
          SpreedlyScaAuthenticateRequest: '#/components/schemas/SpreedlyScaAuthenticateRequest'
    SpreedlyScaAuthenticateRequest:
      type: object
      allOf:
        - $ref: '#/components/schemas/ScaAuthenticateData'
      properties:
        browserInfo:
          type: 'string'
    RedeemStatus:
      type: 'string'
      enum:
        - 'new'
        - 'pre_authorized'
        - 'confirmed'
        - 'declined'
        - 'failed'
        - 'cancelled'
        - 'locked'
    RedeemUserStatus:
      type: 'string'
      enum:
        - 'pending'
        - 'processed'
        - 'canceled'
        - 'failed'
    Currency:
      type: 'string'
      enum:
        - 'GC'
        - 'SC'
        - 'USD'
        - 'CAD'
        - 'AUD'
        - 'GBP'
    KycStatus:
      type: 'string'
      enum:
        - 'initial'
        - 'in_review'
        - 'confirmed'
        - 'id_confirmed'
        - 'doc_review'
        - 'doc_declined'
        - 'session_expired'
        - 'declined'
        - 'blocked'
    QuickPurchaseSupportedType:
      description: 'Used to track source of quick purchase.'
      type: 'string'
      enum:
        - 'quick_purchase_get_coins'
        - 'quick_purchase'
        - 'quick_deposit'
    Mode:
      type: 'string'
      enum:
        - 'dt'
        - 'gc'
        - 'fl'
        - 'sc'
        - 'scpr'
        - 'scprwb'
    PaymentOperation:
      type: 'string'
      enum:
        - 'DEPOSIT'
        - 'REDEEM'
    ConfirmPaymentOrderStatus:
      type: 'string'
      enum:
        - 'success'
        - 'failure'
    WithdrawMethodObject:
      required:
        - withdrawMethod
      type: 'object'
      properties:
        code:
          description: 'Unique code of redeem payment method. Must be used for subsequent operations'
          type: 'string'
        withdrawMethod:
          $ref: '#/components/schemas/WithdrawMethod'
    WithdrawMethodType:
      type: string
      enum:
        - 'masspay_ach'
        - 'skrill'
        - 'nuvei_mazooma_ach'
        - 'prizeout'
        - 'trustly'
        - 'payper'
        - 'aeropay'
        - 'crypto'
        - 'standard_ach'
        - 'standard_ca_ach'
        - 'standard_bsb_ach'
    CardData:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        firstSixDigits:
          type: string
        firstEightDigits:
          type: string
        lastFour:
          type: string
        month:
          type: integer
          format: int32
        year:
          type: integer
          format: int32
          description: 4 digits
        cardType:
          type: string
          description: visa, master
        fingerprint:
          type: string
          description: unique card identifier
        billingAddress:
          $ref: '#/components/schemas/BillingAddress'
      required:
        - firstName
        - lastName
        - firstSixDigits
        - lastFour
        - month
        - year
        - cardType
        - fingerprint
        - billingAddress
    BillingAddress:
      type: 'object'
      properties:
        city:
          type: 'string'
        country:
          type: 'string'
        dateOfBirth:
          type: 'string'
        firstName:
          type: 'string'
        houseNumberOrName:
          type: 'string'
        lastName:
          type: 'string'
        postalCode:
          type: 'string'
        stateOrProvince:
          type: 'string'
        street:
          type: 'string'
      required:
        - city
        - country
        - postalCode
        - street
    GetPurchaseHistoryRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      properties:
        filter:
          $ref: '#/components/schemas/CommonPaymentHistoryFilter'
        dateRange:
          $ref: '#/components/schemas/DateRange'
        pageRequest:
          description: "The optional page details. If not specified, the API returns purchases with default pagination"
          $ref: '#/components/schemas/PageRequest'
        orderType:
          description: 'Type of the order (for now is offer purchase or deposit). Lists all if the type is not specified'
          type: 'string'
          enum:
            - 'offer_purchase'
            - 'deposit'
    GetPurchaseHistoryResponseBody:
      type: 'object'
      required:
        - purchases
        - pagination
      properties:
        purchases:
          type: 'array'
          items:
            $ref: '#/components/schemas/PurchaseItem'
        pagination:
          $ref: '#/components/schemas/Pagination'
    GetRedeemsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      properties:
        filter:
          $ref: '#/components/schemas/CommonPaymentHistoryFilter'
        dateRange:
          $ref: '#/components/schemas/DateRange'
        pageRequest:
          description: "The optional page details. If not specified, the API returns purchases with default pagination"
          $ref: '#/components/schemas/PageRequest'
        redeemType:
          type: 'string'
          enum:
            - 'sc_redeem'
            - 'fiat_redeem'
          description: 'SC redeems are requested with SC currency. Fiat redeems are requested with fiat currency (e.g. USD). Lists all redemptions if the type is not specified'
    GetRedeemsResponseBody:
      type: 'object'
      required:
        - redeems
        - pagination
      properties:
        redeems:
          type: 'array'
          items:
            $ref: '#/components/schemas/RedeemItem'
        pagination:
          $ref: '#/components/schemas/Pagination'
    GetTransactionLimitRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    GetTransactionLimitResponseBody:
      type: 'object'
      properties:
        limits:
          type: 'array'
          items:
            $ref: '#/components/schemas/TransactionLimit'
    SetPurchaseLimitRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      properties:
        limits:
          type: 'array'
          items:
            $ref: '#/components/schemas/SetPurchaseLimitInfo'
    SetPurchaseLimitInfo:
      type: 'object'
      required:
        - threshold
        - period
      properties:
        threshold:
          description: 'Corresponding limit. Up to 14 digits before the decimal point and up to 2 digits after.'
          type: 'number'
          pattern: '^\d{1,14}(\.\d{1,2})?$'
        period:
          description: 'Time period.'
          $ref: "#/components/schemas/LimitPeriod"
    SetPurchaseLimitResponseBody:
      type: 'object'
      properties:
        limits:
          type: 'array'
          items:
            $ref: '#/components/schemas/SetLimitResultInfo'
    SetLimitResultInfo:
      type: 'object'
      properties:
        limitId:
          type: 'integer'
          format: 'int64'
        period:
          $ref: "#/components/schemas/LimitPeriod"
    GetPurchaseLimitsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    GetPurchaseLimitsResponseBody:
      type: 'object'
      properties:
        internalLimits:
          type: 'array'
          items:
            $ref: '#/components/schemas/LimitInfo'
        memberRequestLimits:
          type: 'array'
          items:
            $ref: '#/components/schemas/LimitInfo'
        consumedLimits:
          type: 'array'
          items:
            $ref: '#/components/schemas/ConsumedLimitInfo'
        deferredLimits:
          type: 'array'
          items:
            $ref: '#/components/schemas/DeferredLimitInfo'
    CommonPaymentHistoryFilter:
      type: 'object'
      properties:
        currencyTypes:
          type: 'array'
          items:
            $ref: '#/components/schemas/CurrencyType'
    LimitInfo:
      type: 'object'
      properties:
        limitAmount:
          type: 'string'
        limitEnd:
          type: 'string'
          format: 'date'
        limitResetDate:
          type: 'string'
          format: 'date'
        limitPeriod:
          $ref: "#/components/schemas/LimitPeriod"
    DeferredLimitInfo:
      type: 'object'
      properties:
        limitAmount:
          type: 'string'
        startAt:
          type: 'string'
          format: 'date'
        limitPeriod:
          $ref: "#/components/schemas/LimitPeriod"
        type:
          $ref: "#/components/schemas/DeferredLimitType"
    ConsumedLimitInfo:
      allOf:
        - $ref: '#/components/schemas/LimitInfo'
        - type: object    
          properties:
            limitConsumed:
              type: 'string'
    LimitPeriod:
      type: string
      enum:
        - 'DAILY'
        - 'WEEKLY'
        - 'MONTHLY'
    DeferredLimitType:
      type: string
      enum:
        - 'UPDATE'
        - 'RESET'
    ResetAccountPaymentMethodsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      required:
        - provider
      properties:
        provider:
          type: 'string'
    ResetAccountPaymentMethodsResponseBody:
      type: 'object'
      allOf: [ ]
    ResetPurchaseLimitsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]
    ResetPurchaseLimitsResponseBody:
      type: 'object'
      allOf: [ ]
