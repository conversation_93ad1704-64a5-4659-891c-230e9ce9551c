openapi: 3.0.1
info:
  title: Creators API
  version: '1.0'
tags:
  - name: 'account'
  - name: 'creator'
  - name: 'play-together'
  - name: 'creator-stream-channels'
  - name: 'limited-to-creator-stream-channels'
  - name: 'schedule-stream-create'
  - name: 'schedule-stream-update'
  - name: 'schedule-stream-delete'
  - name: 'schedule-streams'
  - name: 'turn-info'
  - name: 'stream-live-and-upcoming'
  - name: 'aws-stream-channel'
  - name: 'stream-watch-activity'
paths:
  /api/v1/account-info:
    post:
      tags:
        - 'account'
      operationId: 'getAccountInfo'
      summary: "Return account info"
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAccountInfoRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "Information about account"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAccountInfoResponseBody'

  /api/v1/creator:
    post:
      tags:
        - 'creator'
      operationId: 'getCreators'
      summary: "Return creator info"
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCreatorListRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "List of creators"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCreatorListResponseBody'

  /api/v1/creator-not-followed:
    post:
      tags:
        - 'creator-not-followed'
      operationId: 'getNotFollowedCreators'
      summary: "Return list of not followed by current user creators"
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetNotFollowedCreatorsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "List of not followed by current user creators"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCreatorListResponseBody'

  /api/v1/creator-update:
    post:
      tags:
        - 'creator'
      operationId: 'updateCreator'
      x-async-enabled: true
      summary: "Update creator info"
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCreatorRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "Creator info"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateCreatorResponseBody'

  /api/v1/creator-current-earnings:
    post:
      tags:
        - 'creator'
      operationId: 'getCreatorCurrentEarnings'
      summary: "Return creator current earnings"
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCreatorCurrentEarningsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "Creator current earnings"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCreatorCurrentEarningsResponseBody'

  /api/v1/creator-play-together-stats:
    post:
      tags:
        - 'creator'
        - 'play-together'
      operationId: 'GetCreatorRoundsStats '
      summary: "Returns creator's game rounds statistic"
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCreatorRoundStatsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "Creator game rounds statistic"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCreatorRoundStatsResponseBody'

  /api/v1/creator-play-together-history:
    post:
      tags:
        - 'creator'
        - 'play-together'
      operationId: 'GetCreatorMultiplierHistory'
      summary: "Returns creator's multiplier history"
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCreatorMultiplierHistoryRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "Creator multiplier history"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCreatorMultiplierHistoryResponseBody'

  /api/v1/creator-wager-settings:
    post:
      tags:
        - 'creator'
        - 'play-together'
      operationId: 'GetWagerSettings '
      summary: "Returns current creator's wager settings"
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWagerSettingsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "Creator current wager settings"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWagerSettingsResponseBody'

  /api/v1/play-together-session/start:
    post:
      summary: Create a new play together session
      tags:
        - 'play-together'
      operationId: 'createPlayTogetherSession'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePlayTogetherSessionRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "Play together session"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePlayTogetherSessionResponseBody'

  /api/v1/play-together-session:
    post:
      summary: Get list of play together sessions
      tags:
        - 'play-together'
      operationId: 'getPlayTogetherSessions'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPlayTogetherSessionsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "List of play together sessions"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPlayTogetherSessionsResponseBody'

  /api/v1/play-together-session/stop:
    post:
      summary: Stops playTogether Session
      description: |
        Stops playTogether Session of particular follower if it was previously in OPEN state
      tags:
        - 'play-together'
      operationId: 'stopPlayTogetherSession'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StopPlayTogetherSessionRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: Stopped PlayTogetherSession with actual status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StopPlayTogetherSessionResponseBody'

  /api/v1/play-together-session/alive:
    post:
      summary: Marks that playTogether session tab is still active
      description: |
        Every active tab with active playTogether session should send requests to this endpoint periodically.
        This how we can detect if follower doesn't have problems with connection or hasn't closed the tab with playTogether session
      tags:
        - 'play-together'
      operationId: 'playTogetherSessionAlive'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlayTogetherSessionAliveRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: Empty response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlayTogetherSessionAliveResponseBody'

  /api/v1/play-together-product-allowed:
    post:
      summary: API to check if product is allowed for play together feature
      tags:
        - 'play-together'
      operationId: 'getProductIsAllowed'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProductIsAllowedRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "True if product is allowed for play together feature"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProductIsAllowedResponseBody'


  /api/v1/creator-image-upload:
    post:
      summary: Upload image
      description: |
        Upload avatar or banner image
      tags:
        - 'upload image'
      operationId: 'uploadImage'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UploadImageRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: A list of creators
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadImageResponseBody'

  /api/v1/creator-follower:
    post:
      summary: Follow and unfollow user
      description: |
        User can follow and unfollow creator
      tags:
        - 'creator-follower'
      operationId: 'creatorFollower'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatorFollowerRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: true or false
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatorFollowerResponseBody'

  /api/v1/creator-video-channel:
    post:
      summary: Get creator stream channels
      description: |
        Get creator stream channels
      tags:
        - 'creator-stream-channels'
      operationId: 'creatorStreamChannels'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCreatorStreamChannelRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: A list of channels
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCreatorStreamChannelResponseBody'

  /api/v1/creator-aws-stage-and-token:
    post:
      summary: Creates new stage and publish token for creator
      description: Creates new stage and publish token for creator
      tags:
        - 'creator-stream-channels'
        - 'aws-stream-channel'
      operationId: 'createAwsStageAndPublishToken'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAwsStageAndPublishTokenRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: Newly created publish token and url
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateAwsStageAndPublishTokenResponseBody'

  /api/v1/aws-subscribe-token:
    post:
      summary: Creates subscribe token to view creator's stream
      description: Creates subscribe token to view creator's stream
      tags:
        - 'creator-stream-channels'
        - 'aws-stream-channel'
      operationId: 'createAwsSubscribeToken'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAwsSubscribeTokenRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: Returns token that allows to watch creator's stream via AWS channel
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateAwsSubscribeTokenResponseBody'

  /api/v1/video-channel:
    post:
      summary: Get stream channels. Limited to creator account
      description: |
        Get stream channels. Limited to creator account
      tags:
        - 'limited-to-creator-stream-channels'
      operationId: 'limitedToCreatorStreamChannels'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLimitedToCreatorStreamChannelRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: A list of channels
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLimitedToCreatorStreamChannelResponseBody'

  /api/v1/stream-create:
    post:
      summary: Create scheduled stream (authenticated creator)
      description: |
        Create a scheduled stream for a creator.
      tags:
        - 'schedule-stream-create'
      operationId: 'scheduleStreamCreate'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ScheduleStreamCreateRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: Scheduled stream created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScheduleStreamResponseBody'

  /api/v1/stream-update:
    post:
      summary: Update scheduled stream (authenticated creator)
      description: |
        Update a scheduled stream for a creator.
      tags:
        - 'schedule-stream-update'
      operationId: 'scheduleStreamUpdate'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ScheduleStreamUpdateRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: Scheduled stream updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScheduleStreamResponseBody'

  /api/v1/stream-delete:
    post:
      summary: Delete scheduled stream (authenticated creator)
      description: |
        Delete a scheduled stream for a creator.
      tags:
        - 'schedule-stream-delete'
      operationId: 'scheduleStreamDelete'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ScheduleStreamDeleteRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: Scheduled stream deleted successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScheduleStreamDeleteResponseBody'

  /api/v1/stream-watch-activity:
    post:
      summary: API endpoint to log stream watching activity
      description: Works as a heartbeat, every call increments watching time of certain user on certain stream
      tags:
        - 'stream-watch-activity'
      operationId: 'streamWatchActivity'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StreamWatchActivityRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: Stream watch activity logged successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StreamWatchActivityResponseBody'

  /api/v1/stream:
    post:
      summary: Get scheduled streams (authenticated)
      description: |
        Get scheduled streams (authenticated)
      tags:
        - 'schedule-streams'
      operationId: 'scheduleStreams'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetScheduledStreamsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: Get scheduled streams.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetScheduledStreamsResponseBody'

  /api/v1/turn-info:
    post:
      tags:
        - 'turn-info'
      operationId: 'getTurnInfo'
      summary: "Get turn info. Available only for followers"
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetTurnInfoRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: "Information about turn services. Available only for followers"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTurnInfoResponseBody'

  /api/v1/stream-live-and-upcoming:
    post:
      summary: Get info on live and upcoming streams (authenticated)
      description: Get scheduled streams (authenticated)
      tags:
        - 'stream-live-and-upcoming'
      operationId: 'getLiveAndUpcomingStreams'
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLiveAndUpcomingStreamsRequestBody'
      responses:
        '200':
          headers:
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
          description: Get info on live and upcoming streams.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLiveAndUpcomingStreamsResponseBody'

components:
  headers:
    X-Message-Id:
      description: UUID of original request
      schema:
        type: string
        format: uuid
    X-Took:
      description: Processing time
      schema:
        type: integer
    X-Status:
      description: |
        Operation status with [possible values](#components/schema/Status)
      schema:
        $ref: '#/components/schemas/Status'
    X-Status-Text:
      description: Operation text
      schema:
        type: string
  parameters:
    X-Message-Id:
      in: header
      name: X-Message-Id
      description: UUID of the original request
      required: true
      schema:
        type: string
        format: uuid
    X-Timestamp:
      in: header
      name: X-Timestamp
      description: Unix request timestamp
      required: true
      schema:
        type: integer
        format: timestamp
    brandName:
      in: query
      name: brandName
      description: Brand name
      required: true
      schema:
        type: string
    platform:
      in: query
      name: platform
      description: Platform
      required: true
      schema:
        type: string
        enum: [ web, android, ios ]
  schemas:
    # ~~~ Common
    Status:
      type: string
      enum: [ err_ok, err_auth, err_system, err_too_many_request, err_bad_request, err_duplicate, err_not_found, err_denied ]
      description: >
        Statuses:
          * err_ok                    - No error.
          * err_auth                  - Unauthorized access to resource.
          * err_system                - Unexpected server error.
          * err_too_many_request      - You were rate limited.
          * err_bad_request           - Incorrect parameter check X-Status-Text for more details.
          * err_duplicate             - Conflict with the current state of the target resource.
          * err_not_found             - The requested resource could not be found.
          * err_denied                - Access denied.

    Creator:
      type: object
      properties:
        code:
          type: string
        handle:
          type: string
        displayName:
          type: string
        description:
          type: string
        followersCount:
          type: integer
        referralsCount:
          type: integer
        bannerImageUrl:
          type: string
        avatarImageUrl:
          type: string
        remoteCode:
          description: "in format 1abcb0a0/*************"
          type: string
        visible:
          type: boolean
          description: false means that it is test account
        active:
          type: boolean
          description: false means that it is not creator account anymore
        streamsInTestMode:
          type: boolean
          description: true means that streams in test mode, no user notifications about live streams
        landingUrl:
          type: string
          description: Landing builder page URL.
        chatEnabled:
          type: boolean
          description: true means that chat is enabled for this creator
        chatGroupCode:
          type: string
          description: Chat group code for this creator, used to join chat room
        levelPriority:
          type: integer
          description: The creator level priority

    CreatorInfo:
      type: object
      allOf:
        - $ref: '#/components/schemas/Creator'
      properties:
        affiliateId:
          type: string
        rate:
          type: number
          format: double
        flatFee:
          type: number
          format: double
        dealId:
          type: integer

    IceServer:
      type: object
      properties:
        urls:
          type: array
          items:
            type: string
        username:
          type: string
        credential:
          type: string

    Follower:
      type: object
      properties:
        code:
          type: string

    StreamChannel:
      type: object
      required:
        - code
        - status
        - type
        - provider
      properties:
        code:
          type: string
        playbackUrl:
          type: string
        playbackKey:
          description: "will be empty for WEBRTC"
          type: string
        status:
          type: string
          enum: [ ONLINE, OFFLINE ]
        type:
          type: string
          enum: [ WEB_RTC, RTMP ]
        provider:
          type: string
          enum: [ CLOUDFLARE, AWS ]
        updatedAt:
          type: integer
          format: int64
          description: Last updated time of the stream (Unix timestamp).

    CreatorStreamChannel:
      type: object
      required:
        - code
        - url
        - status
        - type
        - provider
      properties:
        code:
          type: string
        url:
          type: string
        key:
          description: "AWS IVS participant token here. Will be empty for cloudflare WEBRTC"
          type: string
        playbackUrl:
          description: "will be empty for aws provider"
          type: string
        playbackKey:
          description: "will be empty for WEBRTC"
          type: string
        status:
          type: string
          enum: [ ONLINE, OFFLINE ]
        type:
          type: string
          enum: [ WEB_RTC, RTMP ]
        provider:
          type: string
          enum: [ CLOUDFLARE, AWS ]
        expiresAt:
          type: integer
          format: int64
          description: "AWS IVS participant token expiration time. (Unix timestamp)"
        updatedAt:
          type: integer
          format: int64
          description: Last updated time of the stream (Unix timestamp).

    GetAccountInfoRequestBody:
      type: object
      allOf: [ ]

    GetAccountInfoResponseBody:
      type: object
      properties:
        creatorInfo:
          $ref: '#/components/schemas/CreatorInfo'
        followerInfo:
          $ref: '#/components/schemas/Follower'

    GetTurnInfoRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    GetTurnInfoResponseBody:
      type: object
      properties:
        iceServers:
          $ref: '#/components/schemas/IceServer'


    GetCreatorCurrentEarningsRequestBody:
      type: object
      allOf: [ ]

    GetCreatorCurrentEarningsResponseBody:
      type: object
      properties:
        amount:
          type: string

    GetCreatorRoundStatsRequestBody:
      type: object
      required:
        - creatorCode
      properties:
        creatorCode:
          type: string

    GetCreatorRoundStatsResponseBody:
      type: object
      required:
        - creatorCode
        - maxMultiplier
      properties:
        creatorCode:
          type: string
        maxMultiplier:
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/CreatorRoundStats'

    GetCreatorMultiplierHistoryRequestBody:
      type: object
      required:
        - creatorCode
      properties:
        creatorCode:
          type: string
        participatedRoundsOnly:
          type: boolean
          default: false
        offset:
          type: integer
          default: 0
        limit:
          type: integer
          default: 50

    GetCreatorMultiplierHistoryResponseBody:
      type: object
      required:
        - creatorCode
      properties:
        creatorCode:
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/CreatorMultiplierHistoryRecord'

    GetWagerSettingsRequestBody:
      type: object
      required:
        - creatorRemoteCode
      properties:
        creatorRemoteCode:
          description: "in format 1abcb0a0/*************"
          type: string

    GetWagerSettingsResponseBody:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/WagerSettingsPerCurrency'

    WagerSettingsPerCurrency:
      type: object
      required:
        - currency
        - maxAllowedWager
        - allowedWagers
      properties:
        currency:
          type: string
        maxAllowedWager:
          type: number
          format: double
        allowedWagers:
          type: array
          items:
            type: number
            format: double
        redeemable:
          type: boolean
        conversionRate:
          type: number
          format: double

    CreatorRoundStats:
      type: object
      required:
        - gameRoundCode
        - modifiedAt
        - multiplier
      properties:
        gameRoundCode:
          type: string
        modifiedAt:
          type: integer
          format: int64
        multiplier:
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/WinPerCurrency'

    CreatorMultiplierHistoryRecord:
      type: object
      required:
        - gameRoundCode
        - creatorMultiplier
      properties:
        gameRoundCode:
          type: string
        followerEntry:
          type: number
          format: double
        followerWin:
          type: number
          format: double
        followerCurrency:
          type: string
        creatorMultiplier:
          type: number
          format: double
        createdAt:
          type: integer
          format: int64

    WinPerCurrency:
      type: object
      required:
        - currency
        - totalWin
        - playersCount
      properties:
        currency:
          type: string
        totalWin:
          type: string
        playersCount:
          type: integer

    GetCreatorListRequestBody:
      type: object
      properties:
        handle:
          type: string
        followerCode:
          type: string
        showOnlyVisible:
          type: boolean
        offset:
          type: integer
          default: 0
        limit:
          type: integer
          default: 10

    GetNotFollowedCreatorsRequestBody:
      type: object
      properties:
        showOnlyVisible:
          type: boolean
        offset:
          type: integer
          default: 0
        limit:
          type: integer
          default: 10

    GetCreatorListResponseBody:
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Creator'
        offset:
          type: integer
          default: 0
        limit:
          type: integer
          default: 10

    UpdateCreatorRequestBody:
      type: object
      properties:
        description:
          type: string
          maxLength: 300
        streamsInTestMode:
          type: boolean


    UpdateCreatorResponseBody:
      allOf:
        - $ref: '#/components/schemas/ShortCreatorResponseBody'

    ShortCreatorResponseBody:
      type: object
      properties:
        code:
          type: string
        handle:
          type: string
        displayName:
          type: string
        description:
          type: string
        followersCount:
          type: integer
        referralsCount:
          type: integer
        bannerImageUrl:
          type: string
        avatarImageUrl:
          type: string
        streamsInTestMode:
          type: boolean

    UploadImageRequestBody:
      type: object
      properties:
        fileType:
          type: string
          description: type of image. Can be avatar or banner
        data:
          type: string
          description: avatar image data in base64 format
        fileFormat:
          type: string
          description: file format of image

    UploadImageResponseBody:
      type: object
      properties:
        url:
          type: string
          description: avatar image url

    CreatePlayTogetherSessionRequestBody:
      type: object
      required:
        - creatorCode
        - bet
        - rounds
        - currency
      properties:
        creatorCode:
          type: string
        creatorRemoteCode:
          description: "in format 1abcb0a0/*************"
          type: string
        bet:
          type: string
        rounds:
          type: integer
        currency:
          type: string

    CreatePlayTogetherSessionResponseBody:
      allOf:
        - $ref: '#/components/schemas/PlayTogetherSessionResponseBody'

    PlayTogetherSessionResponseBody:
      type: object
      properties:
        sessionCode:
          type: string
        creatorCode:
          type: string
        bet:
          type: string
        currency:
          type: string
        rounds:
          type: integer
        roundsPlayed:
          type: integer
        totalBet:
          type: string
        lastWin:
          type: string
        totalWin:
          type: string
        status:
          type: string
        statusReason:
          type: string
          description: reason why playTogetherSession was stopped (present only if session was stopped but not finished naturally)
        lastGameSessionId:
          type: string

    GetPlayTogetherSessionsRequestBody:
      type: object
      properties:
        creatorCode:
          type: string
        status:
          type: string
          "enum": [ "OPEN", "CLOSED" ]
        offset:
          type: integer
          default: 0
        limit:
          type: integer
          default: 10

    GetPlayTogetherSessionsResponseBody:
      type: object
      properties:
        items:
          type: array
          items:
            type: object
            allOf:
              - $ref: '#/components/schemas/PlayTogetherSessionResponseBody'

    StopPlayTogetherSessionRequestBody:
      type: object
      required:
        - sessionCode
        - reason
      properties:
        creatorRemoteCode:
          description: "in format 1abcb0a0/*************"
          type: string
        sessionCode:
          type: string
        reason:
          type: string
          "enum": [ "CANCELLED", "CONNECTION_LOST" ]

    PlayTogetherSessionAliveRequestBody:
      type: object
      required:
        - sessionCode
      properties:
        sessionCode:
          type: string
        disconnectProtection:
          type: boolean

    PlayTogetherSessionAliveResponseBody:
      type: object
      allOf: [ ]

    StopPlayTogetherSessionResponseBody:
      allOf:
        - $ref: '#/components/schemas/PlayTogetherSessionResponseBody'

    GetProductIsAllowedRequestBody:
      type: object
      required:
        - productCode
      properties:
        productCode:
          type: string

    GetProductIsAllowedResponseBody:
      type: object
      required:
        - allowed
      properties:
        allowed:
          type: boolean

    CreatorFollowerRequestBody:
      type: object
      required:
        - creatorCode
        - action
      properties:
        creatorCode:
          type: string
          description: creator internal code.
        action:
          type: string
          "enum": [ "FOLLOW", "UNFOLLOW" ]
          description: action to perform

    CreatorFollowerResponseBody:
      type: object
      required:
        - completed
      properties:
        completed:
          type: boolean
        followerCode:
          type: string
          description: follower code

    GetCreatorStreamChannelRequestBody:
      type: object
      required:
        - creatorCode
      properties:
        creatorCode:
          type: string
          description: creator internal code.

    GetCreatorStreamChannelResponseBody:
      type: object
      properties:
        items:
          type: array
          items:
            type: object
            allOf:
              - $ref: '#/components/schemas/StreamChannel'

    GetLimitedToCreatorStreamChannelRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: "object"
      allOf: [ ]

    GetLimitedToCreatorStreamChannelResponseBody:
      type: object
      properties:
        items:
          type: array
          items:
            type: object
            allOf:
              - $ref: '#/components/schemas/CreatorStreamChannel'

    CreateAwsStageAndPublishTokenRequestBody:
      type: object
      allOf: [ ]

    CreateAwsStageAndPublishTokenResponseBody:
      type: object
      allOf:
        - $ref: '#/components/schemas/CreatorStreamChannel'

    CreateAwsSubscribeTokenRequestBody:
      type: object
      required:
        - creatorCode
      properties:
        creatorCode:
          type: string
          description: creator internal code

    CreateAwsSubscribeTokenResponseBody:
      type: object
      properties:
        token:
          type: string
          description: aws participant token to be able to watch creator's stream

    ScheduleStreamCreateRequestBody:
      type: object
      required:
        - startAt
        - endAt
      properties:
        startAt:
          type: integer
          format: int64
          description: Start time of the stream (Unix timestamp).
        endAt:
          type: integer
          format: int64
          description: End time of the stream (Unix timestamp).

    ScheduleStreamUpdateRequestBody:
      type: object
      required:
        - code
        - startAt
        - endAt
      properties:
        code:
          type: string
          format: uuid
          description: Unique identifier of the scheduled stream.
        startAt:
          type: integer
          format: int64
          description: Start time of the stream (Unix timestamp).
        endAt:
          type: integer
          format: int64
          description: End time of the stream (Unix timestamp).

    ScheduleStreamDeleteRequestBody:
      type: object
      required:
        - code
      properties:
        code:
          type: string
          format: uuid
          description: Unique identifier of the scheduled stream.

    ScheduleStreamResponseBody:
      type: object
      required:
        - code
        - startAt
        - endAt
      properties:
        code:
          type: string
          format: uuid
          description: Unique identifier of the scheduled stream.
        startAt:
          type: integer
          format: int64
          description: Start time of the stream (Unix timestamp).
        endAt:
          type: integer
          format: int64
          description: End time of the stream (Unix timestamp).

    ScheduleStreamDeleteResponseBody:
      type: 'object'
      allOf: [ ]

    StreamWatchActivityRequestBody:
      type: object
      required:
        - streamChannelCode
        - timeWatched
        - deviceType
        - osType
      properties:
        streamChannelCode:
          type: string
          format: uuid
          description: Unique identifier of the stream channel that is being watched
        timeWatched:
          type: integer
          description: Time watched in seconds since last request of same type
        deviceType:
          type: string
          "enum": [ "MOBILE", "PC", "NATIVE" ]
          description: Device type of the user watching the stream
        osType:
          type: string
          "enum": [ "OTHER", "IOS", "ANDROID", "WINDOWS", "MACOS", "LINUX" ]
          description: OS type of the user watching the stream

    StreamWatchActivityResponseBody:
      type: object
      allOf: [ ]

    GetScheduledStreamsRequestBody:
      type: object
      required:
        - creatorCode
      properties:
        creatorCode:
          type: string
          format: uuid
          description: Unique identifier of the creator.
        endAfter:
          type: integer
          format: int64
          description: Unix timestamp. Optional. Streams in response should have endDate after this date.
        offset:
          type: integer
          default: 0
        limit:
          type: integer
          default: 5

    GetScheduledStreamsResponseBody:
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/ScheduleStreamResponseBody'

    GetLiveAndUpcomingStreamsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: 'object'
      allOf: [ ]

    GetLiveAndUpcomingStreamsResponseBody:
      type: object
      properties:
        items:
          type: array
          items:
            type: object
            allOf:
              - $ref: '#/components/schemas/StreamingCreatorInfo'

    StreamingCreatorInfo:
      type: object
      properties:
        handle:
          type: string
        displayName:
          type: string
        avatarImageUrl:
          type: string
        startAt:
          type: integer
          format: int64
          description: Start time of the stream (Unix timestamp).
        live:
          type: boolean
          description: is steam currently online

    # --- web socket notifications ---
    PlayTogetherSessionUpdateNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      allOf:
        - $ref: '#/components/schemas/PlayTogetherSessionResponseBody'

    CreatorRoundStatsNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      properties:
        creatorCode:
          type: string
        gameRoundCode:
          type: string
        modifiedAt:
          type: integer
          format: int64
        ignored:
          type: boolean
        multiplier:
          type: string
        maxMultiplier:
          type: string
        items:
          type: array
          items:
            type: object
            properties:
              currency:
                type: string
              totalWin:
                type: string
              playersCount:
                type: integer

    MaxWagerUpdateNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      properties:
        creatorCode:
          type: string
        currency:
          type: string
        maxAllowedWager:
          type: number
          format: double