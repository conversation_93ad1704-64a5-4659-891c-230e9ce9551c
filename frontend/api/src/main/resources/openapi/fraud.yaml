openapi: 3.0.1
info:
  title: Fraud Endpoints API Spec
  description: Endpoints Fraud
  termsOfService: https://patrianna.com/terms/
  contact:
    email: <EMAIL>
  license:
    name: MIT
  version: "1.0.0"
tags:
  - name: Fraud
    description: Fraud API Endpoints


paths:
  /v1/dispatch/SetAccountFraudInfoRequest:
    post:
      tags:
        - Fraud
      summary: 'SetAccountFraudInfoRequest'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      description: Update seon fingerprint for a given accountId. This endpoint allows for the updating of fraud information associated with an account. Now used because this separate endpoint because we can't pass seon fingerprint via Prizeout callbacks.
      operationId: setAccountFraudInfo
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetAccountFraudInfoRequestBody'

      responses:
        "200":
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Account fraud info updated successfully'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetAccountFraudInfoResponseBody'

  /v1/dispatch/CreatePhoneNumberVerificationRequest:
    post:
      tags:
        - Fraud
      summary: 'CreatePhoneNumberVerificationRequest'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      description: Create phone verification of given phone number for user
      operationId: 'createPhoneNumberVerificationRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePhoneNumberVerificationRequestBody'

      responses:
        "200":
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Phone number verification created and started successfully'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePhoneNumberVerificationResponseBody'

  /v1/dispatch/ConfirmPhoneNumberVerificationRequest:
    post:
      tags:
        - Fraud
      summary: 'CreatePhoneNumberVerificationRequest'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      description: Confirm phone verification of given phone number for user
      operationId: 'confirmPhoneNumberVerificationRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfirmPhoneNumberVerificationRequestBody'

      responses:
        "200":
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Phone number verification created and started successfully'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmPhoneNumberVerificationResponseBody'

  /v1/dispatch/GetAccountPhoneNumberDetailsRequest:
    post:
      tags:
        - Fraud
      summary: 'GetAccountPhoneNumberDetailsRequest'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      description: Get account phone number verification details, such as country code
      operationId: 'getAccountPhoneNumberDetailsRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAccountPhoneNumberDetailsRequestBody'

      responses:
        "200":
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Account phone number details'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAccountPhoneNumberDetailsResponseBody'

  /v1/dispatch/OtpModalTriggeredRequest:
    post:
      tags:
        - Fraud
      summary: 'OtpModalTriggeredRequest'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      description: Create otp trigger rule
      operationId: 'createOtpTriggerRuleRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OtpModalTriggeredRequestBody'

      responses:
        "200":
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Otp trigger rule'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpModalTriggeredResponseBody'

  /v1/dispatch/GetCardVerificationsRequest:
    post:
      tags:
        - Fraud
      summary: 'GetCardVerificationsRequest'
      description: 'Get all user card verifications'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getCardVerificationsRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCardVerificationsRequestBody'
      responses:
        '200':
          headers:
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
          description: 'GetCardVerificationsResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCardVerificationsResponseBody'

  /v1/dispatch/BeginPaymentMethodVerificationRequest:
    post:
      tags:
        - Fraud
      summary: 'BeginPaymentMethodVerificationRequest'
      description: 'If order is marked as needs verification, you need to initiate payment method verification using this call.'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'beginPaymentMethodVerification'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BeginPaymentMethodVerificationRequestBody'
      responses:
        '200':
          headers:
            'X-Took-Id':
              $ref: '#/components/headers/X-Took'
            'X-Status-Text':
              $ref: '#/components/headers/X-Status-Text'
            'X-Status':
              $ref: '#/components/headers/X-Status'
            'X-Message-Id':
              $ref: '#/components/headers/X-Message-Id'
          description: 'BeginPaymentMethodVerificationResponse'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BeginPaymentMethodVerificationResponseBody'

  /v1/dispatch/GetLiveChatSettingsRequest:
    post:
      tags:
        - Fraud
      summary: 'GetLiveChatSettingsRequest'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      description: Get live chat settings request
      operationId: 'getLiveChatSettingsRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLiveChatSettingsRequestBody'
      responses:
        "200":
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Get live chat skip'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLiveChatSettingsResponseBody'

  /v1/dispatch/SaveChatEventRequest:
    post:
      tags:
        - Fraud
      summary: 'SaveChatEventRequest'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      description: Chat event
      operationId: 'saveChatEventRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SaveChatEventRequestBody'
      responses:
        "200":
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Get live chat skip'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SaveChatEventResponseBody'



  /v1/dispatch/GetSkipOtpCheckRequest:
    post:
      tags:
        - Fraud
      summary: 'GetSkipOtpCheckRequest'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      description: Get skip otp check request
      operationId: 'getSkipOtpCheckRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSkipOtpCheckRequestBody'
      responses:
        "200":
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Get skip otp check'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSkipOtpCheckResponseBody'

  /v2/dispatch/GetSkipOtpCheckRequest:
    post:
      tags:
        - FraudV2
      summary: 'GetSkipOtpCheckRequest'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      description: Get skip otp check request
      operationId: 'getSkipOtpCheckRequestV2'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSkipOtpCheckRequestBody'
      responses:
        "200":
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Get skip otp check'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSkipOtpCheckResponseBody'

  /v1/dispatch/GetFraudInboxNotificationsRequest:
    post:
      tags:
        - Fraud
      summary: 'GetFraudInboxNotificationsRequest'
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      description: Get fraud inbox notifications
      operationId: 'getFraudInboxNotifications'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetFraudInboxNotificationsRequestBody'
      responses:
        "200":
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Fraud inbox notifications'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFraudInboxNotificationsResponseBody'

  /v1/dispatch/UpdateFraudInboxNotificationRequest:
    post:
      tags:
        - Fraud
      summary: 'UpdateFraudInboxNotificationRequest'
      description: FE should call this method to update status of notification returned in [Get inbox notifications](#operation/getFraudInboxNotifications).
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'updateFraudInboxNotification'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFraudInboxNotificationRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Fraud inbox notifications updating'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateFraudInboxNotificationResponseBody'

  /v1/dispatch/GetKYCInfoRequest:
    post:
      tags:
        - Fraud
      summary: 'GetKYCInfoRequest'
      description: FE should call this method to get KYC info
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getKYCInfoRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetKYCInfoRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Updated KYC info'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetKYCInfoResponseBody'

  /v2/dispatch/GetKYCInfoRequest:
    post:
      tags:
        - FraudV2
      summary: 'GetKYCInfoRequest'
      description: FE should call this method to get KYC info
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getKYCInfoRequestV2'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetKYCInfoRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Updated KYC info'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetKYCInfoResponseBody'

  /v1/dispatch/GetKYCSettingsRequest:
    post:
      tags:
        - Fraud
      summary: 'GetKYCSettingsRequest'
      description: FE should call this method to get KYC settings
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getKYCSettingsRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetKYCSettingsRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'KYC settings'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetKYCSettingsResponseBody'

  /v1/dispatch/GetConfirmedKYCInfoRequest:
    post:
      tags:
        - Fraud
      summary: 'GetConfirmedKYCInfoRequest'
      description: Returns confirmed account KYC info
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getConfirmedKYCInfoRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetConfirmedKYCInfoRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Updated KYC info'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConfirmedKYCInfoResponseBody'

  /v1/dispatch/SetKYCInfoRequest:
    post:
      tags:
        - Fraud
      summary: 'SetKYCInfoRequest'
      description: FE should call this method to update KYC info
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'setKYCInfoRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetKYCInfoRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Updated KYC info'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetKYCInfoResponseBody'

  /v2/dispatch/SetKYCInfoRequest:
    post:
      tags:
        - FraudV2
      summary: 'SetKYCInfoRequest'
      description: FE should call this method to update KYC info
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'setKYCInfoRequestV2'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetKYCInfoRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Updated KYC info'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetKYCInfoResponseBody'

  /v1/dispatch/UpdateKYCInfoRequest:
    post:
      tags:
        - Fraud
      summary: 'UpdateKYCInfoRequest'
      description: FE should call this method to update KYC info
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'updateKYCInfoRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateKYCInfoRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Updated KYC info'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateKYCInfoResponseBody'

  /v2/dispatch/UpdateKYCInfoRequest:
    post:
      tags:
        - FraudV2
      summary: 'UpdateKYCInfoRequest'
      description: FE should call this method to update KYC info
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'updateKYCInfoRequestV2'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateKYCInfoRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Updated KYC info'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateKYCInfoResponseBody'

  /v1/dispatch/GetZendeskAuthTokenRequest:
    post:
      tags:
        - Fraud
      summary: 'GetZendeskAuthTokenRequest'
      description: generates JWT token for Zendesk authorization
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
      operationId: 'getZendeskAuthTokenRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetZendeskAuthTokenRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Signed Zendesk JWT token'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetZendeskAuthTokenResponseBody'

  /v1/dispatch/GetDocUploadInfoRequest:
    post:
      tags:
        - Fraud
      summary: 'GetDocUploadInfoRequest'
      description: returns available document types to upload
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getDocUploadInfoRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetDocUploadInfoRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Signed Zendesk JWT token'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDocUploadInfoResponseBody'

  /v1/dispatch/BeginDocUploadRequest:
    post:
      tags:
        - Fraud
      summary: 'BeginDocUploadRequest'
      description: returns available document types to upload
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'beginDocUploadRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BeginDocUploadRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Signed Zendesk JWT token'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BeginDocUploadResponseBody'

  /v1/dispatch/GetDocUploadHistoryRequest:
    post:
      tags:
        - Fraud
      summary: 'GetDocUploadHistoryRequest'
      description: returns doc upload history
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'getDocUploadHistoryRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetDocUploadHistoryRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Signed Zendesk JWT token'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDocUploadHistoryResponseBody'

  /v1/dispatch/CreateEmailOtpRequest:
    post:
      tags:
        - Fraud
      summary: 'CreateEmailOtpRequest'
      description: create email otp request
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'createEmailOtpRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateEmailOtpRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Signed Zendesk JWT token'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateEmailOtpResponseBody'

  /v1/dispatch/ConfirmEmailOtpRequest:
    post:
      tags:
        - Fraud
      summary: 'ConfirmEmailOtpRequest'
      description: create email otp request
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/X-Chk'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      operationId: 'confirmEmailOtpRequest'
      x-async-enabled: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfirmEmailOtpRequestBody'
      responses:
        '200':
          headers:
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
          description: 'Signed Zendesk JWT token'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmEmailOtpResponseBody'

components:
  headers:
    X-Message-Id:
      description: UUID of original request
      schema:
        type: string
        format: uuid
    X-Took:
      description: Processing time
      schema:
        type: integer
        example: 42
    X-Status:
      description: Operation status
      schema:
        type: string
        enum: [ err_ok, err_auth, err_system, err_too_many_request, err_bad_request, err_payment ]
        example: [ err_ok, err_auth, err_system, err_too_many_request, err_bad_request, err_payment ]
    X-Status-Text:
      description: Operation text
      schema:
        type: string

  parameters:
    X-Message-Id:
      name: X-Message-Id
      in: header
      description: Uniq Id of request in UUID format
      required: true
      schema:
        $ref: '#/components/schemas/xMessageId'
    X-Timestamp:
      name: X-Timestamp
      in: header
      description: Unix request timestamp
      required: true
      schema:
        type: integer
        format: timestamp
        example: 1694794233734
    X-Chk:
      name: X-Chk
      description: Enable additional request validation (anything or empty)
      in: header
      schema:
        type: string
    brandName:
      name: brandName
      description: Brand name
      required: true
      in: query
      schema:
        type: string
    platform:
      name: platform
      description: Platform
      required: true
      in: query
      schema:
        $ref: '#/components/schemas/qPlatform'

  schemas:
    xMessageId:
      type: string
      description: UUID of original request
      format: uuid
      example: d6aa3881-84de-4b4d-9a1f-aa1512472dd4
    qPlatform:
      type: string
      enum:
        - web
        - android
        - ios

    OtpSource:
      type: string
      enum: [ inbox_notification, my_profile ]

    SetAccountFraudInfoRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: "object"
      properties:
        seonSession:
          type: string
          description: 'New Seon Fingerprint value to be updated'
      required:
        - seonSession
    SetAccountFraudInfoResponseBody:
      type: object
      properties:
        applied:
          type: boolean

    CreatePhoneNumberVerificationRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      properties:
        channel:
          type: string
          description: 'Channel for phone number confirmation'
        phoneNumber:
          type: string
          description: 'Phone number for verification'
        session:
          type: string
        source:
          description: 'Source of OTP verification'
          $ref: '#/components/schemas/OtpSource'
      required:
        - channel
        - phoneNumber

    CreatePhoneNumberVerificationResponseBody:
      type: object
      properties:
        applied:
          type: boolean
        otpLimitResetTimestamp:
          type: 'integer'
          format: 'int64'


    ConfirmPhoneNumberVerificationRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      properties:
        otp:
          type: string
          description: 'OTP verification code'
        phoneNumber:
          type: string
          description: 'Phone number for verification'
        session:
          type: string
      required:
        - channel
        - phoneNumber

    ConfirmPhoneNumberVerificationResponseBody:
      type: object
      properties:
        verified:
          type: boolean

    GetAccountPhoneNumberDetailsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    GetAccountPhoneNumberDetailsResponseBody:
      type: object
      properties:
        countryCode:
          type: integer
      required:
        - countryCode

    OtpModalTriggeredRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      properties:
        setUpNotifications:
          type: boolean
        source:
          description: 'Source of OTP verification'
          $ref: '#/components/schemas/OtpSource'
        accountClosure:
          type: boolean

    OtpModalTriggeredResponseBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    CreateEmailOtpRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    CreateEmailOtpResponseBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    ConfirmEmailOtpRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      properties:
        otp:
          type: string

    ConfirmEmailOtpResponseBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      properties:
        success:
          type: boolean

    GetCardVerificationsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    GetCardVerificationsResponseBody:
      type: object
      properties:
        verificationRequired:
          type: boolean
          description: 'Indicates whether card verification is required or not'
        cards:
          type: array
          items:
            type: object
            properties:
              fingerprint:
                type: string
                description: 'Unique card number identifier, same card will have same fingerprint after any tokenization.'
              cardBin:
                type: string
                description: 'First six digits of card number'
              lastFour:
                type: string
                description: 'Last four digits of card number'
              status:
                enum: [ unverified, require_verification, in_progress, verified, attempt_limit_reached, verification_failed, unverifiable ]
                description: 'Card verification status'

    BeginPaymentMethodVerificationRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      properties:
        fingerprint:
          type: string

    BeginPaymentMethodVerificationResponseBody:
      type: object
      properties:
        redirectUrl:
          type: string
          description: 'URL to redirect'

    GetSkipOtpCheckRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    GetSkipOtpCheckResponseBody:
      type: object
      properties:
        skipOtp:
          type: boolean
        mandatory:
          type: boolean
        trigger:
          description: OTP trigger that was fired
          enum: [ signed_up_trigger, bonus_trigger, utm_source_trigger ]
      required:
        - skipOtp
        - mandatory

    GetLiveChatSettingsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    GetLiveChatSettingsResponseBody:
      type: object
      properties:
        showLiveChat:
          type: boolean
        vip:
          type: boolean
        departments:
          $ref: '#/components/schemas/ChatDepartment'
        tag:
          type: string
        tags:
          type: array
          items:
            type: string
        external_id:
          type: string
        purchaseFlowEnabled:
          type: boolean
        email:
          type: string
      required:
        - showLiveChat
        - purchaseFlowEnabled

    ChatDepartment:
      type: object
      properties:
        enabled:
          type: array
          items:
            type: string
          description: List of enabled departments.
        select:
          type: string
          description: Selected department.
      required:
        - enabled
        - select

    SaveChatEventRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      properties:
        event:
          description: Chat event type
          enum: [ chat_shown, chat_opened ]
        flow:
          description: Chat flow type
          enum: [ purchase_flow, redeem_flow ]
      required:
        - event
        - flow

    SaveChatEventResponseBody:
      type: object
      allOf: [ ]

    GetFraudInboxNotificationsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    GetFraudInboxNotificationsResponseBody:
      type: object
      properties:
        notifications:
          type: array
          items:
            $ref: '#/components/schemas/InboxNotification'
      required:
        - notifications

    UpdateFraudInboxNotificationRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      required:
        - id
        - status
      properties:
        notificationToken:
          description: 'Token of notification returned in [Get inbox notifications](#operation/getInboxNotifications)'
          type: string
        status:
          description: 'New status'
          type: string
          enum:
            - 'read'
            - 'unread'
            - 'removed'

    UpdateFraudInboxNotificationResponseBody:
      type: object
      description: 'Returns updated notification'
      required:
        - notification
      properties:
        notification:
          $ref: '#/components/schemas/InboxNotification'

    InboxNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      properties:
        id:
          type: string
        token:
          type: string
        title:
          type: string
        message:
          type: string
        categoryCode:
          type: string
        status:
          type: 'string'
          enum:
            - 'read'
            - 'unread'
            - 'removed'
        createdAt:
          type: 'integer'
          format: 'int64'
      required:
        - message

    GetKYCInfoRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      properties:
        provider:
          type: string
        phase:
          type: string

    GetKYCInfoResponseBody:
      type: object
      description: 'Returns KYC info'
      required:
        - kyc
        - status
        - allowNextUpload
      properties:
        kyc:
          type: string
        reason:
          type: string
        status:
          type: string
        allowNextUpload:
          type: string
          format: date-time

    GetKYCSettingsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    GetKYCSettingsResponseBody:
      type: object
      properties:
        kycMinAge:
          type: integer
        softKYCStates:
          type: array
          items:
            $ref: '#/components/schemas/KYCStateSettings'

    GetConfirmedKYCInfoRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    GetConfirmedKYCInfoResponseBody:
      type: object
      description: 'Returns confirmed KYC info'
      properties:
        kyc:
          description: Indicates whether the user is required to complete KYC
          type: boolean
        kycStatus:
          description: Account KYC status
          enum: [ initial, in_review, confirmed, id_confirmed, doc_review, doc_declined, session_expired, declined, blocked ]
        kycInfo:
          $ref: '#/components/schemas/KYCInfo'

    KYCInfo:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        country:
          type: string
        state:
          type: string
        city:
          type: string
        zip:
          type: string
        address:
          type: string

    KYCStateSettings:
      type: object
      properties:
        country:
          type: string
        blockedStates:
          type: array
          items:
            type: string
        allowedStates:
          type: array
          items:
            type: string

    SetKYCInfoRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      required:
        - phase
      properties:
        phase:
          description: 'KYC phase'
          type: string

    SetKYCInfoResponseBody:
      type: object
      description: 'Returns updated KYC info'
      required:
        - nextKYC
        - transactionReference
      properties:
        nextKYC:
          type: string
          format: date-time
        transactionReference:
          type: string
        redirectUrl:
          type: string
        scanReference:
          type: string
        status:
          type: string
          enum:
            - success
            - error
        errorReason:
          type: string

    UpdateKYCInfoRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      properties:
        provider:
          description: 'KYC provider'
          type: string
        transactionReference:
          type: string

    UpdateKYCInfoResponseBody:
      type: object
      allOf: [ ]

    SkipOtpChangeNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      required:
        - skipOtp
        - mandatory
        - trigger
      properties:
        skipOtp:
          type: 'boolean'
        mandatory:
          type: 'boolean'
        trigger:
          type: 'string'

    DocUploadCompletedNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      properties:
        status:
          enum: [ pending_review, failed, session_expired ]
          description: Status of the document upload operation

    KYCBlockNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      properties:
        reason:
          type: string

    KYCConfirmNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      properties:
        reason:
          type: string

    KYCDeclineNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      properties:
        reason:
          type: string

    KYCDocBlockNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      properties:
        reason:
          type: string

    KYCDocConfirmNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      allOf: [ ]

    KYCDocDeclineNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      properties:
        reason:
          type: string

    KYCDocErrorUploadNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      allOf: [ ]

    KYCDocSuccessUploadNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      allOf: [ ]

    KYCInReviewNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      allOf: [ ]

    GetZendeskAuthTokenRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    GetZendeskAuthTokenResponseBody:
      type: object
      description: 'Signed Zendesk JWT token'
      required:
        - jwt
      properties:
        jwt:
          type: string

    GetDocUploadInfoRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    GetDocUploadInfoResponseBody:
      type: object
      description: 'Available document types to upload'
      properties:
        types:
          type: array
          items:
            $ref: '#/components/schemas/SupportedDocType'
        uploadedDocsCount:
          type: integer
        maxUploadDocsLimit:
          type: integer
      required:
        - types
        - uploadedDocsCount
        - maxUploadDocsLimit

    SupportedDocType:
      type: object
      properties:
        type:
          enum: [ BS, TR, PA, SA, PSL, OTH ]
          description: Type of document
        title:
          type: string
          description: Name of document
      required:
        - type
        - title
    BeginDocUploadRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      properties:
        docType:
          enum: [ BS, TR, PA, SA, PSL, OTH ]
          description: Document type
        customDocType:
          type: string
      required:
        - docType

    BeginDocUploadResponseBody:
      type: object
      description: 'Returns URL for document upload'
      properties:
        uploadUrl:
          type: string
      required:
        - uploadUrl

    GetDocUploadHistoryRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]

    GetDocUploadHistoryResponseBody:
      type: object
      description: 'Doc upload history'
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/DocUploadHistoryItem'

    DocUploadHistoryItem:
      type: object
      description: 'Doc upload history item'
      properties:
        docType:
          enum: [ BS, TR, PA, SA, PSL, OTH ]
          description: Document type
        docTypeTitle:
          type: string
        customDocType:
          type: string
        uploadStatus:
          enum: [FAILED, APPROVED, REJECTED, PENDING_REVIEW]
          description: Document upload status
        uploadedAt:
          type: string
          format: date-time
      required:
        - types
        - uploadedDocsCount
        - maxUploadDocsLimit