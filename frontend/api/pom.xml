<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>tournament-frontend-parent</artifactId>
        <groupId>com.patrianna.uam</groupId>
        <version>25.08.1-SNAPSHOT</version>
    </parent>
    <artifactId>tournament-frontend-api</artifactId>
    <name>tournament ::: ${project.artifactId}</name>
    <dependencies>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>server-openapi-common</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-transport</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.ws.rs</groupId>
            <artifactId>jakarta.ws.rs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-core-spi</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-jaxrs2-servlet-initializer-v2-jakarta</artifactId>
        </dependency>
        <!-- -->
        <!-- provided -->
        <!-- -->
        <dependency>
            <artifactId>lombok</artifactId>
            <groupId>org.projectlombok</groupId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.swagger.parser.v3</groupId>
            <artifactId>swagger-parser</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- -->
        <!-- test dependencies -->
        <!-- -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-validation-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- -->
        <!--TODO REMOVE AFTER API MIGRATION-->
        <!-- -->
        <dependency>
            <groupId>com.github.victools</groupId>
            <artifactId>jsonschema-generator</artifactId>
            <version>4.30.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>generate-tournament-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <apiPackage>fe.api.tournament</apiPackage>
                            <modelPackage>fe.api.tournament.model</modelPackage>
                            <inputSpec>${project.basedir}/src/main/resources/openapi/tournament.yaml</inputSpec>
                            <generatorName>jaxrs-spec</generatorName>
                            <templateDirectory>${project.basedir}/src/main/resources/openapi/templates
                            </templateDirectory>
                            <apiNameSuffix>TournamentApi</apiNameSuffix>
                            <configOptions>
                                <additionalModelTypeAnnotations>
                                    @lombok.experimental.SuperBuilder
                                    @lombok.NoArgsConstructor
                                    @com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS)
                                </additionalModelTypeAnnotations>
                                <useJakartaEe>true</useJakartaEe>
                                <dateLibrary>java8</dateLibrary>
                                <interfaceOnly>true</interfaceOnly>
                                <supportAsync>true</supportAsync>
                                <sourceFolder>src/gen/java/main</sourceFolder>
                                <useSwaggerAnnotations>false</useSwaggerAnnotations>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Date=Date</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>Date=java.util.Date</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>

                    <execution>
                        <id>generate-tournament-api-doc</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/openapi/tournament.yaml</inputSpec>
                            <generatorName>html</generatorName>
                            <output>${project.build.directory}/generated-sources/openapi/doc/tournament</output>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/generated-sources/openapi/doc
                            </outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${basedir}/src/main/resources/openapi</directory>
                                    <includes>
                                        <include>tournament-openapi-ui-unpkg.html</include>
                                    </includes>
                                    <filtering>false</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>parse-version</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>parse-version</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>add-resource</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${project.build.directory}/generated-sources</directory>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>