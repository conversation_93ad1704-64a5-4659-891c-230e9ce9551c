package fe.endpoints;

import org.jboss.resteasy.spi.HttpRequest;

import com.turbospaces.cfg.ApplicationProperties;

import api.v1.ApiFactory;
import fe.AbstractOpenApiEndpoint;
import fe.MessageDispatcher;
import fe.services.CookieStorage;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.channel.ChannelHandlerContext;
import jakarta.ws.rs.container.AsyncResponse;
import org.springframework.cloud.DynamicCloud;

public class DefaultPickEmApiEndpoint extends AbstractOpenApiEndpoint implements PickEmApiEndpoint {
    public DefaultPickEmApiEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            MessageDispatcher dispatcher,
            CookieStorage cookieStorage,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, dispatcher, cookieStorage, apiFactory);
    }
    @Override
    public void getPickEmMatches(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }
    @Override
    public void getPickEmMatchesByCodes(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }
    @Override
    public void getPickEmPayoutInfo(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }
    @Override
    public void getPickEmLobby(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }
    @Override
    public void getPickEmOptions(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }
    @Override
    public void getUserPickEmEntries(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }
    @Override
    public void submitPickEmEntry(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }
    @Override
    public void getPickEmEntryDetails(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }
    @Override
    public void getPickEmPacks(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }
    @Override
    public void getUserDiscountedLines(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }
    @Override
    public void getPickEmPlayerStats(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }

    @Override
    public void getPickEmTrendingPlayers(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }

    @Override
    public void makePlayerFavourite(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }

    @Override
    public void getUserFavouritePlayers(AsyncResponse async, ChannelHandlerContext ctx, HttpRequest httpReq) throws Throwable {
        send(async, ctx, httpReq);
    }
}
