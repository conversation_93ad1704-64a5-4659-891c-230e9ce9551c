cloud {
  application {
    space_name = gcp-dev
  }
}

spanner.instance.name=local
spanner.database.name=defaultdb

flyway.migration.run-on-start=true
flyway.migration.use-autoconfig-emulator=true

service.kafka.uri = "kafka://127.0.0.1:9092"
service.redis.uri = "redis://localhost:6379"
service.memcached.uri = "memcached://localhost:11211"
service.auth0.uri = "https://eRtrvIAXzU0RUTbOuGlpFHorKg3H4Lqk:<EMAIL>?signingKey=ofs9S5Dor4Va3c72elUIFlgHaLYpxtzhX8TDWMQpCHhjHLv2qUhO7XmQQufgWbap"

# connection.public.domains="localhost"

# for banner and avatar upload (5MB * 10)
http.request.max-size=52428800
service.creator-cloudflare.uri="https://a873223245f2b5e5e5ba9b53eaec8119:<EMAIL>/client"
service.creator-cloudflare-turn.uri="https://a111:<EMAIL>"

ip-rate-limiter.AdminEndpoint.enabled=true
ip-rate-limiter.AdminEndpoint.count=5
ip-rate-limiter.AdminEndpoint.period=PT1M

app.shutdown-hook.enabled=false
app.use.self-signed-certificate=true

# open-API
documentation.auth.check.enabled=false

#
# kafka
#
kafka.system-exit.on-queue-full=false
kafka.nack.on-queue-full=false

#
# spring
#
spring.output.ansi.enabled=detect
use-spreedly.users-created-after="2019-12-03T10:15:30Z"

#app.logging.reset-to=error

service.onl-google-auth-verifier.uri = "https://152449400410-m8ikdfuml0sfjn3hfjqqt8v3jtca3rvi.apps.googleusercontent.com:<EMAIL>"
service.pulsz-google-auth-verifier.uri = "https://86536867990-qbuc7gnegnlejujvkrqmrligvhc35vkt.apps.googleusercontent.com:<EMAIL>"
service.pulszbingo-google-auth-verifier.uri = "https://981108050857-rk303bc8ev5ssds4nri4fbu4lghs2eso.apps.googleusercontent.com:<EMAIL>"
service.onl-google-pubsub-verifier.uri = "https://pubsub-lotto-app-dev:<EMAIL>:443"
service.pulsz-google-pubsub-verifier.uri = "https://pubsub-pulsz-app-dev:<EMAIL>:443"
service.pulsz-apple.uri = "https://SARS2D6H74:<EMAIL>?ios=com.pulsz.app&web=com.pulsz.signin"
***********************************************************************************************************************************************************************************************************************************************************************************************
service.recaptcha.uri = "https://bluedream:<EMAIL>"