package fraud.gateway;

import java.util.regex.Pattern;

import com.google.common.collect.ImmutableSet;

import common.UPSsPatternCollection;
import fraud.FraudProto;

public class FraudGatewayScopedDynamicUPSs extends ImmutableSet.Builder<Pattern> implements UPSsPatternCollection {

    public FraudGatewayScopedDynamicUPSs() {
        addBrandScopedUps(FraudProto.UPS_JUMIO);
        addBrandScopedUps(FraudProto.UPS_JUMIO_DOCS);
        addBrandScopedUps(FraudProto.UPS_VERIFF_IDV);
        addBrandScopedUps(FraudProto.UPS_VERIFF_DOCS);
        addBrandScopedUps(FraudProto.UPS_JUMIO_KYX);
        addBrandScopedUps(FraudProto.UPS_SEON);
        addBrandScopedUps(FraudProto.UPS_VERIFI);
    }
}
