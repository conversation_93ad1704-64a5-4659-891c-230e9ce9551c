package fraud.gateway.di;

import api.v1.ApiFactory;
import com.turbospaces.cfg.ApplicationProperties;
import fraud.api.FraudServiceApi;
import fraud.gateway.FraudGatewayServerProperties;
import fraud.gateway.service.DefaultVeriffConfigService;
import fraud.kyc.jumio.DefaultKycApiEndpoint;
import fraud.kyc.jumio.KYCApiEndpoint;
import fraud.kyc.veriff.DataAggregationService;
import fraud.kyc.veriff.DefaultDataAggregationService;
import fraud.kyc.veriff.DefaultVeriffEndpoint;
import fraud.kyc.veriff.VeriffConfigService;
import fraud.kyc.veriff.VeriffEndpoint;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import uam.api.UamServiceApi;

@Configuration
public class FraudGatewayKYCDiModule {
    @Bean
    public KYCApiEndpoint kycApiEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            FraudServiceApi fraudServiceApi,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        return new DefaultKycApiEndpoint(props, cloud, fraudServiceApi, uamServiceApi, apiFactory);
    }
    @Bean
    public VeriffEndpoint veriffWebhookEndpoint(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            ApiFactory apiFactory,
            DynamicCloud cloud,
            FraudServiceApi fraudServiceApi,
            DataAggregationService dataAggregationService,
            VeriffConfigService veriffConfigService) {
        return new DefaultVeriffEndpoint(
                props,
                meterRegistry,
                apiFactory,
                cloud,
                fraudServiceApi,
                veriffConfigService,
                dataAggregationService);
    }
    @Bean
    public VeriffConfigService veriffConfigService(FraudGatewayServerProperties props) {
        return new DefaultVeriffConfigService(props);
    }
    @Bean
    public DataAggregationService accountAggregationService(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            FraudServiceApi fraudServiceApi) {
        return new DefaultDataAggregationService(props, meterRegistry, uamServiceApi, fraudServiceApi);
    }
}
