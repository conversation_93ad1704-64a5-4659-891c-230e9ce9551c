package fraud.gateway.di;

import fraud.api.temporal.FraudWorkflowPostTemplate;
import fraud.gateway.FraudGatewayServerProperties;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.api.MutableNonPersistentReplyTopic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.rpc.QueuePostTemplate;

import api.v1.ApiFactory;
import fraud.api.DefaultFraudServiceApi;
import fraud.api.FraudServiceApi;
import fraud.gateway.FraudGatewayTopics;
import payment.api.DefaultPaymentServiceApi;
import payment.api.PaymentServiceApi;
import uam.api.DefaultUamServiceApi;
import uam.api.UamServiceApi;

@Configuration
public class FraudGatewayServiceApiDiModule {
    @Bean
    public FraudServiceApi fraudServiceApi(
            FraudGatewayServerProperties props,
            DynamicCloud cloud,
            QueuePostTemplate<?> postTemplate,
            ApiFactory apiFactory,
            FraudWorkflowPostTemplate fraudWorkflowPostTemplate
    ) {
        return new DefaultFraudServiceApi(
                props,
                postTemplate,
                apiFactory,
                new MutableNonPersistentReplyTopic(props, cloud, FraudGatewayTopics.RESP),
                props.FRAUD_TEMPORAL_ENABLED,
                fraudWorkflowPostTemplate
        );
    }
    @Bean
    public UamServiceApi uamServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultUamServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, FraudGatewayTopics.RESP));
    }

    @Bean
    public PaymentServiceApi paymentServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultPaymentServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, FraudGatewayTopics.RESP));
    }
}
