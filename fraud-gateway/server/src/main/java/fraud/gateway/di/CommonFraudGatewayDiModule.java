package fraud.gateway.di;

import java.util.List;

import com.patrianna.uam.service.DefaultVerifiApiEndpoint;
import com.patrianna.uam.service.VerifiApiEndpoint;
import fraud.api.FraudServiceApi;
import fraud.gateway.FraudGatewayServerProperties;
import kyx.jumio.DefaultJumioKYXApiEndpoint;
import kyx.jumio.JumioKYXApiEndpoint;
import kyx.jumio.client.JumioKYXClient;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.CloseableHttpClientFactoryBean;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.resteasy.AdminEndpoint;
import com.turbospaces.resteasy.DefaultAdminEndpoint;
import com.turbospaces.resteasy.ReadyIndicator;
import com.turbospaces.rpc.QueuePostTemplate;

import api.DefaultApiFactory;
import api.v1.ApiFactory;
import fraud.gateway.endpoints.seon.DefaultSeonEventApiEndpoint;
import fraud.gateway.endpoints.seon.SeonEventEndpoint;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import payment.api.PaymentServiceApi;

@Configuration
public class CommonFraudGatewayDiModule {
    @Bean
    public CommonObjectMapper objectMapper() {
        return new CommonObjectMapper();
    }
    @Bean
    public CloseableHttpClientFactoryBean closeableHttpClient(ApplicationProperties props, MeterRegistry meterRegistry) {
        return new CloseableHttpClientFactoryBean(props, meterRegistry);
    }
    @Bean
    public ApiFactory apiFactory(ApplicationProperties props, CommonObjectMapper mapper) {
        return new DefaultApiFactory(props, mapper);
    }
    @Bean
    public SeonEventEndpoint seonEventEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            ApiFactory apiFactory,
            QueuePostTemplate<?> postTemplate,
            CommonObjectMapper mapper) throws Exception {
        return new DefaultSeonEventApiEndpoint(props, cloud, apiFactory, postTemplate, mapper);
    }
    @Bean
    public AdminEndpoint adminApiEndpoint(
            ApplicationProperties props,
            CompositeMeterRegistry meterRegistry,
            HealthCheckRegistry healthCheckRegistry,
            List<ReadyIndicator> probes) {
        return new DefaultAdminEndpoint(props, meterRegistry, healthCheckRegistry, probes);
    }
    @Bean
    public JumioKYXApiEndpoint jumioKYXApiEndpoint(FraudGatewayServerProperties props, MeterRegistry meterRegistry, JumioKYXClient client, FraudServiceApi fraudServiceApi) {
        var allowedIps = props.JUMIO_KYX_IP_WHITELIST.get();
        return new DefaultJumioKYXApiEndpoint(props, meterRegistry, client, fraudServiceApi, allowedIps);
    }

    @Bean
    public VerifiApiEndpoint verifiApiEndpoint(DynamicCloud cloud, PaymentServiceApi paymentServiceApi, FraudServiceApi fraudServiceApi, CommonObjectMapper mapper, FraudGatewayServerProperties props, ApiFactory apiFactory) {
        return new DefaultVerifiApiEndpoint(cloud, paymentServiceApi, fraudServiceApi, mapper, props, apiFactory);
    }
}
