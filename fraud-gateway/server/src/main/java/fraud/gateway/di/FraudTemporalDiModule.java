package fraud.gateway.di;

import java.time.Duration;

import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.temporal.CustomDataConverter;

import api.v1.ApiFactory;
import fraud.api.temporal.FraudTemporalClientFactoryBean;
import fraud.api.temporal.FraudWorkflowPostTemplate;
import fraud.gateway.FraudGatewayServerProperties;
import io.micrometer.core.instrument.MeterRegistry;


@Configuration
public class FraudTemporalDiModule {

    @Bean
    public CustomDataConverter customDataConverter(CommonObjectMapper objectMapper) {
        return new CustomDataConverter(objectMapper);
    }

    @Bean
    public FraudTemporalClientFactoryBean fraudTemporalClientFactory(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            CustomDataConverter customDataConverter) {
        return new FraudTemporalClientFactoryBean(props, cloud, meterRegistry, customDataConverter);
    }

    @Bean
    public FraudWorkflowPostTemplate fraudWorkflowPostTemplate(
            FraudGatewayServerProperties props,
            ApiFactory apiFactory,
            FraudTemporalClientFactoryBean clientFactory) throws Exception {
        return new FraudWorkflowPostTemplate(
                props,
                apiFactory,
                props.REQUEST_REPLY_TIMEOUT.map(Duration::ofSeconds),
                clientFactory
        );
    }
}
