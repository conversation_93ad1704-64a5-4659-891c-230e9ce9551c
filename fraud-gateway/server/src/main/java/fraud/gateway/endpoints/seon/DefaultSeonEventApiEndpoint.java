package fraud.gateway.endpoints.seon;

import java.util.Objects;
import java.util.stream.Collectors;

import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;
import fraud.FraudProto;
import fraud.gateway.util.SignatureUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Component;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.rpc.QueuePostTemplate;

import api.v1.ApiFactory;
import fraud.api.v1.SeonField;
import fraud.api.v1.SeonListChangedEvent;
import fraud.gateway.api.b2b.SeonEvent;
import gateway.AbstractApiEndpoint;
import io.netty.channel.ChannelHandlerContext;
import io.vavr.CheckedFunction0;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Component
public class DefaultSeonEventApiEndpoint extends AbstractApiEndpoint implements SeonEventEndpoint {
    private final CommonObjectMapper objectMapper;
    private final QueuePostTemplate<?> postTemplate;

    @Inject
    public DefaultSeonEventApiEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            ApiFactory apiFactory,
            QueuePostTemplate<?> postTemplate,
            CommonObjectMapper mapper) {
        super(props, apiFactory, cloud);
        this.postTemplate = Objects.requireNonNull(postTemplate);
        this.objectMapper = Objects.requireNonNull(mapper);
    }
    @Override
    public void seonEvent(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, String brandName, String event) throws Throwable {
        validateSignature(async, headers, brandName, event);
        SeonEvent seonEvent = DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedFunction0<>() {
            @Override
            public SeonEvent apply() throws Throwable {
                return objectMapper.readValue(event, SeonEvent.class);
            }
        });
        var fields = seonEvent.getValues().stream()
                .filter(i -> StringUtils.isNoneEmpty(i.getDataField(), i.getValue(), i.getState()))
                .map(item -> SeonField.newBuilder()
                        .setName(item.getDataField())
                        .setValue(item.getValue())
                        .setState(item.getState())
                        .build())
                .collect(Collectors.toSet());
        var blacklistEvent = SeonListChangedEvent.newBuilder()
                .setBrand(brandName)
                .addAllFields(fields);
        postTemplate.sendEvent(blacklistEvent.build());
        async.resume(Response.ok().build());
    }

    private void validateSignature(AsyncResponse async, HttpHeaders headers, String brandName, String event) {
        PlainServiceInfo si = UPSs.findScopedRequiredServiceInfoByName(brandName, cloud, FraudProto.UPS_SEON);
        if (!SignatureUtil.isSignatureValid(event, headers, si.getPassword())) {
            async.resume(Response.status(Response.Status.UNAUTHORIZED)
                    .entity("Invalid signature").build());
            throw new IllegalArgumentException("Invalid signature for SEON event: " + event);
        }
    }
}
