pipeline {
  agent {
    node {
      label 'patrianna-dev'
    }
  }

  triggers {
    pollSCM('H/2 * * * *')
  }

  environment {
    REGISTRY_ADDRESS = 'https://europe-docker.pkg.dev'
    DOCKER_BUILDKIT = 1
    SLACK_CHANNEL = "#backend_party"
  }

  options {
    disableConcurrentBuilds()
    timeout(time: 1, unit: 'HOURS')
  }

  tools {
    maven 'mvn3'
    jdk 'jdk23'
  }

  stages {
    stage('Build') {
      when {
        not {
          changelog '.*\\[maven-release-plugin\\].*'
        }
      }
      steps {
        withCredentials([file(credentialsId: 'patrianna-docker-registry-auth', variable: 'AUTH')]) {
          withCredentials([usernamePassword(credentialsId: 'nexus-oss-registry-auth', usernameVariable: 'NEXUS_OSS_USER', passwordVariable: 'NEXUS_OSS_CREDENTIALS')]) {
            sh "cat $AUTH | docker login -u _json_key --password-stdin ${env.REGISTRY_ADDRESS}"
            sh "mvn -s settings.xml clean package -DfailIfNoTests=false -U -T 1C"
            sh "mvn -s settings.xml deploy -DskipTests -Pdocker -T 1C"
            sh "mvn org.codehaus.mojo:build-helper-maven-plugin:remove-project-artifact -Dbuildhelper.removeAll=false"      
          }
        }
      }
    }

    stage('Reports') {
      steps {
        allure([
          includeProperties: false,
          jdk: '',
          properties: [],
          reportBuildPolicy: 'ALWAYS',
          results: [
            [path: 'server/target/allure-results']
          ]
        ])
      }
    }
  }

  post {
    always {
      deleteDir()
    }
  }
}