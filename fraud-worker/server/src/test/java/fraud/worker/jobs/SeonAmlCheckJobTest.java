package fraud.worker.jobs;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;

import fraud.model.kyc.KYCVerificationRequest;
import fraud.worker.FraudWorkerMasterEbeanJpaManager;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.RawServiceInfo;

import api.v1.KYCStatusSpec;
import api.v1.PlatformSpec;
import bi.seon.SeonApi;
import bi.seon.SeonClient;
import bi.seon.SeonJaxRsApi;
import bi.seon.data.base.SeonAmlCheckRequest;
import bi.seon.data.base.SeonAmlCheckResponse;
import fraud.model.fraud.AmlCheck;
import fraud.worker.FraudWorkerProto;
import fraud.worker.FraudWorkerServerProperties;
import fraud.worker.FraudWorkerSpringBootTestContextBootstrapper;
import fraud.worker.di.CommonFraudWorkerDiModule;
import fraud.worker.di.FraudWorkerQuartzDiModule;
import fraud.worker.di.MockFraudWorkerDatabaseDiModule;
import fraud.worker.di.SeonAmlCheckDiModule;
import fraud.worker.framework.listeners.FraudWorkerBrandDataGenerator;
import fraud.worker.framework.listeners.FraudWorkerOfferDataGenerator;
import fraud.worker.framework.listeners.FraudWorkerPaymentProviderDataGenerator;
import fraud.worker.framework.listeners.FraudWorkerWithdrawProviderDataGenerator;
import fraud.worker.framework.util.FraudWorkerDataGenerationService;
import fraud.worker.model.immutable.payments.PaymentImmutableAccountMetaInfo;
import fraud.worker.model.immutable.payments.PaymentImmutableBrand;
import fraud.worker.model.immutable.payments.PaymentImmutablePaymentOrder;
import io.ebean.Transaction;
import payment.api.PaymentServiceApi;
import payment.model.ProviderIntegrationTypeSpec;
import payment.model.RedeemStatusSpec;
import payment.type.PurchaseProviderSpec;
import uam.api.v1.PaymentProvider;
import uam.model.Account;
import uam.model.Citizen;

@ExtendWith(SpringExtension.class)
@BootstrapWith(FraudWorkerSpringBootTestContextBootstrapper.class)
@ContextConfiguration(classes = {
        MockFraudWorkerDatabaseDiModule.class,
        FraudWorkerQuartzDiModule.class,
        SeonAmlCheckDiModule.class,
        CommonFraudWorkerDiModule.class
})
@TestExecutionListeners(
        mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS,
        listeners = { FraudWorkerBrandDataGenerator.class, FraudWorkerOfferDataGenerator.class, FraudWorkerWithdrawProviderDataGenerator.class })
class SeonAmlCheckJobTest {
    @Autowired
    private FraudWorkerServerProperties props;
    @Autowired
    private ApplicationConfig cfg;
    @Autowired
    private DynamicCloud cloud;
    @SpyBean
    private MockUtil mockUtil;
    @Autowired
    private FraudWorkerMasterEbeanJpaManager ebean;
    @SpyBean
    private SeonAmlCheckJob job;
    @SpyBean
    private SeonClient seonClient;
    @MockBean
    private SeonApi seonApi;
    @MockBean
    private SeonJaxRsApi seonJaxRsApi;
    @MockBean
    private PaymentServiceApi paymentServiceApi;
    @Captor
    ArgumentCaptor<SeonAmlCheckRequest> seonAmlCheckRequestArgumentCaptor;
    private final PlainServiceInfo seonService = new PlainServiceInfo("seon", "http://mock");

    @BeforeEach
    public void beforeEach() throws Throwable {
        cfg.setDefaultProperty(props.AML_CHECK_MONITORING_PERIOD.getKey(), Duration.ofDays(3));
        cloud.addUps(new RawServiceInfo(FraudWorkerProto.UPS_EMAIL_MASK_ACCOUNTS, "test".getBytes(StandardCharsets.UTF_8)));
        cloud.addUps(new RawServiceInfo(FraudWorkerProto.UPS_REAL_EMAIL_MASK_ACCOUNTS, "test".getBytes(StandardCharsets.UTF_8)));
        cloud.addUps(new RawServiceInfo(FraudWorkerProto.UPS_DOC_NUMBER_MASK_KYC, "test".getBytes(StandardCharsets.UTF_8)));
        cloud.addUps(new RawServiceInfo(FraudWorkerProto.UPS_ID_NUMBER_MASK_KYC, "test".getBytes(StandardCharsets.UTF_8)));
        cloud.addUps(seonService);

        try (var tx = ebean.newTransaction()) {
            FraudWorkerPaymentProviderDataGenerator.genPurchaseProvider(ebean, PurchaseProviderSpec.SKRILL, ProviderIntegrationTypeSpec.SKRILL, tx);
            var batchSizeKey = props.AML_CHECK_JOB_BATCH_SIZE.getKey();
            props.cfg().setLocalProperty(batchSizeKey, 1);
            tx.commit();
        }
    }

    @Test
    void testNoAmlCheckForUserWihoutPurchaseAcitivity() throws Throwable {
        LocalDate dob = LocalDate.of(2023, 9, 9);

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = FraudWorkerDataGenerationService.genAccount(ebean, tx);
            Citizen citizen = new Citizen(account.getPerson(), "US");
            citizen.setFirstName("First");
            citizen.setLastName("Last");
            account.getPerson().setBirthDate(dob);
            ebean.save(citizen, tx);
            account.getPerson().setBirthDate(dob);
            ebean.save(account.getPerson(), tx);
            tx.commit();
        }

        job.runOnceBlocking();
        try (var tx = ebean.newReadOnlyTransaction()) {
            Assertions.assertTrue(ebean.amlCheckRepo().find(account.getId(), tx).isEmpty());
        }
        Mockito.verifyNoMoreInteractions(seonClient);
    }

    @Test
    void tesNoCheckSoftKycNotSet() throws Throwable {
        LocalDate dob = LocalDate.of(2023, 9, 9);

        PaymentImmutableBrand brand;
        Account account;
        try (var tx = ebean.newTransaction()) {
            brand = FraudWorkerBrandDataGenerator.getBrand(ebean, tx);
            var offer = FraudWorkerOfferDataGenerator.getOffer(ebean, tx);
            account = FraudWorkerDataGenerationService.genAccount(ebean, tx);
            var date = LocalDate.ofInstant(Instant.now().minus(Duration.ofDays(2)), ZoneId.of("UTC"));
            var order = FraudWorkerDataGenerationService.genOrder(ebean, PaymentProvider.SKRILL, account.getId(), offer, PlatformSpec.WEB, PlatformUtil.randomUUID().toString(), PlatformUtil.randomUUID(), true, new Date(), true, date, tx);
            ebean.save(order, tx);

            PaymentImmutableAccountMetaInfo paymentInfo = new PaymentImmutableAccountMetaInfo();
            paymentInfo.setAccount(order.getAccount());
            paymentInfo.setLastSuccessfulPaymentOrder(order);

            ebean.save(paymentInfo, tx);
            tx.commit();
        }
        doReturn(new SeonAmlCheckResponse()).when(seonClient).amlCheck(brand.getName(), "First Last", dob, account.getHash() + "_" + account.getId());
        job.runOnceBlocking();
        try (var tx = ebean.newReadOnlyTransaction()) {
            Assertions.assertTrue(ebean.amlCheckRepo().find(account.getId(), tx).isEmpty());
        }
        Mockito.verifyNoMoreInteractions(seonClient);
    }

    @Test
    void testHappyPathJumioData() throws Throwable {
        LocalDate dob = LocalDate.of(2023, 9, 9);
        LocalDate jumioDob = LocalDate.of(2020, 9, 9);

        PaymentImmutableBrand brand;
        Account account;
        try (var tx = ebean.newTransaction()) {
            brand = FraudWorkerBrandDataGenerator.getBrand(ebean, tx);
            account = FraudWorkerDataGenerationService.genAccount(ebean, tx);
            var immutableAccount = ebean.fraudAccountRepo().account(account.getId(), tx).get();
            var redeem = FraudWorkerDataGenerationService.genWithdraw(account, ebean, RedeemStatusSpec.PRE_AUTHORIZED, tx);

            PaymentImmutableAccountMetaInfo paymentInfo = new PaymentImmutableAccountMetaInfo();
            paymentInfo.setAccount(redeem.getAccount());
            paymentInfo.setLastSuccessfulWithdraw(redeem);
            redeem.setAt(LocalDate.ofInstant(Instant.now().minus(Duration.ofDays(2)), ZoneId.of("UTC")));

            Citizen citizen = new Citizen(account.getPerson(), "US");
            citizen.setFirstName("First");
            citizen.setLastName("Last");
            account.getPerson().setBirthDate(dob);
            ebean.save(paymentInfo, tx);
            ebean.save(redeem, tx);
            ebean.save(citizen, tx);
            account.getPerson().setBirthDate(dob);
            ebean.save(account.getPerson(), tx);
            KYCVerificationRequest kvr = new KYCVerificationRequest(immutableAccount, LocalDate.now(), PlatformUtil.randomUUID(), PlatformSpec.WEB);
            kvr.setCode("code");
            kvr.setProvider("provider");
            kvr.setDocStatus(KYCStatusSpec.CONFIRMED);
            kvr.setStatus(KYCStatusSpec.ID_CONFIRMED);
            kvr.setIdFirstName("FirstJumio");
            kvr.setIdLastName("LastJumio");
            kvr.setIdBirthDate(jumioDob);
            ebean.save(kvr, tx);
            tx.commit();
        }
        SeonAmlCheckResponse resp = new SeonAmlCheckResponse();
        var amlRes = new SeonAmlCheckResponse.SeonResponseData.SeonAmlDetails();
        resp.getData().setSeonAmlDetails(amlRes);
        amlRes.setPepMatch(true);
        amlRes.setCrimelistMatch(true);
        amlRes.setSanctionMatch(true);
        amlRes.setWatchlistMatch(true);
        doReturn(resp).when(seonClient).amlCheck(brand.getName(), "FirstJumio LastJumio", jumioDob, account.getHash() + "_" + account.getId());
        job.runOnceBlocking();
        try (var tx = ebean.newReadOnlyTransaction()) {
            var check = ebean.amlCheckRepo().find(account.getId(), tx);
            Assertions.assertTrue(check.get().isCrimeListMatch());
            Assertions.assertTrue(check.get().isSanctionMatch());
            Assertions.assertTrue(check.get().isWatchlistMatch());
            Assertions.assertTrue(check.get().isPepMatch());
        }
        verify(seonClient).amlCheck(brand.getName(), "FirstJumio LastJumio", jumioDob, account.getHash() + "_" + account.getId());
        Mockito.verifyNoMoreInteractions(seonClient);
    }

    @Test
    void testHappyPathUserHasSoftKyc() throws Throwable {
        LocalDate dob = LocalDate.of(2023, 9, 9);

        PaymentImmutableBrand brand;
        Account account;
        try (var tx = ebean.newTransaction()) {
            brand = FraudWorkerBrandDataGenerator.getBrand(ebean, tx);
            var offer = FraudWorkerOfferDataGenerator.getOffer(ebean, tx);
            account = FraudWorkerDataGenerationService.genAccount(ebean, tx);
            var date = LocalDate.ofInstant(Instant.now().minus(Duration.ofDays(2)), ZoneId.of("UTC"));
            var order = FraudWorkerDataGenerationService.genOrder(ebean, PaymentProvider.SKRILL, account.getId(), offer, PlatformSpec.WEB, PlatformUtil.randomUUID().toString(), PlatformUtil.randomUUID(), true, new Date(), true, date, tx);
            ebean.save(order, tx);

            PaymentImmutableAccountMetaInfo paymentInfo = new PaymentImmutableAccountMetaInfo();
            paymentInfo.setAccount(order.getAccount());
            paymentInfo.setLastSuccessfulPaymentOrder(order);

            Citizen citizen = new Citizen(account.getPerson(), "US");
            citizen.setFirstName("First");
            citizen.setLastName("Last");
            account.getPerson().setBirthDate(dob);
            ebean.save(paymentInfo, tx);
            ebean.save(citizen, tx);
            account.getPerson().setBirthDate(dob);
            ebean.save(account.getPerson(), tx);
            tx.commit();
        }
        SeonAmlCheckResponse resp = new SeonAmlCheckResponse();
        resp.getData().setSeonAmlDetails(new SeonAmlCheckResponse.SeonResponseData.SeonAmlDetails());
        doReturn(resp).when(seonClient).amlCheck(brand.getName(), "First Last", dob, account.getHash() + "_" + account.getId());
        job.runOnceBlocking();
        try (var tx = ebean.newReadOnlyTransaction()) {
            var check = ebean.amlCheckRepo().find(account.getId(), tx);
            Assertions.assertFalse(check.get().isCrimeListMatch());
            Assertions.assertFalse(check.get().isSanctionMatch());
            Assertions.assertFalse(check.get().isWatchlistMatch());
            Assertions.assertFalse(check.get().isPepMatch());
        }
        verify(seonClient).amlCheck(brand.getName(), "First Last", dob, account.getHash() + "_" + account.getId());
        Mockito.verifyNoMoreInteractions(seonClient);
    }

    @Test
    void testCheckIsAlreadyMade() throws Throwable {
        LocalDate dob = LocalDate.of(2023, 9, 9);

        Account account;
        try (var tx = ebean.newTransaction()) {
            var offer = FraudWorkerOfferDataGenerator.getOffer(ebean, tx);
            account = FraudWorkerDataGenerationService.genAccount(ebean, tx);
            var date = LocalDate.ofInstant(Instant.now().minus(Duration.ofDays(2)), ZoneId.of("UTC"));
            var order = FraudWorkerDataGenerationService.genOrder(ebean, PaymentProvider.SKRILL, account.getId(), offer, PlatformSpec.WEB, PlatformUtil.randomUUID().toString(), PlatformUtil.randomUUID(), true, new Date(), true, date, tx);
            ebean.save(order, tx);

            PaymentImmutableAccountMetaInfo paymentInfo = new PaymentImmutableAccountMetaInfo();
            paymentInfo.setAccount(order.getAccount());
            paymentInfo.setLastSuccessfulPaymentOrder(order);

            Citizen citizen = new Citizen(account.getPerson(), "US");
            citizen.setFirstName("First");
            citizen.setLastName("Last");
            account.getPerson().setBirthDate(dob);
            ebean.save(paymentInfo, tx);
            ebean.save(citizen, tx);
            account.getPerson().setBirthDate(dob);
            ebean.save(account.getPerson(), tx);
            AmlCheck amlCheck = new AmlCheck();
            amlCheck.setAccount(ebean.fraudAccountRepo().requiredAccount(account.getId(), tx));
            ebean.save(amlCheck, tx);
            tx.commit();
        }
        job.runOnceBlocking();
        try (var tx = ebean.newReadOnlyTransaction()) {
            var check = ebean.amlCheckRepo().find(account.getId(), tx);
            Assertions.assertFalse(check.get().isCrimeListMatch());
            Assertions.assertFalse(check.get().isSanctionMatch());
            Assertions.assertFalse(check.get().isWatchlistMatch());
            Assertions.assertFalse(check.get().isPepMatch());
        }
        Mockito.verifyNoMoreInteractions(seonClient);
    }

    @Test
    void testNewCheckIsDone() throws Throwable {
        LocalDate dob = LocalDate.of(2023, 9, 9);
        java.util.Date createdAtInitialCheck = java.util.Date.from(Instant.now().minus(4, ChronoUnit.DAYS));

        PaymentImmutableBrand brand;
        Account account;
        try (var tx = ebean.newTransaction()) {
            brand = FraudWorkerBrandDataGenerator.getBrand(ebean, tx);
            account = FraudWorkerDataGenerationService.genAccount(ebean, tx);
            var offer = FraudWorkerOfferDataGenerator.getOffer(ebean, tx);
            var date = LocalDate.ofInstant(Instant.now().minus(Duration.ofDays(2)), ZoneId.of("UTC"));
            var order = FraudWorkerDataGenerationService.genOrder(ebean, PaymentProvider.SKRILL, account.getId(), offer, PlatformSpec.WEB, PlatformUtil.randomUUID().toString(), PlatformUtil.randomUUID(), true, new Date(), true, date, tx);
            ebean.save(order, tx);

            PaymentImmutableAccountMetaInfo paymentInfo = new PaymentImmutableAccountMetaInfo();
            paymentInfo.setAccount(order.getAccount());
            paymentInfo.setLastSuccessfulPaymentOrder(order);

            Citizen citizen = new Citizen(account.getPerson(), "US");
            citizen.setFirstName("First");
            citizen.setLastName("Last");
            account.getPerson().setBirthDate(dob);
            ebean.save(paymentInfo, tx);
            ebean.save(citizen, tx);
            account.getPerson().setBirthDate(dob);
            ebean.save(account.getPerson(), tx);
            AmlCheck amlCheck = new AmlCheck();
            amlCheck.setAccount(ebean.fraudAccountRepo().requiredAccount(account.getId(), tx));
            ebean.save(amlCheck, tx);
            amlCheck.setCreatedAt(createdAtInitialCheck);
            ebean.save(amlCheck, tx);
            tx.commit();
        }
        SeonAmlCheckResponse resp = new SeonAmlCheckResponse();
        resp.getData().setSeonAmlDetails(new SeonAmlCheckResponse.SeonResponseData.SeonAmlDetails());
        doReturn(resp).when(seonClient).amlCheck(brand.getName(), "First Last", dob, account.getHash() + "_" + account.getId());
        job.runOnceBlocking();
        try (var tx = ebean.newReadOnlyTransaction()) {
            var check = ebean.amlCheckRepo().find(account.getId(), tx);
            Assertions.assertFalse(check.get().isCrimeListMatch());
            Assertions.assertFalse(check.get().isSanctionMatch());
            Assertions.assertFalse(check.get().isWatchlistMatch());
            Assertions.assertFalse(check.get().isPepMatch());
            Assertions.assertNotEquals(createdAtInitialCheck, check.get().getCreatedAt());
        }
        verify(seonClient).amlCheck(brand.getName(), "First Last", dob, account.getHash() + "_" + account.getId());
        Mockito.verifyNoMoreInteractions(seonClient);
    }

    @Test
    void testNotEligibleForCheck() throws Throwable {
        LocalDate dob = LocalDate.of(2023, 9, 9);

        Account account;
        try (var tx = ebean.newTransaction()) {
            var offer = FraudWorkerOfferDataGenerator.getOffer(ebean, tx);
            account = FraudWorkerDataGenerationService.genAccount(ebean, tx);
            // no payment activity for a long time
            var date = LocalDate.ofInstant(Instant.now().minus(Duration.ofDays(42)), ZoneId.of("UTC"));
            var order = FraudWorkerDataGenerationService.genOrder(ebean, PaymentProvider.SKRILL, account.getId(), offer, PlatformSpec.WEB, PlatformUtil.randomUUID().toString(), PlatformUtil.randomUUID(), true, new Date(), true, date, tx);
            ebean.save(order, tx);

            PaymentImmutableAccountMetaInfo paymentInfo = new PaymentImmutableAccountMetaInfo();
            paymentInfo.setAccount(order.getAccount());
            paymentInfo.setLastSuccessfulPaymentOrder(order);

            Citizen citizen = new Citizen(account.getPerson(), "US");
            citizen.setFirstName("First");
            citizen.setLastName("Last");
            account.getPerson().setBirthDate(dob);
            ebean.save(paymentInfo, tx);
            ebean.save(citizen, tx);
            account.getPerson().setBirthDate(dob);
            ebean.save(account.getPerson(), tx);
            tx.commit();
        }
        job.runOnceBlocking();
        try (var tx = ebean.newReadOnlyTransaction()) {
            Assertions.assertTrue(ebean.amlCheckRepo().find(account.getId(), tx).isEmpty());
        }
        Mockito.verifyNoMoreInteractions(seonClient);
    }

    @Test
    void testFuzzyConfig() {
        when(seonJaxRsApi.proxy(seonService)).thenReturn(seonApi);
        seonClient.amlCheck("pulsz", "barak obama", LocalDate.of(1961, 8, 4), "user-id");
        verify(seonApi).amlCheck(any(), seonAmlCheckRequestArgumentCaptor.capture());
        SeonAmlCheckRequest request = seonAmlCheckRequestArgumentCaptor.getValue();
        Assertions.assertEquals("v1", request.config.aml.version);
        Assertions.assertTrue(request.config.aml.fuzzyEnabled);
        Assertions.assertTrue(request.config.aml.fuzzyConfig.phoneticSearchEnabled);
        Assertions.assertTrue(request.config.aml.fuzzyConfig.editDistanceEnabled);
        Assertions.assertEquals(0.9, request.config.aml.fuzzyConfig.scoring.scoreThreshold);
    }

    @Test
    void testBatch() throws Throwable {
        LocalDate dob = LocalDate.of(2023, 9, 9);

        PaymentImmutableBrand brand;
        Account firstAccount, secondAccount;
        try (var tx = ebean.newTransaction()) {
            brand = FraudWorkerBrandDataGenerator.getBrand(ebean, tx);
            var offer = FraudWorkerOfferDataGenerator.getOffer(ebean, tx);
            firstAccount = FraudWorkerDataGenerationService.genAccount(ebean, tx);
            secondAccount = FraudWorkerDataGenerationService.genAccount(ebean, tx);
            var firstOrder = FraudWorkerDataGenerationService.genSuccessfulOrder(ebean, firstAccount.getId(), offer, PaymentProvider.SKRILL, tx);
            var secondOrder = FraudWorkerDataGenerationService.genSuccessfulOrder(ebean, secondAccount.getId(), offer, PaymentProvider.SKRILL, tx);

            createAccountMetaInfo(firstOrder, tx);
            createAccountMetaInfo(secondOrder, tx);

            createCitizen(firstAccount, tx);
            createCitizen(secondAccount, tx);
            firstAccount.getPerson().setBirthDate(dob);
            secondAccount.getPerson().setBirthDate(dob);
            ebean.save(firstAccount.getPerson(), tx);
            ebean.save(secondAccount.getPerson(), tx);
            tx.commit();
        }
        mockSeon(brand, firstAccount, dob);
        mockSeon(brand, secondAccount, dob);
        job.runOnceBlocking();
        try (var tx = ebean.newReadOnlyTransaction()) {
            Assertions.assertTrue(ebean.amlCheckRepo().find(firstAccount.getId(), tx).isPresent());
            Assertions.assertTrue(ebean.amlCheckRepo().find(secondAccount.getId(), tx).isPresent());
        }
        verify(seonClient).amlCheck(brand.getName(), "First Last", dob, firstAccount.getHash() + "_" + firstAccount.getId());
        verify(seonClient).amlCheck(brand.getName(), "First Last", dob, secondAccount.getHash() + "_" + secondAccount.getId());
        Mockito.verifyNoMoreInteractions(seonClient);
    }

    private void createAccountMetaInfo(PaymentImmutablePaymentOrder order, Transaction tx) {
        PaymentImmutableAccountMetaInfo firstAccountMeta = new PaymentImmutableAccountMetaInfo();
        firstAccountMeta.setAccount(order.getAccount());
        firstAccountMeta.setLastSuccessfulPaymentOrder(order);
        ebean.save(firstAccountMeta, tx);
    }

    private void createCitizen(Account account, Transaction tx) {
        Citizen citizen = new Citizen(account.getPerson(), "US");
        citizen.setFirstName("First");
        citizen.setLastName("Last");
        ebean.save(citizen, tx);
    }

    private void mockSeon(PaymentImmutableBrand brand, Account account, LocalDate dob) {
        SeonAmlCheckResponse resp = new SeonAmlCheckResponse();
        resp.getData().setSeonAmlDetails(new SeonAmlCheckResponse.SeonResponseData.SeonAmlDetails());
        doReturn(resp).when(seonClient).amlCheck(brand.getName(), "First Last", dob, account.getHash() + "_" + account.getId());
    }
}
