package fraud.worker.replicators;

import static fraud.worker.framework.util.ZendeskTestUtils.mockZendeskClient;
import static fraud.worker.framework.util.ZendeskTestUtils.toInternalAccountInfo;
import static java.util.Objects.nonNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.Function;

import bi.zendesk.sink.tags.AccountTagUpdateReplicator;
import fraud.worker.FraudWorkerMasterEbeanJpaManager;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.google.common.collect.Lists;
import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.ups.PlainServiceInfo;

import bi.zendesk.ZendeskClient;
import bi.zendesk.ZendeskConfigService;
import bi.zendesk.data.User;
import bi.zendesk.data.request.UpsertUserRequest;
import bi.zendesk.sink.tags.AccountTagCategoryCreateReplicator;
import bi.zendesk.sink.tags.AccountTagCategoryUpdateReplicator;
import fraud.api.FraudServiceApi;
import fraud.api.v1.SetZendeskExportInfoResponse;
import fraud.worker.FraudWorkerServerProperties;
import fraud.worker.FraudWorkerSpringBootTestContextBootstrapper;
import fraud.worker.di.CommonFraudWorkerDiModule;
import fraud.worker.di.MockFraudWorkerDatabaseDiModule;
import fraud.worker.di.ZendeskMockDiModule;
import fraud.worker.framework.listeners.FraudWorkerBrandDataGenerator;
import fraud.worker.framework.util.FraudWorkerDataGenerationService;
import fraud.worker.framework.util.ZendeskTestUtils;
import fraud.worker.model.immutable.WorkerImmutableAccount;
import io.ebean.Transaction;
import io.vavr.CheckedRunnable;
import jakarta.ws.rs.core.Response;
import uam.CrmProto;
import uam.api.UamServiceApi;
import uam.api.v1.AccountInfo;
import uam.api.v1.AccountTag;
import uam.api.v1.AccountTagCategoryInfo;
import uam.api.v1.GetAccountInfoResponse;
import uam.api.v1.internal.AccountTagCategoryCreateEvent;
import uam.api.v1.internal.AccountTagCategoryUpdateEvent;
import uam.api.v1.internal.AccountTagUpdateEvent;

@ExtendWith(SpringExtension.class)
@BootstrapWith(FraudWorkerSpringBootTestContextBootstrapper.class)
@ContextConfiguration(classes = {
        MockFraudWorkerDatabaseDiModule.class,
        CommonFraudWorkerDiModule.class,
        ZendeskMockDiModule.class
})
@TestExecutionListeners(mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS, listeners = {FraudWorkerBrandDataGenerator.class})
class ZendeskAccountTagReplicatorTest {
    @Autowired
    private DynamicCloud cloud;

    @SpyBean
    private MockUtil mockUtil;

    @Autowired
    private FraudWorkerServerProperties props;

    @Autowired
    private ApplicationConfig cfg;

    @Autowired
    protected FraudWorkerMasterEbeanJpaManager ebean;

    @SpyBean
    private AccountTagCategoryCreateReplicator accountTagCategoryCreateReplicator;

    @SpyBean
    private AccountTagCategoryUpdateReplicator accountTagCategoryUpdateReplicator;

    @SpyBean
    private AccountTagUpdateReplicator accountTagUpdateReplicator;

    @SpyBean
    private ZendeskClient client;

    @MockBean
    private UamServiceApi uamServiceApi;

    @MockBean
    private FraudServiceApi fraudServiceApi;

    @Autowired
    private ZendeskConfigService zendeskConfigService;

    @BeforeEach
    public void before() {
        cloud.addUps(new PlainServiceInfo(CrmProto.UPS_ZENDESK, "https://user:<EMAIL>?env=test"));
    }

    @AfterEach
    public void after() {
        cloud.removeUps(CrmProto.UPS_ZENDESK);
    }

    @Test
    void testAccountTagCategoryUpdateEvent() throws Throwable {
        runAccountTagCategoryUpdateTest(ZendeskTestUtils::getDefaultUpsertUserRequest);
    }
    @Test
    void testAccountTagCategoryUpdateEvent_WhenTagShouldBeRemoved() throws Throwable {
        runAccountTagCategoryUpdate_WhenTagShouldBeRemovedTest(ZendeskTestUtils::getDefaultUpsertUserRequest);
    }

    @Test
    void testAccountTagCategoryCreateEvent() throws Throwable {
        runAccountTagCategoryCreateTest(ZendeskTestUtils::getDefaultUpsertUserRequest);
    }

    @Test
    void testAccountTagUpdateEvent_WhenNewTagsAdded() throws Throwable {
        testAccountTagUpdateEvent_WhenNewTagsAdded(ZendeskTestUtils::getDefaultUpsertUserRequest);
    }

    @Test
    void testAccountTagUpdateEvent_WhenTagsRemoved() throws Throwable {
        testAccountTagUpdateEvent_WhenTagsRemoved(ZendeskTestUtils::getDefaultUpsertUserRequest);
    }

    @Test
    void testAccountTagUpdateEvent_WhenAdvantagePlayAdded() throws Throwable {
        testAccountTagUpdateEvent_WhenAdvantagePlayAdded(ZendeskTestUtils::getDefaultUpsertUserRequest);
    }

    private void runAccountTagCategoryUpdateTest(Function<WorkerImmutableAccount, UpsertUserRequest> accountUpsertUserRequestFunction) throws Throwable {
        cfg.setLocalProperty(props.ZENDESK_ACCOUNT_TAG_CATEGORY_EXPORT_ENABLED.getKey(), true);
        when(zendeskConfigService.confZendeskIncludedTags()).thenReturn(Map.of("advantage play", "rac", "creator", "creator_tag"));
        var l = Lists.<CheckedRunnable>newLinkedList();
        try (Transaction tx = ebean.newTransaction()) {
            var generatedAccount = FraudWorkerDataGenerationService.genAccount(ebean, FraudWorkerBrandDataGenerator.BRAND, tx);
            var accountId = generatedAccount.getId();
            var fraudApiCall = mockUtil.mockServiceCall(fraudServiceApi, api -> api.setZendeskExportInfo(any(), any()), SetZendeskExportInfoResponse.getDefaultInstance());
            var uamApiCall = mockUtil.mockServiceCall(uamServiceApi, api -> api.getAccountInfo(any(), any()), buildGetAccountInfoResponse(null, generatedAccount.getCreatedAt().getTime()));

            var account = ebean.workerRepo().requiredAccount(accountId, tx);

            var event = AccountTagCategoryUpdateEvent.newBuilder()
                    .setAccount(toInternalAccountInfo(account))
                    .setCategory(AccountTagCategoryInfo.newBuilder().setCategory("custom")
                            .addAllTags(List.of("advantage play", "creator")).build())
                    .build();

            var expectedRequest = accountUpsertUserRequestFunction.apply(account);
            var userFields = new User.UserFields();
            userFields.brand = "test";
            userFields.tags = Map.of("rac", "rac_bluedream", "creator_tag", "creator_tag_bluedream");
            expectedRequest.user.userFields = userFields;

            l.add(() -> {
                var mock = mockZendeskClient(client, Response.Status.OK);
                var matcher = ZendeskTestUtils.matcher(expectedRequest);
                accountTagCategoryUpdateReplicator.apply(event);

                verify(mock).upsert(any(), argThat(matcher));
                verifyNoMoreInteractions(mock);
                uamApiCall.verify();
                fraudApiCall.verify();
            });
        }
        for (var it : l) {
            it.run();
        }
    }

    private void runAccountTagCategoryUpdate_WhenTagShouldBeRemovedTest(Function<WorkerImmutableAccount, UpsertUserRequest> accountUpsertUserRequestFunction) throws Throwable {
        cfg.setLocalProperty(props.ZENDESK_ACCOUNT_TAG_CATEGORY_EXPORT_ENABLED.getKey(), true);
        when(zendeskConfigService.confZendeskIncludedTags()).thenReturn(Map.of("advantage play", "rac", "creator", "creator_tag"));

        var l = Lists.<CheckedRunnable>newLinkedList();
        try (Transaction tx = ebean.newTransaction()) {
            var generatedAccount = FraudWorkerDataGenerationService.genAccount(ebean, FraudWorkerBrandDataGenerator.BRAND, tx);
            var accountId = generatedAccount.getId();
            var fraudApiCall = mockUtil.mockServiceCall(fraudServiceApi, api -> api.setZendeskExportInfo(any(), any()), SetZendeskExportInfoResponse.getDefaultInstance());
            var uamApiCall = mockUtil.mockServiceCall(uamServiceApi, api -> api.getAccountInfo(any(), any()), buildGetAccountInfoResponse(null, generatedAccount.getCreatedAt().getTime()));

            var account = ebean.workerRepo().requiredAccount(accountId, tx);

            var event = AccountTagCategoryUpdateEvent.newBuilder()
                    .setAccount(toInternalAccountInfo(account))
                    .setCategory(AccountTagCategoryInfo.newBuilder().setCategory("custom")
                            .addAllTags(List.of("creator")).build())
                    .addAllRemovedTags(List.of("advantage play"))
                    .build();

            var expectedRequest = accountUpsertUserRequestFunction.apply(account);
            var userFields = new User.UserFields();
            userFields.brand = "test";
            userFields.tags = Map.of("rac", "", "creator_tag", "creator_tag_bluedream");
            expectedRequest.user.userFields = userFields;

            l.add(() -> {
                var mock = mockZendeskClient(client, Response.Status.OK);
                var matcher = ZendeskTestUtils.matcher(expectedRequest);
                accountTagCategoryUpdateReplicator.apply(event);

                verify(mock).upsert(any(), argThat(matcher));
                verifyNoMoreInteractions(mock);
                uamApiCall.verify();
                fraudApiCall.verify();
            });
        }
        for (var it : l) {
            it.run();
        }
    }

    private void runAccountTagCategoryCreateTest(Function<WorkerImmutableAccount, UpsertUserRequest> accountUpsertUserRequestFunction) throws Throwable {
        cfg.setLocalProperty(props.ZENDESK_ACCOUNT_TAG_CATEGORY_EXPORT_ENABLED.getKey(), true);
        cfg.setLocalProperty(props.CONF_ZENDESK_INCLUDED_TAGS.getKey(), Map.of("advantage play", "rac", "creator", "creator_tag"));

        var l = Lists.<CheckedRunnable>newLinkedList();
        try (Transaction tx = ebean.newTransaction()) {
            var generatedAccount = FraudWorkerDataGenerationService.genAccount(ebean, FraudWorkerBrandDataGenerator.BRAND, tx);
            var accountId = generatedAccount.getId();
            var fraudApiCall = mockUtil.mockServiceCall(fraudServiceApi, api -> api.setZendeskExportInfo(any(), any()),
                    SetZendeskExportInfoResponse.getDefaultInstance());
            var uamApiCall = mockUtil.mockServiceCall(uamServiceApi, api -> api.getAccountInfo(any(), any()),
                    buildGetAccountInfoResponse(null, generatedAccount.getCreatedAt().getTime()));

            var account = ebean.workerRepo().requiredAccount(accountId, tx);

            var event = AccountTagCategoryCreateEvent.newBuilder()
                    .setAccount(toInternalAccountInfo(account))
                    .setCategory(AccountTagCategoryInfo.newBuilder().setCategory("custom")
                            .addAllTags(List.of("advantage play", "creator")).build())
                    .build();

            var expectedRequest = accountUpsertUserRequestFunction.apply(account);
            var userFields = new User.UserFields();
            userFields.brand = "test";
            userFields.tags = Map.of("rac", "rac_bluedream", "creator_tag", "creator_tag_bluedream");
            expectedRequest.user.userFields = userFields;

            l.add(() -> {
                var mock = mockZendeskClient(client, Response.Status.OK);
                var matcher = ZendeskTestUtils.matcher(expectedRequest);
                accountTagCategoryCreateReplicator.apply(event);

                verify(mock).upsert(any(), argThat(matcher));
                verifyNoMoreInteractions(mock);
                uamApiCall.verify();
                fraudApiCall.verify();
            });
        }
        for (var it : l) {
            it.run();
        }
    }

    private void testAccountTagUpdateEvent_WhenNewTagsAdded(Function<WorkerImmutableAccount, UpsertUserRequest> accountUpsertUserRequestFunction) throws Throwable {
        cfg.setLocalProperty(props.ZENDESK_ACCOUNT_TAG_EXPORT_ENABLED.getKey(), true);

        var l = Lists.<CheckedRunnable>newLinkedList();
        try (Transaction tx = ebean.newTransaction()) {
            var generatedAccount = FraudWorkerDataGenerationService.genAccount(ebean, FraudWorkerBrandDataGenerator.BRAND, tx);
            var accountId = generatedAccount.getId();
            var fraudApiCall = mockUtil.mockServiceCall(fraudServiceApi, api -> api.setZendeskExportInfo(any(), any()), SetZendeskExportInfoResponse.getDefaultInstance());
            var uamApiCall = mockUtil.mockServiceCall(uamServiceApi, api -> api.getAccountInfo(any(), any()), buildGetAccountInfoResponse(null, generatedAccount.getCreatedAt().getTime()));

            var account = ebean.workerRepo().requiredAccount(accountId, tx);

            var event = AccountTagUpdateEvent.newBuilder()
                    .setAccount(toInternalAccountInfo(account))
                    .addAddedAccountTags(AccountTag.newBuilder()
                            .setType("high_fraud_score"))
                    .build();

            var expectedRequest = accountUpsertUserRequestFunction.apply(account);
            var userFields = new User.UserFields();
            userFields.brand = "test";
            userFields.tags = Map.of("high_fraud_score", "high_fraud_score_bluedream");
            expectedRequest.user.userFields = userFields;

            l.add(() -> {
                var mock = mockZendeskClient(client, Response.Status.OK);
                var matcher = ZendeskTestUtils.matcher(expectedRequest);
                accountTagUpdateReplicator.apply(event);

                verify(mock).upsert(any(), argThat(matcher));
                verifyNoMoreInteractions(mock);
                uamApiCall.verify();
                fraudApiCall.verify();
            });
        }
        for (var it : l) {
            it.run();
        }
    }

    private void testAccountTagUpdateEvent_WhenTagsRemoved(Function<WorkerImmutableAccount, UpsertUserRequest> accountUpsertUserRequestFunction) throws Throwable {
        cfg.setLocalProperty(props.ZENDESK_ACCOUNT_TAG_EXPORT_ENABLED.getKey(), true);

        var l = Lists.<CheckedRunnable>newLinkedList();
        try (Transaction tx = ebean.newTransaction()) {
            var generatedAccount = FraudWorkerDataGenerationService.genAccount(ebean, FraudWorkerBrandDataGenerator.BRAND, tx);
            var accountId = generatedAccount.getId();
            var fraudApiCall = mockUtil.mockServiceCall(fraudServiceApi, api -> api.setZendeskExportInfo(any(), any()), SetZendeskExportInfoResponse.getDefaultInstance());
            var uamApiCall = mockUtil.mockServiceCall(uamServiceApi, api -> api.getAccountInfo(any(), any()), buildGetAccountInfoResponse(null, generatedAccount.getCreatedAt().getTime()));

            var account = ebean.workerRepo().requiredAccount(accountId, tx);

            var event = AccountTagUpdateEvent.newBuilder()
                    .setAccount(toInternalAccountInfo(account))
                    .addRemovedAccountTags(AccountTag.newBuilder()
                            .setType("high_fraud_score"))
                    .build();

            var expectedRequest = accountUpsertUserRequestFunction.apply(account);
            var userFields = new User.UserFields();
            userFields.brand = "test";
            userFields.tags = Map.of("high_fraud_score", StringUtils.EMPTY);
            expectedRequest.user.userFields = userFields;

            l.add(() -> {
                var mock = mockZendeskClient(client, Response.Status.OK);
                var matcher = ZendeskTestUtils.matcher(expectedRequest);
                accountTagUpdateReplicator.apply(event);

                verify(mock).upsert(any(), argThat(matcher));
                verifyNoMoreInteractions(mock);
                uamApiCall.verify();
                fraudApiCall.verify();
            });
        }
        for (var it : l) {
            it.run();
        }
    }


    private void testAccountTagUpdateEvent_WhenAdvantagePlayAdded(Function<WorkerImmutableAccount, UpsertUserRequest> accountUpsertUserRequestFunction) throws Throwable {
        cfg.setLocalProperty(props.ZENDESK_ACCOUNT_TAG_EXPORT_ENABLED.getKey(), true);
        when(zendeskConfigService.zendeskAdvantagePlayTagField()).thenReturn(Map.of("advantage play", "rac"));

        var l = Lists.<CheckedRunnable>newLinkedList();
        try (Transaction tx = ebean.newTransaction()) {
            var generatedAccount = FraudWorkerDataGenerationService.genAccount(ebean, FraudWorkerBrandDataGenerator.BRAND, tx);
            var accountId = generatedAccount.getId();
            var fraudApiCall = mockUtil.mockServiceCall(fraudServiceApi, api -> api.setZendeskExportInfo(any(), any()), SetZendeskExportInfoResponse.getDefaultInstance());
            var uamApiCall = mockUtil.mockServiceCall(uamServiceApi, api -> api.getAccountInfo(any(), any()), buildGetAccountInfoResponse(null, generatedAccount.getCreatedAt().getTime()));

            var account = ebean.workerRepo().requiredAccount(accountId, tx);

            var event = AccountTagUpdateEvent.newBuilder()
                    .setAccount(toInternalAccountInfo(account))
                    .addAddedAccountTags(AccountTag.newBuilder()
                            .setType("advantage play"))
                    .build();

            var expectedRequest = accountUpsertUserRequestFunction.apply(account);
            var userFields = new User.UserFields();
            userFields.brand = "test";
            userFields.tags = Map.of("rac", "rac_bluedream");
            expectedRequest.user.userFields = userFields;

            l.add(() -> {
                var mock = mockZendeskClient(client, Response.Status.OK);
                var matcher = ZendeskTestUtils.matcher(expectedRequest);
                accountTagUpdateReplicator.apply(event);

                verify(mock).upsert(any(), argThat(matcher));
                verifyNoMoreInteractions(mock);
                uamApiCall.verify();
                fraudApiCall.verify();
            });
        }
        for (var it : l) {
            it.run();
        }
    }

    private static GetAccountInfoResponse buildGetAccountInfoResponse(api.v1.Date date, Long createdAt) {

        AccountInfo.Builder accountInfo = AccountInfo.newBuilder()
                .setLocked(true)
                .setEmail("<EMAIL>")
                .setCountry(Locale.US.getCountry());
        if (nonNull(date)) {
            accountInfo.setBirthDate(date);
        }
        if (nonNull(createdAt)) {
            accountInfo.setCreatedAt(createdAt);
        }
        return GetAccountInfoResponse.newBuilder()
                .setInfo(accountInfo)
                .build();
    }
}
