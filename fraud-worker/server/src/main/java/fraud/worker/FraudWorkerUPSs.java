package fraud.worker;

import com.turbospaces.ups.UPSs;

import common.ImmutableUPSsCollection;

public class FraudWorkerUPSs extends ImmutableUPSsCollection {
    {
        //
        // ~ common
        //
        add(UPSs.INFRA_CORE);
        add(UPSs.INFRA_SERVER);
        add(UPSs.REDIS);
        add(UPSs.CLICKHOUSE);

        add(FraudWorkerProto.UPS_POSTGRES_SLAVE_READ_ONLY);
        add(FraudWorkerProto.UPS_EMAIL_MASK_ACCOUNTS);
        add(FraudWorkerProto.UPS_REAL_EMAIL_MASK_ACCOUNTS);
        add(FraudWorkerProto.UPS_PHONE_NUMBER_MASK_ACCOUNTS);
        add(FraudWorkerProto.UPS_DOC_NUMBER_MASK_KYC);
        add(FraudWorkerProto.UPS_ID_NUMBER_MASK_KYC);
        add(FraudWorkerProto.FISERV_REPORTS_SMTP_CREDENTIALS);
        add(FraudWorkerProto.RAPYD_REPORTS_SMTP_CREDENTIALS);
        add(FraudWorkerProto.UPS_CHECKOUT_REPORTS);
    }
}
