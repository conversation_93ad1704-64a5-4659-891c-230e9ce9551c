package fraud.worker;

import static fraud.worker.services.worldpay.WorldpayChbStatusConverter.ChargebackType.RAPID_DISPUTE_RESOLUTION;
import static fraud.worker.services.worldpay.WorldpayChbStatusConverter.ChargebackType.REPRESENTMENT;
import static fraud.worker.services.worldpay.WorldpayChbStatusConverter.ChargebackType.REQUEST_FOR_INFORMATION;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.DynamicPropertyFactory;
import com.turbospaces.cfg.ScopedProperty;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.logging.ClassUtils;

public class FraudWorkerServerProperties extends ApplicationProperties {
    public final Property<Integer> REQUEST_REPLY_TIMEOUT;
    public final Property<Duration> TEMPORAL_REQUEST_REPLY_TIMEOUT;
    public final ScopedProperty<Integer> PARALLEL_DEFAULT_MAX_REQUESTS;
    public final Property<Duration> PROVIDERS_RELOAD_SECRETS_INTERVAL;
    public final ScopedProperty<Boolean> PAYMENT_TEMPORAL_ENABLED;

    //
    // ~ Aml
    //
    public final Property<Boolean> AML_CHECK_JOB_ENABLED;
    public final Property<Duration> AML_CHECK_JOB_FREQUENCY;
    public final Property<Duration> AML_CHECK_MONITORING_PERIOD;
    public final Property<Integer> AML_CHECK_JOB_BATCH_SIZE;
    //
    // ~ Emerchantpay
    //
    public final Property<Boolean> EMERCHANTPAY_TC40_JOB_ENABLED;
    public final Property<Duration> EMERCHANTPAY_TC40_PULL_ALERTS_JOB_FREQUENCY;
    public final Property<Integer> EMERCHANTPAY_TC40_PULL_START_DAYS_OFFSET;
    public final Property<Boolean> EMERCHANTPAY_TC40_REFUND_ENABLED;
    public final Property<Long> EMERCHANTPAY_TC40_REFUND_THRESHOLD;

    //
    // ~ Emerchantpay reconciliation report job
    //
    public final Property<Boolean> EMERCHANTPAY_TRC_REPORT_JOB_ENABLED;
    public final Property<Duration> EMERCHANTPAY_TRC_REPORT_JOB_FREQUENCY;
    public final Property<LocalTime> EMERCHANTPAY_TRC_REPORT_JOB_ARN_AVAILABILITY_TIME;
    public final Property<String> EMERCHANTPAY_TRC_REPORT_JOB_CRON;

    //
    // Emerchantpay pay
    //
    public final Property<Integer> EMERCHANTPAY_RETRY_ATTEMPTS;
    public final Property<Duration> EMERCHANTPAY_RETRY_INITIAL_INTERVAL;
    public final Property<Integer> EMERCHANTPAY_RETRY_INTERVAL_MULTIPLIER;
    public final Property<Duration> EMERCHANTPAY_RETRY_MAX_INTERVAL;
    public final Property<List<Integer>> EMERCHANTPAY_RETRY_HTTP_CODES;
    public final Property<List<Class<? extends Throwable>>> EMERCHANTPAY_RETRY_EXCEPTION_CLASSES;

    //
    // ~ Fiserv chargeback report job
    //
    public final Property<Boolean> FISERV_CHB_REPORT_JOB_ENABLED;
    public final Property<Duration> FISERV_CHB_REPORT_JOB_FREQUENCY;
    public final Property<String> FISERV_CHB_REPORT_ORDER_SN_COLUMN_NAME;
    public final Property<String> FISERV_CHB_REPORT_STATUS_COLUMN_NAME;
    public final Property<String> FISERV_CHB_REPORT_DATE_COLUMN_NAME;
    public final Property<String> FISERV_CHB_REPORT_RECEIVED_DATE_COLUMN_NAME;
    public final Property<String> FISERV_CHB_REPORT_REASON_COLUMN_NAME;
    public final Property<String> FISERV_CHB_REPORT_REASON_CODE_COLUMN_NAME;
    public final Property<String> SMTP_FISERV_CHB_REPORT_ATTACHMENT_FILENAME_PATTERN;
    public final Property<List<String>> SMTP_FISERV_CHB_REPORT_EMAIL_SUBJECT;
    public final Property<List<String>> SMTP_FISERV_CHB_REPORT_EMAIL_FROM;
    //
    // ~ Fiserv tc40 report job
    //
    public final Property<Boolean> FISERV_TC40_REPORT_JOB_ENABLED;
    public final Property<String> FISERV_TC40_REPORT_ARN_COLUMN_NAME;
    public final Property<String> FISERV_TC40_REPORT_JOB_CRON;
    public final Property<String> FISERV_TC40_REPORT_DATE_COLUMN_NAME;
    public final Property<String> FISERV_TC40_REPORT_FRAUD_TYPE_COLUMN_NAME;
    public final Property<String> SMTP_FISERV_TC40_REPORT_ATTACHMENT_FILENAME_PATTERN;
    public final Property<List<String>> SMTP_FISERV_TC40_REPORT_EMAIL_SUBJECT;
    public final Property<List<String>> SMTP_FISERV_TC40_REPORT_EMAIL_FROM;
    //

    // ~ TC40 Email Notifications Job
    public final Property<Boolean> TC40_EMAIL_NOTIFICATIONS_JOB_ENABLED;
    public final Property<String> TC40_EMAIL_NOTIFICATIONS_JOB_CRON;
    public final Property<Integer> TC40_EMAIL_NOTIFICATIONS_JOB_DAYS_RANGE;
    //

    // ~ Chargeback Email Notifications Job
    public final Property<Boolean> CHARGEBACK_EMAIL_NOTIFICATIONS_JOB_ENABLED;
    public final Property<String> CHARGEBACK_EMAIL_NOTIFICATIONS_JOB_CRON;
    public final Property<Integer> CHARGEBACK_EMAIL_NOTIFICATIONS_JOB_DAYS_RANGE;
    //

    //
    //Fiserv ARN job
    public final Property<Boolean> FISERV_ARN_REPORT_JOB_ENABLED;
    public final Property<String> FISERV_ARN_REPORT_JOB_CRON;
    public final Property<String> FISERV_ARN_REPORT_ORDER_SN_COLUMN_NAME;
    public final Property<String> FISERV_ARN_REPORT_ARN_COLUMN_NAME;
    public final Property<String> SMTP_FISERV_ARN_REPORT_ATTACHMENT_FILENAME_PATTERN;
    public final Property<List<String>> SMTP_FISERV_ARN_REPORT_EMAIL_SUBJECT;
    public final Property<List<String>> SMTP_FISERV_ARN_REPORT_EMAIL_FROM;
    //

    // ~ Worldpay
    public final Property<Map<String, String>> WORLDPAY_PARTY_ID_TO_BRAND_MAP;
    // ~ Worldpay chargeback report job
    public final Property<Boolean> WORLDPAY_CHB_REPORT_JOB_ENABLED;
    public final Property<Duration> WORLDPAY_CHB_REPORT_JOB_FREQUENCY;
    public final Property<String> WORLDPAY_CHB_REPORT_SFTP_SERVER_DIRECTORY;
    public final Property<List<String>> WORLDPAY_CHB_REPORT_SKIP_STATUSES;
    public final Property<Boolean> WORLDPAY_REPORT_PASSWORD_AUTH_ENABLED;
    //
    // ~ Worldpay reconciliation report job
    public final Property<Boolean> WORLDPAY_TRC_REPORT_JOB_ENABLED;
    public final Property<Duration> WORLDPAY_TRC_REPORT_JOB_FREQUENCY;
    public final Property<String> WORLDPAY_TRC_REPORT_SFTP_SERVER_DIRECTORY;
    //
    // ~ Worldpay TC40 report job
    public final Property<Boolean> WORLDPAY_TC40_REPORT_JOB_ENABLED;
    public final Property<Duration> WORLDPAY_TC40_REPORT_JOB_FREQUENCY;
    public final Property<String> WORLDPAY_TC40_REPORT_SFTP_SERVER_DIRECTORY;
    //
    // ~ Paynearme TC40 report job
    public final Property<Boolean> PAYNEARME_REPORT_PASSWORD_AUTH_ENABLED;
    public final Property<Boolean> PAYNEARME_TC40_REPORT_JOB_ENABLED;
    public final Property<Duration> PAYNEARME_TC40_REPORT_JOB_FREQUENCY;
    public final Property<String> PAYNEARME_TC40_REPORT_SFTP_SERVER_DIRECTORY;
    //
    // ~ Rapyd tc40 report job
    //
    public final Property<Boolean> RAPYD_TC40_REPORT_JOB_ENABLED;
    public final Property<String> RAPYD_TC40_REPORT_REFERENCE_ID_COLUMN_NAME;
    public final Property<String> RAPYD_TC40_REPORT_JOB_CRON;
    public final Property<String> RAPYD_TC40_REPORT_DATE_COLUMN_NAME;
    public final Property<String> RAPYD_TC40_REPORT_FRAUD_TYPE_COLUMN_NAME;
    public final Property<String> SMTP_RAPYD_TC40_REPORT_ATTACHMENT_FILENAME_PATTERN;
    public final Property<List<String>> SMTP_RAPYD_TC40_REPORT_EMAIL_SUBJECT;
    public final Property<List<String>> SMTP_RAPYD_TC40_REPORT_EMAIL_FROM;
    //
    // ~ Rapyd reconciliation report job
    //
    public final Property<Boolean> RAPYD_TRC_REPORT_JOB_ENABLED;
    public final Property<String> RAPYD_TRC_REPORT_REFERENCE_ID_COLUMN_NAME;
    public final Property<String> RAPYD_TRC_REPORT_JOB_CRON;
    public final Property<String> RAPYD_TRC_REPORT_ARN_COLUMN_NAME;
    public final Property<String> SMTP_RAPYD_TRC_REPORT_ATTACHMENT_FILENAME_PATTERN;
    public final Property<List<String>> SMTP_RAPYD_TRC_REPORT_EMAIL_SUBJECT;
    public final Property<List<String>> SMTP_RAPYD_TRC_REPORT_EMAIL_FROM;
    //
    // ~ Checkout tc40 report job
    //
    public final Property<Boolean> CHECKOUT_TC40_REPORT_JOB_ENABLED;
    public final Property<String> CHECKOUT_TC40_REPORT_JOB_CRON;
    public final Property<String> CHECKOUT_TC40_REPORT_CODE_ID_COLUMN_NAME;
    public final Property<String> CHECKOUT_TC40_REPORT_DATE_COLUMN_NAME;
    public final Property<String> CHECKOUT_TC40_REPORT_FRAUD_TYPE_COLUMN_NAME;
    public final Property<Integer> CHECKOUT_TC40_REPORT_CHECKABLE_DAYS_BACK;

    //
    //
    //
    // ~ Lock high risk accounts job
    //
    public final Property<Boolean> LOCK_HIGH_RISK_ACCOUNTS_JOB_ENABLED;
    public final Property<Boolean> LOCK_HIGH_RISK_ACCOUNTS_LOCKING_ENABLED;
    public final Property<Duration> LOCK_HIGH_RISK_ACCOUNTS_JOB_FREQUENCY;
    public final Property<Duration> LOCK_HIGH_RISK_ACCOUNTS_DAYS_AFTER_CREATION;
    public final Property<Integer> LOCK_HIGH_RISK_ACCOUNTS_FRAUD_SCORE;
    public final Property<String> LOCK_HIGH_RISK_ACCOUNTS_REASON_MESSAGE;
    public final Property<Boolean> LOCK_HIGH_RISK_ACCOUNTS_SLACK_MESSAGES_ENABLED;
    public final Property<Integer> LOCK_HIGH_RISK_ACCOUNTS_ITERATION_LIMIT;
    public final Property<List<String>> LOCK_HIGH_RISK_ACCOUNTS_BRAND_ENABLED;
    public final Property<Boolean> LOCK_HIGH_RISK_ACCOUNTS_SLEEP_ENABLED;
    public final Property<Integer> LOCK_HIGH_RISK_ACCOUNTS_SLEEP_FROM;
    public final Property<Integer> LOCK_HIGH_RISK_ACCOUNTS_SLEEP_TO;
    public final Property<Integer> LOCK_HIGH_RISK_ACCOUNTS_AFTER_CREATION_RANGE_FROM;
    public final Property<Integer> LOCK_HIGH_RISK_ACCOUNTS_AFTER_CREATION_RANGE_TO;
    //
    // ~ Zendesk
    //
    public final Property<Boolean> EVENT_STREAMING_ZENDESK_ENABLED;
    public final Property<Boolean> EVENT_STREAMING_ZENDESK_RETRY_ENABLED;
    public final Property<List<String>> EXCLUDED_ZENDESK_EMAILS;
    public final Property<Map<String, String>> CONF_ZENDESK_INCLUDED_TAGS;
    public final Property<Boolean> PURCHASE_EVENT_STREAMING_ZENDESK_ENABLED;
    public final Property<Duration> PURCHASE_EVENT_ZENDESK_EXPORT_INTERVAL;
    public final Property<Boolean> PURCHASE_LIMITS_EVENT_STREAMING_ZENDESK_ENABLED;
    public final Property<Boolean> ZENDESK_EXPORT_JOB_ENABLED;
    public final Property<Duration> ZENDESK_EXPORT_JOB_FREQUENCY;
    public final Property<Duration> ZENDESK_EXPORT_JOB_MODIFIED_AT_RANGE_START_PERIOD_AGO;
    public final Property<List<Integer>> ZENDESK_EXPORT_IGNORED_STATUS_CODES;
    // users export rate limit
    public final Property<Duration> ZENDESK_EXPORT_RATE_LIMIT_PERIOD;
    public final Property<Integer> ZENDESK_EXPORT_RATE_LIMIT_COUNT;
    public final Property<Duration> ZENDESK_EXPORT_RATE_LIMIT_TIMEOUT;
    // tickets rate limit
    public final Property<Duration> ZENDESK_TICKETS_RATE_LIMIT_PERIOD;
    public final Property<Integer> ZENDESK_TICKETS_RATE_LIMIT_COUNT;
    public final Property<Duration> ZENDESK_TICKETS_RATE_LIMIT_TIMEOUT;
    public final Property<Integer> ZENDESK_EXPORT_PLAYERS_BATCH_SIZE;
    public final Property<Duration> ZENDESK_EXPORT_INTERVAL;
    public final Property<Boolean> ZENDESK_EXPORT_NOT_UPDATED_PLAYERS_FROM_PREVIOUS_RUN;
    public final Property<Boolean> ZENDESK_PURCHASE_LIMITS_EXPORT_JOB_ENABLED;
    public final Property<Boolean> ZENDESK_INFLUENCER_EXPORT_ENABLED;
    public final Property<Boolean> ZENDESK_CREATOR_EXPORT_ENABLED;
    //vip levels sync job
    public final Property<Boolean> ZENDESK_VIP_LEVELS_JOB_ENABLED;
    public final Property<Integer> ZENDESK_VIP_LEVELS_JOB_BATCH_SIZE;
    //user updates job
    public final Property<Boolean> ZENDESK_USER_UPDATE_JOB_ENABLED;
    public final Property<List<String>> ZENDESK_USER_UPDATE_ENABLED_PER_BRAND;
    public final Property<String> ZENDESK_USER_UPDATE_FROM;
    public final Property<String> ZENDESK_USER_UPDATE_TO;
    public final Property<Duration> ZENDESK_USER_UPDATE_RATE_LIMIT_PERIOD;
    public final Property<Integer> ZENDESK_USER_UPDATE_RATE_LIMIT_COUNT;
    public final Property<Duration> ZENDESK_USER_UPDATE_RATE_LIMIT_TIMEOUT;
    public final Property<Integer> ZENDESK_UPDATE_JOB_FREQUENCY;
    public final Property<Boolean> ZENDESK_ACCOUNT_RESTRICTION_EXPORT_ENABLED;
    public final Property<Boolean> ZENDESK_ACCOUNT_TAG_CATEGORY_EXPORT_ENABLED;
    public final Property<Boolean> ZENDESK_ACCOUNT_TAG_EXPORT_ENABLED;
    public final Property<Boolean> ZENDESK_TICKET_CREATION_ENABLED;
    public final Property<Map<String, String>> ZENDESK_TICKET_CREATION_CUSTOM_FIELDS;
    public final Property<Map<String, String>> ZENDESK_ADVANTAGE_PLAY_TAG_FIELD;
    public final Property<Map<String, String>> ZENDESK_BRANDS;
    public final Property<List<String>> ZENDESK_TICKET_CREATION_INCLDED_REASONS;

    //
    // ~ Seon fuzzy
    //
    public final Property<Boolean> SEON_FUZZY_ENABLED;
    public final Property<Double> SEON_FUZZY_THRESHOLD;
    public final Property<Boolean> SEON_FUZZY_PHONETIC_SEARCH_ENABLED;
    public final Property<Boolean> SEON_FUZZY_EDIT_DISTANCE_ENABLED;
    //
    // ~ Ethoca
    //
    public final Property<List<String>> ETHOCA_PROVIDERS_WHITELIST;
    public final Property<List<String>> ETHOCA_PROVIDERS_BLACKLIST;
    public final Property<Boolean> ETHOCA_PULL_ALERTS_JOB_ENABLED;
    public final Property<Duration> ETHOCA_PULL_ALERTS_JOB_FREQUENCY;
    public final Property<Duration> ETHOCA_REFUND_AGE_THRESHOLD;
    public final Property<Long> ETHOCA_MIN_THRESHOLD;
    public final ScopedProperty<Long> ETHOCA_REFUND_MIN_THRESHOLD;
    public final ScopedProperty<Long> ETHOCA_REFUND_MAX_THRESHOLD;
    public final Property<Boolean> ETHOCA_REFUND_ENABLED;
    public final Property<Long> ETHOCA_PULL_START_OFFSET;
    public final Property<Duration> ETHOCA_CLIENT_CONN_TIMEOUT;
    public final Property<Duration> ETHOCA_CLIENT_SOCKET_TIMEOUT;
    public final Property<Boolean> ETHOCA_PULL_ONLY_DISPUTE_ALERTS;
    public final Property<Boolean> ETHOCA_MATCHING_BY_TIMESTAMP_ENABLED;
    public final Property<Duration> ETHOCA_TIMESTAMP_SEARCH_INTERVAL;
    public final Property<Integer> ETHOCA_SEARCH_INTERVAL_IN_DAYS;
    //
    // ~ Reset OTP limit
    //
    public final Property<Boolean> RESET_OTP_LIMIT_JOB_ENABLED;
    public final Property<Duration> RESET_OTP_LIMIT_JOB_FREQUENCY;
    public final Property<Duration> OTP_LIMIT_TIMEOUT;
    //
    // ~ Card verification
    //
    public final Property<Boolean> CARD_VERIFICATION_LISTENER_ENABLED;
    public final Property<Boolean> CARD_VERIFICATION_LISTENER_RERTY_ENABLED;
    public final Property<Boolean> AUTO_CARD_VERIFICATION_ENABLED;
    public final Property<Duration> AUTO_CARD_VERIFICATION_NEW_CARDS_PERIOD;
    public final Property<Integer> AUTO_CARD_VERIFICATION_NEW_CARDS_LIMIT;
    public final Property<Duration> AUTO_CARD_VERIFICATION_PURCHASE_COUNT_PERIOD;
    public final Property<Integer> AUTO_CARD_VERIFICATION_PURCHASE_COUNT_LIMIT;
    public final Property<BigDecimal> AUTO_CARD_VERIFICATION_TOTAL_PURCHASE_AMOUNT_LIMIT;
    public final Property<Integer> AUTO_CARD_VERIFICATION_TOTAL_PURCHASE_COUNT_LIMIT;
    public final Property<Duration> AUTO_CARD_VERIFICATION_TRANSACTION_COUNT_PERIOD;
    public final Property<Duration> AUTO_CARD_VERIFICATION_TOTAL_PURCHASE_PERIOD;
    //
    public final Property<Boolean> RISK_BASED_AUTO_CARD_VERIFICATION_ENABLED;
    public final Property<List<String>> RISK_BASED_AUTO_CARD_VERIFICATION_EXCLUDED_VIP_LEVELS;
    public final Property<Duration> RISK_BASED_AUTO_CARD_VERIFICATION_PURCHASE_COUNT_PERIOD;
    public final Property<BigDecimal> RISK_BASED_AUTO_CARD_VERIFICATION_TOTAL_PURCHASE_AMOUNT_LIMIT;
    public final ScopedProperty<Integer> RISK_BASED_AUTO_CARD_VERIFICATION_USERS_APPLIED_FROM;
    public final ScopedProperty<Integer> RISK_BASED_AUTO_CARD_VERIFICATION_USERS_APPLIED_TO;
    public final ScopedProperty<Boolean> RISK_BASED_AUTO_CARD_VERIFICATION_ENABLE_FOR_ADMIN;
    public final ScopedProperty<Integer> RISK_BASED_AUTO_CARD_VERIFICATION_APPLICABLE_FRACTION;
    //
    // ~ Clickhouse
    //
    public final Property<Boolean> EVENT_STREAMING_CLICKHOUSE_ENABLED;
    //
    // ~ Background request consumer
    //
    public final Property<Boolean> BACKGROUND_REQUEST_CONSUMER_ENABLED;
    public final Property<Boolean> BACKGROUND_REQUEST_CONSUMER_RETRY_ENABLED;
    public final ScopedProperty<Boolean> FRAUD_TEMPORAL_ENABLED;

    public FraudWorkerServerProperties(DynamicPropertyFactory pf) {
        super(pf);
        REQUEST_REPLY_TIMEOUT = pf.get("request-reply.timeout", int.class).orElse(60);
        TEMPORAL_REQUEST_REPLY_TIMEOUT = pf.get("temporal.request-reply.timeout", Duration.class).orElse(Duration.ofMinutes(1));
        PARALLEL_DEFAULT_MAX_REQUESTS = pf.getScoped("parallel.default.max-requests", int.class, 5);
        PROVIDERS_RELOAD_SECRETS_INTERVAL = pf.get("providers.reload-secrets.interval", Duration.class).orElse(Duration.ofHours(12));

        AML_CHECK_JOB_ENABLED = pf.get("aml.check-job.enabled", boolean.class).orElse(false);
        AML_CHECK_JOB_FREQUENCY = pf.get("aml.check-job-freq", Duration.class).orElse(Duration.parse("PT12H"));
        AML_CHECK_MONITORING_PERIOD = pf.get("aml.check-job-monitoring", Duration.class).orElse(Duration.parse("P30D"));
        AML_CHECK_JOB_BATCH_SIZE = pf.get("aml.check-job.batch-size", int.class).orElse(100);

        EMERCHANTPAY_TC40_JOB_ENABLED = pf.get("emerchantpay.tc40.job.enabled", boolean.class).orElse(false);
        EMERCHANTPAY_TC40_PULL_ALERTS_JOB_FREQUENCY = pf.get("emerchantpay.tc40.job.freq-interval", Duration.class).orElse(Duration.ofMinutes(30));
        EMERCHANTPAY_TC40_PULL_START_DAYS_OFFSET = pf.get("emerchantpay.tc40.job.days.offset", Integer.class).orElse(1);
        EMERCHANTPAY_TC40_REFUND_ENABLED = pf.get("emerchantpay.tc40.job.refund.enabled", boolean.class).orElse(false);
        EMERCHANTPAY_TC40_REFUND_THRESHOLD = pf.get("emerchantpay.tc40.job.refund.threshold", Long.class).orElse(100L);

        EMERCHANTPAY_TRC_REPORT_JOB_ENABLED = pf.get("emerchantpay-trc-report.job.enabled", boolean.class).orElse(false);
        EMERCHANTPAY_TRC_REPORT_JOB_FREQUENCY = pf.get("emerchantpay-trc-report.job.freq-interval", Duration.class).orElse(Duration.ofHours(1));
        EMERCHANTPAY_TRC_REPORT_JOB_ARN_AVAILABILITY_TIME = pf.get("emerchantpay-trc-report.job.arn-availability-time", LocalTime.class).orElse(LocalTime.of(14, 0));
        EMERCHANTPAY_TRC_REPORT_JOB_CRON = pf.get("emerchantpay-trc-report.job.cron", String.class).orElse("0 0 10-15 * * ?");

        EMERCHANTPAY_RETRY_ATTEMPTS = pf.get("emerchantpay.retry.attempts", Integer.class).orElse(3);
        EMERCHANTPAY_RETRY_INITIAL_INTERVAL = pf.get("emerchantpay.retry.initial.interval", Duration.class).orElse(Duration.ofMillis(500));
        EMERCHANTPAY_RETRY_INTERVAL_MULTIPLIER = pf.get("emerchantpay.retry.multiplier", Integer.class).orElse(2);
        EMERCHANTPAY_RETRY_MAX_INTERVAL = pf.get("emerchantpay.retry.max.interval", Duration.class).orElse(Duration.ofSeconds(3));
        EMERCHANTPAY_RETRY_HTTP_CODES = pf.listOfInts("emerchantpay.retry.http.codes")
                .orElse(List.of(429, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511));
        EMERCHANTPAY_RETRY_EXCEPTION_CLASSES = pf.getProperty("emerchantpay.retry.exception.classes")
                .asType(s -> ClassUtils.parseThrowableClasses(s, ","), "java.net.SocketException,java.net.SocketTimeoutException");

        FISERV_CHB_REPORT_JOB_ENABLED = pf.get("fiserv.chb.report.job.enabled", boolean.class).orElse(false);
        FISERV_CHB_REPORT_JOB_FREQUENCY = pf.get("fiserv.chb.report.job.freq-interval", Duration.class).orElse(Duration.ofHours(24));
        FISERV_CHB_REPORT_ORDER_SN_COLUMN_NAME = pf.get("fiserv.chb.report.order-sn.column-name", String.class).orElse("Invoice Number");
        FISERV_CHB_REPORT_STATUS_COLUMN_NAME = pf.get("fiserv.chb.report.status.column-name", String.class).orElse("Chargeback Win/Loss");
        FISERV_CHB_REPORT_DATE_COLUMN_NAME = pf.get("fiserv.chb.report.date.column-name", String.class).orElse("Status Date");
        FISERV_CHB_REPORT_RECEIVED_DATE_COLUMN_NAME = pf.get("fiserv.chb.report.received-date.column-name", String.class).orElse("Received Date");
        FISERV_CHB_REPORT_REASON_COLUMN_NAME = pf.get("fiserv.chb.report.reason.column-name", String.class).orElse("Dispute Reason");
        FISERV_CHB_REPORT_REASON_CODE_COLUMN_NAME = pf.get("fiserv.chb.report.reason.code.column-name", String.class).orElse("Dispute Reason Code");
        SMTP_FISERV_CHB_REPORT_ATTACHMENT_FILENAME_PATTERN = pf.get("smtp.fiserv.chb.email-attachment-file-name-pattern", String.class).orElse("Chargeback_Last_day.xlsx");
        SMTP_FISERV_CHB_REPORT_EMAIL_SUBJECT = pf.listOfStrings("smtp.fiserv.chb.email-subject").orElse(List.of("Your scheduled report Chargeback Last day is ready"));
        SMTP_FISERV_CHB_REPORT_EMAIL_FROM = pf.listOfStrings("smtp.fiserv.chb.email-from").orElse(List.of("<EMAIL>"));

        FISERV_TC40_REPORT_JOB_ENABLED = pf.get("fiserv.tc40.report.job.enabled", boolean.class).orElse(false);
        FISERV_TC40_REPORT_JOB_CRON = pf.get("fiserv.tc40.report.job.cron", String.class).orElse("0 0/30 17-19 * * ?");
        FISERV_TC40_REPORT_ARN_COLUMN_NAME = pf.get("fiserv.tc40.report.arn.column-name", String.class).orElse("Reference Number");
        FISERV_TC40_REPORT_DATE_COLUMN_NAME = pf.get("fiserv.tc40.report.date.column-name", String.class).orElse("Reported Date");
        FISERV_TC40_REPORT_FRAUD_TYPE_COLUMN_NAME = pf.get("fiserv.tc40.report.fraud-type.column-name", String.class).orElse("Fraud Type");
        SMTP_FISERV_TC40_REPORT_ATTACHMENT_FILENAME_PATTERN = pf.get("smtp.fiserv.tc40.email-attachment-file-name-pattern", String.class).orElse("MGB_Daily_TC40.xlsx");
        SMTP_FISERV_TC40_REPORT_EMAIL_SUBJECT = pf.listOfStrings("smtp.fiserv.tc40.email-subject").orElse(List.of("Your scheduled report MGB Daily TC40 is ready"));
        SMTP_FISERV_TC40_REPORT_EMAIL_FROM = pf.listOfStrings("smtp.fiserv.tc40.email-from").orElse(List.of("<EMAIL>"));

        TC40_EMAIL_NOTIFICATIONS_JOB_ENABLED = pf.get("tc40.email-notifications.job.enabled", boolean.class).orElse(false);
        TC40_EMAIL_NOTIFICATIONS_JOB_CRON = pf.get("tc40.email-notifications.job.cron", String.class).orElse("0 0/30 12-15 * * ?");
        TC40_EMAIL_NOTIFICATIONS_JOB_DAYS_RANGE = pf.get("tc40.email-notifications.job.days-range", Integer.class).orElse(2);

        CHARGEBACK_EMAIL_NOTIFICATIONS_JOB_ENABLED = pf.get("chargeback.email-notifications.job.enabled", boolean.class).orElse(false);
        CHARGEBACK_EMAIL_NOTIFICATIONS_JOB_CRON = pf.get("chargeback.email-notifications.job.cron", String.class).orElse("0 0 12-15 * * ?");
        CHARGEBACK_EMAIL_NOTIFICATIONS_JOB_DAYS_RANGE = pf.get("chargeback.email-notifications.job.days-range", Integer.class).orElse(2);

        FISERV_ARN_REPORT_JOB_ENABLED = pf.get("fiserv.arn.report.job.enabled", boolean.class).orElse(false);
        FISERV_ARN_REPORT_JOB_CRON = pf.get("fiserv.arn.report.job.cron", String.class).orElse("0 0/15 13-16 * * ?");
        FISERV_ARN_REPORT_ORDER_SN_COLUMN_NAME = pf.get("fiserv.arn.report.order-sn.column-name", String.class).orElse("Invoice Number");
        FISERV_ARN_REPORT_ARN_COLUMN_NAME = pf.get("fiserv.arn.report.arn.column-name", String.class).orElse("Acquirer Reference Number");
        SMTP_FISERV_ARN_REPORT_ATTACHMENT_FILENAME_PATTERN = pf.get("smtp.fiserv.arn.email-attachment-file-name-pattern", String.class).orElse("B2S_-_Daily_settled_purchases.xlsx");
        SMTP_FISERV_ARN_REPORT_EMAIL_SUBJECT = pf.listOfStrings("smtp.fiserv.arn.email-subject").orElse(List.of("Your scheduled report B2S - Daily settled purchases is ready"));
        SMTP_FISERV_ARN_REPORT_EMAIL_FROM = pf.listOfStrings("smtp.fiserv.arn.email-from").orElse(List.of("<EMAIL>"));

        WORLDPAY_CHB_REPORT_JOB_ENABLED = pf.get("worldpay-chb-report.job.enabled", boolean.class).orElse(false);
        WORLDPAY_CHB_REPORT_JOB_FREQUENCY = pf.get("worldpay-chb-report.job.freq-interval", Duration.class).orElse(Duration.ofHours(24));
        WORLDPAY_CHB_REPORT_SFTP_SERVER_DIRECTORY = pf.get("worldpay-chb-report.sftp-server.dir", String.class).orElse("/Outgoing/");
        WORLDPAY_CHB_REPORT_SKIP_STATUSES = pf.listOfStrings("worldpay-chb-report.skip.statuses")
                .orElse(List.of(RAPID_DISPUTE_RESOLUTION.getValue(), REPRESENTMENT.getValue(), REQUEST_FOR_INFORMATION.getValue()));
        WORLDPAY_REPORT_PASSWORD_AUTH_ENABLED = pf.get("worldpay-report.job.pass-auth.enabled", boolean.class).orElse(false);

        WORLDPAY_TRC_REPORT_JOB_ENABLED = pf.get("worldpay-trc-report.job.enabled", boolean.class).orElse(false);
        WORLDPAY_TRC_REPORT_JOB_FREQUENCY = pf.get("worldpay-trc-report.job.freq-interval", Duration.class).orElse(Duration.ofHours(24));
        WORLDPAY_TRC_REPORT_SFTP_SERVER_DIRECTORY = pf.get("worldpay-trc-report.sftp-server.dir", String.class).orElse("/Outgoing/");

        WORLDPAY_TC40_REPORT_JOB_ENABLED = pf.get("worldpay-tc40-report.job.enabled", boolean.class).orElse(false);
        WORLDPAY_TC40_REPORT_JOB_FREQUENCY = pf.get("worldpay-tc40-report.job.freq-interval", Duration.class).orElse(Duration.ofHours(24));
        WORLDPAY_TC40_REPORT_SFTP_SERVER_DIRECTORY = pf.get("worldpay-tc40-report.sftp-server.dir", String.class).orElse("/Outgoing/");
        WORLDPAY_PARTY_ID_TO_BRAND_MAP = pf.mapOfStringStrings("worldpay.party-id.to.brand", Collections.emptyMap());

        PAYNEARME_REPORT_PASSWORD_AUTH_ENABLED = pf.get("paynearme-report.job.pass-auth.enabled", boolean.class).orElse(false);
        PAYNEARME_TC40_REPORT_JOB_ENABLED = pf.get("paynearme-tc40-report.job.enabled", boolean.class).orElse(false);
        PAYNEARME_TC40_REPORT_JOB_FREQUENCY = pf.get("paynearme-tc40-report.job.freq-interval", Duration.class).orElse(Duration.ofHours(24));
        PAYNEARME_TC40_REPORT_SFTP_SERVER_DIRECTORY = pf.get("paynearme-tc40-report.sftp-server.dir", String.class).orElse("/prod/TC40/");

        RAPYD_TC40_REPORT_JOB_ENABLED = pf.get("rapyd.tc40.report.job.enabled", boolean.class).orElse(false);
        RAPYD_TC40_REPORT_JOB_CRON = pf.get("rapyd.tc40.report.job.cron", String.class).orElse("0 0/30 17-19 * * ?");
        RAPYD_TC40_REPORT_REFERENCE_ID_COLUMN_NAME = pf.get("rapyd.tc40.report.arn.column-name", String.class).orElse("Reference ID");
        RAPYD_TC40_REPORT_DATE_COLUMN_NAME = pf.get("rapyd.tc40.report.date.column-name", String.class).orElse("Reported Date");
        RAPYD_TC40_REPORT_FRAUD_TYPE_COLUMN_NAME = pf.get("rapyd.tc40.report.fraud-type.column-name", String.class).orElse("Fraud Type");
        SMTP_RAPYD_TC40_REPORT_ATTACHMENT_FILENAME_PATTERN = pf.get("smtp.rapyd.tc40.email-attachment-file-name-pattern", String.class).orElse("B2services_-_Megabonanza_-_Automatic_Report_2025-02-11T0400.csv");
        SMTP_RAPYD_TC40_REPORT_EMAIL_SUBJECT = pf.listOfStrings("smtp.rapyd.tc40.email-subject").orElse(List.of("B2services - Megabonanza - Automatic Report"));
        SMTP_RAPYD_TC40_REPORT_EMAIL_FROM = pf.listOfStrings("smtp.rapyd.tc40.email-from").orElse(List.of("<EMAIL>"));

        RAPYD_TRC_REPORT_JOB_ENABLED = pf.get("rapyd.trc.report.job.enabled", boolean.class).orElse(false);
            RAPYD_TRC_REPORT_JOB_CRON = pf.get("rapyd.trc.report.job.cron", String.class).orElse("0 0/30 17-19 * * ?");
        RAPYD_TRC_REPORT_REFERENCE_ID_COLUMN_NAME = pf.get("rapyd.trc.report.order-sn.column-name", String.class).orElse("Reference ID");
        RAPYD_TRC_REPORT_ARN_COLUMN_NAME = pf.get("rapyd.trc.report.arn.column-name", String.class).orElse("ARN");
        SMTP_RAPYD_TRC_REPORT_ATTACHMENT_FILENAME_PATTERN = pf.get("smtp.rapyd.trc.email-attachment-file-name-pattern", String.class).orElse("");
        SMTP_RAPYD_TRC_REPORT_EMAIL_SUBJECT = pf.listOfStrings("smtp.rapyd.trc.email-subject").orElse(List.of(""));
        SMTP_RAPYD_TRC_REPORT_EMAIL_FROM = pf.listOfStrings("smtp.rapyd.trc.email-from").orElse(List.of("<EMAIL>"));

        CHECKOUT_TC40_REPORT_JOB_ENABLED = pf.get("checkout.tc40.job.enabled", boolean.class).orElse(false);
        CHECKOUT_TC40_REPORT_JOB_CRON = pf.get("checkout.tc40.job.cron", String.class).orElse("0 0/30 17-19 * * ?");
        CHECKOUT_TC40_REPORT_CODE_ID_COLUMN_NAME = pf.get("checkout.tc40.report.code.column-name", String.class).orElse("Payment ID");
        CHECKOUT_TC40_REPORT_DATE_COLUMN_NAME = pf.get("checkout.tc40.report.date.column-name", String.class).orElse("Fraud Issue Date");
        CHECKOUT_TC40_REPORT_FRAUD_TYPE_COLUMN_NAME = pf.get("checkout.tc40.report.fraud-type.column-name", String.class).orElse("Fraud Reason");
        CHECKOUT_TC40_REPORT_CHECKABLE_DAYS_BACK = pf.get("checkout.tc40.report.checkable.days", Integer.class).orElse(3);

        LOCK_HIGH_RISK_ACCOUNTS_JOB_ENABLED = pf.get("lock_high_risk_accounts.job.enabled", boolean.class).orElse(false);
        LOCK_HIGH_RISK_ACCOUNTS_LOCKING_ENABLED = pf.get("lock_high_risk_accounts.job.locking-enabled", boolean.class).orElse(false);
        LOCK_HIGH_RISK_ACCOUNTS_JOB_FREQUENCY = pf.get("lock_high_risk_accounts.job.freq-interval", Duration.class).orElse(Duration.ofHours(24));
        LOCK_HIGH_RISK_ACCOUNTS_DAYS_AFTER_CREATION = pf.get("lock_high_risk_accounts.job.days_after_creation", Duration.class).orElse(Duration.ofDays(30));
        LOCK_HIGH_RISK_ACCOUNTS_AFTER_CREATION_RANGE_FROM = pf.get("lock_high_risk_accounts.job.range_after_creation_from", int.class).orElse(35);
        LOCK_HIGH_RISK_ACCOUNTS_AFTER_CREATION_RANGE_TO = pf.get("lock_high_risk_accounts.job.range_after_creation_to", int.class).orElse(30);
        LOCK_HIGH_RISK_ACCOUNTS_FRAUD_SCORE = pf.get("lock_high_risk_accounts.job.fraud_score", int.class).orElse(80);
        LOCK_HIGH_RISK_ACCOUNTS_REASON_MESSAGE = pf.get("lock_high_risk_accounts.job.reason_message", String.class).orElse("Auto-lock Suspected Fraud");
        LOCK_HIGH_RISK_ACCOUNTS_SLACK_MESSAGES_ENABLED = pf.get("lock_high_risk_accounts.slack.messages", Boolean.class).orElse(false);
        LOCK_HIGH_RISK_ACCOUNTS_ITERATION_LIMIT = pf.get("lock_high_risk_accounts.iteration.limit", int.class).orElse(100);
        LOCK_HIGH_RISK_ACCOUNTS_BRAND_ENABLED = pf.listOfStrings("lock_high_risk_accounts.brand.enabled").orElse(Collections.emptyList());
        LOCK_HIGH_RISK_ACCOUNTS_SLEEP_ENABLED = pf.get("lock_high_risk_accounts.sleep-enabled", boolean.class).orElse(false);
        LOCK_HIGH_RISK_ACCOUNTS_SLEEP_FROM = pf.get("lock_high_risk_accounts.sleep-from", int.class).orElse(10);
        LOCK_HIGH_RISK_ACCOUNTS_SLEEP_TO = pf.get("lock_high_risk_accounts.sleep-to", int.class).orElse(20);

        // Zendesk sink
        EVENT_STREAMING_ZENDESK_ENABLED = pf.get("event-streaming.zendesk.enabled", boolean.class).orElse(false);
        EVENT_STREAMING_ZENDESK_RETRY_ENABLED = pf.get("event-streaming.zendesk.retry.enabled", boolean.class).orElse(false);
        EXCLUDED_ZENDESK_EMAILS = pf.listOfStrings("excluded.zendesk.emails").orElse(List.of());
        CONF_ZENDESK_INCLUDED_TAGS = pf.mapOfStringStrings("included.zendesk.tags", Collections.emptyMap());
        PURCHASE_EVENT_STREAMING_ZENDESK_ENABLED = pf.get("purchase-event-streaming.zendesk.enabled", boolean.class).orElse(false);
        PURCHASE_EVENT_ZENDESK_EXPORT_INTERVAL = pf.get("purchase-event-streaming.zendesk-export.interval", Duration.class).orElse(Duration.ofHours(24));
        PURCHASE_LIMITS_EVENT_STREAMING_ZENDESK_ENABLED = pf.get("purchase-limits-event-streaming.zendesk.enabled", boolean.class).orElse(false);
        ZENDESK_EXPORT_JOB_ENABLED = pf.get("zendesk-export.job.enabled", boolean.class).orElse(false);
        ZENDESK_EXPORT_JOB_FREQUENCY = pf.get("zendesk-export.job.freq-interval", Duration.class).orElse(Duration.ofMinutes(30));
        ZENDESK_EXPORT_JOB_MODIFIED_AT_RANGE_START_PERIOD_AGO = pf.get("zendesk-export.job.modified-at-range-start-ago.period", Duration.class).orElse(Duration.ofDays(2));
        ZENDESK_EXPORT_IGNORED_STATUS_CODES = pf.listOfInts("zendesk-export.ignored-status-codes").orElse(List.of(422));
        ZENDESK_EXPORT_RATE_LIMIT_PERIOD = pf.get("zendesk-export.job.rate.limit.period", Duration.class).orElse(Duration.ofSeconds(1));
        ZENDESK_EXPORT_RATE_LIMIT_COUNT = pf.get("zendesk-export.job.rate.limit.count", int.class).orElse(1);
        ZENDESK_EXPORT_RATE_LIMIT_TIMEOUT = pf.get("zendesk-export.job.rate.limit.timeout", Duration.class).orElse(Duration.ofSeconds(5));
        ZENDESK_TICKETS_RATE_LIMIT_PERIOD = pf.get("zendesk-tickets.rate.limit.period", Duration.class).orElse(Duration.ofSeconds(60));
        ZENDESK_TICKETS_RATE_LIMIT_COUNT = pf.get("zendesk-tickets.rate.limit.count", int.class).orElse(100);
        ZENDESK_TICKETS_RATE_LIMIT_TIMEOUT = pf.get("zendesk-tickets.rate.limit.timeout", Duration.class).orElse(Duration.ofSeconds(60));
        ZENDESK_EXPORT_PLAYERS_BATCH_SIZE = pf.get("zendesk-export.job.players.batch.size", Integer.class).orElse(100);
        ZENDESK_EXPORT_INTERVAL = pf.get("zendesk-export.job.interval", Duration.class).orElse(Duration.ofHours(24));
        ZENDESK_EXPORT_NOT_UPDATED_PLAYERS_FROM_PREVIOUS_RUN = pf.get("zendesk-export.update.not-updated.players.from.previous.run", boolean.class)
                .orElse(false);
        ZENDESK_PURCHASE_LIMITS_EXPORT_JOB_ENABLED = pf.get("zendesk.purchase-limits.export.job.enabled", boolean.class).orElse(false);
        ZENDESK_INFLUENCER_EXPORT_ENABLED = pf.get("zendesk.influencer-export.enabled", boolean.class).orElse(false);
        ZENDESK_CREATOR_EXPORT_ENABLED = pf.get("zendesk.creator-export.enabled", boolean.class).orElse(false);
        ZENDESK_VIP_LEVELS_JOB_ENABLED = pf.get("zendesk-vip-levels-export.job.enabled", boolean.class).orElse(false);
        ZENDESK_VIP_LEVELS_JOB_BATCH_SIZE = pf.get("zendesk-vip-levels-export.job.batch-size", int.class).orElse(100);
        ZENDESK_USER_UPDATE_JOB_ENABLED = pf.get("zendesk-user-update.job.enabled", boolean.class).orElse(false);
        ZENDESK_USER_UPDATE_ENABLED_PER_BRAND = pf.listOfStrings("zendesk-user-update.brands").orElse(List.of());
        ZENDESK_USER_UPDATE_FROM = pf.get("zendesk-user-update.job.from", String.class)
                .orElse(PlatformUtil.toLocalUTCDate().format(DateTimeFormatter.ISO_DATE));
        ZENDESK_USER_UPDATE_TO = pf.get("zendesk-user-update.job.to", String.class).orElse(PlatformUtil.toLocalUTCDate().format(DateTimeFormatter.ISO_DATE));
        ZENDESK_USER_UPDATE_RATE_LIMIT_PERIOD = pf.get("zendesk-user-update.job.rate.limit.period", Duration.class).orElse(Duration.ofSeconds(1));
        ZENDESK_USER_UPDATE_RATE_LIMIT_COUNT = pf.get("zendesk-user-update.job.rate.limit.count", int.class).orElse(1);
        ZENDESK_USER_UPDATE_RATE_LIMIT_TIMEOUT = pf.get("zendesk-user-update.job.rate.limit.timeout", Duration.class).orElse(Duration.ofSeconds(5));
        ZENDESK_UPDATE_JOB_FREQUENCY = pf.get("zendesk-user-update.job.freq-interval", int.class).orElse(2);
        ZENDESK_ACCOUNT_RESTRICTION_EXPORT_ENABLED = pf.get( "zendesk.account-restriction-export.enabled", Boolean.class).orElse(false);
        ZENDESK_ACCOUNT_TAG_CATEGORY_EXPORT_ENABLED = pf.get("zendesk.account-tag-category-export.enabled", Boolean.class).orElse(false);
        ZENDESK_ACCOUNT_TAG_EXPORT_ENABLED = pf.get("zendesk.account-tag-export.enabled", Boolean.class).orElse(false);
        ZENDESK_TICKET_CREATION_ENABLED = pf.get("zendesk.ticket-creation.enabled", Boolean.class).orElse(false);
        ZENDESK_TICKET_CREATION_CUSTOM_FIELDS = pf.mapOfStringStrings("zendesk.ticket-creation.custom-fields", Collections.emptyMap());
        ZENDESK_ADVANTAGE_PLAY_TAG_FIELD = pf.mapOfStringStrings("zendesk.advantage-play.tag-field", Map.of("advantage play", "rac"));
        ZENDESK_BRANDS = pf.mapOfStringStrings("zendesk.brands", Collections.emptyMap());
        ZENDESK_TICKET_CREATION_INCLDED_REASONS = pf.listOfStrings("zendesk.ticket-creation.included.reasons").orElse(List.of());

        // Seon fuzzy
        SEON_FUZZY_ENABLED = pf.get("seon.fuzzy.enabled", boolean.class).orElse(true);
        SEON_FUZZY_THRESHOLD = pf.get("seon.fuzzy.threshold", double.class).orElse(0.9);
        SEON_FUZZY_PHONETIC_SEARCH_ENABLED = pf.get("seon.fuzzy.phonetic-search-enabled", boolean.class).orElse(true);
        SEON_FUZZY_EDIT_DISTANCE_ENABLED = pf.get("seon.fuzzy.edit-distance-enabled", boolean.class).orElse(true);

        //Ethoca
        ETHOCA_PROVIDERS_WHITELIST = pf.listOfStrings("ethoca.providers.whitelist");
        ETHOCA_PROVIDERS_BLACKLIST = pf.listOfStrings("ethoca.providers.blacklist");
        ETHOCA_PULL_ALERTS_JOB_ENABLED = pf.get("ethoca.job.enabled", boolean.class).orElse(false);
        ETHOCA_PULL_ALERTS_JOB_FREQUENCY = pf.get("ethoca.job.freq-interval", Duration.class).orElse(Duration.ofMinutes(30));
        ETHOCA_REFUND_AGE_THRESHOLD = pf.get("ethoca.job.refund.age.threshold", Duration.class).orElse(Duration.ofDays(120));
        ETHOCA_MIN_THRESHOLD = pf.get("ethoca.job.alert.min.threshold", Long.class).orElse(5L);
        ETHOCA_REFUND_MIN_THRESHOLD = pf.getScoped("ethoca.job.refund.min.threshold", Long.class, 0L);
        ETHOCA_REFUND_MAX_THRESHOLD = pf.getScoped("ethoca.job.refund.threshold", Long.class, 100L);
        ETHOCA_REFUND_ENABLED = pf.get("ethoca.job.refund.enabled", Boolean.class).orElse(false);
        ETHOCA_PULL_START_OFFSET = pf.get("ethoca.job.start.offset", Long.class).orElse(100L);
        ETHOCA_CLIENT_CONN_TIMEOUT = pf.get("ethoca.client.connection.timeout", Duration.class).orElse(Duration.ofMinutes(150));
        ETHOCA_CLIENT_SOCKET_TIMEOUT = pf.get("ethoca.client.socket.timeout", Duration.class).orElse(Duration.ofMinutes(150));
        ETHOCA_PULL_ONLY_DISPUTE_ALERTS = pf.get("ethoca.pull.only.dispute.alerts", Boolean.class).orElse(false);
        ETHOCA_MATCHING_BY_TIMESTAMP_ENABLED = pf.get("ethoca.matching.timestamp.enabled", Boolean.class).orElse(true);
        ETHOCA_TIMESTAMP_SEARCH_INTERVAL = pf.get("ethoca.timestamp.search.interval", Duration.class).orElse(Duration.ofMinutes(1));
        ETHOCA_SEARCH_INTERVAL_IN_DAYS = pf.get("ethoca.search.interval.in.days", Integer.class).orElse(1);

        //Reset OTP limit
        RESET_OTP_LIMIT_JOB_ENABLED = pf.get("reset.otp-limit.job.enabled", boolean.class).orElse(false);
        RESET_OTP_LIMIT_JOB_FREQUENCY = pf.get("reset.otp-limit.job.freq-interval", Duration.class).orElse(Duration.ofMinutes(1));
        OTP_LIMIT_TIMEOUT = pf.get("otp.limit.timeout", Duration.class).orElse(Duration.ofHours(24));

        //Card verification
        CARD_VERIFICATION_LISTENER_ENABLED = pf.get("card-verification.listener.enabled", boolean.class).orElse(true);
        CARD_VERIFICATION_LISTENER_RERTY_ENABLED = pf.get("card-verification.listener.retry.enabled", boolean.class).orElse(true);
        AUTO_CARD_VERIFICATION_ENABLED = pf.get("auto-card-verification.enabled", boolean.class).orElse(false);
        AUTO_CARD_VERIFICATION_NEW_CARDS_PERIOD = pf.get("auto-card-verification.new-cards-for-period.period", Duration.class).orElse(Duration.ofDays(30));
        AUTO_CARD_VERIFICATION_NEW_CARDS_LIMIT = pf.get("auto-card-verification.new-cards-for-period.limit", Integer.class).orElse(5);
        AUTO_CARD_VERIFICATION_PURCHASE_COUNT_PERIOD = pf.get("auto-card-verification.card-purchases-for-period.period", Duration.class)
                .orElse(Duration.ofHours(24));
        AUTO_CARD_VERIFICATION_PURCHASE_COUNT_LIMIT = pf.get("auto-card-verification.card-purchases-for-period.limit", Integer.class).orElse(20);
        AUTO_CARD_VERIFICATION_TOTAL_PURCHASE_AMOUNT_LIMIT = pf.get("auto-card-verification.total-card-purchases-amount.limit", BigDecimal.class).orElse(new BigDecimal(1050));
        AUTO_CARD_VERIFICATION_TOTAL_PURCHASE_COUNT_LIMIT = pf.get("auto-card-verification.total-card-purchases-count.limit", Integer.class).orElse(50);
        AUTO_CARD_VERIFICATION_TRANSACTION_COUNT_PERIOD = pf.get("auto-card-verification.transaction-count-period", Duration.class).orElse(Duration.ofDays(120));
        AUTO_CARD_VERIFICATION_TOTAL_PURCHASE_PERIOD = pf.get("auto-card-verification.total-purchase-period", Duration.class).orElse(Duration.ofDays(120));
        //
        RISK_BASED_AUTO_CARD_VERIFICATION_ENABLED = pf.get("risk-based-auto-card-verification.enabled", boolean.class).orElse(false);;
        RISK_BASED_AUTO_CARD_VERIFICATION_EXCLUDED_VIP_LEVELS = pf.listOfStrings("risk-based-auto-card-verification.excluded-vip-levels").orElse(List.of());
        RISK_BASED_AUTO_CARD_VERIFICATION_PURCHASE_COUNT_PERIOD = pf.get("risk-based-auto-card-verification.card-purchases-for-period.period", Duration.class).orElse(Duration.ofDays(30));
        RISK_BASED_AUTO_CARD_VERIFICATION_TOTAL_PURCHASE_AMOUNT_LIMIT = pf.get("risk-based-auto-card-verification.total-card-purchases-amount.limit", BigDecimal.class).orElse(new BigDecimal(1254));
        RISK_BASED_AUTO_CARD_VERIFICATION_USERS_APPLIED_FROM = pf.getScoped("risk-based-auto-card-verification.users-applied-from", Integer.class, 0);
        RISK_BASED_AUTO_CARD_VERIFICATION_USERS_APPLIED_TO = pf.getScoped("risk-based-auto-card-verification.users-applied-to", Integer.class, 0);
        RISK_BASED_AUTO_CARD_VERIFICATION_ENABLE_FOR_ADMIN = pf.getScoped("risk-based-auto-card-verification.enable-for-admin", Boolean.class, false);
        RISK_BASED_AUTO_CARD_VERIFICATION_APPLICABLE_FRACTION = pf.getScoped("risk-based-auto-card-verification.applicable-fraction", Integer.class, 0);
        //
        // ~ ClickHouse
        //
        EVENT_STREAMING_CLICKHOUSE_ENABLED = pf.get("event-streaming.clickhouse.enabled", boolean.class).orElse(false);

        // Background request consumer
        BACKGROUND_REQUEST_CONSUMER_ENABLED = pf.get("background-request-consumer.enabled", boolean.class).orElse(false);
        BACKGROUND_REQUEST_CONSUMER_RETRY_ENABLED = pf.get("background-request-consumer.retry.enabled", boolean.class).orElse(false);

        FRAUD_TEMPORAL_ENABLED = pf.getScoped("fraud.temporal.enabled", boolean.class, false);
        PAYMENT_TEMPORAL_ENABLED = pf.getScoped("payment.temporal.enabled", boolean.class, true);
    }
}
