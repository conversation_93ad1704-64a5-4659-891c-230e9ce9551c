package fraud.worker.di;

import java.time.Duration;

import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.turbospaces.api.MutableNonPersistentReplyTopic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.rpc.QueuePostTemplate;
import com.turbospaces.temporal.CustomDataConverter;
import com.turbospaces.temporal.TemporalDiModule;

import api.v1.ApiFactory;
import fraud.api.DefaultFraudServiceApi;
import fraud.api.FraudServiceApi;
import fraud.api.temporal.FraudTemporalClientFactoryBean;
import fraud.api.temporal.FraudWorkflowPostTemplate;
import fraud.worker.FraudWorkerServerProperties;
import fraud.worker.FraudWorkerTopics;
import io.micrometer.core.instrument.MeterRegistry;
import payment.api.DefaultPaymentServiceApi;
import payment.api.PaymentServiceApi;
import payment.api.temporal.PaymentTemporalClientFactoryBean;
import payment.api.temporal.PaymentWorkflowPostTemplate;
import uam.api.DefaultUamServiceApi;
import uam.api.UamServiceApi;

@Import({
        TemporalDiModule.class,
})
@Configuration
public class FraudWorkerServiceApiDiModule {
    @Bean
    public UamServiceApi uamServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultUamServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, FraudWorkerTopics.RESP));
    }

    @Bean
    public PaymentWorkflowPostTemplate paymentWorkflowPostTemplate(
            FraudWorkerServerProperties props,
            ApiFactory apiFactory,
            PaymentTemporalClientFactoryBean clientFactory) throws Exception {
        return new PaymentWorkflowPostTemplate(props, apiFactory, props.REQUEST_REPLY_TIMEOUT.map(Duration::ofSeconds), clientFactory);
    }

    @Bean
    public PaymentTemporalClientFactoryBean paymentTemporalClientFactory(ApplicationProperties props, DynamicCloud cloud, MeterRegistry meterRegistry, CustomDataConverter customDataConverter) {
        return new PaymentTemporalClientFactoryBean(props, cloud, meterRegistry, customDataConverter);
    }

    @Bean
    public PaymentServiceApi paymentServiceApi(FraudWorkerServerProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory, PaymentWorkflowPostTemplate paymentWorkflowPostTemplate) {
        return new DefaultPaymentServiceApi(props, postTemplate, apiFactory,
                new MutableNonPersistentReplyTopic(props, cloud, FraudWorkerTopics.RESP),
                props.PAYMENT_TEMPORAL_ENABLED,
                paymentWorkflowPostTemplate
        );
    }
    @Bean
    public FraudTemporalClientFactoryBean fraudTemporalClientFactory(ApplicationProperties props, DynamicCloud cloud, MeterRegistry meterRegistry, CustomDataConverter customDataConverter) {
        return new FraudTemporalClientFactoryBean(props, cloud, meterRegistry, customDataConverter);
    }
    @Bean
    public FraudWorkflowPostTemplate fraudWorkflowPostTemplate(
            FraudWorkerServerProperties props,
            ApiFactory apiFactory,
            FraudTemporalClientFactoryBean clientFactory) throws Exception {
        return new FraudWorkflowPostTemplate(
                props,
                apiFactory,
                props.TEMPORAL_REQUEST_REPLY_TIMEOUT,
                clientFactory
        );
    }
    @Bean
    public FraudServiceApi fraudServiceApi(
            FraudWorkerServerProperties props,
            DynamicCloud cloud,
            QueuePostTemplate<?> postTemplate,
            ApiFactory apiFactory,
            FraudWorkflowPostTemplate fraudWorkflowPostTemplate) {
        return new DefaultFraudServiceApi(
            props,
            postTemplate,
            apiFactory,
            new MutableNonPersistentReplyTopic(props, cloud, FraudWorkerTopics.RESP),
            props.FRAUD_TEMPORAL_ENABLED,
                fraudWorkflowPostTemplate
        );
    }
}
