package fraud.worker.di;

import bi.zendesk.ZendeskConfigService;
import bi.zendesk.ZendeskRateLimiter;
import bi.zendesk.ZendeskTicketsRateLimiter;
import fraud.worker.config.DefaultZendeskConfigService;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;

import bi.zendesk.ZendeskClient;
import bi.zendesk.ZendeskJaxRsClient;
import fraud.worker.FraudWorkerServerProperties;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;

@Configuration
public class ZendeskDiModule {
    @Bean
    public ZendeskJaxRsClient zendeskApiClient(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            CloseableHttpClient httpClient,
            CommonObjectMapper mapper) {
        return new ZendeskJaxRsClient(props, meterRegistry, rateLimiterRegistry, httpClient, mapper);
    }

    @Bean
    public ZendeskConfigService zendeskConfigService(FraudWorkerServerProperties props) {
        return new DefaultZendeskConfigService(props);
    }

    @Bean
    public ZendeskClient zendeskClient(ZendeskJaxRsClient zendeskJaxRsClient, ZendeskConfigService zendeskConfigService) {
        return new ZendeskClient(zendeskJaxRsClient, zendeskConfigService);
    }

    @Bean
    public ZendeskRateLimiter zendeskRateLimiter(FraudWorkerServerProperties props, RateLimiterRegistry registry) {
        var config = RateLimiterConfig.custom()
                .limitRefreshPeriod(props.ZENDESK_EXPORT_RATE_LIMIT_PERIOD.get())
                .limitForPeriod(props.ZENDESK_EXPORT_RATE_LIMIT_COUNT.get())
                .timeoutDuration(props.ZENDESK_EXPORT_RATE_LIMIT_TIMEOUT.get())
                .build();
        return new ZendeskRateLimiter(registry, config);
    }

    @Bean
    public ZendeskTicketsRateLimiter zendeskTicketsRateLimiter(FraudWorkerServerProperties props, RateLimiterRegistry registry) {
        var config = RateLimiterConfig.custom()
                .limitRefreshPeriod(props.ZENDESK_TICKETS_RATE_LIMIT_PERIOD.get())
                .limitForPeriod(props.ZENDESK_TICKETS_RATE_LIMIT_COUNT.get())
                .timeoutDuration(props.ZENDESK_TICKETS_RATE_LIMIT_TIMEOUT.get())
                .build();
        return new ZendeskTicketsRateLimiter(registry, config);
    }
}
