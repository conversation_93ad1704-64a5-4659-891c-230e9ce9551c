package fraud.worker.tools;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.ebean.GeneratePostgresMigration;

import fraud.worker.FraudWorkerEntities;
import fraud.worker.FraudWorkerModelProperties;
import fraud.worker.FraudWorkerOnlyEntities;

public class GenerateFraudWorkerPostgresMigration {
    public static void main(String... args) throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        FraudWorkerModelProperties props = new FraudWorkerModelProperties(cfg.factory());

        cfg.loadLocalDevProperties();
        cfg.setDefaultProperty(props.APP_DB_MIGRATION_PATH.getKey(), "db/fraud-worker-migration");

        GeneratePostgresMigration.generate(new FraudWorkerOnlyEntities(), props);
    }
}
