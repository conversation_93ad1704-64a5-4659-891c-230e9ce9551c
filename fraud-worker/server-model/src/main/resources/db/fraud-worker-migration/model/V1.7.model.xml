<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createSchema name="back_office"/>
        <alterColumn columnName="account_id" tableName="fraud.account_card_verification_info" references="fraud.accounts.id" foreignKeyName="fk_account_card_verification_info_account_id" dropForeignKey="fk_account_card_verification_info_account_id"/>
        <addColumn tableName="uam.account_daily_bonuses">
            <column name="initial_segment" type="varchar(20)"/>
            <column name="segment" type="varchar(20)"/>
            <column name="segment_updated_at" type="timestamp"/>
        </addColumn>
        <alterColumn columnName="account_id" tableName="fraud.account_fraud_info" references="fraud.accounts.id" foreignKeyName="fk_account_fraud_info_account_id" dropForeignKey="fk_account_fraud_info_account_id"/>
        <addColumn tableName="fraud.account_fraud_info">
            <column name="sign_up_device_session" type="varchar"/>
        </addColumn>
        <addColumn tableName="uam.account_invitation_info">
            <column name="confirmed_gc" type="decimal(16,2)"/>
            <column name="confirmed_sc" type="decimal(16,2)"/>
            <column name="pending_gc_lvl1" type="decimal(16,2)"/>
            <column name="pending_sc_lvl1" type="decimal(16,2)"/>
            <column name="pending_gc_lvl2" type="decimal(16,2)"/>
            <column name="pending_sc_lvl2" type="decimal(16,2)"/>
        </addColumn>
        <addColumn tableName="payment.account_offer_reward_log">
            <column name="available_from" type="timestamp"/>
        </addColumn>
        <alterColumn columnName="account_id" tableName="fraud.account_otp_limit" references="fraud.accounts.id" foreignKeyName="fk_account_otp_limit_account_id" dropForeignKey="fk_account_otp_limit_account_id"/>
        <alterColumn columnName="integration_type" tableName="payment.account_payment_aggregation" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_account_payment_aggregation_integration_type"/>
        <addHistoryTable baseTable="payment.account_payment_settings"/>
        <addColumn tableName="payment.account_payment_settings" withHistory="true">
            <column name="modified_by" type="varchar"/>
        </addColumn>
        <addColumn tableName="payment.account_provider_black_list">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <alterColumn columnName="tag" tableName="uam.account_tags" checkConstraint="check ( tag in ('high_fraud_score','monitor','complaint_received','refund_made','big_winner','refund_next_pending_rd','part_of_the_promotion_list','removed_from_the_promotion_list','pm_verified','refund_request','rd_fail_issues','concerning_comments_received','purchase_rec_done','high_turnover','high_rd_cancellation','high_purchase_in_24h','high_purchase_in_7d','free_sc_misuse','legal_threat','gameplay_adv','promo_adv','amoe_adv','confirmed_adv','advantage_play','risky_t','risky_s','payment_plan','nsc','charcgeback_monitoring','suspicious_behaviour','legal_arbitration','top_spender','docs_requested_manual_check','docs_submitted_manual_check'))" checkConstraintName="ck_account_tags_tag"/>
        <alterColumn columnName="type" tableName="payment.withdraw_methods" checkConstraint="check ( type in ('ach','sci','skrill','skrill_ach','nuvei_mazooma_ach','masspay_ach','prizeout','trustly','payper','standard_ach','crypto','orbital','airwallex_ach','aeropay_ach','aeropay','checkbook_ach','paynearme_ach','nsc'))" checkConstraintName="ck_withdraw_methods_type"/>
        <alterColumn columnName="account_id" tableName="fraud.aml_check" withHistory="true" references="fraud.accounts.id" foreignKeyName="fk_aml_check_account_id" dropForeignKey="fk_aml_check_account_id"/>
        <createTable name="back_office.approval" pkName="pk_approval">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="operation_type" type="varchar" notnull="true"/>
            <column name="request_json" type="jsonb" notnull="true"/>
            <column name="requested_by" type="varchar" notnull="true"/>
            <column name="reviewers" type="varchar[]" notnull="true"/>
            <column name="approved_by" type="varchar[]"/>
            <column name="rejected_by" type="varchar[]"/>
            <column name="status" type="varchar(8)" notnull="true" checkConstraint="check ( status in ('PENDING','APPROVED','REJECTED','EXECUTED','FAILED'))" checkConstraintName="ck_approval_status"/>
            <column name="comment" type="varchar"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_approval_brand_id" foreignKeyIndex="ix_approval_brand_id"/>
            <column name="account_count" type="bigint"/>
            <column name="change_summary" type="varchar"/>
            <column name="expire_at" type="timestamp"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <alterColumn columnName="entity_type" tableName="uam.backoffice_events" checkConstraint="check ( entity_type in ('product','product_category','brand_banner_template','brand_daily_bonus_template','brand_email_template','brand_offer_template','brand_social_media_reward','brand_reward_creditor','brand_reward_campaign','brand_legal_rule','brand_otp_trigger_rules','brand_free_spins_campaign','game_lobby_feature_setting','game_lobby_group','game_lobby_group_assign','game_lobby_group_unassign','game_lobby_game_theme','brand_game_allowance_by_location','brand_game_allowance_by_registration','brand_mission_update','brand_mission_reward_update','brand_mission_freespin_reward_update','brand_mission_step_update','brand_mission_step_reward_update','brand_mission_step_freespin_reward_update','brand_mission_step_order_update','mass_account_status','mass_account_change','mass_account_tag','mass_account_lock','mass_account_unlock','mass_account_segmentation','mass_email_send','mass_reward','mass_account_comment','mass_account_free_spin','supplier','rnd_audience','rnd_discounted_line','rnd_pick_em_pack_config','rnd_pick_em_options_order','rnd_reward_bundle','rnd_reward_template','rnd_trading_alert_config','rnd_issue_user_reward','rnd_server_config_modify','rnd_leagues','rnd_pick_em_markets','rnd_pick_em_user_limits','rnd_player','rnd_teams','rnd_unrecognized','rnd_user_segment','brand_wheel_of_winners','brand_feature_settings','unknown'))" checkConstraintName="ck_backoffice_events_entity_type"/>
        <addColumn tableName="payment.blacklist" withHistory="true">
            <column name="modified_by" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.bonus_rewards">
            <column name="reference" type="varchar(36)"/>
        </addColumn>
        <createTable name="uam.feature_settings_brand" pkName="pk_feature_settings_brand">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="country_code" type="varchar(2)"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_feature_settings_brand_brand_id" foreignKeyIndex="ix_feature_settings_brand_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="feature_value" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_feature_settings_brand_brand_id_code_country_code" columnNames="brand_id,code,country_code" oneToOne="false" nullableColumns="brand_id,country_code"/>
        </createTable>
        <alterColumn columnName="brand_id" tableName="fraud.brand_settings" currentType="integer" notnull="false" currentNotnull="true" dropUnique="uq_brand_settings_brand_id" dropForeignKey="fk_brand_settings_brand_id"/>
        <addColumn tableName="fraud.brand_settings">
            <column name="fraud_brand_id" type="integer" notnull="true" uniqueOneToOne="uq_brand_settings_fraud_brand_id" references="fraud.brands.id" foreignKeyName="fk_brand_settings_fraud_brand_id"/>
        </addColumn>
        <addColumn tableName="payment.card_bin_info" withHistory="true">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <addColumn tableName="payment.card_payment_method" withHistory="true">
            <column name="payment_account_reference" type="varchar"/>
        </addColumn>
        <alterColumn columnName="account_id" tableName="fraud.card_verification_meta_info" withHistory="true" references="fraud.accounts.id" foreignKeyName="fk_card_verification_meta_info_account_id" foreignKeyIndex="ix_card_verification_meta_info_account_id" dropForeignKey="fk_card_verification_meta_info_account_id" dropForeignKeyIndex="ix_card_verification_meta_info_account_id"/>
        <addColumn tableName="fraud.card_verification_meta_info" withHistory="true">
            <column name="automated_trigger_at" type="timestamp"/>
        </addColumn>
        <alterColumn columnName="account_id" tableName="fraud.card_verification_request" withHistory="true" references="fraud.accounts.id" foreignKeyName="fk_card_verification_request_account_id" foreignKeyIndex="ix_card_verification_request_account_id" dropForeignKey="fk_card_verification_request_account_id" dropForeignKeyIndex="ix_card_verification_request_account_id"/>
        <addColumn tableName="fraud.card_verification_request" withHistory="true">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <addColumn tableName="payment.chargeback">
            <column name="order_at" type="date"/>
        </addColumn>
        <alterColumn columnName="integration_type" tableName="payment.chargeback_config" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_chargeback_config_integration_type"/>
        <addColumn tableName="payment.chargeback_config">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <addColumn tableName="payment.chargeback_history">
            <column name="reason_code" type="varchar"/>
            <column name="order_at" type="date"/>
        </addColumn>
        <alterColumn columnName="account_id" tableName="fraud.chat_events" references="fraud.accounts.id" foreignKeyName="fk_chat_events_account_id" foreignKeyIndex="ix_chat_events_account_id" dropForeignKey="fk_chat_events_account_id" dropForeignKeyIndex="ix_chat_events_account_id"/>
        <addColumn tableName="uam.countries">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <alterColumn columnName="currency" tableName="payment.crypto_payment_methods" checkConstraint="check ( currency in ('ltc','usdt','usdc','eth','bch','btc','tst','cro','ape','uni','pol','xrp'))" checkConstraintName="ck_crypto_payment_methods_currency"/>
        <alterColumn columnName="network" tableName="payment.crypto_payment_methods" checkConstraint="check ( network in ('trx','sol','eth','bnb','btc','ltc','polygon','bch','avax','ton','teth'))" checkConstraintName="ck_crypto_payment_methods_network"/>
        <alterColumn columnName="source_currency" tableName="payment.crypto_purchase_details" checkConstraint="check ( source_currency in ('ltc','usdt','usdc','eth','bch','btc','tst','cro','ape','uni','pol','xrp'))" checkConstraintName="ck_crypto_purchase_details_source_currency"/>
        <alterColumn columnName="source_network" tableName="payment.crypto_purchase_details" checkConstraint="check ( source_network in ('trx','sol','eth','bnb','btc','ltc','polygon','bch','avax','ton','teth'))" checkConstraintName="ck_crypto_purchase_details_source_network"/>
        <alterColumn columnName="to_currency" tableName="payment.crypto_rates" checkConstraint="check ( to_currency in ('ltc','usdt','usdc','eth','bch','btc','tst','cro','ape','uni','pol','xrp'))" checkConstraintName="ck_crypto_rates_to_currency"/>
        <alterColumn columnName="target_currency" tableName="payment.crypto_withdrawal_details" checkConstraint="check ( target_currency in ('ltc','usdt','usdc','eth','bch','btc','tst','cro','ape','uni','pol','xrp'))" checkConstraintName="ck_crypto_withdrawal_details_target_currency"/>
        <alterColumn columnName="target_network" tableName="payment.crypto_withdrawal_details" checkConstraint="check ( target_network in ('trx','sol','eth','bnb','btc','ltc','polygon','bch','avax','ton','teth'))" checkConstraintName="ck_crypto_withdrawal_details_target_network"/>
        <addColumn tableName="uam.daily_bonus_templates">
            <column name="segment" type="varchar(20)"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_daily_bonus_templates_brand_id_code_country_code" tableName="uam.daily_bonus_templates" columnNames="DROP CONSTRAINT" nullableColumns="brand_id,country_code"/>
        <addUniqueConstraint constraintName="uq_daily_bonus_templates_brand_id_country_code_segment_code" tableName="uam.daily_bonus_templates" columnNames="brand_id,country_code,segment,code" oneToOne="false" nullableColumns="brand_id,country_code,segment"/>
        <addHistoryTable baseTable="fraud.doc_upload_requests"/>
        <alterColumn columnName="account_id" tableName="fraud.doc_upload_requests" references="fraud.accounts.id" foreignKeyName="fk_doc_upload_requests_account_id" foreignKeyIndex="ix_doc_upload_requests_account_id" dropForeignKey="fk_doc_upload_requests_account_id" dropForeignKeyIndex="ix_doc_upload_requests_account_id"/>
        <alterColumn columnName="status" tableName="fraud.doc_upload_requests" checkConstraint="check ( status in ('initiated','session_expired','failed','pending_review','approved','rejected','in_review'))" checkConstraintName="ck_doc_upload_requests_status"/>
        <addColumn tableName="fraud.doc_upload_requests" withHistory="true">
            <column name="uploaded_at" type="timestamp"/>
            <column name="in_review_at" type="timestamp"/>
            <column name="final_actioned_at" type="timestamp"/>
            <column name="agent_name" type="varchar"/>
            <column name="comment" type="varchar"/>
            <column name="custom_doc_type" type="varchar"/>
        </addColumn>
        <alterColumn columnName="brand_id" tableName="payment.dynamic_secure_3d_checks" currentType="integer" notnull="false" currentNotnull="true"/>
        <createTable name="fraud.email_log" pkName="pk_email_log">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="identifier" type="varchar" notnull="true"/>
            <column name="source" type="varchar(10)" notnull="true" checkConstraint="check ( source in ('tc40','chargeback'))" checkConstraintName="ck_email_log_source"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_email_log_source_identifier" columnNames="source,identifier" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="fraud.email_otp_request" identityType="identity" pkName="pk_email_otp_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="fraud.accounts.id" foreignKeyName="fk_email_otp_request_account_id"/>
            <column name="otp" type="varchar" notnull="true"/>
            <column name="verified" type="boolean" defaultValue="false" notnull="true"/>
            <column name="expire_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.external_reward" pkName="pk_external_reward">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="reward_code" type="varchar" notnull="true"/>
            <column name="source" type="varchar(26)" notnull="true" checkConstraint="check ( source in ('UNKNOWN_SOURCE','QUEST_SOURCE','RANDOM_REWARD_SOURCE','INTERNAL_SOURCE','PLATFORM_TOURNAMENT_SOURCE','CHAIN_OFFERS_REWARD_SOURCE','UNRECOGNIZED'))" checkConstraintName="ck_external_reward_source"/>
            <column name="status" type="varchar(25)" notnull="true" checkConstraint="check ( status in ('UNKNOWN_REWARD_ASSIGNMENT','REWARD_ASSIGNED','REWARD_NOT_APPLICABLE','REWARD_ASSIGNMENT_FAILED','UNRECOGNIZED'))" checkConstraintName="ck_external_reward_status"/>
            <column name="bonus_amount" type="decimal"/>
            <column name="order_id" type="bigint" references="payment.payment_orders.id" foreignKeyName="fk_external_reward_order_id" foreignKeyIndex="ix_external_reward_order_id"/>
            <column name="order_at" type="date"/>
        </createTable>
        <createTable name="core.features" pkName="pk_features">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_features_brand_id" foreignKeyIndex="ix_features_brand_id"/>
            <column name="enabled_for_web" type="boolean" defaultValue="false" notnull="true"/>
            <column name="enabled_for_ios" type="boolean" defaultValue="false" notnull="true"/>
            <column name="enabled_for_android" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_features_brand_id_name" columnNames="brand_id,name" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="fraud.accounts" pkName="pk_accounts">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="hash" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="fraud.brands.id" foreignKeyName="fk_accounts_brand_id" foreignKeyIndex="ix_accounts_brand_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <addColumn tableName="fraud.fraud_applied_rules">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <createTable name="fraud.brands" pkName="pk_brands">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true" unique="uq_brands_name"/>
            <column name="fraud_lock_threshold" type="integer"/>
            <column name="fraud_suspect_threshold" type="integer"/>
            <column name="phone_expiration" type="integer"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <alterColumn columnName="account_id" tableName="fraud.fraud_response" references="fraud.accounts.id" foreignKeyName="fk_fraud_response_account_id" foreignKeyIndex="ix_fraud_response_account_id" dropForeignKey="fk_fraud_response_account_id" dropForeignKeyIndex="ix_fraud_response_account_id"/>
        <addColumn tableName="fraud.fraud_rules">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.giveaway_subscriber">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.gps_history">
            <column name="payload" type="varchar(4000)"/>
            <column name="platform" type="varchar(7)" checkConstraint="check ( platform in ('web','android','ios'))" checkConstraintName="ck_gps_history_platform"/>
            <column name="app_name" type="varchar"/>
        </addColumn>
        <alterColumn columnName="url" tableName="uam.homepage_features" currentType="varchar" notnull="false" currentNotnull="true"/>
        <addColumn tableName="uam.homepage_features">
            <column name="feature_type" type="varchar(10)" notnull="true" checkConstraint="check ( feature_type in ('icon','background','widget','settings'))" checkConstraintName="ck_homepage_features_feature_type"/>
            <column name="url_mobile" type="varchar"/>
            <column name="gold" type="boolean" defaultValue="false" notnull="true"/>
            <column name="logged_out" type="boolean" defaultValue="false" notnull="true"/>
            <column name="animation" type="varchar"/>
            <column name="placement" type="varchar"/>
            <column name="layout" type="varchar"/>
        </addColumn>
        <alterColumn columnName="account_id" tableName="uam.kyc_verification_requests" references="fraud.accounts.id" foreignKeyName="fk_kyc_verification_requests_account_id" foreignKeyIndex="ix_kyc_verification_requests_account_id" dropForeignKey="fk_kyc_verification_requests_account_id" dropForeignKeyIndex="ix_kyc_verification_requests_account_id"/>
        <addColumn tableName="payment.inbox_notifications" withHistory="true">
            <column name="crypto_payment_order_at" type="date"/>
        </addColumn>
        <alterColumn columnName="account_id" tableName="fraud.inbox_notifications" withHistory="true" references="fraud.accounts.id" foreignKeyName="fk_inbox_notifications_account_id" foreignKeyOnDelete="RESTRICT" foreignKeyOnUpdate="RESTRICT" dropForeignKey="fk_inbox_notifications_account_id"/>
        <addColumn tableName="fraud.kyc_risk_spend_policy">
            <column name="fraud_brand_id" type="integer" references="fraud.brands.id" foreignKeyName="fk_kyc_risk_spend_policy_fraud_brand_id" foreignKeyIndex="ix_kyc_risk_spend_policy_fraud_brand_id"/>
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <addColumn tableName="fraud.kyc_risk_spend_policy_history">
            <column name="fraud_brand_id" type="integer" references="fraud.brands.id" foreignKeyName="fk_kyc_risk_spend_policy_history_fraud_brand_id" foreignKeyIndex="ix_kyc_risk_spend_policy_history_fraud_brand_id"/>
        </addColumn>
        <addColumn tableName="uam.missions">
            <column name="priority" type="integer"/>
        </addColumn>
        <addColumn tableName="payment.offer_templates" withHistory="true">
            <column name="welcome_offer_type" type="varchar(13)" notnull="true" checkConstraint="check ( welcome_offer_type in ('none','default','utm','affiliate','referral_code','raf'))" checkConstraintName="ck_offer_templates_welcome_offer_type"/>
        </addColumn>
        <alterColumn columnName="status" tableName="payment.offer_template_change_log" checkConstraint="check ( status in ('sent_for_approval','auto_approved','approved','rejected','draft'))" checkConstraintName="ck_offer_template_change_log_status"/>
        <alterColumn columnName="account_id" tableName="fraud.otp_trigger_audit" withHistory="true" references="fraud.accounts.id" foreignKeyName="fk_otp_trigger_audit_account_id" dropForeignKey="fk_otp_trigger_audit_account_id"/>
        <alterColumn columnName="otp_trigger_rule" tableName="fraud.otp_trigger_audit" withHistory="true" checkConstraint="check ( otp_trigger_rule in ('signed_up_trigger','bonus_trigger','utm_source_trigger','set_up_notifications_trigger','bonus_acceptance_trigger','account_closure_trigger'))" checkConstraintName="ck_otp_trigger_audit_otp_trigger_rule"/>
        <createTable name="fraud.otp_trigger_events" pkName="pk_otp_trigger_events">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="fraud.accounts.id" foreignKeyName="fk_otp_trigger_events_account_id" foreignKeyIndex="ix_otp_trigger_events_account_id"/>
            <column name="trigger" type="varchar(28)" notnull="true" checkConstraint="check ( trigger in ('signed_up_trigger','bonus_trigger','utm_source_trigger','set_up_notifications_trigger','bonus_acceptance_trigger','account_closure_trigger'))" checkConstraintName="ck_otp_trigger_events_trigger"/>
            <column name="source" type="varchar(18)" checkConstraint="check ( source in ('my_profile','inbox_notification'))" checkConstraintName="ck_otp_trigger_events_source"/>
            <column name="at" type="date" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <alterColumn columnName="brand_id" tableName="fraud.otp_trigger_rules" currentType="integer" notnull="false" currentNotnull="true" dropForeignKey="fk_otp_trigger_rules_brand_id" dropForeignKeyIndex="ix_otp_trigger_rules_brand_id"/>
        <addColumn tableName="fraud.otp_trigger_rules">
            <column name="fraud_brand_id" type="integer" notnull="true" references="fraud.brands.id" foreignKeyName="fk_otp_trigger_rules_fraud_brand_id" foreignKeyIndex="ix_otp_trigger_rules_fraud_brand_id"/>
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <alterColumn columnName="integration_type" tableName="payment.providers" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_providers_integration_type"/>
        <addColumn tableName="payment.payment_orders_bonus_abuse">
            <column name="order_at" type="date"/>
        </addColumn>
        <addColumn tableName="payment.payment_order_meta_info">
            <column name="order_at" type="date"/>
        </addColumn>
        <alterColumn columnName="account_id" tableName="fraud.phone_number_request" withHistory="true" references="fraud.accounts.id" foreignKeyName="fk_phone_number_request_account_id" foreignKeyIndex="ix_phone_number_request_account_id" dropForeignKey="fk_phone_number_request_account_id" dropForeignKeyIndex="ix_phone_number_request_account_id"/>
        <addColumn tableName="fraud.phone_number_request" withHistory="true">
            <column name="source" type="varchar(18)" checkConstraint="check ( source in ('my_profile','inbox_notification'))" checkConstraintName="ck_phone_number_request_source"/>
        </addColumn>
        <addColumn tableName="uam.promotions">
            <column name="image" type="varchar(4000)"/>
            <column name="tag" type="varchar"/>
            <column name="tag2" type="varchar"/>
            <column name="important" type="boolean" defaultValue="false" notnull="true"/>
            <column name="shimmer" type="boolean" defaultValue="false" notnull="true"/>
            <column name="style" type="varchar"/>
        </addColumn>
        <alterColumn columnName="integration_type" tableName="payment.provider_redeem_limit_policies" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_provider_redeem_limit_policies_integration_type"/>
        <addColumn tableName="payment.refund_history">
            <column name="order_at" type="date"/>
        </addColumn>
        <alterColumn columnName="report_type" tableName="fraud.report_log" checkConstraint="check ( report_type in ('worldpay_chargebacks','worldpay_reconciliation','worldpay_tc40','paynearme_tc40','fiserv_chargebacks','fiserv_tc40','emerchantpay_reconciliation','fiserv_arn','rapyd_tc40','checkout_tc40','rapyd_reconciliation'))" checkConstraintName="ck_report_log_report_type"/>
        <alterColumn columnName="payment_method_type" tableName="payment.rescue_purchase_providers_settings" checkConstraint="check ( payment_method_type in ('SpreedlyGateway','SpreedlyGatewayApplePay','SpreedlyGatewayGooglePay','PayWithMyBank','Trustly','FiservCard','FiservGooglePay','FiservApplePay','NuveiGateway','Skrill','AppleInApp','AndroidInApp','Payper','Crypto','AeroPay'))" checkConstraintName="ck_rescue_purchase_providers_settings_payment_method_type"/>
        <addColumn tableName="payment.rescue_purchase_providers_settings">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <alterColumn columnName="source_system" tableName="uam.reward_creditors" checkConstraint="check ( source_system in ('ysi','b2','bloomreach','silver_social','rum','pmsg','fullstop','pca'))" checkConstraintName="ck_reward_creditors_source_system"/>
        <alterColumn columnName="integration_type" tableName="payment.routing_error_config" withHistory="true" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_routing_error_config_integration_type"/>
        <addColumn tableName="payment.transaction_limit">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <alterColumn columnName="integration_type" tableName="payment.volume_allocation_config" withHistory="true" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_volume_allocation_config_integration_type"/>
        <alterColumn columnName="provider" tableName="payment.volume_allocation_config" withHistory="true" checkConstraint="check ( provider in ('skrill','trustly','nuvei_mazooma_ach','fiserv','spreedly','spreedly_rapyd','spreedly_rapyd_2','spreedly_rapyd_3','spreedly_rapyd_4','spreedly_rapyd_5','spreedly_fiserv','spreedly_fiserv_2','spreedly_fiserv_3','spreedly_fiserv_4','spreedly_fiserv_5','spreedly_fiserv_6','spreedly_emerchantpay','spreedly_emerchantpay_2','spreedly_emerchantpay_3','spreedly_emerchantpay_4','spreedly_emerchantpay_5','spreedly_emerchantpay_6','fiserv_google_pay','fiserv_apple_pay','spreedly_apple_pay','spreedly_fiserv_apple_pay','spreedly_fiserv_apple_pay_2','spreedly_fiserv_apple_pay_3','spreedly_fiserv_apple_pay_4','spreedly_fiserv_apple_pay_5','spreedly_fiserv_apple_pay_6','spreedly_google_pay','spreedly_fiserv_google_pay','spreedly_fiserv_google_pay_2','spreedly_fiserv_google_pay_3','spreedly_fiserv_google_pay_4','spreedly_fiserv_google_pay_5','spreedly_fiserv_google_pay_6','spreedly_test_gateway','spreedly_test_gateway_2','spreedly_test_gateway_3','spreedly_worldpay','spreedly_checkout','spreedly_checkout_apple_pay','spreedly_checkout_google_pay','spreedly_worldpay_2','spreedly_worldpay_3','spreedly_worldpay_4','spreedly_worldpay_apple_pay','spreedly_worldpay_apple_pay_2','spreedly_worldpay_apple_pay_3','spreedly_worldpay_apple_pay_4','spreedly_worldpay_google_pay','spreedly_worldpay_google_pay_2','spreedly_worldpay_google_pay_3','spreedly_worldpay_google_pay_4','spreedly_emerchantpay_apple_pay','spreedly_emerchantpay_apple_pay_2','spreedly_emerchantpay_apple_pay_3','spreedly_emerchantpay_apple_pay_4','spreedly_emerchantpay_apple_pay_5','spreedly_emerchantpay_apple_pay_6','spreedly_emerchantpay_google_pay','spreedly_emerchantpay_google_pay_2','spreedly_emerchantpay_google_pay_3','spreedly_emerchantpay_google_pay_4','spreedly_emerchantpay_google_pay_5','spreedly_emerchantpay_google_pay_6','spreedly_nuvei','spreedly_nuvei_2','spreedly_paynearme','spreedly_paynearme_2','spreedly_paynearme_apple_pay','spreedly_paynearme_apple_pay_2','spreedly_paynearme_google_pay','spreedly_paynearme_google_pay_2','payper','apple_in_app','android_in_app','crypto','orbital','aeropay'))" checkConstraintName="ck_volume_allocation_config_provider"/>
        <addColumn tableName="payment.volume_allocation_config" withHistory="true">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <alterColumn columnName="type" tableName="uam.wallet_sessions" checkConstraint="check ( type in ('payment','withdraw','lotto','bingo','game','mini_game','internal','clean_up','gold_mine_jackpot','my_stash_jackpot','jackpot','cashback','quizbeat','randomizer','multi_play_game','play_together','tournament','chargeback','unknown'))" checkConstraintName="ck_wallet_sessions_type"/>
        <addColumn tableName="uam.wallet_sessions">
            <column name="platform" type="varchar(7)" checkConstraint="check ( platform in ('web','android','ios'))" checkConstraintName="ck_wallet_sessions_platform"/>
        </addColumn>
        <addColumn tableName="uam.wheel_of_winners_sections">
            <column name="type_id" type="bigint" references="uam.wheel_of_winners_sections_type.id" foreignKeyName="fk_wheel_of_winners_sections_type_id" foreignKeyIndex="ix_wheel_of_winners_sections_type_id"/>
            <column name="data" type="jsonb"/>
        </addColumn>
        <createTable name="uam.wheel_of_winners_sections_type" pkName="pk_wheel_of_winners_sections_type">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="label" type="varchar(128)" notnull="true"/>
            <column name="type" type="varchar(128)" notnull="true" checkConstraint="check ( type in ('gold_sweepstake_coins','free_spins','purchase_offer','prize_draw'))" checkConstraintName="ck_wheel_of_winners_sections_type_type" unique="uq_wheel_of_winners_sections_type_type"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <alterColumn columnName="account_id" tableName="fraud.zendesk_export_info" references="fraud.accounts.id" foreignKeyName="fk_zendesk_export_info_account_id" dropForeignKey="fk_zendesk_export_info_account_id"/>
        <alterForeignKey name="fk_account_notifications_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_notifications_account_id" tableName="uam.account_notifications"/>
        <alterForeignKey name="fk_account_notifications_notification_category_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_notifications_notification_category_id" tableName="uam.account_notifications"/>
        <alterForeignKey name="fk_account_notification_history_notification_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_notification_history_notification_id" tableName="uam.account_notification_history"/>
        <alterForeignKey name="fk_bonus_reward_account_notifications_account_notificatio_1" columnNames="DROP FOREIGN KEY" tableName="uam.bonus_reward_account_notifications"/>
        <alterForeignKey name="fk_bonus_reward_account_notifications_bonus_reward_id" columnNames="DROP FOREIGN KEY" tableName="uam.bonus_reward_account_notifications"/>
        <alterForeignKey name="fk_free_spin_account_notifications_account_notification_id" columnNames="DROP FOREIGN KEY" tableName="uam.free_spin_account_notifications"/>
        <alterForeignKey name="fk_free_spin_account_notifications_account_free_spin_id" columnNames="DROP FOREIGN KEY" tableName="uam.free_spin_account_notifications"/>
        <alterForeignKey name="fk_jackpot_account_free_contribution_notifications_jackpo_1" columnNames="DROP FOREIGN KEY" tableName="uam.jackpot_account_free_contribution_notifications"/>
        <alterForeignKey name="fk_jackpot_account_free_contribution_notifications_accoun_2" columnNames="DROP FOREIGN KEY" tableName="uam.jackpot_account_free_contribution_notifications"/>
        <alterForeignKey name="fk_notification_categories_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_notification_categories_brand_id" tableName="uam.notification_categories"/>
        <alterForeignKey name="fk_promotion_account_notifications_account_notification_id" columnNames="DROP FOREIGN KEY" tableName="uam.promotion_account_notifications"/>
        <alterForeignKey name="fk_promotion_account_notifications_account_promotion_id" columnNames="DROP FOREIGN KEY" tableName="uam.promotion_account_notifications"/>
        <alterForeignKey name="fk_account_preferences_account_notifications_account_noti_1" columnNames="DROP FOREIGN KEY" tableName="uam.account_preferences_account_notifications"/>
        <createIndex indexName="ix_feature_settings_brand_enabled" tableName="uam.feature_settings_brand" columns="enabled"/>
        <createIndex indexName="ix_feature_settings_brand_country_code" tableName="uam.feature_settings_brand" columns="country_code"/>
        <createIndex indexName="ix_email_otp_request_account_id" tableName="fraud.email_otp_request" columns="account_id"/>
        <dropIndex indexName="ix_kyc_risk_spend_policy_currency" tableName="fraud.kyc_risk_spend_policy" platforms="POSTGRES"/>
        <createIndex indexName="ix_kyc_risk_spend_policy_currency" tableName="fraud.kyc_risk_spend_policy" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_kyc_risk_spend_policy_currency ON fraud.kyc_risk_spend_policy (currency) WHERE fraud_brand_id is null" platforms="POSTGRES"/>
        <createIndex indexName="ix_kyc_risk_spend_policy_fraud_brand_id_currency" tableName="fraud.kyc_risk_spend_policy" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_kyc_risk_spend_policy_brand_id_currency ON fraud.kyc_risk_spend_policy (fraud_brand_id, currency) WHERE fraud_brand_id is not null" platforms="POSTGRES"/>
        <createIndex indexName="ix_missions_brand_id_priority" tableName="uam.missions" columns="brand_id,priority"/>
        <createIndex indexName="ix_missions_enabled_brand_priority_timerange" tableName="uam.missions" columns="" definition="create index ix_missions_enabled_brand_priority_timerange on uam.missions (brand_id, priority, start_at) where enabled = true and priority is not null" platforms="POSTGRES"/>
        <createIndex indexName="ix_otp_trigger_events_at" tableName="fraud.otp_trigger_events" columns="at"/>
        <createIndex indexName="ix_otp_trigger_rules_fraud_brand_id" tableName="fraud.otp_trigger_rules" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_otp_trigger_rules_fraud_brand_id ON fraud.otp_trigger_rules (fraud_brand_id) WHERE country is null" platforms="POSTGRES"/>
        <createIndex indexName="ix_otp_trigger_rules_fraud_brand_id_country" tableName="fraud.otp_trigger_rules" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_otp_trigger_rules_fraud_brand_id_country ON fraud.otp_trigger_rules (fraud_brand_id, country) WHERE country is not null" platforms="POSTGRES"/>
        <createIndex indexName="ix_payment_orders_account_id_internal_status_at" tableName="payment.payment_orders" columns="account_id,internal_status,at"/>
        <createIndex indexName="ix_payment_orders_chargeback_at" tableName="payment.payment_orders" columns="chargeback_at"/>
        <createIndex indexName="ix_payment_orders_tc40_received_at" tableName="payment.payment_orders" columns="tc40_received_at"/>
        <dropIndex indexName="ix_account_notifications_account_id_notification_category_1" tableName="uam.account_notifications"/>
        <dropIndex indexName="ix_bonus_reward_account_notifications_account_notificatio_1" tableName="uam.bonus_reward_account_notifications"/>
        <dropIndex indexName="ix_bonus_reward_account_notifications_bonus_reward_id" tableName="uam.bonus_reward_account_notifications"/>
        <dropIndex indexName="ix_free_spin_account_notifications_account_notification_id" tableName="uam.free_spin_account_notifications"/>
        <dropIndex indexName="ix_free_spin_account_notifications_account_free_spin_id" tableName="uam.free_spin_account_notifications"/>
        <dropIndex indexName="ix_jackpot_account_free_contribution_notifications_accoun_1" tableName="uam.jackpot_account_free_contribution_notifications"/>
        <dropIndex indexName="ix_jackpot_account_free_contribution_notifications_jackpo_2" tableName="uam.jackpot_account_free_contribution_notifications"/>
        <dropIndex indexName="ix_kyc_risk_spend_policy_brand_id_currency" tableName="fraud.kyc_risk_spend_policy" platforms="POSTGRES"/>
        <dropIndex indexName="ix_missions_brand_id" tableName="uam.missions"/>
        <dropIndex indexName="ix_otp_trigger_rules_brand_id" tableName="fraud.otp_trigger_rules" platforms="POSTGRES"/>
        <dropIndex indexName="ix_otp_trigger_rules_brand_id_country" tableName="fraud.otp_trigger_rules" platforms="POSTGRES"/>
        <dropIndex indexName="ix_promotion_account_notifications_account_notification_id" tableName="uam.promotion_account_notifications"/>
        <dropIndex indexName="ix_promotion_account_notifications_account_promotion_id" tableName="uam.promotion_account_notifications"/>
        <dropIndex indexName="ix_account_preferences_account_notifications_account_noti_1" tableName="uam.account_preferences_account_notifications"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropColumn columnName="attempts" tableName="uam.account_kyc_info"/>
        <dropColumn columnName="payment_webhook_url" tableName="fraud.brand_settings"/>
        <dropColumn columnName="widget" tableName="uam.homepage_features"/>
        <dropColumn columnName="brand_id" tableName="fraud.kyc_risk_spend_policy"/>
        <dropColumn columnName="brand_id" tableName="fraud.kyc_risk_spend_policy_history"/>
        <dropTable name="uam.account_notifications" sequenceCol="id"/>
        <dropTable name="uam.account_notification_history" sequenceCol="id"/>
        <dropTable name="uam.bonus_reward_account_notifications" sequenceCol="id"/>
        <dropTable name="uam.free_spin_account_notifications" sequenceCol="id"/>
        <dropTable name="uam.jackpot_account_free_contribution_notifications" sequenceCol="id"/>
        <dropTable name="uam.notification_categories" sequenceCol="id"/>
        <dropTable name="uam.promotion_account_notifications" sequenceCol="id"/>
        <dropTable name="uam.account_preferences_account_notifications" sequenceCol="id"/>
    </changeSet>
</migration>