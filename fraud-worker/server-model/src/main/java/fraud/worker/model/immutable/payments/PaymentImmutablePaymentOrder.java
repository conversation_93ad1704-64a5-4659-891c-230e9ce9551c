package fraud.worker.model.immutable.payments;

import api.v1.PlatformSpec;
import com.turbospaces.api.CommonModelContraints;
import common.CoreConstraints;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;
import org.apache.commons.lang3.builder.CompareToBuilder;
import payment.card.CardTransactionDetails;
import payment.model.AccountPaymentMethod;
import payment.model.ChargebackStatusSpec;
import payment.model.OrderStatusSpec;
import payment.model.PaymentOrderPK;
import payment.model.ScaAuthentication;
import uam.api.v1.PaymentProvider;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.Optional;
import java.util.UUID;

@Entity
@Table(
        name = "payment_orders",
        schema = Schemas.PAYMENT,
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"provider", "code"})
        })
@Index(columnNames = {"account_id", "success", "at"})
@EqualsAndHashCode(of = {"transactionId"})
@Getter
@Setter
public class PaymentImmutablePaymentOrder implements Comparable<PaymentImmutablePaymentOrder> {
    public static final String CREATED = "created";

    @EmbeddedId
    private PaymentImmutablePaymentOrderPK pk;

    @Column(nullable = false, unique = true)
    private UUID transactionId;

    @ManyToOne
    private PaymentImmutableProvider provider;

    @Column(nullable = false, name = "provider")
    private String providerName;

    @Column(nullable = false)
    private String code;

    @Column(nullable = false, unique = true)
    private String orderSn;

    @Column
    @Index
    private String sourceId;

    @ManyToOne(optional = false)
    private PaymentImmutableAccount account;

    @Column(nullable = false)
    private boolean sweepstake;

    @Column(nullable = false, length = CoreConstraints.CURRENCY_CODE)
    private String currency;

    /* amount of order in account currency, calculated by FX rate */
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE, nullable = false)
    private BigDecimal amount;

    @Column
    @Deprecated(forRemoval = true)
    private String status;

    @Column
    private Integer fraudScore;

    @Column
    private String fraudRequestId;

    @Column
    private boolean success;

    @ManyToOne(cascade = CascadeType.ALL)
    private ScaAuthentication scaAuthentication;

    @Column(nullable = false)
    private PlatformSpec platform;

    @Column(nullable = false, length = CommonModelContraints.MAX)
    private String userAgent;

    @Column(nullable = false, length = CommonModelContraints.IP)
    private String remoteIp;

    @ManyToOne
    private AccountPaymentMethod paymentMethod;

    @OneToOne(cascade = CascadeType.ALL)
    private CardTransactionDetails cardTransactionDetails;

    @ManyToOne
    private PaymentImmutableOfferTemplate offer;

    @Column
    private String description;

    @Column(nullable = false)
    private boolean retryConditionTriggered;

    @Column(nullable = false)
    @Index(unique = false)
    private boolean refunded;

    @Column
    private LocalDate chargebackAt;

    @Column
    @Index(unique = false)
    private ChargebackStatusSpec chargebackStatus;

    @Column(nullable = false)
    @Index(unique = false)
    private LocalDate at;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    @Column
    private String ethocaId;

    @Column
    private OrderStatusSpec internalStatus;

    @Column
    private LocalDate fraudReportedAt;

    @Column
    private Date tc40ReceivedAt;

    public PaymentImmutablePaymentOrder() {
        setStatus(CREATED);
        setInternalStatus(OrderStatusSpec.CREATED);
    }

    public void setProvider(PaymentImmutableProvider provider) {
        this.provider = provider;
        this.providerName = provider.getCode();// todo delete later field
    }

    public String getProviderName() {
        // old data support
        return Optional.ofNullable(provider).map(PaymentImmutableProvider::getCode).orElse(providerName);
    }

    public Optional<String> fraudRequestId() {
        return Optional.ofNullable(getFraudRequestId());
    }

    public Optional<CardTransactionDetails> cardTransactionDetails() {
        return Optional.ofNullable(getCardTransactionDetails());
    }

    @Override
    public int compareTo(PaymentImmutablePaymentOrder o) {
        CompareToBuilder compareTo = new CompareToBuilder();
        compareTo.append(getAmount(), o.getAmount());
        compareTo.append(getCurrency(), o.getCurrency());
        compareTo.append(getProviderName(), o.getProviderName());
        return compareTo.toComparison();
    }

    public Optional<PaymentImmutableOfferTemplate> offer() {
        return Optional.ofNullable(offer);
    }

    public boolean isOfferPurchase() {
        return offer().isPresent();
    }

    public PaymentProvider provider() {
        return getProvider().toPaymentProvider();
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public LocalDate getAt() {
        if (pk == null) {
            pk = new PaymentImmutablePaymentOrderPK();
        }
        return pk.getAt();
    }

    public void setAt(LocalDate at) {
        if (pk == null) {
            pk = new PaymentImmutablePaymentOrderPK();
        }
        pk.setAt(at);
    }

    public void setId(Long id) {
        if (pk == null) {
            pk = new PaymentImmutablePaymentOrderPK();
        }
        pk.setId(id);
    }

    public Long getId() {
        if (pk == null) {
            pk = new PaymentImmutablePaymentOrderPK();
        }
        return pk.getId();
    }

}
