package fraud.worker.model.immutable.payments;

import common.CoreConstraints;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "account_meta_info", schema = Schemas.PAYMENT)
@Getter
@Setter
@EqualsAndHashCode(of = {"id"})
public class PaymentImmutableAccountMetaInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    @OneToOne
    private PaymentImmutableAccount account;
    @OneToOne
    @JoinColumn(name = "last_payment_order_id", referencedColumnName = "id")
    @JoinColumn(name = "last_successful_payment_order_at", referencedColumnName = "at")
    private PaymentImmutablePaymentOrder lastSuccessfulPaymentOrder;
    @OneToOne
    @JoinColumn(name = "last_withdraw_id")
    private PaymentImmutableWithdrawMoneyRequest lastSuccessfulWithdraw;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal totalDepositAmount;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal totalWithdrawAmount;
    //implicitly not nullable
    @Column
    private int depositCount;
    @Column
    private int fiatDepositCount;
    @Column
    private int withdrawCount;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public PaymentImmutableAccountMetaInfo() {
    }

    public PaymentImmutableAccountMetaInfo(PaymentImmutableAccount account) {
        this.id = account.getId();
        this.account = account;
    }
}
