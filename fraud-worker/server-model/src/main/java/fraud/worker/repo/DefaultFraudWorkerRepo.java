package fraud.worker.repo;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import api.v1.ApplicationException;
import api.v1.Code;
import fraud.model.EmailLog;
import fraud.model.EmailSourceSpec;
import fraud.model.card.AccountCardVerificationInfo;
import fraud.model.fraud.AccountFraudInfo;
import fraud.model.fraud.AmlCheck;
import fraud.model.card.CardVerificationMetaInfo;
import fraud.model.card.CardVerificationRequest;
import fraud.model.fraud.FraudAppliedRule;
import fraud.model.fraud.FraudResponse;
import fraud.model.fraud.FraudRule;
import fraud.model.otp.PhoneNumberRequest;
import fraud.model.card.query.QAccountCardVerificationInfo;
import fraud.model.fraud.query.QAccountFraudInfo;
import fraud.model.fraud.query.QAmlCheck;
import fraud.model.card.query.QCardVerificationMetaInfo;
import fraud.model.card.query.QCardVerificationRequest;
import fraud.model.fraud.query.QFraudAppliedRule;
import fraud.model.fraud.query.QFraudResponse;
import fraud.model.fraud.query.QFraudRule;
import fraud.model.otp.query.QPhoneNumberRequest;
import fraud.model.query.QEmailLog;
import fraud.worker.model.immutable.WorkerImmutableAccount;
import fraud.worker.model.immutable.WorkerImmutableBrand;
import fraud.worker.model.immutable.payments.PaymentImmutableAccount;
import fraud.worker.model.immutable.payments.PaymentImmutableAccountMetaInfo;
import fraud.worker.model.immutable.payments.PaymentImmutableAccountPurchaseLimit;
import fraud.worker.model.immutable.payments.PaymentImmutablePaymentOrder;
import fraud.worker.model.immutable.payments.query.QPaymentImmutableAccount;
import fraud.worker.model.immutable.payments.query.QPaymentImmutableAccountMetaInfo;
import fraud.worker.model.immutable.payments.query.QPaymentImmutableAccountPurchaseLimit;
import fraud.worker.model.immutable.payments.query.QPaymentImmutablePaymentOrder;
import fraud.worker.model.immutable.query.QWorkerImmutableAccount;
import org.apache.commons.collections4.CollectionUtils;

import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.JpaManager;

import fraud.model.otp.AccountOtpLimit;
import fraud.model.ReportLog;
import fraud.model.ReportType;
import fraud.model.otp.query.QAccountOtpLimit;
import fraud.model.query.QReportLog;
import io.ebean.Transaction;
import lombok.extern.slf4j.Slf4j;
import payment.model.ChargebackStatusSpec;
import payment.model.query.QAccountMetaInfo;

@Slf4j
public class DefaultFraudWorkerRepo implements FraudWorkerRepo {

    private final JpaManager ebean;

    public DefaultFraudWorkerRepo(JpaManager ebean) {
        this.ebean = Objects.requireNonNull(ebean);
    }

    @Override
    public void acceptEligibleForFraudCheck(long lastVerifiedId, int batchSize, Duration monitoringPeriod, Consumer<Long> action, Transaction tx) {
        LocalDate now = PlatformUtil.toLocalUTCDate();
        LocalDate paymentOperationOffset = now.minusDays(monitoringPeriod.toDays());

        QAccountMetaInfo q = new QAccountMetaInfo(ebean).usingTransaction(tx);
        q.select(QAccountMetaInfo.alias().account.id);
        q.account.id.gt(lastVerifiedId);
        q.or().lastSuccessfulPaymentOrder.pk.at.ge(paymentOperationOffset).lastSuccessfulWithdraw.at.ge(paymentOperationOffset).endOr();
        q.setMaxRows(batchSize);
        q.orderBy().account.id.asc();
        q.findEach(meta -> action.accept(meta.getAccount().getId()));
    }
    @Override
    public List<Long> getDistinctAccountsFromAccountPurchaseLimits(Transaction tx) {
        QPaymentImmutableAccountPurchaseLimit q = new QPaymentImmutableAccountPurchaseLimit(ebean).usingTransaction(tx);
        q.select(QPaymentImmutableAccountPurchaseLimit.alias().account.id);
        q.setDistinct(true);
        q.inactive.eq(false);
        return q.findList().stream()
                .map(PaymentImmutableAccountPurchaseLimit::getAccount)
                .map(PaymentImmutableAccount::getId).toList();
    }

    @Override
    public Optional<ReportLog> reportLog(String reportIdentifier, ReportType reportType, Transaction tx) {
        QReportLog q = new QReportLog(ebean).usingTransaction(tx);
        q.reportType.eq(reportType);
        q.reportIdentifier.eq(reportIdentifier);
        return q.findOneOrEmpty();
    }

    @Override
    public List<AccountOtpLimit> otpExceededLimits(Transaction tx) {
        QAccountOtpLimit q = new QAccountOtpLimit(ebean).usingTransaction(tx);
        q.limitExceeded.eq(true);
        return q.findList();
    }

    @Override
    public List<PaymentImmutablePaymentOrder> matchWithPaymentOrders(FindPaymentParams params, Transaction tx) {
        QPaymentImmutablePaymentOrder qpm = new QPaymentImmutablePaymentOrder(ebean).usingTransaction(tx);
        // @formatter:off
        if (params.getFirstSixDigits() != null && params.getCardLast4() != null) {
            qpm.and()
                    .paymentMethod.cardPaymentMethod.bin.eq(params.getFirstSixDigits())
                    .paymentMethod.cardPaymentMethod.lastFour.eq(params.getCardLast4())
                    .endAnd();
        }
        // @formatter:on
        if (Objects.nonNull(params.getAuthCode())) {
            qpm.cardTransactionDetails.authCode.eq(params.getAuthCode());
        }
        if (CollectionUtils.isNotEmpty(params.getProvidersBlacklist())) {
            qpm.providerName.notIn(params.getProvidersBlacklist());
        }
        if (CollectionUtils.isNotEmpty(params.getProvidersWhitelist())) {
            qpm.providerName.in(params.getProvidersWhitelist());
        }
        qpm.success.eq(true);
        qpm.amount.eq(params.getAmount());

        if (params.getTimestamp() != null) {
            qpm.at.eq(LocalDate.ofInstant(params.getTimestamp(), ZoneOffset.UTC));
            qpm.createdAt.ge(new Date(params.getTimestamp().minusMillis(params.getTimestampInterval().toMillis()).toEpochMilli()));
            qpm.createdAt.le(new Date(params.getTimestamp().plusMillis(params.getTimestampInterval().toMillis()).toEpochMilli()));
        } else {
            qpm.at.ge(
                    LocalDate.ofInstant(params.getAroundTransactionTime().minus(params.getAroundTransactionIntervalInDays(), ChronoUnit.DAYS), ZoneOffset.UTC));
            qpm.at.le(
                    LocalDate.ofInstant(params.getAroundTransactionTime().plus(params.getAroundTransactionIntervalInDays(), ChronoUnit.DAYS), ZoneOffset.UTC));
        }

        List<PaymentImmutablePaymentOrder> found = qpm.findList();
        if (found.size() > 1) {
            final String ids = found.stream().map(Object::toString).collect(Collectors.joining(";"));
            log.warn("More than one transaction were found params:{}, results:{}", params, ids);
            return found;
        }
        if (found.size() == 1) {
            log.debug("Found paymentOrderId:{}, for params:{}", found.get(0).getId(), params);
            return found;
        }
        log.debug("PaymentOrder not found, for params:{}", params);
        return Collections.emptyList();
    }

    @Override
    public WorkerImmutableAccount requiredAccount(Long id, Transaction tx) {
        QWorkerImmutableAccount q = new QWorkerImmutableAccount(ebean).usingTransaction(tx);
        q.setId(id);
        return q.findOne();
    }

    @Override
    public List<WorkerImmutableAccount> accounts(List<Long> accountIds, Transaction tx) {
        QWorkerImmutableAccount q = new QWorkerImmutableAccount(ebean).usingTransaction(tx);
        q.id.in(accountIds);
        q.amlCheck.fetch();
        return q.findList();
    }

    @Override
    public List<WorkerImmutableAccount> accountsByBrandAndCreatedAtInterval(WorkerImmutableBrand brand, Date from, Date to, Transaction tx) {
        QWorkerImmutableAccount q = new QWorkerImmutableAccount(ebean).usingTransaction(tx);
        q.brand.eq(brand);
        q.createdAt.between(from, to);
        return q.findList();
    }

    @Override
    public PaymentImmutablePaymentOrder paymentOrderByARN(String acquirerReferenceNumber, Transaction tx) {
        QPaymentImmutablePaymentOrder q = new QPaymentImmutablePaymentOrder(ebean).usingTransaction(tx);
        q.cardTransactionDetails.acquirerReferenceNumber.eq(acquirerReferenceNumber);
        return q.findOne();
    }

    @Override
    public PaymentImmutablePaymentOrder paymentOrderByProviderAndCode(String providerName, String code, Transaction tx) {
        QPaymentImmutablePaymentOrder q = new QPaymentImmutablePaymentOrder(ebean).usingTransaction(tx);
        q.providerName.eq(providerName);
        q.code.eq(code);
        return q.findOne();
    }

    @Override
    public PaymentImmutablePaymentOrder requiredOrderByTransactionId(UUID id, Transaction tx) throws ApplicationException {
        return orderByTransactionId(id, tx).orElseThrow(ApplicationException.orElseThrow("unable to find order: " + id, Code.ERR_NOT_FOUND));
    }

    @Override
    public PaymentImmutablePaymentOrder requiredSuccessOrderByOrderSn(String orderSn, Transaction tx) throws ApplicationException {
        QPaymentImmutablePaymentOrder q = new QPaymentImmutablePaymentOrder(ebean).usingTransaction(tx);
        q.orderSn.eq(orderSn);
        q.success.eq(true);

        return q.findOneOrEmpty().orElseThrow(ApplicationException.orElseThrow("unable to find order: " + orderSn, Code.ERR_NOT_FOUND));
    }

    @Override
    public PaymentImmutablePaymentOrder requiredOrderBySn(String orderSn, Transaction tx) throws ApplicationException {
        QPaymentImmutablePaymentOrder q = new QPaymentImmutablePaymentOrder(ebean).usingTransaction(tx);
        q.orderSn.eq(orderSn);
        return q.findOneOrEmpty().orElseThrow(ApplicationException.orElseThrow("unable to find order: " + orderSn, Code.ERR_NOT_FOUND));
    }

    @Override
    public Optional<PaymentImmutablePaymentOrder> orderByTransactionId(UUID id, Transaction tx) {
        QPaymentImmutablePaymentOrder q = new QPaymentImmutablePaymentOrder(ebean).usingTransaction(tx);
        q.transactionId.eq(id);
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<PaymentImmutablePaymentOrder> order(List<String> providers, String code, Transaction tx) {
        QPaymentImmutablePaymentOrder q = new QPaymentImmutablePaymentOrder(ebean).usingTransaction(tx);
        q.providerName.in(providers);
        q.code.eq(code);
        return q.findOneOrEmpty();
    }

    @Override
    public List<EmailLog> emailLogs(List<String> identifiers, EmailSourceSpec emailSource, Transaction tx) {
    QEmailLog q = new QEmailLog(ebean).usingTransaction(tx);
        q.identifier.in(identifiers);
        q.source.eq(emailSource);
        return q.findList();
    }

    @Override
    public List<PaymentImmutablePaymentOrder> tc40ordersByDateRange(Integer daysRange, Transaction tx) {
        QPaymentImmutablePaymentOrder q = new QPaymentImmutablePaymentOrder(ebean).usingTransaction(tx);
        Date daysFromNow = Date.from(Instant.now().minus(Duration.ofDays(daysRange)).truncatedTo(ChronoUnit.DAYS));
        q.offer.isNotNull();
        q.tc40ReceivedAt.gt(daysFromNow);
        return q.findList();
    }

    @Override
    public List<PaymentImmutablePaymentOrder> chargebackOrdersByDateRange(Integer daysRange, Transaction tx) {
        QPaymentImmutablePaymentOrder q = new QPaymentImmutablePaymentOrder(ebean).usingTransaction(tx);
        LocalDate daysFromNow = Instant.now()
                .minus(Duration.ofDays(daysRange))
                .truncatedTo(ChronoUnit.DAYS)
                .atZone(ZoneOffset.UTC)
                .toLocalDate();
        q.offer.isNotNull();
        q.chargebackAt.gt(daysFromNow);
        q.chargebackStatus.eq(ChargebackStatusSpec.CHARGEBACK);
        return q.findList();
    }

    @Override
    public PaymentImmutableAccount paymentRequiredAccount(Long id, Transaction tx) {
        QPaymentImmutableAccount q = new QPaymentImmutableAccount(ebean).usingTransaction(tx);
        q.setId(id);
        return q.findOne();
    }

    @Override
    public Optional<PaymentImmutableAccountMetaInfo> accountMetaInfo(long id, Transaction tx) {
        QPaymentImmutableAccountMetaInfo q = new QPaymentImmutableAccountMetaInfo(ebean).usingTransaction(tx);
        q.id.eq(id);
        return q.findOneOrEmpty();
    }


    public void acceptFraudResponse(Collection<Long> ids, java.util.function.Consumer<FraudResponse> consumer, Transaction tx) {
        QFraudResponse q = new QFraudResponse(ebean).usingTransaction(tx);
        q.id.in(ids);
        q.findEach(consumer);
    }

    public void acceptPhoneNumberRequest(Collection<Long> ids, java.util.function.Consumer<PhoneNumberRequest> consumer, Transaction tx) {
        QPhoneNumberRequest q = new QPhoneNumberRequest(ebean).usingTransaction(tx);
        q.id.in(ids);
        q.findEach(consumer);
    }

    public void acceptAccountFraudInfo(Collection<Long> ids, java.util.function.Consumer<AccountFraudInfo> consumer, Transaction tx) {
        QAccountFraudInfo q = new QAccountFraudInfo(ebean).usingTransaction(tx);
        q.id.in(ids);
        q.findEach(consumer);
    }

    public void acceptAmlCheck(Collection<Long> ids, java.util.function.Consumer<AmlCheck> consumer, Transaction tx) {
        QAmlCheck q = new QAmlCheck(ebean).usingTransaction(tx);
        q.id.in(ids);
        q.findEach(consumer);
    }

    public void acceptAccountCardVerificationInfo(Collection<Long> ids, java.util.function.Consumer<AccountCardVerificationInfo> consumer, Transaction tx) {
        QAccountCardVerificationInfo q = new QAccountCardVerificationInfo(ebean).usingTransaction(tx);
        q.id.in(ids);
        q.findEach(consumer);
    }

    public void acceptCardVerificationMetaInfo(Collection<Long> ids, java.util.function.Consumer<CardVerificationMetaInfo> consumer, Transaction tx) {
        QCardVerificationMetaInfo q = new QCardVerificationMetaInfo(ebean).usingTransaction(tx);
        q.id.in(ids);
        q.findEach(consumer);
    }

    public void acceptCardVerificationRequest(Collection<Long> ids, java.util.function.Consumer<CardVerificationRequest> consumer, Transaction tx) {
        QCardVerificationRequest q = new QCardVerificationRequest(ebean).usingTransaction(tx);
        q.id.in(ids);
        q.findEach(consumer);
    }

    public void acceptFraudAppliedRule(Collection<Long> ids, java.util.function.Consumer<FraudAppliedRule> consumer, Transaction tx) {
        QFraudAppliedRule q = new QFraudAppliedRule(ebean).usingTransaction(tx);
        q.id.in(ids);
        q.findEach(consumer);
    }


    public void acceptFraudRule(Collection<String> ids, java.util.function.Consumer<FraudRule> consumer, Transaction tx) {
        QFraudRule q = new QFraudRule(ebean).usingTransaction(tx);
        q.id.in(ids);
        q.findEach(consumer);
    }
}
