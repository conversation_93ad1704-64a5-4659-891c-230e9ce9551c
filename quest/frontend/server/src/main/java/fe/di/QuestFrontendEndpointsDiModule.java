package fe.di;

import api.v1.ApiFactory;
import com.turbospaces.cfg.ApplicationProperties;
import fe.MessageDispatcher;
import fe.api.questline.V1QuestlineApiEndpoint;
import fe.endpoints.DefaultQuestLegacyDocumentationEndpoint;
import fe.endpoints.DefaultQuestlineDocumentationEndpoint;
import fe.endpoints.DefaultQuestlineApiEndpoint;
import fe.endpoints.QuestLegacyDocumentationEndpoint;
import fe.endpoints.QuestlineDocumentationEndpoint;
import fe.handlers.quest.QuestHandler;
import fe.services.CookieStorage;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan(basePackageClasses = {
        QuestHandler.class
})
public class QuestFrontendEndpointsDiModule {

    @Bean
    public V1QuestlineApiEndpoint questlineApiEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            MessageDispatcher dispatcher,
            CookieStorage cookieStorage,
            ApiFactory apiFactory) {
        return new DefaultQuestlineApiEndpoint(props, cloud, meterRegistry, dispatcher, cookieStorage, apiFactory);
    }

    @Bean
    public QuestlineDocumentationEndpoint questDocumentationEndpoint(
            ApplicationProperties props,
            ApiFactory apiFactory,
            DynamicCloud cloud,
            MeterRegistry meterRegistry) {
        return new DefaultQuestlineDocumentationEndpoint(props, apiFactory, cloud, meterRegistry);
    }

    @Bean
    public QuestLegacyDocumentationEndpoint questLegacyDocumentationEndpoint(
            ApplicationProperties props,
            ApiFactory apiFactory,
            DynamicCloud cloud,
            MeterRegistry meterRegistry) {
        return new DefaultQuestLegacyDocumentationEndpoint(props, apiFactory, cloud, meterRegistry);
    }

}
