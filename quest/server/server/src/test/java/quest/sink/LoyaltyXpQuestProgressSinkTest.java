package quest.sink;

import static loyalty.api.v1.AccountBalanceEventType.ACCOUNT_BALANCE_EVENT_TYPE_CREDIT;
import static loyalty.api.v1.AccountBalanceSource.ACCOUNT_BALANCE_SOURCE_SYSTEM;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.atMost;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.math.BigDecimal;
import java.net.URI;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.record.TimestampType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.google.protobuf.Any;
import com.google.protobuf.GeneratedMessageV3;
import com.turbospaces.cfg.DynamicPropertyFactory;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.ThreadPoolContextWorker;
import com.turbospaces.kafka.KafkaRecord;
import com.turbospaces.rpc.QueuePostTemplate;

import api.v1.AccountRoutingInfo;
import api.v1.ApiFactory;
import engagement.model.quest.QuestInstance;
import engagement.model.quest.QuestStatusSpec;
import engagement.model.quest.QuestTemplate;
import engagement.model.quest.QuestlineAccount;
import engagement.model.quest.QuestlineBrand;
import engagement.model.quest.contribution.LoyaltyXpQuestContributionTemplate;
import engagement.model.quest.contribution.QuestContributionEventLog;
import engagement.model.quest.line.DetailViewEmbeddable;
import engagement.model.quest.line.QuestlineDetailsViewSpec;
import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineStartTriggerTypeSpec;
import engagement.model.quest.line.QuestlineStatusSpec;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.model.quest.line.QuestlineTypeSpec;
import engagement.model.quest.line.placement.Placement;
import engagement.model.quest.line.placement.PlacementTemplate;
import engagement.model.quest.line.placement.PlacementTemplateTypeSpec;
import engagement.model.quest.milestone.ProgressMilestone;
import engagement.repo.QuestEbeanJpaManager;
import io.cloudevents.core.builder.CloudEventBuilder;
import io.cloudevents.kafka.KafkaMessageFactory;
import io.micrometer.core.instrument.MeterRegistry;
import loyalty.api.v1.AccountBalanceEventType;
import loyalty.api.v1.AccountBalanceSource;
import loyalty.api.v1.LoyaltyAccountBalanceUpdateEvent;
import quest.InitialTestDataGenerator;
import quest.QuestServerProperties;
import quest.QuestlineExternalIdentityManager;
import quest.TestDataCommonUtil;
import quest.api.v1.MilestoneUpdatedEvent;
import quest.api.v1.QuestlineUpdatedEvent;
import quest.config.H2DatabaseDiModule;
import quest.config.QuestSpringBootTestContextBootstrapper;
import quest.di.QuestServiceApiDiModule;
import quest.di.QuestTestServiceDiModule;
import quest.mock.MockCommonDiModule;
import quest.mock.MockUtilConfiguration;
import quest.service.AccountQuestProgressListener;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@ExtendWith(SpringExtension.class)
@BootstrapWith(QuestSpringBootTestContextBootstrapper.class)
@ContextConfiguration(classes = {
        QuestServiceApiDiModule.class,
        QuestTestServiceDiModule.class,
        MockCommonDiModule.class,
        MockUtilConfiguration.class,
        H2DatabaseDiModule.class,
        QuestServerProperties.class,
        DynamicPropertyFactory.class
})
@TestExecutionListeners(mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS,
        listeners = { InitialTestDataGenerator.class })
class LoyaltyXpQuestProgressSinkTest {

    @Autowired
    QuestServerProperties props;
    @Autowired
    MeterRegistry meterRegistry;
    @Autowired
    ThreadPoolContextWorker contextWorker;
    @Autowired
    QuestlineExternalIdentityManager identityManager;
    @MockitoSpyBean
    QuestEbeanJpaManager ebean;
    @MockitoSpyBean
    AccountQuestProgressListener accountQuestProgressListener;
    @MockitoBean
    KafkaListenerEndpointRegistry registry;
    @MockitoBean
    QueuePostTemplate<?> queuePostTemplate;

    private QuestProgressSink sink;
    private Acknowledgment ack;

    @BeforeEach
    void setUp() throws Exception {
        sink = new QuestProgressSink(registry, meterRegistry, props, contextWorker,
                ebean, accountQuestProgressListener, identityManager, queuePostTemplate);
        sink.afterPropertiesSet();
        ack = mock(Acknowledgment.class);
    }

    @Test
    void xpEventsAdvanceQuestline_happyPath() throws Throwable {
        // ––––– Given brand, account, XP variant and questline –––––
        QuestlineBrand brand;
        QuestlineAccount account;
        try (var tx = ebean.newTransaction()) {
            brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            tx.commit();
        }
        UUID xpVariant = PlatformUtil.randomUUID(); // loyalty‑XP variant observed by quest
        BigDecimal threshold = new BigDecimal("50");

        PlacementTemplate pt = saveTemplate(brand, PlacementTemplateTypeSpec.INBOX_NOTIFICATION);
        QuestlineTemplate qlt = createXpQuestlineTemplate(brand, xpVariant, threshold);
        ebean.save(qlt);
        ebean.save(Placement.builder().placementTemplate(pt).questlineTemplate(qlt)
                .url1("icon").theme(pt.themes().getFirst()).build());

        QuestlineInstance instance = new QuestlineInstance(qlt, account, null);
        ebean.save(instance);
        // schedule quest immediately
        instance.getQuests().getFirst().setScheduledAt(Date.from(Instant.now().minusSeconds(5)));
        ebean.save(instance.getQuests().getFirst());

        // ––––– 1st XP event: +20 XP (40%) –––––
        sendXpEvent(account, brand, xpVariant, "tx‑1", 0, 20);
        assertQuestlineProgress(instance.getId(), new BigDecimal("40.000"));

        // ––––– 2nd XP event: +30 XP (reaches 100%) –––––
        sendXpEvent(account, brand, xpVariant, "tx‑2", 20, 30);

        QuestlineInstance db = ebean.find(QuestlineInstance.class).findOne();
        assertQuestLine(db, QuestlineStatusSpec.COMPLETED, false, new BigDecimal("100.000"));
        assertQuest(db.getQuests().getFirst(), QuestStatusSpec.COMPLETED, false, 1);
        verify(queuePostTemplate, atLeastOnce()).sendEvent(any(MilestoneUpdatedEvent.class));
        verify(queuePostTemplate, atMost(1))
                .sendEvent(
                        isA(QuestlineUpdatedEvent.class));
    }

    @Test
    void xpEventWithWrongVariantIsSkipped() throws Throwable {
        QuestlineBrand brand;
        QuestlineAccount account;
        try (var tx = ebean.newTransaction()) {
            brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            tx.commit();
        }
        UUID observedVariant = PlatformUtil.randomUUID();
        QuestlineTemplate qt = createXpQuestlineTemplate(brand, observedVariant, new BigDecimal("10"));
        ebean.save(qt);
        ebean.save(new QuestlineInstance(qt, account, null));

        // event with DIFFERENT variant
        UUID otherVariant = PlatformUtil.randomUUID();
        sendXpEvent(account, brand, otherVariant, "tx‑x", 0, 10);

        QuestlineInstance db = ebean.find(QuestlineInstance.class).findOne();
        assertEquals(0, BigDecimal.ZERO.compareTo(db.getContribution().getValue()));
        verifyNoInteractions(queuePostTemplate);
    }

    @Test
    void idempotency_duplicateXpCloudEventIgnored() throws Throwable {
        QuestlineBrand brand;
        QuestlineAccount account;
        try (var tx = ebean.newTransaction()) {
            brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            tx.commit();
        }
        UUID variant = PlatformUtil.randomUUID();
        QuestlineTemplate qt = createXpQuestlineTemplate(brand, variant, new BigDecimal("5"));
        ebean.save(qt);
        ebean.save(new QuestlineInstance(qt, account, null));

        LoyaltyAccountBalanceUpdateEvent evt = buildXpEvent(account, brand, variant, "dup‑id‑1",
                ACCOUNT_BALANCE_EVENT_TYPE_CREDIT, ACCOUNT_BALANCE_SOURCE_SYSTEM, "", 0, 5);
        // same CloudEvent id twice
        String ceId = "dup‑cloud‑id";
        sink.accept(Flux.just(toKafkaRecord(evt, ceId)), ack, null);
        sink.accept(Flux.just(toKafkaRecord(evt, ceId)), ack, null);

        assertEquals(1, ebean.find(QuestContributionEventLog.class).findCount());
        verify(ack, times(2)).acknowledge();
    }

    // ─────────────────── helper: build template ────────────────────
    private QuestlineTemplate createXpQuestlineTemplate(QuestlineBrand brand, UUID variantCode, BigDecimal threshold) {
        LoyaltyXpQuestContributionTemplate contrib = LoyaltyXpQuestContributionTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .xpVariantCode(variantCode)
                .name("name")
                .displayName("display")
                .completionThreshold(threshold)
                .build();

        ProgressMilestone questlineMs = ProgressMilestone.builder()
                .code(PlatformUtil.randomUUID())
                .progress(100)
                .rewardIconUrl("https://icon.png")
                .build();

        QuestTemplate qt = QuestTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .name("xp‑quest")
                .displayName("xp‑quest")
                .displayDescription("desc")
                .orderingIndex(1)
                .contributions(List.of(contrib))
                .milestones(List.of(ProgressMilestone.builder()
                        .code(PlatformUtil.randomUUID())
                        .progress(100)
                        .rewardIconUrl("https://icon.png")
                        .build()))
                .build();

        DetailViewEmbeddable dv = new DetailViewEmbeddable();
        dv.setType(QuestlineDetailsViewSpec.SIMPLE_QUEST_POPUP);

        return QuestlineTemplate.builder()
                .brand(brand)
                .code(PlatformUtil.randomUUID())
                .type(QuestlineTypeSpec.SIMPLE_QUEST)
                .name("xp‑ql")
                .displayName("xp‑ql")
                .displayName("promo")
                .displayTagline("tag")
                .displayDescription("desc")
                .displayNameShort("desc")
                .quests(List.of(qt))
                .milestones(List.of(questlineMs)) // ← added
                .detailView(dv)
                .startTimerTrigger(QuestlineStartTriggerTypeSpec.USER_MAKES_ANY_PROGRESS)
                .build();
    }

    // ─────────────────── helper: send event via sink ────────────────────
    private void sendXpEvent(QuestlineAccount acc, QuestlineBrand brand, UUID variant,
            String txn, int before, int amount) throws Throwable {
        LoyaltyAccountBalanceUpdateEvent evt = buildXpEvent(acc, brand, variant, txn,
                ACCOUNT_BALANCE_EVENT_TYPE_CREDIT, ACCOUNT_BALANCE_SOURCE_SYSTEM, "", before, amount);
        sink.accept(Flux.from(Mono.just(toKafkaRecord(evt))), ack, null);
    }

    private LoyaltyAccountBalanceUpdateEvent buildXpEvent(QuestlineAccount acc, QuestlineBrand brand,
            UUID variant, String txn, AccountBalanceEventType eventType,
            AccountBalanceSource source, String sourceRef,
            int before, int amount) {
        AccountRoutingInfo routing = AccountRoutingInfo.newBuilder()
                .setId(acc.getRemoteId())
                .setHash(acc.getHash())
                .setBrand(brand.getName())
                .build();

        return LoyaltyAccountBalanceUpdateEvent.newBuilder()
                .setRouting(routing)
                .setVariantCode(variant.toString())
                .setVariantAbbreviation("XP")
                .setTransactionCode(txn)
                .setEventType(eventType)
                .setSource(source)
                .setSourceReference(sourceRef)
                .setXpBefore(String.valueOf(before))
                .setXpAmount(String.valueOf(amount))
                .setXpCurrent(String.valueOf(before + amount))
                .setAt(System.currentTimeMillis())
                .build();
    }

    // ─────────────────── helper: assertions ────────────────────
    private void assertQuestlineProgress(long instanceId, BigDecimal expectedPercent) {
        QuestlineInstance db = ebean.find(QuestlineInstance.class, instanceId);
        assertEquals(expectedPercent, db.getContribution().toBigDecimal());
    }

    // existing static assert helpers from QuestProgressSinkTest:
    private static void assertQuestLine(QuestlineInstance ql, QuestlineStatusSpec status,
            boolean active, BigDecimal contribution) {
        assertNotNull(ql);
        assertEquals(status, ql.getStatus());
        assertEquals(active, ql.isActive());
        assertEquals(contribution, ql.getContribution().toBigDecimal());
    }
    private static void assertQuest(QuestInstance quest, QuestStatusSpec status,
            boolean active, int contribSize) {
        assertNotNull(quest);
        assertEquals(status, quest.getStatus());
        assertEquals(active, quest.isActive());
        assertEquals(contribSize, quest.getContributions().size());
    }

    // ─────────────────── helper: Kafka CloudEvent builder ────────────────────
    private KafkaRecord toKafkaRecord(LoyaltyAccountBalanceUpdateEvent evt) {
        return toKafkaRecord(evt, PlatformUtil.randomUUID().toString());
    }
    private KafkaRecord toKafkaRecord(GeneratedMessageV3 msg, String cloudEventId) {
        long now = System.currentTimeMillis();
        Any any = Any.pack(msg);
        var ce = CloudEventBuilder.v1().withSource(URI.create("loyalty"))
                .withId(cloudEventId)
                .withType(any.getTypeUrl())
                .withTime(OffsetDateTime.ofInstant(Instant.ofEpochMilli(now), ZoneId.of("UTC")))
                .withExtension(ApiFactory.CLOUD_EVENT_NATIVE_FORMAT, true)
                .withData(any::toByteArray)
                .build();

        var writer = KafkaMessageFactory.createWriter("events", null, now, "key‑" + cloudEventId);
        var prodRec = writer.writeBinary(ce);
        var consRec = new ConsumerRecord<>(prodRec.topic(), 0, 0, -1, TimestampType.NO_TIMESTAMP_TYPE,
                -1, -1, prodRec.key().getBytes(), prodRec.value(), prodRec.headers(), Optional.empty());
        return new KafkaRecord(consRec);
    }

    // helper copy of previous saveTemplate()
    private PlacementTemplate saveTemplate(QuestlineBrand brand, PlacementTemplateTypeSpec type) throws Throwable {
        PlacementTemplate tmpl;
        try (var tx = ebean.newTransaction()) {
            tmpl = new PlacementTemplate(brand, type, PlatformUtil.randomAlphabetic(6), type.name())
                    .themes(List.of("theme‑1"));
            ebean.placementRepo().save(tmpl, tx);
            tx.commit();
        }
        return tmpl;
    }
}
