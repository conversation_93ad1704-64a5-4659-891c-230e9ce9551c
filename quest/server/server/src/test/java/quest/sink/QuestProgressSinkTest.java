package quest.sink;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static uam.model.TransactionTypeSpec.DEBIT;

import java.io.UncheckedIOException;
import java.math.BigDecimal;
import java.net.URI;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Random;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.record.TimestampType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.google.protobuf.Any;
import com.google.protobuf.GeneratedMessageV3;
import com.turbospaces.cfg.DynamicPropertyFactory;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.executor.ThreadPoolContextWorker;
import com.turbospaces.kafka.KafkaRecord;
import com.turbospaces.rpc.QueuePostTemplate;

import api.v1.ApiFactory;
import engagement.model.quest.QuestInstance;
import engagement.model.quest.QuestStatusSpec;
import engagement.model.quest.QuestTemplate;
import engagement.model.quest.QuestlineAccount;
import engagement.model.quest.QuestlineBrand;
import engagement.model.quest.contribution.PlayQuestContributionTemplate;
import engagement.model.quest.contribution.QuestContributionEventLog;
import engagement.model.quest.contribution.QuestContributionInstance;
import engagement.model.quest.contribution.WagerQuestContributionTemplate;
import engagement.model.quest.line.DetailViewEmbeddable;
import engagement.model.quest.line.QuestlineDetailsViewSpec;
import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineStartTriggerTypeSpec;
import engagement.model.quest.line.QuestlineStatusSpec;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.model.quest.line.QuestlineTypeSpec;
import engagement.model.quest.line.placement.Placement;
import engagement.model.quest.line.placement.PlacementTemplate;
import engagement.model.quest.line.placement.PlacementTemplateTypeSpec;
import engagement.model.quest.milestone.ProgressMilestone;
import engagement.repo.QuestEbeanJpaManager;
import io.cloudevents.core.builder.CloudEventBuilder;
import io.cloudevents.kafka.KafkaMessageFactory;
import io.micrometer.core.instrument.MeterRegistry;
import quest.InitialTestDataGenerator;
import quest.QuestServerProperties;
import quest.QuestlineExternalIdentityManager;
import quest.TestDataCommonUtil;
import quest.api.v1.MilestoneUpdatedEvent;
import quest.api.v1.QuestlineUpdatedEvent;
import quest.config.H2DatabaseDiModule;
import quest.config.QuestSpringBootTestContextBootstrapper;
import quest.di.QuestServiceApiDiModule;
import quest.di.QuestTestServiceDiModule;
import quest.mock.MockCommonDiModule;
import quest.mock.MockUtilConfiguration;
import quest.service.AccountQuestProgressListener;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import uam.api.v1.WalletTransactionInfo;
import uam.api.v1.internal.AccountInfo;
import uam.api.v1.internal.GameRoundEvent;
import uam.model.TransactionTypeSpec;
import uam.model.WalletSessionTypeSpec;

@ExtendWith(SpringExtension.class)
@BootstrapWith(QuestSpringBootTestContextBootstrapper.class)
@ContextConfiguration(classes = {
        QuestServiceApiDiModule.class,
        QuestTestServiceDiModule.class,
        MockCommonDiModule.class,
        MockUtilConfiguration.class,
        H2DatabaseDiModule.class,
        QuestServerProperties.class,
        DynamicPropertyFactory.class,
})
@TestExecutionListeners(
        mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS,
        listeners = { InitialTestDataGenerator.class })
class QuestProgressSinkTest {

    @Autowired
    private QuestServerProperties props;
    @Autowired
    private MeterRegistry meterRegistry;
    @Autowired
    private ThreadPoolContextWorker contextWorker;
    @Autowired
    private QuestlineExternalIdentityManager identityManager;
    @MockitoSpyBean
    private QuestEbeanJpaManager ebean;
    @MockitoSpyBean
    private AccountQuestProgressListener accountQuestProgressListener;
    @MockitoBean
    private KafkaListenerEndpointRegistry registry;

    @MockitoBean
    private QueuePostTemplate<?> queuePostTemplate;

    private QuestProgressSink sink;
    private Acknowledgment ack;

    @BeforeEach
    void setUp() throws Exception {
        sink = new QuestProgressSink(registry, meterRegistry, props, contextWorker, ebean, accountQuestProgressListener, identityManager, queuePostTemplate);
        sink.afterPropertiesSet();
        ack = mock(Acknowledgment.class);
    }

    @Test
    void ackBatchIfNoActiveQuestLinesFoundTest() throws Throwable {
        QuestlineBrand brand;
        QuestlineAccount account;
        try (var tx = ebean.newTransaction()) {
            brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            tx.commit();
        }

        var placementTemplate = saveTemplate(brand, PlacementTemplateTypeSpec.INBOX_NOTIFICATION);

        // no quest lines
        var workUnit = toKafkaWorkUnit(GameRoundEvent.newBuilder()
                .setAccount(createAccountInfo(account))
                .setSessionType(WalletSessionTypeSpec.GAME.code())
                .setSessionId("1")
                .setSource("1")
                .setAt(new Date().getTime())
                .setTxWagerAmount("5")
                .setCurrency(InitialTestDataGenerator.SC_CURRENCY.name())
                .addAllWalletTransactions(List.of(createWalletTransactionInfo(new BigDecimal("5"), InitialTestDataGenerator.SC_CURRENCY.name(), DEBIT)))
                .build());
        sink.accept(Flux.from(Mono.just(workUnit)), ack, null);

        verify(ack).acknowledge();

        var milestoneQuestline = ProgressMilestone.builder()
                .code(PlatformUtil.randomUUID())
                .progress(100)
                .rewardIconUrl("https://google.com/icon.png")
                .rewardCodes(List.of(PlatformUtil.randomUUID()))
                .build();

        var questTemplate = QuestTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .orderingIndex(1)
                .name("quest1")
                .displayName("quest1")
                .displayDescription("desc")
                .milestones(List.of(ProgressMilestone.builder()
                        .code(PlatformUtil.randomUUID())
                        .rewardIconUrl("https://google.com/icon.png")
                        .progress(100)
                        .build()))
                .build();
        // no active
        var detailView = new DetailViewEmbeddable();
        detailView.setType(QuestlineDetailsViewSpec.SIMPLE_QUEST_POPUP);
        detailView.setListHeaderImageUrl("https://goooogle.com/icon.png");

        var questlineTemplate = QuestlineTemplate.builder()
                .brand(brand)
                .type(QuestlineTypeSpec.SIMPLE_QUEST)
                .name("quest line")
                .code(PlatformUtil.randomUUID())
                .displayName("promo")
                .displayTagline("tag")
                .displayDescription("desc")
                .displayNameShort("desc")
                .milestones(List.of(milestoneQuestline))
                .quests(List.of(questTemplate))
                .startTimerTrigger(QuestlineStartTriggerTypeSpec.USER_MAKES_ANY_PROGRESS)
                .detailView(detailView)
                .build();
        ebean.save(questlineTemplate);

        var inboxPlacement = Placement.builder().placementTemplate(placementTemplate).questlineTemplate(questlineTemplate)
                .url1("icon").theme(placementTemplate.themes().getFirst()).build();

        ebean.save(inboxPlacement);

        var questlineInstance1 = new QuestlineInstance(questlineTemplate, account, null);
        questlineInstance1.setStatus(QuestlineStatusSpec.COMPLETED);
        var questlineInstance2 = new QuestlineInstance(questlineTemplate, account, null);
        questlineInstance2.setStatus(QuestlineStatusSpec.EXPIRED);
        var questlineInstance3 = new QuestlineInstance(questlineTemplate, account, null);
        questlineInstance3.setStatus(QuestlineStatusSpec.RETRACTED);
        ebean.saveAll(questlineInstance1, questlineInstance2, questlineInstance3);

        workUnit = toKafkaWorkUnit(GameRoundEvent.newBuilder()
                .setAccount(createAccountInfo(account))
                .setSessionType(WalletSessionTypeSpec.GAME.code())
                .setSessionId("1")
                .setSource("1")
                .setAt(new Date().getTime())
                .setTxWagerAmount("5")
                .setCurrency(InitialTestDataGenerator.SC_CURRENCY.name())
                .addAllWalletTransactions(List.of(createWalletTransactionInfo(new BigDecimal("5"), InitialTestDataGenerator.SC_CURRENCY.name(), DEBIT)))
                .build());
        sink.accept(Flux.from(Mono.just(workUnit)), ack, null);

        verify(ack, times(2)).acknowledge();
    }

    //
    // ~ spin count contribution
    //

    @Test
    void simpleQuestLineWithSingleProgressConditionTest() throws Throwable {
        QuestlineBrand brand;
        QuestlineAccount account;
        try (var tx = ebean.newTransaction()) {
            brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            tx.commit();
        }

        var placementTemplate = saveTemplate(brand, PlacementTemplateTypeSpec.INBOX_NOTIFICATION);

        var milestoneQuest = ProgressMilestone.builder()
                .code(PlatformUtil.randomUUID())
                .progress(100)
                .rewardIconUrl("https://google.com/icon.png")
                .build();

        var milestoneQuestline = ProgressMilestone.builder()
                .code(PlatformUtil.randomUUID())
                .progress(100)
                .rewardIconUrl("https://google.com/icon.png")
                .build();

        // templates
        var contribution = PlayQuestContributionTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .name("name")
                .displayName("display")
                .minWager(BigDecimal.ONE)
                .currency(InitialTestDataGenerator.SC_CURRENCY)
                .completionThreshold(new BigDecimal("3"))
                .build();
        var questTemplate = QuestTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .orderingIndex(1)
                .name("quest1")
                .displayName("quest1")
                .displayDescription("desc")
                .contributions(List.of(contribution))
                .milestones(List.of(milestoneQuest))
                .build();
        var detailView = new DetailViewEmbeddable();
        detailView.setType(QuestlineDetailsViewSpec.SIMPLE_QUEST_POPUP);
        detailView.setListHeaderImageUrl("https://goooogle.com/icon.png");

        var questlineTemplate = QuestlineTemplate.builder()
                .type(QuestlineTypeSpec.SIMPLE_QUEST)
                .name("quest line")
                .brand(brand)
                .code(PlatformUtil.randomUUID())
                .quests(List.of(questTemplate))
                .displayName("promo")
                .displayTagline("tag")
                .displayDescription("desc")
                .displayNameShort("desc")
                .minutesToStartExpire(10)
                .milestones(List.of(milestoneQuestline))
                .startTimerTrigger(QuestlineStartTriggerTypeSpec.USER_MAKES_ANY_PROGRESS)
                .detailView(detailView)
                .build();
        ebean.save(questlineTemplate);

        var inboxPlacement = Placement.builder().placementTemplate(placementTemplate).questlineTemplate(questlineTemplate)
                .url1("icon").theme(placementTemplate.themes().getFirst()).build();

        ebean.save(inboxPlacement);

        // instance
        var questlineInstance = new QuestlineInstance(questlineTemplate, account, null);
        ebean.save(questlineInstance);

        // assign
        var questInstance = questlineInstance.getQuests().getFirst();
        questInstance.setScheduledAt(Date.from(Instant.now().minusSeconds(10)));
        ebean.save(questInstance);

        // 1
        var workUnit = toKafkaWorkUnit(GameRoundEvent.newBuilder()
                .setAccount(createAccountInfo(account))
                .setSessionType(WalletSessionTypeSpec.GAME.code())
                .setSessionId("1")
                .setSource("1")
                .setAt(new Date().getTime())
                .setTxWagerAmount("5")
                .setCurrency(InitialTestDataGenerator.SC_CURRENCY.name())
                .addAllWalletTransactions(List.of(createWalletTransactionInfo(new BigDecimal("5"), InitialTestDataGenerator.SC_CURRENCY.name(), DEBIT)))
                .build());
        sink.accept(Flux.from(Mono.just(workUnit)), ack, null);

        var questLine = ebean.find(QuestlineInstance.class).findOne();
        assertQuestLine(questLine, QuestlineStatusSpec.CREATED, true, new BigDecimal("33.333"));
        assertQuest(questLine.getQuests().getFirst(), QuestStatusSpec.CREATED, true, 1);
        var contributionInstance = questLine.getQuests().getFirst().getContributions().getFirst();
        assertContribution(contributionInstance, new BigDecimal("1.000"), new BigDecimal("33.333"));
        assertEventLog(ebean.find(QuestContributionEventLog.class, 1), new BigDecimal("1.000"), contributionInstance);

        // 2
        workUnit = toKafkaWorkUnit(GameRoundEvent.newBuilder()
                .setAccount(createAccountInfo(account))
                .setSessionType(WalletSessionTypeSpec.GAME.code())
                .setSessionId("2")
                .setSource("2")
                .setAt(new Date().getTime())
                .setTxWagerAmount("5")
                .setCurrency(InitialTestDataGenerator.SC_CURRENCY.name())
                .addAllWalletTransactions(List.of(createWalletTransactionInfo(new BigDecimal("5"), InitialTestDataGenerator.SC_CURRENCY.name(), DEBIT)))
                .build());
        sink.accept(Flux.from(Mono.just(workUnit)), ack, null);

        questLine = ebean.find(QuestlineInstance.class).findOne();
        assertQuestLine(questLine, QuestlineStatusSpec.CREATED, true, new BigDecimal("66.667"));
        assertQuest(questLine.getQuests().getFirst(), QuestStatusSpec.CREATED, true, 1);
        contributionInstance = questLine.getQuests().getFirst().getContributions().getFirst();
        assertContribution(contributionInstance, new BigDecimal("2.000"), new BigDecimal("66.667"));
        assertEventLog(ebean.find(QuestContributionEventLog.class, 2), new BigDecimal("1.000"), contributionInstance);

        // 3
        workUnit = toKafkaWorkUnit(GameRoundEvent.newBuilder()
                .setAccount(createAccountInfo(account))
                .setSessionType(WalletSessionTypeSpec.GAME.code())
                .setSessionId("3")
                .setSource("3")
                .setAt(new Date().getTime())
                .setTxWagerAmount("5")
                .setCurrency(InitialTestDataGenerator.SC_CURRENCY.name())
                .addAllWalletTransactions(List.of(createWalletTransactionInfo(new BigDecimal("5"), InitialTestDataGenerator.SC_CURRENCY.name(), DEBIT)))
                .build());
        sink.accept(Flux.from(Mono.just(workUnit)), ack, null);

        verify(queuePostTemplate, times(1)).sendEvent(any(MilestoneUpdatedEvent.class));
        verify(queuePostTemplate, times(1)).sendEvent(any(QuestlineUpdatedEvent.class));

        questLine = ebean.find(QuestlineInstance.class).findOne();

        assertQuestLine(questLine, QuestlineStatusSpec.COMPLETED, false, new BigDecimal("100.000"));
        assertQuest(questLine.getQuests().getFirst(), QuestStatusSpec.COMPLETED, false, 1);
        contributionInstance = questLine.getQuests().getFirst().getContributions().getFirst();
        assertContribution(contributionInstance, new BigDecimal("3.000"), new BigDecimal("100.000"));
        assertEventLog(ebean.find(QuestContributionEventLog.class, 3), new BigDecimal("1.000"), contributionInstance);
    }

    @Test
    void simpleQuestLineWithMultipleProgressConditionsTest() throws Throwable {
        QuestlineBrand brand;
        QuestlineAccount account;
        try (var tx = ebean.newTransaction()) {
            brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            tx.commit();
        }

        var placementTemplate = saveTemplate(brand, PlacementTemplateTypeSpec.INBOX_NOTIFICATION);

        var milestoneQuest = ProgressMilestone.builder()
                .code(PlatformUtil.randomUUID())
                .progress(100)
                .rewardIconUrl("https://google.com/icon.png")
                .build();

        var milestoneQuestline = ProgressMilestone.builder()
                .code(PlatformUtil.randomUUID())
                .progress(100)
                .rewardIconUrl("https://google.com/icon.png")
                .build();

        // templates
        var playContribution = PlayQuestContributionTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .name("play")
                .displayName("display")
                .minWager(BigDecimal.ONE)
                .currency(InitialTestDataGenerator.SC_CURRENCY)
                .completionThreshold(new BigDecimal("2"))
                .build();
        var wagerContribution = WagerQuestContributionTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .name("wager")
                .displayName("display")
                .minWager(BigDecimal.ONE)
                .currency(InitialTestDataGenerator.SC_CURRENCY)
                .completionThreshold(new BigDecimal("10"))
                .build();
        var questTemplate = QuestTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .orderingIndex(1)
                .name("quest1")
                .displayName("quest1")
                .displayDescription("desc")
                .milestones(List.of(milestoneQuest))
                .contributions(List.of(playContribution, wagerContribution))
                .build();

        var detailView = new DetailViewEmbeddable();
        detailView.setType(QuestlineDetailsViewSpec.SIMPLE_QUEST_POPUP);
        detailView.setListHeaderImageUrl("https://goooogle.com/icon.png");

        var questlineTemplate = QuestlineTemplate.builder()
                .brand(brand)
                .type(QuestlineTypeSpec.SIMPLE_QUEST)
                .name("quest line")
                .code(PlatformUtil.randomUUID())
                .quests(List.of(questTemplate))
                .displayName("promo")
                .displayTagline("tag")
                .displayDescription("desc")
                .displayNameShort("desc")
                .milestones(List.of(milestoneQuestline))
                .startTimerTrigger(QuestlineStartTriggerTypeSpec.USER_MAKES_ANY_PROGRESS)
                .detailView(detailView)
                .build();
        ebean.save(questlineTemplate);

        var inboxPlacement = Placement.builder().placementTemplate(placementTemplate).questlineTemplate(questlineTemplate)
                .url1("icon").theme(placementTemplate.themes().getFirst()).build();
        ebean.save(inboxPlacement);

        // instance
        var questlineInstance = new QuestlineInstance(questlineTemplate, account, null);
        ebean.save(questlineInstance);

        // assign
        var questInstance = questlineInstance.getQuests().getFirst();
        questInstance.setScheduledAt(Date.from(Instant.now().minusSeconds(10)));
        ebean.save(questInstance);

        // play & wager
        var workUnit = toKafkaWorkUnit(GameRoundEvent.newBuilder()
                .setAccount(createAccountInfo(account))
                .setSessionType(WalletSessionTypeSpec.GAME.code())
                .setSessionId("1")
                .setSource("1")
                .setTxWagerAmount("5")
                .setCurrency(InitialTestDataGenerator.SC_CURRENCY.name())
                .addAllWalletTransactions(List.of(createWalletTransactionInfo(new BigDecimal("5"), InitialTestDataGenerator.SC_CURRENCY.name(), DEBIT)))
                .build());
        sink.accept(Flux.from(Mono.just(workUnit)), ack, null);

        var questLine = ebean.find(QuestlineInstance.class).findOne();
        assertNotNull(questLine);
        assertQuestLine(questLine, QuestlineStatusSpec.COMPLETED, false, new BigDecimal("100.000"));
        assertQuest(questLine.getQuests().getFirst(), QuestStatusSpec.COMPLETED, false, 2);

        var contributionInstance1 = ebean.find(QuestContributionInstance.class, 1);
        assertNotNull(contributionInstance1);
        assertContribution(contributionInstance1, new BigDecimal("1.000"), new BigDecimal("50.000"));
        assertEventLog(ebean.find(QuestContributionEventLog.class, 1), new BigDecimal("1.000"), contributionInstance1);

        var contributionInstance2 = ebean.find(QuestContributionInstance.class, 2);
        assertNotNull(contributionInstance2);
        assertContribution(contributionInstance2, new BigDecimal("5.000"), new BigDecimal("50.000"));
        assertEventLog(ebean.find(QuestContributionEventLog.class, 2), new BigDecimal("5.000"), contributionInstance2);
    }

    @Test
    void idempotencyCheckTest() throws Throwable {
        QuestlineBrand brand;
        QuestlineAccount account;
        try (var tx = ebean.newTransaction()) {
            brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            tx.commit();
        }

        var placementTemplate = saveTemplate(brand, PlacementTemplateTypeSpec.INBOX_NOTIFICATION);

        // templates
        var contribution = PlayQuestContributionTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .name("name")
                .displayName("display")
                .minWager(BigDecimal.ONE)
                .currency(InitialTestDataGenerator.SC_CURRENCY)
                .completionThreshold(new BigDecimal("3"))
                .build();
        var questTemplate = QuestTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .orderingIndex(1)
                .name("quest1")
                .displayName("quest1")
                .displayDescription("desc")
                .contributions(List.of(contribution))
                .milestones(
                        List.of(ProgressMilestone.builder().rewardIconUrl("https://google.com/icon.png").code(PlatformUtil.randomUUID()).progress(100).build()))
                .build();
        var detailView = new DetailViewEmbeddable();
        detailView.setType(QuestlineDetailsViewSpec.SIMPLE_QUEST_POPUP);
        detailView.setListHeaderImageUrl("https://goooogle.com/icon.png");

        var questlineTemplate = QuestlineTemplate.builder()
                .type(QuestlineTypeSpec.SIMPLE_QUEST)
                .name("quest line")
                .brand(brand)
                .code(PlatformUtil.randomUUID())
                .quests(List.of(questTemplate))
                .displayName("promo")
                .displayTagline("tag")
                .displayDescription("desc")
                .displayNameShort("desc")
                .milestones(
                        List.of(ProgressMilestone.builder().rewardIconUrl("https://google.com/icon.png").code(PlatformUtil.randomUUID()).progress(100).build()))
                .startTimerTrigger(QuestlineStartTriggerTypeSpec.USER_MAKES_ANY_PROGRESS)
                .detailView(detailView)
                .build();
        ebean.save(questlineTemplate);

        var inboxPlacement = Placement.builder().placementTemplate(placementTemplate).questlineTemplate(questlineTemplate)
                .url1("icon").theme(placementTemplate.themes().getFirst()).build();
        ebean.save(inboxPlacement);

        // instance
        var questlineInstance = new QuestlineInstance(questlineTemplate, account, null);
        ebean.save(questlineInstance);

        // assign
        var questInstance = questlineInstance.getQuests().getFirst();
        questInstance.setScheduledAt(Date.from(Instant.now().minusSeconds(10)));
        ebean.save(questInstance);

        // first
        var workUnit = toKafkaWorkUnit(GameRoundEvent.newBuilder()
                .setAccount(createAccountInfo(account))
                .setSessionType(WalletSessionTypeSpec.GAME.code())
                .setSessionId("1")
                .setSource("1")
                .setAt(new Date().getTime())
                .setTxWagerAmount("5")
                .setCurrency(InitialTestDataGenerator.SC_CURRENCY.name())
                .addAllWalletTransactions(List.of(createWalletTransactionInfo(new BigDecimal("5"), InitialTestDataGenerator.SC_CURRENCY.name(), DEBIT)))
                .build(), "1");
        sink.accept(Flux.from(Mono.just(workUnit)), ack, null);
        verify(ack).acknowledge();

        var questLine = ebean.find(QuestlineInstance.class).findOne();
        assertQuestLine(questLine, QuestlineStatusSpec.CREATED, true, new BigDecimal("33.333"));
        assertQuest(questLine.getQuests().getFirst(), QuestStatusSpec.CREATED, true, 1);
        var contributionInstance = questLine.getQuests().getFirst().getContributions().getFirst();
        assertContribution(contributionInstance, new BigDecimal("1.000"), new BigDecimal("33.333"));
        Assertions.assertEquals(1, ebean.find(QuestContributionEventLog.class).findCount());
        assertEventLog(ebean.find(QuestContributionEventLog.class, 1), new BigDecimal("1.000"), contributionInstance);

        // duplicate
        workUnit = toKafkaWorkUnit(GameRoundEvent.newBuilder()
                .setAccount(createAccountInfo(account))
                .setSessionType(WalletSessionTypeSpec.GAME.code())
                .setSessionId("1")
                .setSource("1")
                .setAt(new Date().getTime())
                .setTxWagerAmount("5")
                .setCurrency(InitialTestDataGenerator.SC_CURRENCY.name())
                .addAllWalletTransactions(List.of(createWalletTransactionInfo(new BigDecimal("5"), InitialTestDataGenerator.SC_CURRENCY.name(), DEBIT)))
                .build(), "1");
        sink.accept(Flux.from(Mono.just(workUnit)), ack, null);
        verify(ack, times(2)).acknowledge();

        questLine = ebean.find(QuestlineInstance.class).findOne();
        assertQuestLine(questLine, QuestlineStatusSpec.CREATED, true, new BigDecimal("33.333"));
        assertQuest(questLine.getQuests().getFirst(), QuestStatusSpec.CREATED, true, 1);
        contributionInstance = questLine.getQuests().getFirst().getContributions().getFirst();
        assertContribution(contributionInstance, new BigDecimal("1.000"), new BigDecimal("33.333"));
        Assertions.assertEquals(1, ebean.find(QuestContributionEventLog.class).findCount());
        assertEventLog(ebean.find(QuestContributionEventLog.class, 1), new BigDecimal("1.000"), contributionInstance);
    }

    @Test
    void noAckOnDbConnectionLostTest() throws Throwable {
        QuestlineBrand brand;
        QuestlineAccount account1, account2;
        try (var tx = ebean.newTransaction()) {
            brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account1 = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            account2 = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            tx.commit();
        }

        var placementTemplate = saveTemplate(brand, PlacementTemplateTypeSpec.INBOX_NOTIFICATION);

        // templates
        var milestoneQuest = ProgressMilestone.builder()
                .code(PlatformUtil.randomUUID())
                .progress(100)
                .rewardIconUrl("https://google.com/icon.png")
                .build();

        var milestoneQuestline = ProgressMilestone.builder()
                .code(PlatformUtil.randomUUID())
                .progress(100)
                .rewardIconUrl("https://google.com/icon.png")
                .build();

        var playContribution = PlayQuestContributionTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .name("play")
                .displayName("display")
                .minWager(BigDecimal.ONE)
                .currency(InitialTestDataGenerator.SC_CURRENCY)
                .completionThreshold(new BigDecimal("1"))
                .build();
        var questTemplate = QuestTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .orderingIndex(1)
                .name("quest")
                .displayName("quest")
                .displayDescription("desc")
                .contributions(List.of(playContribution))
                .milestones(List.of(milestoneQuest))
                .build();
        var detailView = new DetailViewEmbeddable();
        detailView.setType(QuestlineDetailsViewSpec.SIMPLE_QUEST_POPUP);
        detailView.setListHeaderImageUrl("https://goooogle.com/icon.png");

        var questlineTemplate = QuestlineTemplate.builder()
                .brand(brand)
                .type(QuestlineTypeSpec.SIMPLE_QUEST)
                .name("quest line")
                .code(PlatformUtil.randomUUID())
                .quests(List.of(questTemplate))
                .displayName("promo")
                .displayTagline("tag")
                .displayDescription("desc")
                .displayNameShort("desc")
                .milestones(List.of(milestoneQuestline))
                .startTimerTrigger(QuestlineStartTriggerTypeSpec.USER_MAKES_ANY_PROGRESS)
                .detailView(detailView)
                .build();
        ebean.save(questlineTemplate);

        var inboxPlacement = Placement.builder().placementTemplate(placementTemplate).questlineTemplate(questlineTemplate)
                .url1("icon").theme(placementTemplate.themes().getFirst()).build();
        ebean.save(inboxPlacement);

        // instances
        var questLineInstance1 = new QuestlineInstance(questlineTemplate, account1, null);
        assertTrue(questLineInstance1.isActive());
        ebean.save(questLineInstance1);
        var questLineInstance2 = new QuestlineInstance(questlineTemplate, account2, null);
        assertTrue(questLineInstance2.isActive());
        ebean.save(questLineInstance2);

        doThrow(UncheckedIOException.class).when(ebean).questRepo();

        // play
        var workUnit1 = toKafkaWorkUnit(GameRoundEvent.newBuilder()
                .setAccount(createAccountInfo(account1))
                .setSessionType(WalletSessionTypeSpec.GAME.code())
                .setSessionId("1")
                .setSource("1")
                .setTxWagerAmount("5")
                .setCurrency(InitialTestDataGenerator.SC_CURRENCY.name())
                .addAllWalletTransactions(List.of(createWalletTransactionInfo(new BigDecimal("5"), InitialTestDataGenerator.SC_CURRENCY.name(), DEBIT)))
                .build());
        var workUnit2 = toKafkaWorkUnit(GameRoundEvent.newBuilder()
                .setAccount(createAccountInfo(account2))
                .setSessionType(WalletSessionTypeSpec.GAME.code())
                .setSessionId("1")
                .setSource("1")
                .setTxWagerAmount("5")
                .setCurrency(InitialTestDataGenerator.SC_CURRENCY.name())
                .addAllWalletTransactions(List.of(createWalletTransactionInfo(new BigDecimal("5"), InitialTestDataGenerator.SC_CURRENCY.name(), DEBIT)))
                .build());

        // db connection lost
        Assertions.assertThrows(UncheckedIOException.class, () -> sink.accept(Flux.fromIterable(List.of(workUnit1, workUnit2)), ack, null));
        verifyNoInteractions(ack);

        // processing error
        Mockito.reset(ebean);
        doThrow(NullPointerException.class).when(accountQuestProgressListener).onGameRound(any(), any(), any(), any());

        sink.accept(Flux.fromIterable(List.of(workUnit1, workUnit2)), ack, null);
        verifyNoInteractions(ack);

        // happy path now
        Mockito.reset(ebean, accountQuestProgressListener);
        sink.accept(Flux.fromIterable(List.of(workUnit1, workUnit2)), ack, null);

        // complete questline & quests
        var questLine1 = ebean.find(QuestlineInstance.class, 1);
        assertQuestLine(questLine1, QuestlineStatusSpec.COMPLETED, false, new BigDecimal("100.000"));
        var questLine2 = ebean.find(QuestlineInstance.class, 2);
        assertQuestLine(questLine2, QuestlineStatusSpec.COMPLETED, false, new BigDecimal("100.000"));
    }

    private static void assertQuestLine(QuestlineInstance questLine, QuestlineStatusSpec status, boolean isActive, BigDecimal contribution) {
        assertNotNull(questLine);
        assertEquals(status, questLine.getStatus());
        assertEquals(isActive, questLine.isActive());
        assertEquals(contribution, questLine.getContribution().toBigDecimal());
    }

    private static void assertQuest(QuestInstance quest, QuestStatusSpec status, boolean isActive, int contributionSize) {
        assertNotNull(quest);
        assertEquals(status, quest.getStatus());
        assertEquals(isActive, quest.isActive());
        assertEquals(contributionSize, quest.getContributions().size());
    }

    private static void assertContribution(QuestContributionInstance instance, BigDecimal contribution, BigDecimal contributionPercent) {
        assertEquals(contribution, instance.getContribution().toBigDecimal());
        assertEquals(contributionPercent, instance.contributionPercent().toBigDecimal());
    }

    private static void assertEventLog(QuestContributionEventLog log, BigDecimal contribution, QuestContributionInstance instance) {
        assertNotNull(log);
        assertEquals(contribution, log.getContribution());
        assertEquals(instance, log.getQuestContributionInstance());
    }

    private <T extends GeneratedMessageV3> KafkaRecord toKafkaWorkUnit(T message) {
        return toKafkaWorkUnit(message, PlatformUtil.randomUUID().toString());
    }

    private <T extends GeneratedMessageV3> KafkaRecord toKafkaWorkUnit(T message, String eventId) {
        var now = System.currentTimeMillis();
        var any = Any.pack(message);
        var cloudEvent = CloudEventBuilder.v1().withSource(URI.create("cloud-app-id")).newBuilder()
                .withId(eventId)
                .withData(any::toByteArray)
                .withTime(OffsetDateTime.ofInstant(Instant.ofEpochMilli(now), ZoneId.of("UTC")))
                .withExtension(ApiFactory.CLOUD_EVENT_NATIVE_FORMAT, true)
                .withType(any.getTypeUrl());
        var messageWriter = KafkaMessageFactory.createWriter("events", null, now, "key");
        var producerRecord = messageWriter.writeBinary(cloudEvent.build());
        var consumerRecord = new ConsumerRecord<>(producerRecord.topic(), 0, 0, -1, TimestampType.NO_TIMESTAMP_TYPE, -1, -1,
                producerRecord.key().getBytes(), producerRecord.value(), producerRecord.headers(),
                Optional.empty());
        return new KafkaRecord(consumerRecord);
    }

    private static WalletTransactionInfo createWalletTransactionInfo(BigDecimal bet, String currency, TransactionTypeSpec type) {
        return WalletTransactionInfo.newBuilder()
                .setAmount(bet.toString())
                .setCurrency(currency)
                .setType(type.toString())
                .setReference(PlatformUtil.randomAlphabetic(10))
                .build();
    }

    private AccountInfo createAccountInfo(QuestlineAccount account) {
        return AccountInfo.newBuilder()
                .setId(account.getRemoteId())
                .setBrandName(account.getBrand().getName())
                .setRoutingKey(account.getHash())
                .build();
    }

    private PlacementTemplate saveTemplate(QuestlineBrand brand, PlacementTemplateTypeSpec type) throws Throwable {
        PlacementTemplate template;
        var rnd = new Random().nextInt() + type.name();
        try (var tx = ebean.newTransaction()) {
            template = new PlacementTemplate(brand, type, rnd, rnd).themes(List.of("theme-1"));
            ebean.placementRepo().save(template, tx);
            tx.commit();
        }
        return template;
    }

}
