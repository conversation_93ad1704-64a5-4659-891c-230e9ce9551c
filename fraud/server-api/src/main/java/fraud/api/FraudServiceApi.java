package fraud.api;

import com.google.protobuf.Empty;
import com.turbospaces.api.ServiceApi;
import com.turbospaces.rpc.ApiResponse;

import fraud.api.v1.BeginCardVerificationRequest;
import fraud.api.v1.BeginCardVerificationResponse;
import fraud.api.v1.BeginDocUploadRequest;
import fraud.api.v1.BeginDocUploadResponse;
import fraud.api.v1.BlockKYCInfoRequest;
import fraud.api.v1.BlockKYCInfoResponse;
import fraud.api.v1.CheckDocumentIdExistsRequest;
import fraud.api.v1.CheckDocumentIdExistsResponse;
import fraud.api.v1.CheckEmailRequest;
import fraud.api.v1.CheckEmailResponse;
import fraud.api.v1.CompleteCardVerificationRequest;
import fraud.api.v1.CompleteDocUploadRequest;
import fraud.api.v1.CompleteDocUploadResponse;
import fraud.api.v1.ConfirmEmailOtpRequest;
import fraud.api.v1.ConfirmEmailOtpResponse;
import fraud.api.v1.ConfirmKYCInfoRequest;
import fraud.api.v1.ConfirmKYCInfoResponse;
import fraud.api.v1.ConfirmPhoneNumberVerificationRequest;
import fraud.api.v1.ConfirmPhoneNumberVerificationResponse;
import fraud.api.v1.CreateDisputeDecisionRequest;
import fraud.api.v1.CreateDisputeDecisionResponse;
import fraud.api.v1.CreateEmailOtpRequest;
import fraud.api.v1.CreateEmailOtpResponse;
import fraud.api.v1.CreateKycVerificationRequest;
import fraud.api.v1.CreateKycVerificationResponse;
import fraud.api.v1.CreatePhoneNumberVerificationRequest;
import fraud.api.v1.CreatePhoneNumberVerificationResponse;
import fraud.api.v1.DeclineKYCInfoRequest;
import fraud.api.v1.DeclineKYCInfoResponse;
import fraud.api.v1.DocUploadKYCInfoRequest;
import fraud.api.v1.DocUploadKYCInfoResponse;
import fraud.api.v1.ExpireKYCInfoRequest;
import fraud.api.v1.ExpireKYCInfoResponse;
import fraud.api.v1.GetAccountFraudInfoRequest;
import fraud.api.v1.GetAccountFraudInfoResponse;
import fraud.api.v1.GetAccountFraudRoutingInfoRequest;
import fraud.api.v1.GetAccountFraudRoutingInfoResponse;
import fraud.api.v1.GetAccountOtpLimitRequest;
import fraud.api.v1.GetAccountOtpLimitResponse;
import fraud.api.v1.GetAccountOtpTriggerRulesRequest;
import fraud.api.v1.GetAccountOtpTriggerRulesResponse;
import fraud.api.v1.GetAccountPhoneNumberDetailsRequest;
import fraud.api.v1.GetAccountPhoneNumberDetailsResponse;
import fraud.api.v1.GetBrandDocUploadInfoRequest;
import fraud.api.v1.GetBrandDocUploadInfoResponse;
import fraud.api.v1.GetCardVerificationsRequest;
import fraud.api.v1.GetCardVerificationsResponse;
import fraud.api.v1.GetConfirmedKYCInfoRequest;
import fraud.api.v1.GetConfirmedKYCInfoResponse;
import fraud.api.v1.GetDocUploadHistoryRequest;
import fraud.api.v1.GetDocUploadHistoryResponse;
import fraud.api.v1.GetDocUploadInfoRequest;
import fraud.api.v1.GetDocUploadInfoResponse;
import fraud.api.v1.GetEthocaSettingsRequest;
import fraud.api.v1.GetEthocaSettingsResponse;
import fraud.api.v1.GetFraudInboxNotificationsRequest;
import fraud.api.v1.GetFraudInboxNotificationsResponse;
import fraud.api.v1.GetKYCInfoRequest;
import fraud.api.v1.GetKYCInfoResponse;
import fraud.api.v1.GetKYCRiskSpendPolicyRequest;
import fraud.api.v1.GetKYCRiskSpendPolicyResponse;
import fraud.api.v1.GetKYCSettingsRequest;
import fraud.api.v1.GetKYCSettingsResponse;
import fraud.api.v1.GetLiveChatSettingsRequest;
import fraud.api.v1.GetLiveChatSettingsResponse;
import fraud.api.v1.GetOtpTriggerRulesRequest;
import fraud.api.v1.GetOtpTriggerRulesResponse;
import fraud.api.v1.GetZendeskAccountAuthTokenRequest;
import fraud.api.v1.GetZendeskAccountAuthTokenResponse;
import fraud.api.v1.InvalidateCacheRequest;
import fraud.api.v1.InvalidateCacheResponse;
import fraud.api.v1.LabelTransactionRequest;
import fraud.api.v1.LabelTransactionResponse;
import fraud.api.v1.NotifyDocUploadRequiredRequest;
import fraud.api.v1.NotifyDocUploadRequiredResponse;
import fraud.api.v1.OtpTriggeredRequest;
import fraud.api.v1.OtpTriggeredResponse;
import fraud.api.v1.PaymentFraudCheckRequest;
import fraud.api.v1.PaymentFraudCheckResponse;
import fraud.api.v1.RemoteAdminCreateKycIdVerificationRequest;
import fraud.api.v1.RemoteAdminCreateKycIdVerificationResponse;
import fraud.api.v1.RemoteAdminCreateKycPoaVerificationRequest;
import fraud.api.v1.RemoteAdminCreateKycPoaVerificationResponse;
import fraud.api.v1.RemoteAdminSetAccountKycAttemptsRequest;
import fraud.api.v1.RemoteAdminSetAccountKycAttemptsResponse;
import fraud.api.v1.RemoveKYCRiskSpendPolicyRequest;
import fraud.api.v1.RemoveKYCRiskSpendPolicyResponse;
import fraud.api.v1.ResetKYCInfoRequest;
import fraud.api.v1.ResetKYCInfoResponse;
import fraud.api.v1.SaveChatEventRequest;
import fraud.api.v1.SaveChatEventResponse;
import fraud.api.v1.SaveEthocaSettingsRequest;
import fraud.api.v1.SaveEthocaSettingsResponse;
import fraud.api.v1.SaveKYCRiskSpendPolicyRequest;
import fraud.api.v1.SaveKYCRiskSpendPolicyResponse;
import fraud.api.v1.SaveOtpTriggerRulesRequest;
import fraud.api.v1.SaveOtpTriggerRulesResponse;
import fraud.api.v1.SendFraudProviderCtxRequest;
import fraud.api.v1.SendFraudProviderCtxResponse;
import fraud.api.v1.SetAccountFraudInfoRequest;
import fraud.api.v1.SetAccountFraudInfoResponse;
import fraud.api.v1.SetAccountOtpLimitRequest;
import fraud.api.v1.SetAccountOtpLimitResponse;
import fraud.api.v1.SetCardVerificationInfoRequest;
import fraud.api.v1.SetCardVerificationInfoResponse;
import fraud.api.v1.SetCardVerificationStatusRequest;
import fraud.api.v1.SetCardVerificationStatusResponse;
import fraud.api.v1.SetDocUploadInfoRequest;
import fraud.api.v1.SetDocUploadInfoResponse;
import fraud.api.v1.SetDocUploadStatusRequest;
import fraud.api.v1.SetDocUploadStatusResponse;
import fraud.api.v1.SetFraudCheckResultRequest;
import fraud.api.v1.SetFraudCheckResultResponse;
import fraud.api.v1.SetKYCInfoRequest;
import fraud.api.v1.SetKYCInfoResponse;
import fraud.api.v1.SetKycDocVerificationInfoRequest;
import fraud.api.v1.SetKycDocVerificationInfoResponse;
import fraud.api.v1.SetKycVerificationInfoRequest;
import fraud.api.v1.SetKycVerificationInfoResponse;
import fraud.api.v1.SetPhoneNumberVerificationInfoRequest;
import fraud.api.v1.SetPhoneNumberVerificationInfoResponse;
import fraud.api.v1.SetReviewStateKYCRequest;
import fraud.api.v1.SetReviewStateKYCResponse;
import fraud.api.v1.SetVerificationInfoRequest;
import fraud.api.v1.SetVerificationInfoResponse;
import fraud.api.v1.SetZendeskExportInfoRequest;
import fraud.api.v1.SetZendeskExportInfoResponse;
import fraud.api.v1.UpdateDocVerificationAttemptRequest;
import fraud.api.v1.UpdateDocVerificationAttemptResponse;
import fraud.api.v1.UpdateFraudInboxNotificationsResponse;
import fraud.api.v1.UpdateFraudInboxNotificationsRequest;
import fraud.api.v1.DeleteAccountRequest;
import fraud.api.v1.DeleteAccountResponse;
import fraud.api.v1.UpdateKVRRequest;
import fraud.api.v1.UpdateKVRResponse;
import fraud.api.v1.UpdateKYCInfoRequest;
import fraud.api.v1.UpdateKYCInfoResponse;
import io.netty.util.AsciiString;

public interface FraudServiceApi extends ServiceApi {
    ApiResponse<InvalidateCacheResponse> invalidateCache(InvalidateCacheRequest request);
    ApiResponse<GetAccountFraudInfoResponse> getAccountFraudInfo(GetAccountFraudInfoRequest request, AsciiString routingKey);
    ApiResponse<GetAccountPhoneNumberDetailsResponse> getAccountPhoneNumberDetails(GetAccountPhoneNumberDetailsRequest request, AsciiString routingKey);
    ApiResponse<LabelTransactionResponse> labelTransaction(LabelTransactionRequest request, AsciiString routingKey);
    ApiResponse<PaymentFraudCheckResponse> paymentFraudCheck(PaymentFraudCheckRequest request, AsciiString routingKey);
    ApiResponse<SendFraudProviderCtxResponse> sendFraudProviderCtx(SendFraudProviderCtxRequest request, AsciiString routingKey);
    ApiResponse<SetFraudCheckResultResponse> setFraudCheckResult(SetFraudCheckResultRequest request, AsciiString routingKey);
    ApiResponse<SetAccountFraudInfoResponse> setAccountFraudInfo(SetAccountFraudInfoRequest request, AsciiString routingKey);
    ApiResponse<CheckEmailResponse> checkEmail(CheckEmailRequest request, AsciiString routingKey, Integer timeout);
    ApiResponse<GetKYCRiskSpendPolicyResponse> getKYCRiskSpendPolicy(GetKYCRiskSpendPolicyRequest request, AsciiString routingKey);
    ApiResponse<SaveKYCRiskSpendPolicyResponse> saveKYCRiskSpendPolicy(SaveKYCRiskSpendPolicyRequest request, AsciiString routingKey);
    ApiResponse<RemoveKYCRiskSpendPolicyResponse> removeKYCRiskSpendPolicy(RemoveKYCRiskSpendPolicyRequest request, AsciiString routingKey);
    ApiResponse<CreatePhoneNumberVerificationResponse> createPhoneNumberVerification(CreatePhoneNumberVerificationRequest request, AsciiString routingKey);
    ApiResponse<ConfirmPhoneNumberVerificationResponse> confirmPhoneNumberVerification(ConfirmPhoneNumberVerificationRequest request, AsciiString routingKey);
    ApiResponse<BeginCardVerificationResponse> beginCardVerification(BeginCardVerificationRequest request, AsciiString routingKey);
    ApiResponse<Empty> completeCardVerification(CompleteCardVerificationRequest request, AsciiString routingKey);
    ApiResponse<SetCardVerificationStatusResponse> setCardVerificationStatus(SetCardVerificationStatusRequest request, AsciiString routingKey);
    ApiResponse<GetAccountOtpLimitResponse> getAccountOtpLimit(GetAccountOtpLimitRequest request, AsciiString routingKey);
    ApiResponse<SetAccountOtpLimitResponse> setAccountOtpLimit(SetAccountOtpLimitRequest request, AsciiString routingKey);
    ApiResponse<GetAccountOtpTriggerRulesResponse> getAccountOtpTriggerRules(GetAccountOtpTriggerRulesRequest request, AsciiString routingKey);
    ApiResponse<GetOtpTriggerRulesResponse> getOtpTriggerRules(GetOtpTriggerRulesRequest request, AsciiString routingKey);
    ApiResponse<SaveOtpTriggerRulesResponse> saveOtpTriggerRules(SaveOtpTriggerRulesRequest request, AsciiString routingKey);
    ApiResponse<SetPhoneNumberVerificationInfoResponse> setPhoneNumberVerificationInfo(SetPhoneNumberVerificationInfoRequest request, AsciiString routingKey);
    ApiResponse<OtpTriggeredResponse> otpModalTriggered(OtpTriggeredRequest request, AsciiString routingKey);
    ApiResponse<GetCardVerificationsResponse> getCardVerifications(GetCardVerificationsRequest request, AsciiString routingKey);
    ApiResponse<SetCardVerificationInfoResponse> setCardVerificationInfo(SetCardVerificationInfoRequest request, AsciiString routingKey);
    ApiResponse<GetLiveChatSettingsResponse> getLiveChatSettings(GetLiveChatSettingsRequest request, AsciiString routingKey);
    ApiResponse<SaveChatEventResponse> saveChatEvent(SaveChatEventRequest request, AsciiString routingKey);
    ApiResponse<GetEthocaSettingsResponse> getEthocaSettings(GetEthocaSettingsRequest request, AsciiString routingKey);
    ApiResponse<SaveEthocaSettingsResponse> saveEthocaSettings(SaveEthocaSettingsRequest request, AsciiString routingKey);
    ApiResponse<GetFraudInboxNotificationsResponse> getInboxNotifications(GetFraudInboxNotificationsRequest request, AsciiString routingKey);
    ApiResponse<UpdateFraudInboxNotificationsResponse> updateInboxNotification(UpdateFraudInboxNotificationsRequest request, AsciiString routingKey);
    ApiResponse<SetZendeskExportInfoResponse> setZendeskExportInfo(SetZendeskExportInfoRequest request, AsciiString routingKey);
    ApiResponse<DeleteAccountResponse> deleteAccount(DeleteAccountRequest request, AsciiString routingKey);
    ApiResponse<GetZendeskAccountAuthTokenResponse> getZendeskAccountAuthToken(GetZendeskAccountAuthTokenRequest request, AsciiString routingKey);
    ApiResponse<UpdateKYCInfoResponse> updateKycInfo(UpdateKYCInfoRequest request, AsciiString routingKey);
    ApiResponse<UpdateKVRResponse> updateKycVerificationRequestInBackground(UpdateKVRRequest request, AsciiString routingKey);
    ApiResponse<SetKYCInfoResponse> setKYCInfo(SetKYCInfoRequest request, AsciiString routingKey);
    ApiResponse<SetKycVerificationInfoResponse> setKycVerificationInfo(SetKycVerificationInfoRequest request, AsciiString routingKey);
    ApiResponse<BlockKYCInfoResponse> blockKycInfo(BlockKYCInfoRequest request, AsciiString routingKey);
    ApiResponse<DeclineKYCInfoResponse> declineKycInfo(DeclineKYCInfoRequest request, AsciiString routingKey);
    ApiResponse<SetKycDocVerificationInfoResponse> setKycDocVerification(SetKycDocVerificationInfoRequest request, AsciiString routingKey);
    ApiResponse<DocUploadKYCInfoResponse> docUploadKycInfo(DocUploadKYCInfoRequest request, AsciiString routingKey);
    ApiResponse<SetReviewStateKYCResponse> setKYCReview(SetReviewStateKYCRequest request, AsciiString routingKey);
    ApiResponse<ExpireKYCInfoResponse> expireKycInfo(ExpireKYCInfoRequest request, AsciiString routingKey);
    ApiResponse<ResetKYCInfoResponse> resetKycInfo(ResetKYCInfoRequest request, AsciiString routingKey);
    ApiResponse<GetKYCInfoResponse> getKYCInfo(GetKYCInfoRequest request, AsciiString routingKey);
    ApiResponse<GetKYCSettingsResponse> getKYCSettings(GetKYCSettingsRequest request, AsciiString routingKey);
    ApiResponse<CheckDocumentIdExistsResponse> checkDocumentIdExists(CheckDocumentIdExistsRequest request, AsciiString routingKey);
    ApiResponse<GetAccountFraudRoutingInfoResponse> getAccountFraudRoutingInfo(GetAccountFraudRoutingInfoRequest request);
    ApiResponse<RemoteAdminSetAccountKycAttemptsResponse> remoteAdminSetAccountKycAttempts(RemoteAdminSetAccountKycAttemptsRequest request, AsciiString routingKey);
    ApiResponse<RemoteAdminCreateKycIdVerificationResponse> remoteAdminCreateKycIdVerification(RemoteAdminCreateKycIdVerificationRequest request, AsciiString routingKey);
    ApiResponse<RemoteAdminCreateKycPoaVerificationResponse> remoteAdminCreateKycPoaVerification(RemoteAdminCreateKycPoaVerificationRequest request, AsciiString routingKey);
    ApiResponse<SetVerificationInfoResponse> setVerificationInfo(SetVerificationInfoRequest request, AsciiString routingKey);
    ApiResponse<CreateKycVerificationResponse> createKycVerificationRequest(CreateKycVerificationRequest request, AsciiString routingKey);
    ApiResponse<ConfirmKYCInfoResponse> confirmKycInfo(ConfirmKYCInfoRequest request, AsciiString routingKey);
    ApiResponse<UpdateDocVerificationAttemptResponse> updateDocVerificationAttempt(UpdateDocVerificationAttemptRequest request, AsciiString routingKey);
    ApiResponse<GetDocUploadInfoResponse> getDocUploadInfo(GetDocUploadInfoRequest request, AsciiString routingKey);
    ApiResponse<GetBrandDocUploadInfoResponse> getBrandDocUploadInfo(GetBrandDocUploadInfoRequest request, AsciiString routingKey);
    ApiResponse<GetConfirmedKYCInfoResponse> getConfirmedKYCInfo(GetConfirmedKYCInfoRequest request, AsciiString routingKey);
    ApiResponse<BeginDocUploadResponse> beginDocUpload(BeginDocUploadRequest request, AsciiString routingKey);
    ApiResponse<SetDocUploadInfoResponse> setDocUploadInfo(SetDocUploadInfoRequest request, AsciiString routingKey);
    ApiResponse<CompleteDocUploadResponse> completeDocUpload(CompleteDocUploadRequest request, AsciiString routingKey);
    ApiResponse<NotifyDocUploadRequiredResponse> notifyDocUploadRequiredRequest(NotifyDocUploadRequiredRequest request, AsciiString routingKey);
    ApiResponse<SetDocUploadStatusResponse> setDocUploadStatus(SetDocUploadStatusRequest request, AsciiString routingKey);
    ApiResponse<GetDocUploadHistoryResponse> getDocUploadHistory(GetDocUploadHistoryRequest request, AsciiString routingKey);
    ApiResponse<CreateEmailOtpResponse> createEmailOtpRequest(CreateEmailOtpRequest request, AsciiString routingKey);
    ApiResponse<ConfirmEmailOtpResponse> confirmEmailOtpRequest(ConfirmEmailOtpRequest request, AsciiString routingKey);
    ApiResponse<CreateDisputeDecisionResponse> createDisputeDecisionRequest(CreateDisputeDecisionRequest request, AsciiString routingKey);

}
