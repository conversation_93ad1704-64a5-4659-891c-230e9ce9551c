package fraud.repo;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import api.v1.ApplicationException;
import fraud.model.FraudAccount;
import fraud.model.inbox.InboxNotificationTypeSpec;
import fraud.model.inbox.InboxNotification;
import io.ebean.Transaction;

public interface InboxNotificationRepo {
    InboxNotification save(InboxNotification notification, Transaction tx);
    InboxNotification requiredByToken(Long accountId, UUID token, Transaction tx) throws ApplicationException;

    List<InboxNotification> findActiveByAccount(FraudAccount account,
                                                InboxNotificationTypeSpec type,
                                                Date activeFrom,
                                                int batchSize,
                                                Transaction tx);

    InboxNotification findNotDeletedByAccountAndType(Long accountId, InboxNotificationTypeSpec type, Transaction tx);
}