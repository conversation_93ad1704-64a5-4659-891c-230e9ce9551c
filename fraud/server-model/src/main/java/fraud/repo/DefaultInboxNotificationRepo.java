package fraud.repo;

import api.v1.ApplicationException;
import api.v1.Code;
import fraud.model.FraudAccount;
import fraud.model.inbox.InboxNotificationStatusSpec;
import fraud.model.inbox.InboxNotificationTypeSpec;
import fraud.model.inbox.InboxNotification;
import fraud.model.inbox.query.QInboxNotification;
import io.ebean.Database;
import io.ebean.Transaction;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

public class DefaultInboxNotificationRepo implements InboxNotificationRepo {
    private final Database ebean;

    public DefaultInboxNotificationRepo(Database ebean) {
        this.ebean = Objects.requireNonNull(ebean);
    }

    @Override
    public InboxNotification save(InboxNotification notification, Transaction tx) {
        ebean.save(notification, tx);
        return notification;
    }

    @Override
    public InboxNotification requiredByToken(Long accountId, UUID token, Transaction tx) throws ApplicationException {
        QInboxNotification q = new QInboxNotification(ebean).usingTransaction(tx);
        q.account.id.eq(accountId);
        q.token.eq(token);
        return q.findOneOrEmpty().orElseThrow(() -> ApplicationException.of("Inbox notification not found", Code.ERR_NOT_FOUND));
    }

    public InboxNotification findNotDeletedByAccountAndType(Long accountId, InboxNotificationTypeSpec type, Transaction tx) {
        return new QInboxNotification(ebean).usingTransaction(tx)
                .account.id.eq(accountId)
                .type.eq(type)
                .status.notEqualTo(InboxNotificationStatusSpec.REMOVED)
                .findOne();
    }

    @Override
    public List<InboxNotification> findActiveByAccount(FraudAccount account,
                                                       InboxNotificationTypeSpec type,
                                                       Date activeFrom,
                                                       int batchSize,
                                                       Transaction tx) {
        var query = new QInboxNotification(ebean).usingTransaction(tx);
        query.account.eq(account);
        query.type.eq(type);
        query.status.notEqualTo(InboxNotificationStatusSpec.REMOVED);
        query.activeFrom.le(activeFrom);
        query.setMaxRows(batchSize);
        return query.findList();
    }
}
