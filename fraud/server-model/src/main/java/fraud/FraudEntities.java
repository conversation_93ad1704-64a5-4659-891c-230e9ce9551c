package fraud;

import java.util.List;

import fraud.model.EmailLog;
import fraud.model.EmailOtpRequest;
import fraud.model.FraudAccount;
import fraud.model.FraudBrand;
import fraud.model.DisputeDecision;
import fraud.model.card.AccountCardVerificationInfo;
import fraud.model.fraud.AccountFraudInfo;
import fraud.model.otp.AccountOtpLimit;
import fraud.model.fraud.AmlCheck;
import fraud.model.BrandSettings;
import fraud.model.card.CardVerificationMetaInfo;
import fraud.model.card.CardVerificationRequest;
import fraud.model.DebeziumSignal;
import fraud.model.DocUploadSettings;
import fraud.model.EthocaSettings;
import fraud.model.fraud.FraudAppliedRule;
import fraud.model.fraud.FraudDeclineRule;
import fraud.model.fraud.FraudResponse;
import fraud.model.fraud.FraudRule;
import fraud.model.fraud.FraudRuleCategoryDetails;
import fraud.model.fraud.FraudTransactionLabels;
import fraud.model.inbox.InboxNotification;
import fraud.model.fraud.IpDetails;
import fraud.model.kyc.KYCRiskSpendPolicy;
import fraud.model.kyc.KYCRiskSpendPolicyHistory;
import fraud.model.otp.OtpTriggerAudit;
import fraud.model.otp.OtpTriggerEvent;
import fraud.model.otp.OtpTriggerRules;
import fraud.model.otp.PhoneNumberRequest;
import fraud.model.ReportLog;
import fraud.model.chat.ZendeskExportInfo;
import fraud.model.chat.ChatEvents;
import fraud.model.doc.upload.DocUploadRequest;
import fraud.model.FraudPolicy;
import fraud.model.PhonePolicy;
import fraud.model.kyc.AccountKycInfo;
import fraud.model.kyc.KYCVerificationRequest;
import model.CoreEntities;

public class FraudEntities extends CoreEntities {
    public FraudEntities() {
        super();

        addImmutable();

        add(FraudBrand.class);
        add(FraudAccount.class);
        add(DebeziumSignal.class);
        add(AccountFraudInfo.class);
        add(FraudRule.class);
        add(FraudResponse.class);
        add(FraudAppliedRule.class);
        add(FraudDeclineRule.class);
        add(AmlCheck.class);
        add(FraudTransactionLabels.class);
        add(IpDetails.class);
        add(KYCRiskSpendPolicy.class);
        add(CardVerificationMetaInfo.class);
        add(CardVerificationRequest.class);
        add(BrandSettings.class);
        add(PhoneNumberRequest.class);
        add(AccountOtpLimit.class);
        add(OtpTriggerRules.class);
        add(OtpTriggerAudit.class);
        add(ChatEvents.class);
        add(ReportLog.class);
        add(EmailLog.class);
        add(AccountCardVerificationInfo.class);
        add(InboxNotification.class);
        add(EthocaSettings.class);
        add(ZendeskExportInfo.class);
        add(KYCRiskSpendPolicyHistory.class);
        add(KYCVerificationRequest.class);
        add(AccountKycInfo.class);
        add(FraudRuleCategoryDetails.class);
        add(DocUploadRequest.class);
        add(DocUploadSettings.class);
        add(OtpTriggerEvent.class);
        add(EmailOtpRequest.class);
        add(DisputeDecision.class);
    }

    private void addImmutable() {
        add(FraudPolicy.class);
        add(PhonePolicy.class);
    }
    @Override
    public List<Class<?>> cdc() {
        return List.of(
                DebeziumSignal.class,
                FraudResponse.class,
                PhoneNumberRequest.class,
                AccountFraudInfo.class,
                AmlCheck.class,
                AccountCardVerificationInfo.class,
                CardVerificationMetaInfo.class,
                CardVerificationRequest.class,
                FraudAppliedRule.class,
                FraudRule.class
        );
    }
}
