
UPDATE rnd_data.teams t
SET logo_alt_url = manual_alt_logos.logo_alt_url
    FROM (
    VALUES
    ('B59C1C735494', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Atlanta_Hawks.svg'),
    ('BE2A4976ABA4', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Boston_Celtics.svg'),
    ('C0312DE08463', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Denver_Nuggets.svg'),
    ('C65360931346', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Charlotte_Hornets.svg'),
    ('CA98E3A931AE', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Chicago_Bull.svg'),
    ('CC72CD00EB95', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Orlando_Magic.svg'),
    ('D5348BDFEBCC', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Cleveland_Cavaliers.svg'),
    ('D8EC6878976A', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Oklahoma_City_Thunder.svg'),
    ('DF2D9E9E8E20', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Houston_Rockets.svg'),
    ('DFC9A735A4D7', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Golden_State_Warriors.svg'),
    ('E89F51275352', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_San_Antonio_Spurs.svg'),
    ('EDF03AD3C346', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Philadelphia_76ers.svg'),
    ('F342C875E571', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Indiana_Pacers.svg'),
    ('FDAE71FA88C6', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Minnesota_Timberwolves.svg'),
    ('6A36E386117E', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_New_York_Knicks.svg'),
    ('6C595835C85B', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_New_Orleans_Pelicans.svg'),
    ('0054C2679F77', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Portland_Trail_Blazers.svg'),
    ('04E36C744934', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_SacramentoKings.svg'),
    ('14682EF45C4D', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Milwaukee_Bucks.svg'),
    ('26BB4DC5722F', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Miami_Heat.svg'),
    ('2C653B0A5BBF', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Memphis_Grizzlies.svg'),
    ('3E1EA77686B9', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Los_Angeles_Clippers.svg'),
    ('417F4FFF4625', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Toronto_Raptors.svg'),
    ('5988658C6B9B', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_of_the_Detroit_Pistons.svg'),
    ('6EFBD520BB7E', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Brooklyn_Nets.svg'),
    ('7165DAB9CAE4', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Dallas_Mavericks.svg'),
    ('73AEB6743EC2', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Los_Angeles_Lakers.svg'),
    ('7C902BB2E272', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Utah_Jazz.svg'),
    ('013D1D5F4D18', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Nottingham_Forest_FC.svg'),
    ('1F74DDDE7110', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/FC_Wolverhampton_Wanderers.svg'),
    ('263AF016D0C5', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Brighton_Hove_Albion_FC_1.svg'),
    ('273700707FB1', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Newcastle_United_FC.svg'),
    ('35DDD3D70565', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Tottenham_Hotspur.svg'),
    ('48B92509529C', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Arsenal_FC.svg'),
    ('4B94C3B57377', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Burnley_F.C..svg'),
    ('8F17F23FB753', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Washington_Wizards.svg'),
    ('9BF9A5FD18B1', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Phoenix_Suns.svg'),
    ('957C00F85D81', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/West_Ham_United.svg'),
    ('982A5B2282B1', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Luton_Town_FC.svg'),
    ('A477D0C02A28', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/FC_Chelsea.svg'),
    ('AF8DDBC0795A', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Manchester_United_FC.svg'),
    ('B196AD1A3F37', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Brentford_FC.svg'),
    ('C5F8130E6580', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Aston_Villa.svg'),
    ('C65F416ACC52', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/FC_Sheffield_United.svg'),
    ('D6AD821C3B5E', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Fulham_FC.svg'),
    ('E22B557B1960', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Crystal_Palace_FC.svg'),
    ('E69E55FFCF65', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Manchester_City_FC.svg'),
    ('F2CCF38E0A5A', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/AFC_Bournemouth.svg'),
    ('F55F77B71202', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Everton_Football_Club.svg'),
    ('F799E43513D4', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Liverpool_FC.svg')
) AS manual_alt_logos(code, logo_alt_url)
WHERE t.code = manual_alt_logos.code
  AND t.data_provider_id = 2; -- OpticOdds

UPDATE rnd_data.teams t
SET logo_alt_url = manual_alt_logos.logo_alt_url
    FROM (
    VALUES
    ('Germany', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Germany.png'),
    ('Switzerland', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Switzerland.png'),
    ('Scotland', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Scotland.png'),
    ('Hungary', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Hungary.png'),
    ('Spain', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Spain.png'),
    ('Croatia', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Croatia.png'),
    ('Italy', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Italy.png'),
    ('Albania', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Albania.png'),
    ('Slovenia', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Slovenia.png'),
    ('Denmark', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Denmark.png'),
    ('Serbia', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Serbia.png'),
    ('England', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/England.png'),
    ('Poland', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Poland.png'),
    ('France', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/France.png'),
    ('Netherlands', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Netherlands.png'),
    ('Austria', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Austria.png'),
    ('Belgium', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Belgium.png'),
    ('Slovakia', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Slovakia.png'),
    ('Romania', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Romania.png'),
    ('Ukraine', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Ukraine.png'),
    ('Türkiye', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Turkey.png'),
    ('Georgia', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Georgia.png'),
    ('Portugal', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Portugal.png'),
    ('Czechia', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Czech_Republic.png')
) AS manual_alt_logos(name, logo_alt_url)
WHERE t.name = manual_alt_logos.name
  AND t.data_provider_id = 2; -- OpticOdds

UPDATE rnd_data.teams t
SET jersey_logo_url     = manual_jerseys.jersey_logo_url,
    jersey_number_color = manual_jerseys.jersey_number_color
FROM (VALUES ('Switzerland', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Switzerland.png', 'green'),
             ('Italy', 'https://storage.googleapis.com/www.sportsmillions.com/images/euro/Italy.png', 'red'))
         AS manual_jerseys(name, jersey_logo_url, jersey_number_color)
WHERE t.name = manual_jerseys.name
  AND t.data_provider_id = 2; -- OpticOdds

UPDATE rnd_data.teams t
SET logo_alt_url = manual_alt_logos.logo_alt_url
    FROM (
    VALUES
    ('11', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Indiana_Pacers.svg'),
    ('1', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Atlanta_Hawks.svg'),
    ('2', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Boston_Celtics.svg'),
    ('3', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_New_Orleans_Pelicans.svg'),
    ('4', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Chicago_Bull.svg'),
    ('5', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Cleveland_Cavaliers.svg'),
    ('6', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Dallas_Mavericks.svg'),
    ('7', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Denver_Nuggets.svg'),
    ('8', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_of_the_Detroit_Pistons.svg'),
    ('9', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Golden_State_Warriors.svg'),
    ('10', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Houston_Rockets.svg'),
    ('12', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Los_Angeles_Clippers.svg'),
    ('13', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Los_Angeles_Lakers.svg'),
    ('14', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Miami_Heat.svg'),
    ('15', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Milwaukee_Bucks.svg'),
    ('16', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Minnesota_Timberwolves.svg'),
    ('17', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Brooklyn_Nets.svg'),
    ('18', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_New_York_Knicks.svg'),
    ('19', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Orlando_Magic.svg'),
    ('20', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Philadelphia_76ers.svg'),
    ('21', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Phoenix_Suns.svg'),
    ('22', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Portland_Trail_Blazers.svg'),
    ('23', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_SacramentoKings.svg'),
    ('24', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_San_Antonio_Spurs.svg'),
    ('25', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Oklahoma_City_Thunder.svg'),
    ('26', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Utah_Jazz.svg'),
    ('27', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Washington_Wizards.svg'),
    ('28', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Toronto_Raptors.svg'),
    ('29', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Memphis_Grizzlies.svg'),
    ('5312', 'https://storage.googleapis.com/www.sportsmillions.com/images/team/Logo_Charlotte_Hornets.svg')
) AS manual_alt_logos(external_id, logo_alt_url)
WHERE t.external_id = manual_alt_logos.external_id
  AND t.data_provider_id = 3; -- Swish

DO $$
DECLARE
    match_id rnd_data.matches.id%TYPE;
    league_id rnd_data.leagues.id%TYPE;
    home_team_id rnd_data.teams.id%TYPE;
    away_team_id rnd_data.teams.id%TYPE;
BEGIN
    select id from rnd_data.teams where name = 'Ukraine' into home_team_id;
    select id from rnd_data.teams where name = 'Germany' into away_team_id;
    select id from rnd_data.leagues where code = 'UEFA_EURO' into league_id;

    if match_id is not null AND home_team_id is not null AND away_team_id is not null
    then
        INSERT INTO rnd_data.matches (league_id, season_id, home_id, away_id, winner_id, closed, closed_at, play_at, home_score, away_score, dirty, data_provider_id, first_completed_at, last_completed_at, last_completed_version, created_at, modified_at, version, predicted_home_score, predicted_away_score, type, code, status, api_play_at, external_id)
        VALUES (league_id, null, home_team_id, away_team_id, home_team_id, true, '2024-06-14 19:00:00.000000 +00:00', '2024-06-01 10:38:59.086000 +00:00', 2, 1, false, 2, null, null, null, now(), now(), 1, 2, 1, 'UEFA_EURO', gen_random_uuid(), 'FINISHED', '2024-06-01', '35312-17445-2024-06-01')
        ON CONFLICT DO NOTHING
        returning id into match_id;

        RAISE NOTICE 'Match ID: %', match_id;

        INSERT INTO rnd_data.team_match_stats (match_id, team_id, data_provider_id, dirty, created_at, modified_at, version, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, type, qualifier, sys_period)
        VALUES (match_id, home_team_id, 2, false, now(), now(), 1, 13.5, 4.5, 1.5, 1.5, 11.5, 16.5, 469.5, 3.5, 'UEFA_EURO', 'HOME', tstzrange(current_timestamp, null))
        ON CONFLICT DO NOTHING;

        INSERT INTO rnd_data.team_match_stats (match_id, team_id, data_provider_id, dirty, created_at, modified_at, version, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, type, qualifier, sys_period)
        VALUES (match_id, away_team_id, 2, false, now(), now(), 1, 13.5, 4.5, 1.5, 1.5, 11.5, 16.5, 469.5, 3.5, 'UEFA_EURO', 'AWAY', tstzrange(current_timestamp, null))
        ON CONFLICT DO NOTHING;
    end if;

END $$;

UPDATE rnd_data.league_config SET is_active = true;