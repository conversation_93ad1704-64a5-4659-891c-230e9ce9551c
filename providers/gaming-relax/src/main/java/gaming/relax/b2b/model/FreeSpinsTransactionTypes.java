package gaming.relax.b2b.model;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum FreeSpinsTransactionTypes {
    FS_WITHDRAW("fswithdraw"),
    FS_DEPOSIT("fsdeposit"),
    FS_PAYOUT("freespinspayout");

    private final String code;

    public static FreeSpinsTransactionTypes fromCode(String code) {
        for (FreeSpinsTransactionTypes type : FreeSpinsTransactionTypes.values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }

        return null;
    }
}
