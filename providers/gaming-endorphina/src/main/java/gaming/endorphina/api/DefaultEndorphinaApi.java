package gaming.endorphina.api;

import static common.model.ProviderSpec.ENDORPHINA;

import java.net.URI;
import java.security.SecureRandom;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Optional;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.cloud.spring.data.spanner.core.SpannerReadOptions;
import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.net.HttpHeaders;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.repo.DefaultSpannerTransactionalCallback;
import aggregator.wallet.patrianna.UamSeamlessWalletClient;
import common.AbstractGameSessionApi;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.flatten.TempTokenFlatten;
import common.model.Currency;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.launch.GameLaunchInfo;
import common.model.launch.GameLaunchResponse;
import gaming.endorphina.api.model.EndorphinaProductSpec;
import gaming.endorphina.api.model.Parameters;
import gaming.endorphina.utils.Mappers;
import jakarta.ws.rs.BadRequestException;
import okhttp3.OkHttpClient;
import okhttp3.Request;

@Service
public class DefaultEndorphinaApi extends AbstractGameSessionApi<EndorphinaProductSpec> implements EndorphinaApi, EndorphinaLaunchApi {

    private static final String GAMES_PATH = "/api/seamless/rest/v1/games";
    private static final String LAUNCH_PATH = "/api/sessions/seamless/rest/v1";
    private static final String DEMO_LAUNCH_PATH = "/api/link/accountId/%s/hash/%s";
    private static final String SOCIAL = "social";
    private static final int SESSION_ID_LENGTH = 24;

    private final SecureRandom defaultNumberGenerator;

    public DefaultEndorphinaApi(AggregatorServerProperties props,
            DynamicCloud cloud,
            OkHttpClient httpClient,
            CommonObjectMapper mapper,
            SpannerTemplate spannerTemplate,
            CacheManager cacheManager,
            UamSeamlessWalletClient client) {
        super(props, cloud, httpClient, mapper, spannerTemplate, cacheManager, client);
        defaultNumberGenerator = new SecureRandom();
        defaultNumberGenerator.nextBytes(new byte[64]);
    }

    @Override
    public GameLaunchResponse launch(GameLaunchInfo gameLaunchInfo, String operator) throws Exception {
        if (gameLaunchInfo.isDemo()) {
            return demoLaunch(gameLaunchInfo, operator);
        }

        var si = getPlainServiceInfo(OperatorSpec.fromString(operator));
        var sessionId = generateUniqueSessionId();
        var params = Mappers.getParamsWithSignature(getLaunchParams(gameLaunchInfo, si.getUserName(), sessionId), si);
        var uri = makeURI(LAUNCH_PATH, si, params);

        Optional<TempTokenFlatten> tokenOpt = TempTokenFlatten.underscore()
                .readExternalOpt(gameLaunchInfo.getToken());
        if (tokenOpt.isEmpty()) {
            throw new BadRequestException("Invalid temporary token");
        }
        TempTokenFlatten tokenFlatten = tokenOpt.get();
        String accountId = tokenFlatten.getAccountId();

        saveGameSession(gameLaunchInfo, operator, accountId, sessionId, null);

        return new GameLaunchResponse(uri.toString());
    }

    @Override
    public Collection<EndorphinaProductSpec> getProducts(String operator, PlainServiceInfo si) throws Exception {
        var params = Mappers.getParamsWithSignature(getProductsParams(Currency.SC.name(), si.getUserName()), si);
        var uri = makeURI(GAMES_PATH, si, params);
        var get = buildGetHttpRequest(uri);

        var json = sendRequest(get);

        return mapper.readValue(json, new TypeReference<>() {});
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.ENDORPHINA;
    }

    private GameLaunchResponse demoLaunch(GameLaunchInfo gameLaunchInfo, String operator) throws Exception {
        var si = getDemoPlainServiceInfo(operator);
        var uri = makeURI(getDemoLaunchPath(gameLaunchInfo.getGameId(), si.getUserName()), si);
        var httpRequest = buildGetHttpRequest(uri);
        return new GameLaunchResponse(sendRequest(httpRequest));
    }

    private static String getDemoLaunchPath(String gameId, String accountId) {
        return DEMO_LAUNCH_PATH.formatted(accountId, DigestUtils.md5Hex(gameId));
    }

    private LinkedHashMap<String, String> getLaunchParams(GameLaunchInfo gameLaunchInfo, String merchantId, String sessionId) {
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        params.put(Parameters.EXIT.code(), gameLaunchInfo.getHomeUrl());
        params.put(Parameters.MERCHANT_ID.code(), merchantId);
        params.put(Parameters.PROFILE.code(), SOCIAL);
        params.put(Parameters.SESSION_ID.code(), sessionId);
        params.values().removeIf(StringUtils::isEmpty);
        return params;
    }

    private PlainServiceInfo getDemoPlainServiceInfo(String operator) {
        String si = String.format("%s-%s-%s-demo", AggregatorWildcardUPSs.IRGS_PREFIX, operator, provider().code());
        return UPSs.findRequiredServiceInfoByName(cloud, si);
    }

    private LinkedHashMap<String, String> getProductsParams(String currency, String merchantId) {
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        params.put(Parameters.CURRENCY.code(), currency);
        params.put(Parameters.MERCHANT_ID.code(), merchantId);
        return params;
    }

    private String generateUniqueSessionId() {
        String sessionId;
        do {
            sessionId = NanoIdUtils.randomNanoId(defaultNumberGenerator, NanoIdUtils.DEFAULT_ALPHABET,
                    SESSION_ID_LENGTH);
        } while (sessionIdAlreadyExists(sessionId));
        return sessionId;
    }

    private boolean sessionIdAlreadyExists(String sessionId) {
        return spannerTemplate.performReadOnlyTransaction(new DefaultSpannerTransactionalCallback<>(cacheManager,
                callback -> {
                    var brandRepo = callback.brandRepo();
                    return brandRepo.gameSessionExists(sessionId, ENDORPHINA.code());
                }), new SpannerReadOptions());
    }

    private Request buildGetHttpRequest(URI uri) {
        return new Request.Builder()
                .get()
                .url(uri.toString())
                .addHeader(HttpHeaders.ACCEPT, ContentType.APPLICATION_JSON.getMimeType())
                .addHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_FORM_URLENCODED.getMimeType())
                .build();
    }
}
