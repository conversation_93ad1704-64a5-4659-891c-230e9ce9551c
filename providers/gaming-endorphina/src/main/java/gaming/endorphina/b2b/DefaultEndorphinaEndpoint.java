package gaming.endorphina.b2b;

import static aggregator.wallet.patrianna.types.WalletTransactionInfo.TYPE_CREDIT;
import static aggregator.wallet.patrianna.types.WalletTransactionInfo.TYPE_DEBIT;
import static aggregator.wallet.patrianna.types.WalletTransactionInfo.TYPE_REFUND;
import static common.model.ProviderSpec.ENDORPHINA;
import static common.utils.BaseMapper.addPrefixToGameId;
import static common.utils.HiddenModeUtils.determineProviderByHiddenMode;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Component;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.UnexpectedHttpStatusException;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.wallet.patrianna.types.AbstractBalanceResponse;
import aggregator.wallet.patrianna.types.AccountBalanceRequestInfo;
import aggregator.wallet.patrianna.types.AtInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.IdentityByAccountIdInfo;
import aggregator.wallet.patrianna.types.IdentityInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletTransactionInfo;
import common.AbstractApiEndpoint;
import common.AggregatorServerProperties;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.limiter.ErrorRateLimiter;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.http.CommonHttpUtils;
import gaming.endorphina.EndorphinaGamingService;
import gaming.endorphina.b2b.exception.GamingServiceException;
import gaming.endorphina.b2b.model.AuthResponse;
import gaming.endorphina.b2b.model.BalanceResponse;
import gaming.endorphina.b2b.model.BetRequest;
import gaming.endorphina.b2b.model.CancelRequest;
import gaming.endorphina.b2b.model.CheckResponse;
import gaming.endorphina.b2b.model.EndorphinaTxRequest;
import gaming.endorphina.b2b.model.ErrorResponse;
import gaming.endorphina.b2b.model.ResponseEntity;
import gaming.endorphina.b2b.model.ResponseStatus;
import gaming.endorphina.b2b.model.TxBalanceResponse;
import gaming.endorphina.b2b.model.ValidationResult;
import gaming.endorphina.b2b.model.WinRequest;
import gaming.endorphina.utils.Mappers;
import io.netty.channel.ChannelHandlerContext;
import io.netty.util.AsciiString;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriInfo;

@Component
public class DefaultEndorphinaEndpoint extends AbstractApiEndpoint implements EndorphinaEndpoint {

    private static final String UNABLE_TO_FIND_ACCOUNT = "unable to find account";
    private static final String UNABLE_TO_FIND_GAME_SESSION = "unable to find game session";
    private static final String HAS_BEEN_EXPIRED_OR_HAS_BEEN_USED = "has been expired or has been used";
    private static final String INSUFFICIENT_COINS = "Insufficient coins";
    private static final String UNABLE_TO_FIND_PRODUCT_BY_CODE = "unable to find product by code";
    private static final String UNABLE_TO_FIND_AT_LEAST_ONE_TRANSACTION = "unable to find at least one transaction";
    private static final Pattern SESSION_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9_\\-~.]{1,24}$");

    private final EndorphinaGamingService gamingService;
    private final CommonHttpUtils httpUtils;
    private final ErrorRateLimiter errorRateLimiter;

    public DefaultEndorphinaEndpoint(AggregatorServerProperties props,
            DynamicCloud cloud,
            EndorphinaGamingService gamingService,
            CommonHttpUtils httpUtils,
            ErrorRateLimiter errorRateLimiter) {
        super(props, cloud);
        this.gamingService = Objects.requireNonNull(gamingService);
        this.httpUtils = Objects.requireNonNull(httpUtils);
        this.errorRateLimiter = Objects.requireNonNull(errorRateLimiter);
    }

    @Override
    public void check(AsyncResponse async,
            UriInfo uriInfo,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            String param,
            String sign) {
        verifySign(async, uriInfo, operator, Mappers.getCheckRequestParams(param), sign);

        var si = getPlainServiceInfo(operator);
        var merchantId = si.getUserName();
        var checkResponseParams = Mappers.getCheckResponseParams(param, merchantId);

        var response = new CheckResponse(merchantId, param, Mappers.getSignature(checkResponseParams, si));
        sendOkResponse(async, response);
    }

    @Override
    public void auth(AsyncResponse async,
            UriInfo uriInfo,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            String sessionId,
            String sign) throws Throwable {
        var params = Mappers.getAuthParams(sessionId);
        var validationResult = validateWithoutAccount(async, uriInfo, operator, params, sessionId, sign);
        if (validationResult.isInvalid()) {
            return;
        }

        gamingService.auth(sessionId, operator, remoteIp(ctx, headers)).addCallback(new FutureCallback<>() {
            @Override
            public void onSuccess(AuthResponse result) {
                try {
                    sendOkResponse(async, result);
                } catch (Exception e) {
                    onFailure(e);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                httpUtils.sendResponse(async, logAndMapToErrorResponse(t));
            }
        }, MoreExecutors.directExecutor());
    }

    @Override
    public void balance(AsyncResponse async,
            UriInfo uriInfo,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            String sessionId,
            String sign,
            String providerCurrency,
            String gameId,
            String playerId) throws Throwable {
        var params = Mappers.getBalanceParams(providerCurrency, gameId, playerId, sessionId);
        var validationResult = validate(async, uriInfo, operator, params, sessionId, sign, playerId);
        if (validationResult.isInvalid()) {
            return;
        }

        var account = validationResult.getAccountFlatten();
        var currency = account.getCurrency();
        var identity = toIdentity(account.getAccountId(), remoteIp(ctx, headers));
        var requestInfo = new AccountBalanceRequestInfo(identity);
        var routingKey = account.getRoutingKey();
        MDC.put(MdcTags.MDC_ROUTING_KEY, routingKey);

        gamingService.getBalance(requestInfo, operator, AsciiString.cached(routingKey))
                .addCallback(
                        getBalanceResponseCallback(async, currency, providerCurrency, BalanceResponse::new),
                        MoreExecutors.directExecutor());
    }

    @Override
    public void bet(AsyncResponse async,
            UriInfo uriInfo,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            BetRequest req,
            InputStream io) throws Throwable {
        var signParams = Mappers.getBetParams(req);
        var validationResult = validate(async, uriInfo, operator, signParams, req.getSessionId(), req.getSign(), req.getPlayerId());
        if (validationResult.isInvalid()) {
            return;
        }

        var account = validationResult.getAccountFlatten();
        var currency = account.getCurrency();
        var identity = toIdentity(account.getAccountId(), remoteIp(ctx, headers));

        var requestInfo = getWalletSessionRequestInfo(req, TYPE_DEBIT, identity, currency);

        gamingService.bet(requestInfo, operator, req.getSessionId(), AsciiString.cached(account.getRoutingKey()))
                .addCallback(getBalanceResponseCallback(async, currency, req.getTransactionId(), TxBalanceResponse::new),
                        MoreExecutors.directExecutor());
    }

    @Override
    public void win(AsyncResponse async,
            UriInfo uriInfo,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            WinRequest req,
            InputStream io) throws Throwable {
        var signParams = Mappers.getWinParams(req);
        var validationResult = validate(async, uriInfo, operator, signParams, req.getSessionId(), req.getSign(), req.getPlayerId());
        if (validationResult.isInvalid()) {
            return;
        }

        var account = validationResult.getAccountFlatten();
        var currency = account.getCurrency();
        var identity = toIdentity(account.getAccountId(), remoteIp(ctx, headers));
        var requestInfo = getWalletSessionRequestInfo(req, TYPE_CREDIT, identity, currency);

        gamingService.win(requestInfo, operator, req.getSessionId(), AsciiString.cached(account.getRoutingKey()))
                .addCallback(getBalanceResponseCallback(async, currency,
                        requestInfo.getTransactions().getFirst().getReference(), TxBalanceResponse::new),
                        MoreExecutors.directExecutor());
    }

    @Override
    public void cancel(AsyncResponse async,
            UriInfo uriInfo,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            CancelRequest req,
            InputStream io) throws Throwable {
        var signParams = Mappers.getCancelParams(req);
        var validationResult = validate(async, uriInfo, operator, signParams, req.getSessionId(), req.getSign(), req.getPlayerId());
        if (validationResult.isInvalid()) {
            return;
        }

        var account = validationResult.getAccountFlatten();
        var currency = account.getCurrency();
        var identity = toIdentity(account.getAccountId(), remoteIp(ctx, headers));
        var gameRoundId = getGameRoundId(req);

        var requestInfo = CancelWalletSessionRequestInfo.builder()
                .identity(identity)
                .sessionId(gameRoundId)
                .source(determineProviderByHiddenMode(props, ENDORPHINA).code())
                .at(new AtInfo(PlatformUtil.toLocalUTCDate()))
                .transactions(List.of(WalletTransactionInfo.builder()
                        .currency(currency)
                        .amount(Mappers.fromProviderAmount(new BigDecimal(req.getAmount())))
                        .reference(req.getTransactionId())
                        .type(TYPE_REFUND).build()))
                .build();

        gamingService.cancel(requestInfo, operator, req.getSessionId(), AsciiString.cached(account.getRoutingKey()))
                .addCallback(getBalanceResponseCallback(async, currency, req.getTransactionId(), TxBalanceResponse::new),
                        MoreExecutors.directExecutor());
    }

    private ProviderSpec provider() {
        return ProviderSpec.ENDORPHINA;
    }

    private PlainServiceInfo getPlainServiceInfo(OperatorSpec operator) {
        String serviceInfoKey = operator.irgsUps(provider().code());
        return UPSs.findRequiredServiceInfoByName(cloud, serviceInfoKey);
    }

    private String getGameRoundId(EndorphinaTxRequest req) {
        return req.getSessionId().concat("-").concat(req.getGameRoundId());
    }

    private ValidationResult validateWithoutAccount(AsyncResponse async,
            UriInfo uriInfo,
            OperatorSpec operator,
            LinkedHashMap<String, String> params,
            String sessionId,
            String sign) {
        verifySign(async, uriInfo, operator, params, sign);

        if (isNotValidSessionId(sessionId)) {
            sendValidationError(async, ResponseStatus.TOKEN_NOT_FOUND);
            return new ValidationResult();
        }

        return new ValidationResult(null, true);
    }

    private ValidationResult validate(AsyncResponse async,
            UriInfo uriInfo,
            OperatorSpec operator,
            LinkedHashMap<String, String> params,
            String sessionId,
            String sign,
            String playerId) {
        var validationResult = validateWithoutAccount(async, uriInfo, operator, params, sessionId, sign);
        return validationResult.isInvalid() ? validationResult : checkAccount(async, playerId);
    }

    private void verifySign(AsyncResponse async,
            UriInfo uriInfo,
            OperatorSpec operator,
            LinkedHashMap<String, String> params,
            String sign) {
        if (isNotValidSign(sign, params, operator)) {
            String message = "Wrong signature - params: %s, uri: %s".formatted(params, uriInfo.getRequestUri());
            logger.debug(message);
            sendValidationError(async, ResponseStatus.ACCESS_DENIED);
            throw new WebApplicationException("Malformed request", Response.Status.FORBIDDEN);
        }
    }

    private ValidationResult checkAccount(AsyncResponse async, String playerId) {
        var accountOpt = AccountRoutingCurrencyFlatten.underscore().readExternalOpt(playerId);
        if (accountOpt.isEmpty()) {
            sendValidationError(async, ResponseStatus.ACCESS_DENIED, "Validation error. Wrong player id.");
            return new ValidationResult();
        }
        var account = accountOpt.get();
        MDC.put(MdcTags.MDC_ROUTING_KEY, account.getRoutingKey());
        return new ValidationResult(account, true);
    }

    private boolean isNotValidSessionId(String sessionId) {
        return !SESSION_ID_PATTERN.matcher(sessionId).matches();
    }

    private boolean isNotValidSign(String sign, LinkedHashMap<String, String> params, OperatorSpec operator) {
        return !StringUtils.equals(sign, Mappers.getSignature(params, getPlainServiceInfo(operator)));
    }

    private WalletSessionRequestInfo getWalletSessionRequestInfo(EndorphinaTxRequest req,
            String txType,
            IdentityInfo identity,
            String currency) {
        var transactionId = TYPE_CREDIT.equals(txType) && req.getTransactionId() == null
                ? ((WinRequest) req).getBetTransactionId()
                : req.getTransactionId();
        var gameRoundId = getGameRoundId(req);
        return getWalletSessionRequestInfo(identity, gameRoundId, req.getGameId(), txType, currency,
                transactionId, new BigDecimal(req.getAmount()));
    }

    private WalletSessionRequestInfo getWalletSessionRequestInfo(
            IdentityInfo identity,
            String gameRoundId,
            String gameId,
            String txType,
            String currency,
            String transactionId,
            BigDecimal amount) {
        return WalletSessionRequestInfo.builder()
                .identity(identity)
                .sessionId(gameRoundId)
                .product(addPrefixToGameId(gameId, ProviderSpec.ENDORPHINA))
                .source(determineProviderByHiddenMode(props, ENDORPHINA).code())
                .at(new AtInfo(PlatformUtil.toLocalUTCDate()))
                .complete(Mappers.isComplete(txType))
                .transactions(List.of(WalletTransactionInfo.builder()
                        .currency(currency)
                        .reference(transactionId)
                        .amount(Mappers.fromProviderAmount(amount))
                        .type(txType).build()))
                .build();
    }

    private static IdentityInfo toIdentity(String accountId, String remoteIp) {
        var byAccountId = new IdentityByAccountIdInfo(accountId, remoteIp);
        return new IdentityInfo(byAccountId);
    }

    private FutureCallback<AbstractBalanceResponse> getBalanceResponseCallback(AsyncResponse async,
            String currency,
            String supplierParam,
            BiFunction<BigDecimal, String, ResponseEntity> responseSupplier) {
        return new FutureCallback<>() {
            @Override
            public void onSuccess(AbstractBalanceResponse result) {
                try {
                    result.getAmount(currency).ifPresent(
                            balance -> sendOkResponse(
                                    async,
                                    responseSupplier.apply(Mappers.toProviderAmount(balance.getBalance()), supplierParam)));
                } catch (Exception e) {
                    onFailure(e);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                httpUtils.sendResponse(async, logAndMapToErrorResponse(t));
            }
        };
    }

    private void sendOkResponse(AsyncResponse async, ResponseEntity response) {
        httpUtils.sendResponse(async, Response.ok(response).build());
    }

    private void sendValidationError(AsyncResponse async, ResponseStatus status) {
        logger.warn(status.getDefaultMessage());
        sendErrorResponse(async, new ErrorResponse(status), status.getStatus());
    }

    private void sendValidationError(AsyncResponse async, ResponseStatus status, String message) {
        logger.warn(message);
        sendErrorResponse(async, new ErrorResponse(status.getCode(), message), status.getStatus());
    }

    private void sendErrorResponse(AsyncResponse async, ErrorResponse response, int status) {
        httpUtils.sendResponse(async, Response.status(status).entity(response).build());
    }

    private Response logAndMapToErrorResponse(Throwable origin) {
        var responseStatus = ResponseStatus.UNKNOWN_ERROR;
        var cause = getRootCause(origin);

        ErrorResponse response = null;
        if (cause instanceof UnexpectedHttpStatusException ex) {
            MDC.put(MdcTags.MDC_ERROR_CODE, String.valueOf(ex.getCode()));

            response = switch (ex.getCode()) {
                case HttpStatus.SC_PAYMENT_REQUIRED -> new ErrorResponse(responseStatus = ResponseStatus.INSUFFICIENT_FUNDS);
                case HttpStatus.SC_UNAUTHORIZED, HttpStatus.SC_FORBIDDEN, HttpStatus.SC_LOCKED -> new ErrorResponse(
                        responseStatus = ResponseStatus.TOKEN_EXPIRED);
                case HttpStatus.SC_NOT_FOUND -> new ErrorResponse(responseStatus = checkExceptionMessage(ex.getEntity(), responseStatus));
                default -> new ErrorResponse(ResponseStatus.INTERNAL_ERROR.getCode(),
                        StringUtils.isNotEmpty(ex.getEntity()) ? ex.getEntity() : ex.getMessage());
            };
        } else if (cause instanceof GamingServiceException ex) {
            responseStatus = ex.getStatus();
            response = new ErrorResponse(ex.getCode(), ex.getMessage());
        } else {
            responseStatus = checkExceptionMessage(cause.getMessage(), responseStatus);
        }

        if (responseStatus == ResponseStatus.UNKNOWN_ERROR) {
            responseStatus = ResponseStatus.INTERNAL_ERROR;
            response = response == null ? new ErrorResponse(responseStatus.getCode(), cause.getMessage()) : response;

            logError(cause, errorRateLimiter.check(cause));
        } else {
            logWarn(cause);
        }

        response = response == null ? new ErrorResponse(responseStatus) : response;

        return Response.status(responseStatus.getStatus()).entity(response).build();
    }

    private ResponseStatus checkExceptionMessage(String message, ResponseStatus responseStatus) {
        if (message.contains(UNABLE_TO_FIND_ACCOUNT)) {
            responseStatus = ResponseStatus.TOKEN_NOT_FOUND;
        } else if (message.contains(UNABLE_TO_FIND_GAME_SESSION) || message.contains(HAS_BEEN_EXPIRED_OR_HAS_BEEN_USED)) {
            responseStatus = ResponseStatus.TOKEN_EXPIRED;
        } else if (message.contains(INSUFFICIENT_COINS)) {
            responseStatus = ResponseStatus.INSUFFICIENT_FUNDS;
        } else if (message.contains(UNABLE_TO_FIND_PRODUCT_BY_CODE)) {
            responseStatus = ResponseStatus.INTERNAL_ERROR;
        } else if (message.contains(UNABLE_TO_FIND_AT_LEAST_ONE_TRANSACTION)) {
            responseStatus = ResponseStatus.INTERNAL_ERROR;
        }
        return responseStatus;
    }
}
