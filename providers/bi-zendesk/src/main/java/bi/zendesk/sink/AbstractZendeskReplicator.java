package bi.zendesk.sink;

import static bi.zendesk.ZendeskUtils.CONF_EXCLUDED_EMAILS;
import static bi.zendesk.ZendeskUtils.isEmailAllowed;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import bi.zendesk.adapter.UamApiAccountInfoAdapter;
import fraud.worker.FraudWorkerMasterEbeanJpaManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.PlatformSpec;
import bi.zendesk.ZendeskClient;
import bi.zendesk.adapter.AccountInfoAdapter;
import bi.zendesk.data.User;
import fraud.api.FraudServiceApi;
import fraud.api.v1.SetZendeskExportInfoRequest;
import fraud.model.chat.ZendeskExportInfo;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;
import uam.CrmProto;
import uam.api.UamServiceApi;
import uam.api.v1.GetAccountInfoRequest;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;

@Slf4j
public abstract class AbstractZendeskReplicator<E, A extends AccountInfoAdapter> implements SinkReplicator {
    protected final ZendeskClient zendeskClient;
    protected final RateLimiter rateLimiter;
    protected final FraudWorkerMasterEbeanJpaManager ebean;
    protected final UamServiceApi uamServiceApi;
    protected final ApplicationProperties props;
    protected final DynamicCloud cloud;
    protected final FraudServiceApi fraudServiceApi;

    public AbstractZendeskReplicator(ZendeskClient zendeskClient, RateLimiter rateLimiter, FraudWorkerMasterEbeanJpaManager ebean, UamServiceApi uamServiceApi, ApplicationProperties props, DynamicCloud cloud, FraudServiceApi fraudServiceApi) {
        this.zendeskClient = zendeskClient;
        this.rateLimiter = rateLimiter;
        this.ebean = ebean;
        this.uamServiceApi = uamServiceApi;
        this.props = props;
        this.cloud = Objects.requireNonNull(cloud);
        this.fraudServiceApi = fraudServiceApi;
    }

    protected void sendWithRateLimit(A account, E event, PlainServiceInfo si) throws Throwable {
        log.debug("Sending request to Zendesk for user {}", account);
        RateLimiter.waitForPermission(rateLimiter);
        var resp = zendeskClient.upsertUser(account.convertToUser(extractUserFields(account, event)), si);
        log.debug("Zendesk updated purchases info of user: {}", resp);
    }

    @SuppressWarnings("unchecked")
    public void apply(E event) throws Throwable {
        var account = getAccount(event);
        if (isEnabled()) {
            Optional<PlainServiceInfo> optsi = UPSs.findScopedServiceInfoByName(account.getBrand(), cloud, CrmProto.UPS_ZENDESK);
            if (optsi.isPresent()) {
                List<String> excludedEmails = props.cfg().getList(CONF_EXCLUDED_EMAILS, List.of());
                if (shouldUpsertZendesk(event) && isEmailAllowed(account.getRealEmail(), excludedEmails)) {
                    enhanceAccountInfo(account);
                    sendWithRateLimit(account, event, optsi.get());
                    if (isLogUpdateRequired(account)) {
                        updateZendeskExportLog(account, event);
                    }
                }
            }
        }
    }

    protected User.UserFields extractUserFields(A account, E event) throws Throwable {
        var userFields = account.extractUserFields();
        return account.enrichUserFieldsWithDbInfo(ebean, userFields);
    }

    protected boolean isLogUpdateRequired(AccountInfoAdapter account) {
        return account.isLogUpdateRequired();
    }

    public void enhanceAccountInfo(AccountInfoAdapter account) throws Throwable {
        Optional<ZendeskExportInfo> exportLog;
        try (var tx = ebean.newReadOnlyTransaction()) {
            exportLog = ebean.zendeskRepo().zendeskExportLog(account.getId(), tx);
        }
        if (exportLog.isPresent() && StringUtils.isNotBlank(exportLog.get().getEmail())) {
            setEmailAndLogUpdateRequired(account, exportLog.get().getEmail(), false);
        } else if (account instanceof UamApiAccountInfoAdapter) { // ~ always used with getAccountInfo call
            account.setEmail(account.getEmail());
            account.setLogUpdateRequired(true);
            setEmailAndLogUpdateRequired(account, account.getEmail(), true);
        } else {
            var req = createGetAccountInfoRequest(account.getId());
            var routingKey = AsciiString.cached(account.getRoutingKey());
            var accountInfo = uamServiceApi.getAccountInfo(req, routingKey).get().unpackAndVerifyOk().getInfo();
            setEmailAndLogUpdateRequired(account, accountInfo.getEmail(), true);
        }
    }

    private static void setEmailAndLogUpdateRequired(AccountInfoAdapter account, String email, boolean logUpdateRequired) {
        account.setEmail(email);
        account.setLogUpdateRequired(logUpdateRequired);
    }

    protected void updateZendeskExportLog(AccountInfoAdapter account, E event) throws Exception {
        var req = SetZendeskExportInfoRequest.newBuilder()
                .setIdentity(createIdentity(account.getId()))
                .setIsUpdated(true)
                .setEmail(account.getEmail());
        getTotalPurchaseAmount(event).ifPresent(amount -> req.setTotalPurchase(String.valueOf(amount)));
        fraudServiceApi.setZendeskExportInfo(req.build(), AsciiString.cached(account.getRoutingKey())).get().verifyOk();
    }

    protected Optional<BigDecimal> getTotalPurchaseAmount(E event) {
        return Optional.empty();
    }

    protected GetAccountInfoRequest createGetAccountInfoRequest(long accountId) {
        return GetAccountInfoRequest.newBuilder()
                .setIdentity(createIdentity(accountId))
                .build();
    }

    protected Identity createIdentity(long accountId) {
        return Identity.newBuilder()
                .setByAccountId(IdentityByAccountId.newBuilder()
                        .setAccountId(accountId)
                        .setPlatform(PlatformSpec.WEB.code())
                        .build())
                .setServiceName(props.CLOUD_APP_ID.get())
                .build();
    }

    protected boolean isEnabled() {
        return true;
    }

    protected abstract A getAccount(E event) throws Exception;

    protected boolean shouldUpsertZendesk(E event) throws Throwable {
        return true;
    }
}
