package bi.zendesk.sink;

import api.v1.KYCStatusSpec;
import bi.zendesk.ZendeskClient;
import bi.zendesk.ZendeskConfigService;
import bi.zendesk.ZendeskTicketsRateLimiter;
import bi.zendesk.ZendeskUtils;
import bi.zendesk.adapter.UamApiAccountInfoAdapter;
import bi.zendesk.data.Ticket;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.PlainServiceInfo;
import fraud.api.FraudServiceApi;
import fraud.api.v1.KYCProvider;
import fraud.api.v1.KYCStatusUpdateEvent;
import fraud.api.v1.KYCVerificationRequest;
import fraud.worker.FraudWorkerMasterEbeanJpaManager;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;

import java.util.List;
import java.util.Optional;

import static bi.zendesk.ZendeskUtils.toLocalDate;

@Slf4j
@Service
public class CreateTicketZendeskReplicator extends AbstractZendeskReplicator<KYCStatusUpdateEvent, UamApiAccountInfoAdapter> implements SinkReplicator {
    public static final String KYC_FAILED_SUBJECT = "KYC failed attempt";
    public static final String KYC_NOTIFICATION_TAG = "kyc_agent_notification";
    public static final String DEFAULT_PUBLIC_FALSE = "false";
    private static final List<KYCStatusSpec> APPLICABLE_STATUSES = List.of(
            KYCStatusSpec.DECLINED,
            KYCStatusSpec.BLOCKED,
            KYCStatusSpec.DOC_DECLINED
    );
    private final ZendeskConfigService configService;

    public CreateTicketZendeskReplicator(ZendeskClient zendeskClient,
                                         ZendeskTicketsRateLimiter rateLimiter,
                                         FraudWorkerMasterEbeanJpaManager ebean,
                                         UamServiceApi uamServiceApi,
                                         FraudServiceApi fraudServiceApi,
                                         ApplicationProperties props,
                                         DynamicCloud cloud,
                                         ZendeskConfigService configService) {
        super(zendeskClient, rateLimiter, ebean, uamServiceApi, props, cloud, fraudServiceApi);
        this.configService = configService;
    }

    @Override
    protected void sendWithRateLimit(UamApiAccountInfoAdapter account, KYCStatusUpdateEvent event, PlainServiceInfo si) throws Throwable {
        var ticket = convertToTicket(event, account);
        log.debug("Sending request to Zendesk for ticket {}", ticket);
        RateLimiter.waitForPermission(rateLimiter);
        zendeskClient.createTicket(ticket, si);
        log.debug("Zendesk ticket request has been sent");
    }

    private Ticket convertToTicket(KYCStatusUpdateEvent event, UamApiAccountInfoAdapter account) {
        var ticket = new Ticket();
        ticket.subject = KYC_FAILED_SUBJECT;
        ticket.tags = List.of(KYC_NOTIFICATION_TAG);
        ticket.comment = new Ticket.Comment(KYC_FAILED_SUBJECT, false);
        var name = "%s %s".formatted(account.getFirstName(), account.getLastName());
        ticket.requester = new Ticket.Requester(name, account.getEmail());
        extractBrandId(account.getBrand()).ifPresent(ticket::setBrandId);
        ticket.customFields = extractCustomFields(event.getVerificationRequest());
        return ticket;
    }

    private Optional<Long> extractBrandId(String brandName) {
        var zendeskBrands = configService.zendeskBrands();
        return Optional.ofNullable(zendeskBrands.get(brandName)).map(Long::valueOf);
    }

    private List<Ticket.CustomField> extractCustomFields(KYCVerificationRequest kycVerificationRequest) {
        var customFieldsMap = configService.zendeskTicketsCustomFields();
        return customFieldsMap.entrySet().stream()
                .map(entry -> {
                    Long fieldId = Long.valueOf(entry.getKey());
                    String fieldName = entry.getValue();
                    String eventValue = getVerificationRequestValue(kycVerificationRequest, fieldName);
                    return new Ticket.CustomField(fieldId, eventValue);
                })
                .toList();
    }

    private static String getVerificationRequestValue(KYCVerificationRequest kycVerificationRequest, String fieldName) {
        return switch (fieldName) {
            case "reason" -> kycVerificationRequest.getReason().getShortReason();
            case "phase" -> kycVerificationRequest.getKycPhase().name();
            case "provider" -> kycVerificationRequest.getProvider();
            case "status" -> kycVerificationRequest.getStatus();
            case "reason_desc" -> kycVerificationRequest.getReason().getDesc();
            case "created_at" -> toLocalDate(kycVerificationRequest.getCreatedAt());
            case "id_type" -> kycVerificationRequest.getIdType();
            case "doc_status" -> kycVerificationRequest.getDocStatus();
            case "session_id" -> kycVerificationRequest.getReason().getCode();
            case "public" -> DEFAULT_PUBLIC_FALSE;
            default -> null;
        };
    }

    @Override
    protected boolean isEnabled() {
        return props.cfg().getBoolean(ZendeskUtils.TICKET_CREATION_ENABLED, false);
    }

    @Override
    protected boolean shouldUpsertZendesk(KYCStatusUpdateEvent event) {
        return !isManualProvider(event) && isEventApplicable(event.getStatus()) && isReasonApplicable(event);
    }

    @Override
    protected UamApiAccountInfoAdapter getAccount(KYCStatusUpdateEvent event) throws Exception {
        var req = createGetAccountInfoRequest(event.getAccount().getAccountId());
        return new UamApiAccountInfoAdapter(uamServiceApi.getAccountInfo(req, AsciiString.cached(event.getAccount().getRoutingKey()))
                .get()
                .unpackAndVerifyOk().getInfo());
    }

    private boolean isReasonApplicable(KYCStatusUpdateEvent event) {
        if (event.hasVerificationRequest() && event.getVerificationRequest().hasReason()) {
            String normalizedReason = event.getVerificationRequest().getReason().getShortReason().replaceAll(",", "");
            return props.cfg().getList(ZendeskUtils.TICKET_CREATION_INCLUDED_REASONS, List.of()).contains(normalizedReason);
        }
        return false;
    }

    private static boolean isEventApplicable(String status) {
        return APPLICABLE_STATUSES.contains(KYCStatusSpec.fromString(status));
    }

    private static boolean isManualProvider(KYCStatusUpdateEvent event) {
        return KYCProvider.MANUAL.name().equalsIgnoreCase(event.getVerificationRequest().getProvider());
    }
}