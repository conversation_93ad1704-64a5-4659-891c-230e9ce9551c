package gaming.ruby.api.freespins;

import static gaming.ruby.api.DefaultRubyApi.buildAuthHeader;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.collect.Lists;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.freespins.patrianna.UamFreeSpinsClient;
import aggregator.model.FreeSpinCampaign;
import common.AbstractFreeSpinsApi;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.exception.FreeSpinsExceededTimeException;
import common.exception.FreeSpinsException;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CancelFreeSpinsRequest;
import common.model.freespins.CancelFreeSpinsResponse;
import common.model.freespins.CancelPlayerFreeSpinRequest;
import common.model.freespins.CancelPlayerFreeSpinResponse;
import common.model.freespins.CreateFreeSpinResult;
import common.model.freespins.CreateFreeSpinsBaseRequest;
import common.model.freespins.CreateFreeSpinsCampaignRequest;
import common.model.freespins.CreateFreeSpinsCampaignResponse;
import common.model.freespins.CreateFreeSpinsRequest;
import common.model.freespins.CreateFreeSpinsResponse;
import common.model.freespins.FreeSpinCampaignStatus;
import common.model.freespins.FreeSpinIdempotencyStatus;
import common.model.freespins.FreeSpinsInfo;
import common.model.freespins.FreeSpinsStatus;
import common.utils.FreeSpinsUtils;
import gaming.ruby.Mappers;
import gaming.ruby.api.freespins.model.PlayerInfoRequest;
import io.micrometer.core.instrument.MeterRegistry;

@Service
public class DefaultFreeSpinsRubyService extends AbstractFreeSpinsApi {
    private static final String CAMPAIGN_OK_ONGOING = "ONGOING";
    private static final String CAMPAIGN_OK_UPCOMING = "UPCOMING";
    private static final String CAMPAIGN_DISABLED = "DISABLED";
    public static final String REQUEST_ID_EXCEEDED_32_CHARS = "Request id exceeded 32 characters";
    public static final int REQUEST_ID_MAX_LENGTH = 32;

    private final RubyFreeSpinsApi rubyApi;
    private final CommonObjectMapper mapper;

    public DefaultFreeSpinsRubyService(
            AggregatorServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            RubyFreeSpinsApi rubyApi,
            UamFreeSpinsClient uamFreeSpinsClient,
            SpannerTemplate spannerTemplate,
            CacheManager cacheManager,
            CommonObjectMapper mapper) {
        super(props, cloud, meterRegistry, uamFreeSpinsClient, spannerTemplate, cacheManager);
        this.rubyApi = rubyApi;
        this.mapper = mapper;
    }

    @Override
    public CreateFreeSpinsResponse createFreeSpins(OperatorSpec operator, CreateFreeSpinsRequest request) throws FreeSpinsException, JsonProcessingException {
        var createFreeSpinsResponseOpt = getCreateFreeSpinsResponseByRequestId(request.getBonusCode(), operator, provider(), request.getRequestId());
        if (createFreeSpinsResponseOpt.isPresent()) {
            var response = createFreeSpinsResponseOpt.get();
            validateBonusCodeFromRequestId(request, response.getBonusCode());
            return response;
        }
        validateRequestId(request.getRequestId());
        var createFreeSpinResult = createFreeSpins(operator, request, request.getPlayerIdList());
        var freeSpinsInfoResponse = createFreeSpinResult.freeSpinsInfo();
        List<FreeSpinsInfo> freeSpinsResponseList = Mappers.toFreeSpinsInfoListWithPlayers(
                request.getPlayerIdList(), freeSpinsInfoResponse);

        var accountFreeSpinsList = prepareAccountFreeSpinsList(operator, provider(), request, freeSpinsResponseList, FreeSpinsStatus.CREATED);
        saveFreeSpinsWithIdempotencyKey(operator, provider(), request.getRequestId(), accountFreeSpinsList, FreeSpinIdempotencyStatus.FINISHED);

        return new CreateFreeSpinsResponse(request.getBonusCode(), freeSpinsResponseList, createFreeSpinResult.currency());
    }

    @Override
    public CancelPlayerFreeSpinResponse cancelPlayerFreeSpin(OperatorSpec operator, CancelPlayerFreeSpinRequest request) {
        PlainServiceInfo psi = AggregatorWildcardUPSs.findCurrencyScopedIrgsServiceInfo(
                request.getCurrency().toLowerCase(), cloud, operator.code(), provider().code());

        var freeSpin = request.getFreeSpin();
        CancelPlayerFreeSpinResponse response = new CancelPlayerFreeSpinResponse(request.getCampaign(), freeSpin);
        var freeSpinCampaignOpt = readFreeSpinCampaign(request.getCampaign(), operator, provider());
        if (freeSpinCampaignOpt.isEmpty()) {
            response.setCode(HttpStatus.SC_NOT_FOUND);
            response.setMessage(BONUS_CODE_NOT_FOUND_MSG);
            return response;
        }
        var freeSpinResponse = removePlayerFromBonus(psi, request.getCurrency(), freeSpin);
        response.setFreeSpin(freeSpinResponse);
        return response;
    }

    @Override
    public CancelFreeSpinsResponse cancelFreeSpinsBatching(OperatorSpec operator, CancelFreeSpinsRequest request) throws Exception {
        PlainServiceInfo psi = AggregatorWildcardUPSs.findCurrencyScopedIrgsServiceInfo(
                request.getCurrency().toLowerCase(), cloud, operator.code(), provider().code());

        List<FreeSpinsInfo> canceledFreeSpinsList = Collections.synchronizedList(Lists.newArrayList());
        CancelFreeSpinsResponse response = new CancelFreeSpinsResponse(request.getCampaign(), canceledFreeSpinsList);

        var freeSpinCampaignOpt = readFreeSpinCampaign(request.getCampaign(), operator, provider());
        if (freeSpinCampaignOpt.isEmpty()) {
            response.setCode(HttpStatus.SC_NOT_FOUND);
            response.setMessage(BONUS_CODE_NOT_FOUND_MSG);
            return response;
        }
        if (request.getFreeSpinsList().size() == 1) {
            var freeSpinInfo = removePlayerFromBonus(psi, request.getCurrency(), request.getFreeSpinsList().getFirst());
            response.setFreeSpinsList(List.of(freeSpinInfo));
        } else {
            try {
                cancelFreeSpinsBufferProcessing(request, canceledFreeSpinsList, psi);
            } catch (FreeSpinsExceededTimeException e) {
                logger.error(e.getMessage(), e);
                response.setCode(HttpStatus.SC_REQUEST_TOO_LONG);
                response.setMessage(e.getMessage());
            }
        }

        return response;
    }

    @Override
    protected CreateFreeSpinsCampaignResponse createProviderFreeSpinsCampaign(OperatorSpec operator, CreateFreeSpinsCampaignRequest request) throws Exception {
        return super.createProviderFreeSpinsCampaign(operator, request);
    }

    @Override
    protected void validateProviderFreeSpinsCampaign(OperatorSpec operator, CreateFreeSpinsBaseRequest request) throws Exception {
        super.validateProviderFreeSpinsCampaign(operator, request);
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.RUBY;
    }

    private CreateFreeSpinResult createFreeSpins(
            OperatorSpec operator,
            CreateFreeSpinsBaseRequest request,
            List<String> playerIdList) throws JsonProcessingException {
        FreeSpinCampaign freeSpinCampaign;
        var campaignOpt = readFreeSpinsCampaign(operator, provider(), request.getBonusCode());

        if (campaignOpt.isPresent()) {
            freeSpinCampaign = campaignOpt.get();
        } else {
            insertFreeSpinCampaign(operator, provider(), request, FreeSpinCampaignStatus.CREATED);
            freeSpinCampaign = FreeSpinsUtils.toFreeSpinCampaign(request);
        }

        PlainServiceInfo psi = AggregatorWildcardUPSs.findCurrencyScopedIrgsServiceInfo(freeSpinCampaign.getCurrency().toLowerCase(),
                cloud,
                operator.code(),
                provider().code());

        String auth = buildAuthHeader(psi);
        var acountIdList = Mappers.toAccountIdList(playerIdList);
        int expiresInDays = getExpiresInDays(freeSpinCampaign.getStartDate(), freeSpinCampaign.getExpirationDate());
        var type = FreeSpinsUtils.getCheckedFreeSpinsType(freeSpinCampaign.getType());
        var createNewCampaignRequest = Mappers.toCreateNewCampaignRequestWithoutGame(freeSpinCampaign, expiresInDays, acountIdList, type);
        if (StringUtils.isNotEmpty(request.getRequestId())) {
            createNewCampaignRequest.setRequestId(request.getRequestId());
        }

        // Ruby requires that the campaign start date and time should not be in the past.
        // However, we cannot guarantee this for all free spins due to processing delays.
        var now = LocalDateTime.now(ZoneOffset.UTC);
        if (createNewCampaignRequest.getStartDateTime().isBefore(now)) {
            createNewCampaignRequest.setStartDateTime(now);
            createNewCampaignRequest.setExpiresInDays(getExpiresInDays(now, createNewCampaignRequest.getEndDateTime()));
        }

        PlainServiceInfo psiCopy = new PlainServiceInfo(psi.getId(), psi.getUri());
        List<String> games = Arrays.stream(freeSpinCampaign.getGames().split(",")).toList();
        createNewCampaignRequest.setGameIds(games);

        String providerRequest = mapper.writeValueAsString(createNewCampaignRequest);
        var freeSpinsInfoBuilder = FreeSpinsInfo.builder();
        try {
            var resp = rubyApi.createNewCampaign(psiCopy, providerRequest, auth);
            freeSpinsInfoBuilder
                    .freeSpinsId(resp.getId())
                    .isApplied(CAMPAIGN_OK_UPCOMING.equals(resp.getStatus()) || CAMPAIGN_OK_ONGOING.equals(resp.getStatus()))
                    .message(resp.getMessage());
        } catch (Exception e) {
            freeSpinsInfoBuilder
                    .isApplied(false)
                    .message(e.getMessage());
        }
        return CreateFreeSpinResult.builder()
                .freeSpinsInfo(freeSpinsInfoBuilder.build())
                .currency(freeSpinCampaign.getCurrency())
                .build();
    }

    private static int getExpiresInDays(LocalDateTime startDate, LocalDateTime expirationDate) {
        var minsBetween = ChronoUnit.MINUTES.between(startDate, expirationDate);
        double days = minsBetween / 1440.0;
        return (int) Math.ceil(days);
    }

    @Override
    protected FreeSpinsInfo removePlayerFromBonus(PlainServiceInfo psi, String currency, FreeSpinsInfo freeSpin) {
        String auth = buildAuthHeader(psi);
        PlainServiceInfo psiCopy = new PlainServiceInfo(psi.getId(), psi.getUri());
        PlayerInfoRequest playerInfoRequest = new PlayerInfoRequest(freeSpin.getPlayerId(), freeSpin.getFreeSpinsId());
        var freeSpinsInfoBuilder = FreeSpinsInfo.builder();
        try {
            var resp = rubyApi.removePlayerFromCampaign(psiCopy, playerInfoRequest, auth);
            if (Objects.isNull(resp)) {
                freeSpinsInfoBuilder
                        .playerId(freeSpin.getPlayerId())
                        .freeSpinsId(freeSpin.getFreeSpinsId())
                        .isApplied(true);
            } else {
                freeSpinsInfoBuilder
                        .playerId(freeSpin.getPlayerId())
                        .freeSpinsId(freeSpin.getFreeSpinsId())
                        .message(resp.getCode())
                        .isApplied(false);
            }
        } catch (Exception e) {
            freeSpinsInfoBuilder
                    .playerId(freeSpin.getPlayerId())
                    .freeSpinsId(freeSpin.getFreeSpinsId())
                    .isApplied(false)
                    .message(e.getMessage());
        }
        return freeSpinsInfoBuilder.build();
    }

    private void validateRequestId(String requestId) throws FreeSpinsException {
        if (requestId.length() > REQUEST_ID_MAX_LENGTH) {
            throw new FreeSpinsException(REQUEST_ID_EXCEEDED_32_CHARS);
        }
    }
}
