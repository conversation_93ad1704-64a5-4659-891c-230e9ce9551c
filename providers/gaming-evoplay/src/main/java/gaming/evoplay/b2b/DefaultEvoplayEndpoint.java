package gaming.evoplay.b2b;

import static gaming.evoplay.Mappers.errorMessage;

import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.LinkedList;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;
import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.UnexpectedHttpStatusException;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenRequestInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenResponseInfo;
import aggregator.wallet.patrianna.types.IdentityByAccountIdInfo;
import aggregator.wallet.patrianna.types.IdentityInfo;
import aggregator.wallet.patrianna.types.WalletSessionResponseInfo;
import aggregator.wallet.patrianna.types.WalletTransactionInfo;
import common.AbstractApiEndpoint;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.flatten.TempTokenFlatten;
import common.limiter.ErrorRateLimiter;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.HiddenModeUtils;
import common.utils.WalletSessionUtils;
import gaming.evoplay.EvoplayGamingService;
import gaming.evoplay.Mappers;
import gaming.evoplay.b2b.model.Details;
import gaming.evoplay.b2b.model.ErrorCode;
import gaming.evoplay.b2b.model.ResponseToEvoplay;
import gaming.evoplay.exception.EvoplayException;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.QueryStringDecoder;
import io.netty.util.AsciiString;
import io.vavr.CheckedFunction0;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.AbstractMultivaluedMap;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

public class DefaultEvoplayEndpoint extends AbstractApiEndpoint implements EvoplayEndpoint {
    private static final String CALLBACK_VERSION = "2";
    public static final String PARAM_TOKEN = "token";
    public static final String PARAM_CALLBACK_ID = "callback_id";
    public static final String PARAM_NAME = "name";
    public static final String PARAM_ROUND_ID = "data[round_id]";
    public static final String PARAM_ACTION_ID = "data[action_id]";
    public static final String PARAM_FINAL_ACTION = "data[final_action]";
    public static final String PARAM_AMOUNT = "data[amount]";
    public static final String PARAM_CURRENCY = "data[currency]";
    public static final String PARAM_DETAILS = "data[details]";
    public static final String PARAM_REFUND_ROUND_ID = "data[refund_round_id]";
    public static final String PARAM_REFUND_ACTION_ID = "data[refund_action_id]";
    public static final String PARAM_REFUND_CALLBACK_ID = "data[refund_callback_id]";
    public static final String PARAM_SIGNATURE = "signature";
    private static final String PARAM_ID = "data[id]";
    private static final String PARAM_USER_ID = "data[user_id]";
    private static final String PARAM_WALLET_TYPE = "data[wallet_type]";
    private static final String WALLET_TYPE_REAL = "real";
    private static final String DEFAULT_GAME = "no_game";
    private static final String BONUS_GAME = "bonus";

    private final EvoplayGamingService gamingService;
    private final CommonObjectMapper mapper;
    private final ErrorRateLimiter errorRateLimiter;

    public DefaultEvoplayEndpoint(AggregatorServerProperties props,
            DynamicCloud cloud,
            EvoplayGamingService gamingService,
            CommonObjectMapper mapper,
            ErrorRateLimiter errorRateLimiter) {
        super(props, cloud);
        this.gamingService = Objects.requireNonNull(gamingService);
        this.mapper = Objects.requireNonNull(mapper);
        this.errorRateLimiter = Objects.requireNonNull(errorRateLimiter);
    }

    @Override
    public void callback(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, OperatorSpec operator, InputStream io) throws Exception {
        io.reset();
        String payload = IOUtils.toString(io, StandardCharsets.UTF_8);
        QueryStringDecoder decoder = new QueryStringDecoder(payload, false);
        MultivaluedMap<String, String> parameters = new AbstractMultivaluedMap<>(decoder.parameters()) {};

        logger.debug("request: {}", parameters);
        try {
            switch (parameters.getFirst(PARAM_NAME)) {
                case "init" -> init(async, ctx, headers, operator, parameters);
                case "bet" -> bet(async, ctx, headers, operator, parameters);
                case "win" -> win(async, ctx, headers, operator, parameters);
                case "refund" -> refund(async, ctx, headers, operator, parameters);
                case "BalanceIncrease" -> increaseBalance(async, ctx, headers, operator, parameters);
                default -> sendError(async, ErrorCode.INVALID_CALLBACK_NAME);
            }
        } catch (Throwable t) {
            sendError(async, logAndMapToErrorCode(t));
        }
    }

    private void init(
            AsyncResponse async,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            MultivaluedMap<String, String> parameters) {
        checkSignature(parameters, operator);
        var token = parameters.getFirst(PARAM_TOKEN);
        var tempTokenOpt = tempTokenFlatten(token);
        var remoteIp = remoteIp(ctx, headers);

        if (tempTokenOpt.isEmpty()) {
            sendError(async, ErrorCode.INVALID_KEY);
            return;
        }

        var tempToken = tempTokenOpt.get();
        MDC.put(MdcTags.MDC_ROUTING_KEY, tempToken.getRoutingKey());
        var identity = toIdentity(remoteIp, tempToken.getAccountId());
        var proxyReq = new GetPermanentTokenRequestInfo();
        proxyReq.setIdentity(identity);
        proxyReq.setToken(token);

        gamingService.authenticate(
                proxyReq,
                DEFAULT_GAME,
                ProviderSpec.EVOPLAY,
                operator,
                AsciiString.cached(tempToken.getRoutingKey())).addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(GetPermanentTokenResponseInfo resp) {
                        try {
                            for (var balance : resp.getBalances()) {
                                if (balance.getCurrency().equals(tempToken.getCurrency())) {
                                    MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                    async.resume(ResponseToEvoplay.ok(String.valueOf(balance.getBalance()), balance.getCurrency()));
                                }
                            }
                        } catch (Throwable t) {
                            onFailure(t);
                        }
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        sendError(async, logAndMapToErrorCode(t));
                    }
                }, MoreExecutors.directExecutor());
    }

    private void bet(
            AsyncResponse async,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            MultivaluedMap<String, String> parameters) throws Throwable {
        checkSignature(parameters, operator);
        var token = parameters.getFirst(PARAM_TOKEN);
        var amount = parameters.getFirst(PARAM_AMOUNT);
        var currency = parameters.getFirst(PARAM_CURRENCY);
        var roundId = getShortenRoundIdIfNeeded(parameters.getFirst(PARAM_ROUND_ID));
        var actionId = parameters.getFirst(PARAM_ACTION_ID);
        var finalAction = parameters.getFirst(PARAM_FINAL_ACTION);
        var details = readDetails(parameters);
        var remoteIp = remoteIp(ctx, headers);
        var flattenOpt = tempTokenFlatten(token);
        if (flattenOpt.isEmpty()) {
            sendError(async, ErrorCode.INVALID_KEY);
            return;
        }
        var flatten = flattenOpt.get();
        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        if (!flatten.getCurrency().equals(currency)) {
            sendError(async, ErrorCode.INVALID_CURRENCY);
            return;
        }
        if (Objects.isNull(amount)) {
            sendError(async, ErrorCode.INVALID_AMOUNT);
            return;
        }
        var identity = toIdentity(remoteIp, flatten.getAccountId());
        var walletRequest = Mappers.toWalletSessionRequestInfo(
                determineProviderSpec(),
                WalletTransactionInfo.TYPE_DEBIT,
                roundId,
                toBoolean(finalAction),
                actionId,
                new BigDecimal(amount),
                identity,
                currency,
                PlatformUtil.toLocalUTCDate(),
                details.getGameId(),
                details.getFreeSpinCampaign(),
                details.getFreeSpinId());

        gamingService.submit(
                walletRequest,
                token,
                ProviderSpec.EVOPLAY,
                operator,
                AsciiString.cached(flatten.getRoutingKey())).addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(WalletSessionResponseInfo resp) {
                        try {
                            for (var balance : resp.getBalances()) {
                                if (balance.getCurrency().equals(flatten.getCurrency())) {
                                    MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                    async.resume(ResponseToEvoplay.ok(String.valueOf(balance.getBalance()), balance.getCurrency()));
                                }
                            }
                        } catch (Throwable t) {
                            onFailure(t);
                        }
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        sendError(async, logAndMapToErrorCode(t));
                    }
                }, MoreExecutors.directExecutor());
    }

    private void win(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, OperatorSpec operator, MultivaluedMap<String, String> parameters)
            throws Throwable {
        checkSignature(parameters, operator);
        var token = parameters.getFirst(PARAM_TOKEN);
        var roundId = getShortenRoundIdIfNeeded(parameters.getFirst(PARAM_ROUND_ID));
        var actionId = parameters.getFirst(PARAM_ACTION_ID);
        var amount = parameters.getFirst(PARAM_AMOUNT);
        var finalAction = parameters.getFirst(PARAM_FINAL_ACTION);
        var details = readDetails(parameters);

        var remoteIp = remoteIp(ctx, headers);
        var flattenOpt = tempTokenFlatten(token);
        if (flattenOpt.isEmpty()) {
            sendError(async, ErrorCode.INVALID_KEY);
            return;
        }
        if (Objects.isNull(amount)) {
            sendError(async, ErrorCode.INVALID_AMOUNT);
            return;
        }
        var flatten = flattenOpt.get();
        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        var identity = toIdentity(remoteIp, flatten.getAccountId());
        var walletRequest = Mappers.toWalletSessionRequestInfo(
                determineProviderSpec(),
                WalletTransactionInfo.TYPE_CREDIT,
                roundId,
                toBoolean(finalAction),
                actionId,
                new BigDecimal(amount),
                identity,
                flatten.getCurrency(),
                PlatformUtil.toLocalUTCDate(),
                details.getGameId(),
                details.getFreeSpinCampaign(),
                details.getFreeSpinId());

        gamingService.submit(
                walletRequest,
                token,
                ProviderSpec.EVOPLAY,
                operator,
                AsciiString.cached(flatten.getRoutingKey())).addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(WalletSessionResponseInfo resp) {
                        try {
                            for (var balance : resp.getBalances()) {
                                if (balance.getCurrency().equals(flatten.getCurrency())) {
                                    MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                    async.resume(ResponseToEvoplay.ok(String.valueOf(balance.getBalance()), balance.getCurrency()));
                                }
                            }
                        } catch (Throwable t) {
                            onFailure(t);
                        }
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        sendError(async, logAndMapToErrorCode(t));
                    }
                }, MoreExecutors.directExecutor());
    }

    private void refund(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, OperatorSpec operator, MultivaluedMap<String, String> parameters) {
        checkSignature(parameters, operator);
        var token = parameters.getFirst(PARAM_TOKEN);
        var refundRoundId = getShortenRoundIdIfNeeded(parameters.getFirst(PARAM_REFUND_ROUND_ID));
        var refundActionId = parameters.getFirst(PARAM_REFUND_ACTION_ID);
        var amount = parameters.getFirst(PARAM_AMOUNT);

        var remoteIp = remoteIp(ctx, headers);
        var flattenOpt = tempTokenFlatten(token);
        if (flattenOpt.isEmpty()) {
            sendError(async, ErrorCode.INVALID_KEY);
            return;
        }
        var flatten = flattenOpt.get();
        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        var identity = toIdentity(remoteIp, flatten.getAccountId());

        var walletRequest = Mappers.toCancelWalletSessionRequestInfo(
                refundRoundId,
                refundActionId,
                new BigDecimal(amount),
                identity,
                flatten.getCurrency(),
                PlatformUtil.toLocalUTCDate(),
                determineProviderSpec());

        gamingService.refund(
                walletRequest,
                token,
                ProviderSpec.EVOPLAY,
                operator,
                AsciiString.cached(flatten.getRoutingKey())).addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(AccountBalanceResponseInfo resp) {
                        try {
                            for (var balance : resp.getBalances()) {
                                if (balance.getCurrency().equals(flatten.getCurrency())) {
                                    MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                    async.resume(ResponseToEvoplay.ok(String.valueOf(balance.getBalance()), balance.getCurrency()));
                                }
                            }
                        } catch (Throwable t) {
                            onFailure(t);
                        }
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        sendError(async, logAndMapToErrorCode(t));
                    }
                }, MoreExecutors.directExecutor());
    }

    private void increaseBalance(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, OperatorSpec operator,
            MultivaluedMap<String, String> parameters) {
        var txId = parameters.getFirst(PARAM_ID);
        var userId = parameters.getFirst(PARAM_USER_ID);
        var currency = parameters.getFirst(PARAM_CURRENCY);
        var amountStr = parameters.getFirst(PARAM_AMOUNT);
        var walletType = parameters.getFirst(PARAM_WALLET_TYPE);
        checkSignature(parameters, operator, currency);
        if (!isBalanceIncreaseEnabled()) {
            sendError(async, ErrorCode.WL_ERROR);
            return;
        }

        var accountFlattenOpt = AccountRoutingCurrencyFlatten.underscore().readExternalOpt(userId);
        if (accountFlattenOpt.isEmpty()) {
            sendError(async, ErrorCode.INVALID_KEY);
            return;
        }
        var accountFlatten = accountFlattenOpt.get();
        MDC.put(MdcTags.MDC_ROUTING_KEY, accountFlatten.getRoutingKey());
        if (Objects.isNull(amountStr) || !walletType.equals(WALLET_TYPE_REAL)) {
            sendError(async, ErrorCode.INVALID_AMOUNT);
            return;
        }
        var walletRequest = Mappers.toWalletSessionRequestInfo(
                determineProviderSpec(),
                WalletTransactionInfo.TYPE_CREDIT,
                shortenWalletSession(txId),
                true,
                txId,
                new BigDecimal(amountStr),
                toIdentity(remoteIp(ctx, headers), accountFlatten.getAccountId()),
                currency,
                PlatformUtil.toLocalUTCDate(),
                BONUS_GAME,
                null,
                null);

        gamingService.submitBonusCredit(
                walletRequest,
                ProviderSpec.EVOPLAY,
                operator,
                AsciiString.cached(accountFlatten.getRoutingKey())).addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(WalletSessionResponseInfo resp) {
                        try {
                            for (var balance : resp.getBalances()) {
                                if (balance.getCurrency().equals(currency)) {
                                    MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                    async.resume(ResponseToEvoplay.ok(String.valueOf(balance.getBalance()), balance.getCurrency()));
                                }
                            }
                        } catch (Throwable t) {
                            onFailure(t);
                        }
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        sendError(async, logAndMapToErrorCode(t));
                    }
                }, MoreExecutors.directExecutor());
    }

    private ProviderSpec determineProviderSpec() {
        return HiddenModeUtils.determineProviderByHiddenMode(props, ProviderSpec.EVOPLAY);
    }

    private static boolean toBoolean(String value) {
        return "1".equals(value);
    }

    private String getShortenRoundIdIfNeeded(String roundId) {
        if (roundId.length() > props.MAX_ROUND_ID_LENGTH.get()) {
            return shortenWalletSession(roundId);
        }
        return roundId;
    }

    private static void sendError(AsyncResponse async, ErrorCode errorCode) {
        MDC.remove(MdcTags.MDC_ROUTING_KEY);
        async.resume(okResponse(ResponseToEvoplay.error(errorCode.callbackCanBeResent(), errorMessage(errorCode))));
    }

    private Details readDetails(MultivaluedMap<String, String> parameters) throws Throwable {
        var detailsStr = parameters.getFirst(PARAM_DETAILS);
        return DefaultBlockHoundIntegration.allowBlockingUnchecked((CheckedFunction0<Details>) () -> mapper.readValue(detailsStr, Details.class));
    }

    void checkSignature(MultivaluedMap<String, String> parameters, OperatorSpec operator) {
        checkSignature(parameters, operator, currencyFromToken(parameters));
    }

    private void checkSignature(MultivaluedMap<String, String> parameters, OperatorSpec operator, String currency) {
        if (props.isDevMode()) {
            return;
        }
        var si = AggregatorWildcardUPSs.<PlainServiceInfo> findCurrencyScopedIrgsServiceInfo(
                currency.toLowerCase(), cloud, operator.code(), ProviderSpec.EVOPLAY.code());
        logger.debug("checking signature of {} ...", parameters);

        var signature = parameters.getFirst(PARAM_SIGNATURE);
        var beforeMD5 = new LinkedList<String>();
        var data = new LinkedList<String>();

        beforeMD5.add(si.getUserName());
        beforeMD5.add(CALLBACK_VERSION);
        parameters.keySet().forEach(key -> {
            if (!PARAM_SIGNATURE.equals(key)) {
                String value = parameters.getFirst(key);
                if (key.startsWith("data[")) {
                    data.add(value);
                } else {
                    beforeMD5.add(value);
                }
            }
        });
        if (!data.isEmpty()) {
            beforeMD5.add(String.join(":", data));
        }
        beforeMD5.add(si.getPassword());
        malformed(signature, DigestUtils.md5Hex(String.join("*", beforeMD5)));
    }

    private boolean isBalanceIncreaseEnabled() {
        return Boolean.TRUE.equals(props.EVOPLAY_INCREASE_BALANCE.get());
    }

    static Optional<TempTokenFlatten> tempTokenFlatten(String token) {
        return TempTokenFlatten.underscore()
                .readExternalOpt(token);
    }

    static String currencyFromToken(MultivaluedMap<String, String> parameters) {
        var token = parameters.getFirst(PARAM_TOKEN);
        if (token == null) {
            throw new WebApplicationException("Malformed request", Response.Status.FORBIDDEN);
        }
        var flatten = tempTokenFlatten(token).get();
        return flatten.getCurrency();
    }

    static IdentityInfo toIdentity(String remoteIp, String accountId) {
        var byAccountId = new IdentityByAccountIdInfo(accountId, remoteIp);
        return new IdentityInfo(byAccountId);
    }

    private static String shortenWalletSession(String roundId) {
        return WalletSessionUtils.shortenWalletSession(roundId);
    }

    ErrorCode logAndMapToErrorCode(Throwable throwable) {
        var codeToReturn = ErrorCode.WL_ERROR;
        if (throwable instanceof UnexpectedHttpStatusException ex) {
            MDC.put(MdcTags.MDC_ERROR_CODE, String.valueOf(ex.getCode()));
            codeToReturn = byCode(ex.getCode());
        }
        if (throwable instanceof EvoplayException ex) {
            MDC.put(MdcTags.MDC_ERROR_CODE, String.valueOf(ex.getCode()));
            codeToReturn = byCode(ex.getCode());
        }
        if (codeToReturn == ErrorCode.WL_ERROR) {
            var cause = getRootCause(throwable);
            logError(throwable, errorRateLimiter.check(cause));
        } else {
            logWarn(throwable);
        }
        return ErrorCode.WL_ERROR;
    }

    static ErrorCode byCode(int code) {
        return switch (code) {
            case HttpStatus.SC_PAYMENT_REQUIRED -> ErrorCode.NOT_ENOUGH_MONEY;
            case HttpStatus.SC_UNAUTHORIZED, HttpStatus.SC_FORBIDDEN, HttpStatus.SC_NOT_FOUND, HttpStatus.SC_LOCKED -> ErrorCode.INVALID_KEY;
            default -> ErrorCode.WL_ERROR;
        };
    }

    static Response okResponse(Object entity) {
        return Response.status(Response.Status.OK).entity(entity).build();
    }
}
