package gaming.playtech.api.freespins;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpStatus;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.stereotype.Service;

import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.freespins.patrianna.UamFreeSpinsClient;
import aggregator.model.FreeSpinCampaign;
import common.AbstractFreeSpinsApi;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.exception.FreeSpinsExceededTimeException;
import common.exception.FreeSpinsException;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CancelFreeSpinsRequest;
import common.model.freespins.CancelFreeSpinsResponse;
import common.model.freespins.CancelPlayerFreeSpinRequest;
import common.model.freespins.CancelPlayerFreeSpinResponse;
import common.model.freespins.CreateFreeSpinResult;
import common.model.freespins.CreateFreeSpinsRequest;
import common.model.freespins.CreateFreeSpinsResponse;
import common.model.freespins.FreeSpinIdempotencyStatus;
import common.model.freespins.FreeSpinsInfo;
import common.model.freespins.FreeSpinsStatus;
import common.utils.BaseMapper;
import gaming.playtech.Mappers;
import gaming.playtech.api.PlaytechFreeSpinsApi;
import gaming.playtech.api.freespins.model.GiveFreeSpinsResponse;
import gaming.playtech.api.freespins.model.RemoveBonusRequest;
import gaming.playtech.b2b.model.common.PlayerData;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.handler.codec.http.QueryStringDecoder;
import jakarta.inject.Inject;

@Service
public class DefaultFreeSpinsPlaytechService extends AbstractFreeSpinsApi {
    private static final String COUNTRY_CODE = "US";
    private static final List<String> LIVE_SLOTS_FS_GAMES = List.of("bfbl", "ejpl"); // todo temporary, live slots games, should check from db
    private final PlaytechFreeSpinsApi playtechFreeSpinsApi;

    @Inject
    public DefaultFreeSpinsPlaytechService(
            AggregatorServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            PlaytechFreeSpinsApi playtechFreeSpinsApi,
            UamFreeSpinsClient uamFreeSpinsClient,
            SpannerTemplate spannerTemplate,
            CacheManager cacheManager) {
        super(props, cloud, meterRegistry, uamFreeSpinsClient, spannerTemplate, cacheManager);
        this.playtechFreeSpinsApi = Objects.requireNonNull(playtechFreeSpinsApi);
    }

    @Override
    public CreateFreeSpinsResponse createFreeSpins(OperatorSpec operator, CreateFreeSpinsRequest request) throws Exception {
        var createFreeSpinsResponseOpt = getCreateFreeSpinsResponseByRequestId(request.getBonusCode(), operator, provider(), request.getRequestId());
        if (createFreeSpinsResponseOpt.isPresent()) {
            var response = createFreeSpinsResponseOpt.get();
            validateBonusCodeFromRequestId(request, response.getBonusCode());
            return new CreateFreeSpinsResponse(response.getBonusCode(), List.of(response.getFreeSpinsList().getFirst()), response.getCurrency());
        }

        PlainServiceInfo psi = getServiceInfo(cloud);
        var createFreeSpinResult = createFreeSpin(operator, request, psi);
        var freeSpinsInfoResponse = createFreeSpinResult.freeSpinsInfo();

        return new CreateFreeSpinsResponse(request.getBonusCode(), List.of(freeSpinsInfoResponse), createFreeSpinResult.currency());
    }

    @Override
    public CancelPlayerFreeSpinResponse cancelPlayerFreeSpin(OperatorSpec operator, CancelPlayerFreeSpinRequest request) throws Exception {
        var freeSpin = request.getFreeSpin();
        CancelPlayerFreeSpinResponse response = new CancelPlayerFreeSpinResponse(request.getCampaign(), freeSpin);
        var freeSpinCampaignOpt = readFreeSpinCampaign(request.getCampaign(), operator, provider());
        if (freeSpinCampaignOpt.isEmpty()) {
            response.setCode(HttpStatus.SC_NOT_FOUND);
            response.setMessage(BONUS_CODE_NOT_FOUND_MSG);
            return response;
        }
        PlainServiceInfo psi = getServiceInfo(cloud);
        var playerId = freeSpin.getPlayerId();
        var playerIdOperator = BaseMapper.toPlayerIdWithOperatorShortCode(playerId, operator.code());
        freeSpin.setPlayerId(playerIdOperator);
        var freeSpinResponse = removePlayerFromBonus(psi, request.getCurrency(), freeSpin);
        freeSpinResponse.setPlayerId(playerId);
        response.setFreeSpin(freeSpinResponse);
        return response;
    }

    @Override
    public CancelFreeSpinsResponse cancelFreeSpinsBatching(OperatorSpec operator, CancelFreeSpinsRequest request) throws Exception {
        PlainServiceInfo psi = getServiceInfo(cloud);
        List<FreeSpinsInfo> canceledFreeSpinsList = Collections.synchronizedList(Lists.newArrayList());
        CancelFreeSpinsResponse response = new CancelFreeSpinsResponse(request.getCampaign(), canceledFreeSpinsList);

        var freeSpinCampaignOpt = readFreeSpinCampaign(request.getCampaign(), operator, provider());
        if (freeSpinCampaignOpt.isEmpty()) {
            response.setCode(HttpStatus.SC_NOT_FOUND);
            response.setMessage(BONUS_CODE_NOT_FOUND_MSG);
            return response;
        }

        toPlayerIdsWithOperator(operator, request.getFreeSpinsList());

        if (request.getFreeSpinsList().size() == 1) {
            var freeSpinInfo = removePlayerFromBonus(psi, request.getCurrency(), request.getFreeSpinsList().getFirst());
            response.setFreeSpinsList(List.of(freeSpinInfo));
        } else {
            try {
                cancelFreeSpinsBufferProcessing(request, canceledFreeSpinsList, psi);
            } catch (FreeSpinsExceededTimeException e) {
                logger.error(e.getMessage(), e);
                response.setCode(HttpStatus.SC_REQUEST_TOO_LONG);
                response.setMessage(e.getMessage());
            }
        }

        if (CollectionUtils.isNotEmpty(response.getFreeSpinsList())) {
            toSystemPlayerIds(response.getFreeSpinsList());
        }

        return response;
    }

    private static void toSystemPlayerIds(List<FreeSpinsInfo> freeSpinsList) {
        freeSpinsList.forEach(freeSpinsInfo -> {
            var playerId = BaseMapper.removeOperatorShortCode(freeSpinsInfo.getPlayerId());
            freeSpinsInfo.setPlayerId(playerId);
        });
    }

    private static void toPlayerIdsWithOperator(OperatorSpec operator, List<FreeSpinsInfo> canceledFreeSpinsList) {
        canceledFreeSpinsList.forEach(freeSpin -> {
            var playerIdOperator = BaseMapper.toPlayerIdWithOperatorShortCode(freeSpin.getPlayerId(), operator.code());
            freeSpin.setPlayerId(playerIdOperator);
        });
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.PLAYTECH;
    }

    @Override
    protected FreeSpinsInfo removePlayerFromBonus(PlainServiceInfo psi, String currency, FreeSpinsInfo freeSpin) throws Exception {
        var playtechRequest = RemoveBonusRequest.builder()
                .requestId(PlatformUtil.randomUUID().toString())
                .bonusInstanceCode(freeSpin.getFreeSpinsId())
                .username(freeSpin.getPlayerId())
                .build();
        var playtechResponse = playtechFreeSpinsApi.removeBonus(playtechRequest, psi);
        var freeSpinsInfoBuilder = FreeSpinsInfo.builder();
        if (Objects.isNull(playtechResponse.getError())) {
            freeSpinsInfoBuilder
                    .freeSpinsId(freeSpin.getFreeSpinsId())
                    .playerId(freeSpin.getPlayerId())
                    .isApplied(true);
        } else {
            logger.warn("Free spins cancellation error: {}", playtechResponse.getError().getDescription());
            freeSpinsInfoBuilder
                    .isApplied(false)
                    .message(playtechResponse.getError().getCode());
        }

        return freeSpinsInfoBuilder.build();
    }

    private CreateFreeSpinResult createFreeSpin(
            OperatorSpec operator,
            CreateFreeSpinsRequest request,
            PlainServiceInfo psi) throws Exception {
        var bonusCode = request.getBonusCode();
        var campaignOpt = readFreeSpinsCampaign(operator, provider(), bonusCode);

        if (campaignOpt.isEmpty()) {
            throw new FreeSpinsException(String.format("FS Campaign not found by bonus code: %s", bonusCode));
        }
        FreeSpinCampaign freeSpinCampaign = campaignOpt.get();

        var playerData = PlayerData.builder()
                .countryCode(COUNTRY_CODE)
                .currencyCode(Mappers.toProviderCurrency(freeSpinCampaign.getCurrency()))
                .build();
        var playerId = request.getPlayerId();
        var playerIdOperator = BaseMapper.toPlayerIdWithOperatorShortCode(request.getPlayerId(), operator.code());
        request.setPlayerId(playerIdOperator);
        GiveFreeSpinsResponse playtechResponse;
        var freeSpinsInfoBuilder = FreeSpinsInfo.builder();
        var gameCode = freeSpinCampaign.getGames().split(",")[0];
        if (gameCode.contains(";")) {
            var gameFs = Mappers.toProviderFSLiveGameCode(gameCode);
            if (LIVE_SLOTS_FS_GAMES.contains(gameFs)) {
                playtechResponse = sendGiveFreeSpins(request, psi, playerData, gameFs, freeSpinCampaign);
            } else {
                playtechResponse = sendGiveFreeChips(request, psi, playerData, gameFs, freeSpinCampaign);
            }
        } else {
            playtechResponse = sendGiveFreeSpins(request, psi, playerData, gameCode, freeSpinCampaign);
        }

        if (Objects.isNull(playtechResponse.getError())) {
            freeSpinsInfoBuilder
                    .freeSpinsId(String.valueOf(playtechResponse.getBonusInstanceCode()))
                    .playerId(playerId)
                    .isApplied(true);

            var freeSpinsInfoList = List.of(freeSpinsInfoBuilder.build());
            var accountFreeSpinsList = prepareAccountFreeSpinsList(operator, provider(), request, freeSpinsInfoList, FreeSpinsStatus.CREATED);

            saveFreeSpinsWithIdempotencyKey(operator, provider(), request.getRequestId(), accountFreeSpinsList, FreeSpinIdempotencyStatus.FINISHED);
        } else {
            logger.warn("Free spins creation error: {}", playtechResponse.getError().getDescription());
            freeSpinsInfoBuilder
                    .isApplied(false)
                    .message(playtechResponse.getError().getCode());
        }

        return CreateFreeSpinResult.builder()
                .freeSpinsInfo(freeSpinsInfoBuilder.build())
                .currency(freeSpinCampaign.getCurrency())
                .build();
    }

    private GiveFreeSpinsResponse sendGiveFreeChips(CreateFreeSpinsRequest request, PlainServiceInfo psi, PlayerData playerData, String gameCode,
            FreeSpinCampaign campaign) throws Exception {
        var bonusTemplate = getBonusTemplate(psi, "bonusTemplateLive");
        var playtechRequest = Mappers.toGiveFreeSpinsRequestGoldenChips(request.getRequestId(), bonusTemplate, request.getPlayerId(), playerData, gameCode,
                campaign);
        return playtechFreeSpinsApi.createGoldenChips(playtechRequest, psi);
    }

    private GiveFreeSpinsResponse sendGiveFreeSpins(CreateFreeSpinsRequest request, PlainServiceInfo psi, PlayerData playerData, String gameCode,
            FreeSpinCampaign freeSpinCampaign) throws Exception {
        var bonusTemplate = getBonusTemplate(psi, "bonusTemplate");
        var playtechRequest = Mappers.toGiveFreeSpinsRequest(request.getRequestId(), bonusTemplate, request.getPlayerId(), playerData, gameCode,
                freeSpinCampaign);
        return playtechFreeSpinsApi.createFreeSpins(playtechRequest, psi);
    }

    private static String getBonusTemplate(PlainServiceInfo info, String freeSpinsType) {
        QueryStringDecoder decoder = new QueryStringDecoder(info.getUri());
        Map<String, List<String>> opts = decoder.parameters();
        return Iterables.getOnlyElement(opts.get(freeSpinsType));
    }

    private static <T extends ServiceInfo> T getServiceInfo(DynamicCloud cloud) {
        String si = String.format("%s-%s-key-pass", AggregatorWildcardUPSs.IRGS_PREFIX, ProviderSpec.PLAYTECH.code());
        return UPSs.findRequiredServiceInfoByName(cloud, si);
    }
}
