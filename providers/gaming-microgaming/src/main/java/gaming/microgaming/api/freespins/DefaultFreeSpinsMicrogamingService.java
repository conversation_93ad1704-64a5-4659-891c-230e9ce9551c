package gaming.microgaming.api.freespins;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.http.HttpStatus;
import org.springframework.cloud.DynamicCloud;
import org.springframework.util.StringUtils;

import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.collect.Lists;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.freespins.patrianna.UamFreeSpinsClient;
import aggregator.model.FreeSpinCampaign;
import common.AbstractFreeSpinsApi;
import common.AggregatorServerProperties;
import common.exception.FreeSpinsExceededTimeException;
import common.exception.FreeSpinsException;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CancelFreeSpinsRequest;
import common.model.freespins.CancelFreeSpinsResponse;
import common.model.freespins.CancelPlayerFreeSpinRequest;
import common.model.freespins.CancelPlayerFreeSpinResponse;
import common.model.freespins.CreateFreeSpinsBaseRequest;
import common.model.freespins.CreateFreeSpinsCampaignRequest;
import common.model.freespins.CreateFreeSpinsCampaignResponse;
import common.model.freespins.CreateFreeSpinsRequest;
import common.model.freespins.CreateFreeSpinsResponse;
import common.model.freespins.FreeSpinCampaignStatus;
import common.model.freespins.FreeSpinIdempotencyStatus;
import common.model.freespins.FreeSpinsInfo;
import common.model.freespins.FreeSpinsStatus;
import common.utils.BaseMapper;
import common.utils.FreeSpinsUtils;
import gaming.microgaming.api.MicrogamingFreeSpinsApi;
import gaming.microgaming.api.freespins.model.AssignedFreeSpinInstance;
import gaming.microgaming.api.freespins.model.CreateFreeSpinsBatchResult;
import gaming.microgaming.api.freespins.model.OfferGame;
import gaming.microgaming.api.freespins.model.request.AssignPlayerRequest;
import gaming.microgaming.api.freespins.model.request.AssignPlayersRequest;
import gaming.microgaming.api.freespins.model.request.CancelFreeSpinRequest;
import gaming.microgaming.api.freespins.model.request.CreateOfferRequest;
import gaming.microgaming.api.freespins.model.response.AssignPlayerResponse;
import gaming.microgaming.api.freespins.model.response.AssignPlayersResponse;
import gaming.microgaming.api.freespins.model.response.CancelFreeSpinResponse;
import gaming.microgaming.api.freespins.model.response.CreateOfferResponse;
import gaming.microgaming.utility.Mappers;
import io.micrometer.core.instrument.MeterRegistry;

public class DefaultFreeSpinsMicrogamingService extends AbstractFreeSpinsApi {
    private static final int maxNumberRounds = 100;
    private static final int maxDurationAvailableInDaysAfterAwarded = 99;
    private final MicrogamingFreeSpinsApi freeSpinsApi;

    public DefaultFreeSpinsMicrogamingService(AggregatorServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamFreeSpinsClient uamFreeSpinsClient,
            SpannerTemplate spannerTemplate,
            CacheManager cacheManager,
            MicrogamingFreeSpinsApi freeSpinsApi) {
        super(props, cloud, meterRegistry, uamFreeSpinsClient, spannerTemplate, cacheManager);
        this.freeSpinsApi = freeSpinsApi;
    }

    @Override
    public CreateFreeSpinsCampaignResponse createFreeSpinsCampaign(OperatorSpec operator, CreateFreeSpinsCampaignRequest request) throws Exception {
        var campaignOpt = readFreeSpinsCampaign(operator, provider(), request.getBonusCode());

        if (campaignOpt.isPresent() && StringUtils.hasText(campaignOpt.get().getExternalCampaignId())) {
            validateByExistingCampaign(campaignOpt.get(), request);

            return new CreateFreeSpinsCampaignResponse(request.getBonusCode());
        }

        PlainServiceInfo serviceInfo = getServiceInfo(operator.code(), request.getCurrency());

        var createFreeSpinsRequest = CreateFreeSpinsRequest.builder()
                .bonusCode(request.getBonusCode())
                .currency(request.getCurrency())
                .betAmount(request.getBetAmount())
                .spins(request.getSpins())
                .startDate(request.getStartDate())
                .expirationDate(request.getExpirationDate())
                .games(request.getGames())
                .requestId(request.getRequestId())
                .operator(operator.code())
                .provider(provider().code())
                .build();

        CreateOfferResponse offer = createOffer(createFreeSpinsRequest, copyPlainServiceInfo(serviceInfo));

        if (!offer.isSuccess()) {
            logger.warn(offer.getMessage());
            throw new FreeSpinsException(offer.getMessage());
        }

        if (campaignOpt.isEmpty()) {
            insertFreeSpinCampaign(operator, provider(), request, FreeSpinCampaignStatus.CREATED, offer.getOfferId());
        } else {
            campaignOpt.get().setExternalCampaignId(offer.getOfferId());
            updateFreeSpinCampaign(campaignOpt.get());
        }

        return new CreateFreeSpinsCampaignResponse(request.getBonusCode());
    }

    @Override
    public CreateFreeSpinsResponse createFreeSpins(OperatorSpec operator, CreateFreeSpinsRequest request) throws Exception {
        var createFreeSpinsResponseOpt = getCreateFreeSpinsResponseByRequestId(request.getBonusCode(), operator, provider(), request.getRequestId());

        if (createFreeSpinsResponseOpt.isPresent()) {
            var response = createFreeSpinsResponseOpt.get();
            validateBonusCodeFromRequestId(request, response.getBonusCode());

            return new CreateFreeSpinsResponse(response.getBonusCode(), response.getFreeSpinsList(), response.getCurrency());
        }

        var createFreeSpinsResult = createFreeSpinsBatch(operator, request);
        List<FreeSpinsInfo> freeSpins = createFreeSpinsResult.freeSpins();

        var accountFreeSpinsList = prepareAccountFreeSpinsList(operator, provider(), request, freeSpins, FreeSpinsStatus.CREATED);
        saveFreeSpinsWithIdempotencyKey(operator, provider(), request.getRequestId(), accountFreeSpinsList, FreeSpinIdempotencyStatus.FINISHED);

        return new CreateFreeSpinsResponse(request.getBonusCode(), freeSpins, createFreeSpinsResult.currency());
    }

    @Override
    protected void validateProviderFreeSpinsCampaign(OperatorSpec operator, CreateFreeSpinsBaseRequest request) throws Exception {
        super.validateProviderFreeSpinsCampaign(operator, request);
        checkStartDate(request.getStartDate());
        checkFreeSpinsAmount(request.getSpins());
        checkDuration(request.getStartDate(), request.getExpirationDate());
    }

    @Override
    public CancelPlayerFreeSpinResponse cancelPlayerFreeSpin(OperatorSpec operator, CancelPlayerFreeSpinRequest request) throws Exception {
        PlainServiceInfo serviceInfo = getServiceInfo(operator.code(), request.getCurrency());

        CancelPlayerFreeSpinResponse response = new CancelPlayerFreeSpinResponse();
        response.setBonusCode(request.getCampaign());

        var freeSpinCampaignOpt = readFreeSpinCampaign(request.getCampaign(), operator, provider());

        if (freeSpinCampaignOpt.isEmpty()) {
            response.setCode(HttpStatus.SC_NOT_FOUND);
            response.setMessage(BONUS_CODE_NOT_FOUND_MSG);

            return response;
        }

        var freeSpinInfo = removePlayerFromBonus(serviceInfo, request.getCurrency(), request.getFreeSpin());
        response.setFreeSpin(freeSpinInfo);

        return response;
    }

    @Override
    public CancelFreeSpinsResponse cancelFreeSpinsBatching(OperatorSpec operator, CancelFreeSpinsRequest request) throws Exception {
        PlainServiceInfo serviceInfo = getServiceInfo(operator.code(), request.getCurrency());

        List<FreeSpinsInfo> canceledFreeSpinsList = Collections.synchronizedList(Lists.newArrayList());
        CancelFreeSpinsResponse response = new CancelFreeSpinsResponse(request.getCampaign(), canceledFreeSpinsList);

        var freeSpinCampaignOpt = readFreeSpinCampaign(request.getCampaign(), operator, provider());

        if (freeSpinCampaignOpt.isEmpty()) {
            response.setCode(HttpStatus.SC_NOT_FOUND);
            response.setMessage(BONUS_CODE_NOT_FOUND_MSG);

            return response;
        }

        if (request.getFreeSpinsList() != null && !request.getFreeSpinsList().isEmpty()) {
            if (request.getFreeSpinsList().size() == 1) {
                var freeSpinInfo = removePlayerFromBonus(serviceInfo, request.getCurrency(), request.getFreeSpinsList().getFirst());

                response.setFreeSpinsList(List.of(freeSpinInfo));
            } else {
                try {
                    cancelFreeSpinsBufferProcessing(request, canceledFreeSpinsList, serviceInfo);
                } catch (FreeSpinsExceededTimeException e) {
                    logger.error(e.getMessage(), e);

                    response.setCode(HttpStatus.SC_REQUEST_TOO_LONG);
                    response.setMessage(e.getMessage());
                }
            }
        } else {
            logger.error("Free spins batching cancel list is invalid {}", request.getFreeSpinsList());
            response.setMessage("Free spins batching cancel list is invalid");
            response.setCode(HttpStatus.SC_BAD_REQUEST);
        }

        return response;
    }

    @Override
    public FreeSpinsInfo removePlayerFromBonus(PlainServiceInfo serviceInfo, String currency, FreeSpinsInfo freeSpin) throws Exception {
        var response = cancelFreeSpins(freeSpin, getOperatorFromServiceInfo(serviceInfo), currency, serviceInfo);

        if (response.isSuccess()) {
            return FreeSpinsInfo.builder()
                    .playerId(freeSpin.getPlayerId())
                    .freeSpinsId(freeSpin.getFreeSpinsId())
                    .isApplied(true)
                    .build();
        }

        return FreeSpinsInfo.builder()
                .playerId(freeSpin.getPlayerId())
                .isApplied(false)
                .message(response.getMessage())
                .build();
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.MICROGAMING;
    }

    private List<FreeSpinsInfo> createPlayerFreeSpins(OperatorSpec operator,
            CreateFreeSpinsRequest request) throws Exception {
        var campaignOpt = readFreeSpinsCampaign(operator, provider(), request.getBonusCode());
        CreateOfferResponse offerResponse = new CreateOfferResponse();
        FreeSpinCampaign freeSpinCampaign;
        PlainServiceInfo serviceInfo;

        if (campaignOpt.isPresent() && StringUtils.hasText(campaignOpt.get().getExternalCampaignId())) {
            offerResponse.setOfferId(campaignOpt.get().getExternalCampaignId());
            freeSpinCampaign = campaignOpt.get();
            serviceInfo = getServiceInfo(operator.code(), freeSpinCampaign.getCurrency());
        } else {
            var createFreeSpinsRequest = CreateFreeSpinsRequest.builder()
                    .bonusCode(request.getBonusCode())
                    .currency(request.getCurrency())
                    .betAmount(request.getBetAmount())
                    .spins(request.getSpins())
                    .startDate(request.getStartDate())
                    .expirationDate(request.getExpirationDate())
                    .games(request.getGames())
                    .requestId(request.getRequestId())
                    .playerIdList(List.of(request.getPlayerId()))
                    .operator(operator.code())
                    .provider(provider().code())
                    .build();

            serviceInfo = getServiceInfo(operator.code(), request.getCurrency());
            offerResponse = createOffer(createFreeSpinsRequest, copyPlainServiceInfo(serviceInfo));

            if (offerResponse.isSuccess()) {
                if (campaignOpt.isEmpty()) {
                    insertFreeSpinCampaign(operator, provider(), request, FreeSpinCampaignStatus.CREATED, offerResponse.getOfferId());
                } else {
                    campaignOpt.get().setExternalCampaignId(offerResponse.getOfferId());
                    updateFreeSpinCampaign(campaignOpt.get());
                }
            }

            freeSpinCampaign = FreeSpinsUtils.toFreeSpinCampaign(createFreeSpinsRequest);
        }

        String freeSpinsId;
        String message;
        boolean isApplied;

        if (offerResponse.isSuccess()) {
            AssignPlayerRequest assignPlayerRequest = AssignPlayerRequest.builder()
                    .offerAvailableToPlayerFromDateUTC(Mappers.toDateTimeFreeSpinsFormat(freeSpinCampaign.getStartDate()))
                    .offerId(offerResponse.getOfferId())
                    .playerId(request.getPlayerId())
                    .operator(operator.code())
                    .currency(freeSpinCampaign.getCurrency())
                    .build();

            AssignPlayerResponse assignPlayerResponse = freeSpinsApi.assignPlayer(assignPlayerRequest, serviceInfo);

            if (assignPlayerResponse.isSuccess()) {
                freeSpinsId = assignPlayerResponse.getInstanceId();
                message = null;
                isApplied = true;
            } else {
                freeSpinsId = null;
                message = offerResponse.getMessage();
                isApplied = false;
            }
        } else {
            freeSpinsId = null;
            message = offerResponse.getMessage();
            isApplied = false;
        }

        return List.of(FreeSpinsInfo.builder()
                .playerId(request.getPlayerId())
                .freeSpinsId(freeSpinsId)
                .message(message)
                .isApplied(isApplied)
                .build());
    }

    private CreateFreeSpinsBatchResult createFreeSpinsBatch(OperatorSpec operator,
            CreateFreeSpinsRequest request) throws Exception {
        var campaignOpt = readFreeSpinsCampaign(operator, provider(), request.getBonusCode());
        CreateOfferResponse offerResponse = new CreateOfferResponse();
        FreeSpinCampaign freeSpinCampaign = null;
        PlainServiceInfo serviceInfo;

        if (campaignOpt.isPresent() && StringUtils.hasText(campaignOpt.get().getExternalCampaignId())) {
            offerResponse.setOfferId(campaignOpt.get().getExternalCampaignId());
            freeSpinCampaign = campaignOpt.get();
            serviceInfo = getServiceInfo(operator.code(), freeSpinCampaign.getCurrency());
        } else {
            var createFreeSpinsRequest = CreateFreeSpinsRequest.builder()
                    .bonusCode(request.getBonusCode())
                    .currency(request.getCurrency())
                    .betAmount(request.getBetAmount())
                    .spins(request.getSpins())
                    .startDate(request.getStartDate())
                    .expirationDate(request.getExpirationDate())
                    .games(request.getGames())
                    .requestId(request.getRequestId())
                    .playerIdList(request.getPlayerIdList())
                    .operator(operator.code())
                    .provider(provider().code())
                    .build();

            serviceInfo = getServiceInfo(operator.code(), request.getCurrency());
            offerResponse = createOffer(createFreeSpinsRequest, copyPlainServiceInfo(serviceInfo));

            if (offerResponse.isSuccess()) {
                if (campaignOpt.isEmpty()) {
                    insertFreeSpinCampaign(operator, provider(), request, FreeSpinCampaignStatus.CREATED, offerResponse.getOfferId());
                    freeSpinCampaign = FreeSpinsUtils.toFreeSpinCampaign(createFreeSpinsRequest);
                    freeSpinCampaign.setExternalCampaignId(offerResponse.getOfferId());
                } else {
                    campaignOpt.get().setExternalCampaignId(offerResponse.getOfferId());
                    updateFreeSpinCampaign(campaignOpt.get());
                    freeSpinCampaign = campaignOpt.get();
                }
            }
        }

        Map<String, String> freeSpinsIdPerPlayer;
        String message;
        boolean isApplied;

        if (offerResponse.isSuccess() && freeSpinCampaign != null) {
            AssignPlayersRequest assignPlayersRequest = AssignPlayersRequest.builder()
                    .offerAvailableToPlayerFromDateUTC(Mappers.toDateTimeFreeSpinsFormat(freeSpinCampaign.getStartDate()))
                    .offerId(offerResponse.getOfferId())
                    .players(request.getPlayerIdList())
                    .operator(operator.code())
                    .currency(freeSpinCampaign.getCurrency())
                    .build();

            AssignPlayersResponse assignPlayersResponse = freeSpinsApi.assignPlayers(assignPlayersRequest, serviceInfo);

            if (assignPlayersResponse.isSuccess()) {
                freeSpinsIdPerPlayer = assignPlayersResponse.getInstances()
                        .stream()
                        .collect(Collectors.toMap(AssignedFreeSpinInstance::getPlayerId, AssignedFreeSpinInstance::getInstanceId));
                message = null;
                isApplied = true;
            } else {
                freeSpinsIdPerPlayer = Map.of();
                message = assignPlayersResponse.getMessage();
                isApplied = false;
            }
        } else {
            freeSpinsIdPerPlayer = Map.of();
            message = offerResponse.getMessage();
            isApplied = false;
        }

        List<FreeSpinsInfo> freeSpins = request.getPlayerIdList()
                .stream()
                .map(playerId -> FreeSpinsInfo.builder()
                        .playerId(playerId)
                        .freeSpinsId(freeSpinsIdPerPlayer.get(playerId))
                        .message(message)
                        .isApplied(isApplied)
                        .build())
                .toList();

        return CreateFreeSpinsBatchResult.builder()
                .freeSpins(freeSpins)
                .currency(freeSpinCampaign.getCurrency())
                .build();
    }

    private CreateOfferResponse createOffer(CreateFreeSpinsRequest request, PlainServiceInfo serviceInfo) throws Exception {
        var games = request.getGames().stream()
                .map(gameCode -> new OfferGame(BaseMapper.removePrefixFromGameId(gameCode, provider())))
                .toList();
        CreateOfferRequest offerRequest = CreateOfferRequest.builder()
                .offerName(request.getBonusCode())
                .offerGames(games)
                .durationAvailableInDaysAfterAwarded(Duration.between(request.getStartDate(), request.getExpirationDate()).toDays())
                .numberOfRounds(request.getSpins())
                .offerAvailableFromDateUTC(Mappers.toDateTimeFreeSpinsFormat(request.getStartDate()))
                .offerAvailableToDateUTC(Mappers.toDateTimeFreeSpinsFormat(request.getExpirationDate()))
                .operator(request.getOperator())
                .currency(request.getCurrency())
                .build();

        return freeSpinsApi.createOffer(offerRequest, serviceInfo);
    }

    private CancelFreeSpinResponse cancelFreeSpins(FreeSpinsInfo freeSpinsInfo,
            String operator,
            String currency,
            PlainServiceInfo serviceInfo) throws Exception {
        CancelFreeSpinRequest request = CancelFreeSpinRequest.builder()
                .playerId(freeSpinsInfo.getPlayerId())
                .instanceId(freeSpinsInfo.getFreeSpinsId())
                .operator(operator)
                .currency(currency)
                .build();

        return freeSpinsApi.cancelFreeSpins(request, serviceInfo);
    }

    private void checkDuration(LocalDateTime startDate, LocalDateTime expirationDate) throws FreeSpinsException {
        var durationOfCampaignInDays = Duration.between(startDate, expirationDate).toDays();
        if (durationOfCampaignInDays > maxDurationAvailableInDaysAfterAwarded) {
            throw new FreeSpinsException(String.format("Free spins campaign duration can not be longer than max limit %s, actual duration: %s",
                    maxDurationAvailableInDaysAfterAwarded, durationOfCampaignInDays));
        }
    }

    private void checkStartDate(LocalDateTime startDate) throws FreeSpinsException {
        LocalDateTime currentDateTime = LocalDateTime.now(ZoneOffset.UTC);

        if (!startDate.isAfter(currentDateTime)) {
            throw new FreeSpinsException("Free spins start date has to be in the future");
        }
    }

    private void checkFreeSpinsAmount(int spins) throws FreeSpinsException {
        if (spins > maxNumberRounds) {
            throw new FreeSpinsException(String.format("Free spins amount can not be greater than max limit %s, actual spins: %s", maxNumberRounds, spins));
        }
    }

    private PlainServiceInfo copyPlainServiceInfo(PlainServiceInfo source) {
        return new PlainServiceInfo(source.getId(), source.getScheme(), source.getHost(), source.getPort(),
                source.getUserName(), source.getPassword(), source.getPath());
    }

    private PlainServiceInfo getServiceInfo(String operator, String currency) {
        return UPSs.findRequiredServiceInfoByName(cloud, OperatorSpec.fromString(operator).irgsUps(provider().code(), currency.toLowerCase()));
    }

    private String getOperatorFromServiceInfo(PlainServiceInfo serviceInfo) {
        return serviceInfo.getId().split("-")[1];
    }
}
