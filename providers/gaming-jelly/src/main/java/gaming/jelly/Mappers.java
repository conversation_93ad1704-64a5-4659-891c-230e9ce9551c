package gaming.jelly;

import static common.GameLaunchService.DESKTOP;
import static common.GameLaunchService.MOBILE;
import static common.utils.BaseMapper.addPrefixToGameId;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;

import com.turbospaces.common.PlatformUtil;

import aggregator.model.Account;
import aggregator.wallet.patrianna.types.AtInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.IdentityInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletTransactionInfo;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.launch.GameLaunchResponse;
import common.utils.WalletSessionUtils;
import gaming.jelly.b2b.model.BalanceResponse;
import gaming.jelly.b2b.model.CancelRequest;
import gaming.jelly.b2b.model.InitResponse;
import gaming.jelly.b2b.model.PlayStatusResponse;
import gaming.jelly.b2b.model.TransactionRequest;
import gaming.jelly.b2b.model.TransactionResponse;
import gaming.jelly.b2b.model.base.ErrorResponse;
import gaming.jelly.b2b.model.common.Error;

public interface Mappers {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'");
    String PROVIDER_SC = "SCP";
    String PROVIDER_GC = "GCP";
    String GC = "GC";
    String SC = "SC";

    static String toProviderCurrency(String currency) {
        return switch (currency) {
            case SC -> PROVIDER_SC;
            case GC -> PROVIDER_GC;
            default -> currency;
        };
    }

    static String toClientType(String type) {
        return DESKTOP.equals(type) || MOBILE.equals(type) ? type : DESKTOP;
    }

    static ErrorResponse toErrorResponse(Error error) {
        return new ErrorResponse(error);
    }

    static InitResponse toInitResponse(String token, BigDecimal balance, String currency) {
        return new InitResponse(token, balance, toProviderCurrency(currency));
    }

    static PlayStatusResponse toPlayStatusResponse(String token) {
        return new PlayStatusResponse(token);
    }

    static BalanceResponse toBalanceResponse(String token, BigDecimal balance, String currency) {
        return new BalanceResponse(token, balance, toProviderCurrency(currency));
    }

    static TransactionResponse toTransactionResponse(String token, BigDecimal balance, String currency, String transactionId) {
        return new TransactionResponse(token, balance, toProviderCurrency(currency), transactionId);
    }

    static WalletSessionRequestInfo toWalletSessionRequestInfo(
            TransactionRequest request,
            String currency,
            IdentityInfo identity,
            ProviderSpec provider,
            String accountId,
            OperatorSpec operator,
            int maxSessionIdLength) {

        return WalletSessionRequestInfo.builder()
                .identity(identity)
                .sessionId(getSessionId(request.getRoundId(), accountId, operator.code(), maxSessionIdLength))
                .complete(isComplete(request.getOperation()))
                .product(addPrefixToGameId(request.getGame(), ProviderSpec.JELLY))
                .source(provider.code())
                .at(new AtInfo(PlatformUtil.toLocalUTCDate()))
                .transactions(List.of(toTransactionInfo(request.getOperation(), request, currency)))
                .build();
    }

    static CancelWalletSessionRequestInfo toCancelWalletSessionRequestInfo(
            CancelRequest request,
            String currency,
            IdentityInfo identity,
            ProviderSpec provider,
            String accountId,
            OperatorSpec operator,
            int maxSessionIdLength) {
        return CancelWalletSessionRequestInfo.builder()
                .identity(identity)
                .sessionId(getSessionId(request.getRoundId(), accountId, operator.code(), maxSessionIdLength))
                .source(provider.code())
                .transactions(List.of(toTransactionInfo(WalletTransactionInfo.TYPE_REFUND, request, currency)))
                .at(new AtInfo(PlatformUtil.toLocalUTCDate()))
                .build();
    }

    static WalletTransactionInfo toTransactionInfo(String type, TransactionRequest request, String currency) {
        return WalletSessionUtils
                .toTransactionInfo(type.toLowerCase(), currency, request.getTransactionId(), request.getAmount());
    }

    static WalletTransactionInfo toTransactionInfo(String type, CancelRequest request, String currency) {
        return WalletSessionUtils
                .toTransactionInfo(type.toLowerCase(), currency, request.getTransactionId(), BigDecimal.ZERO);
    }

    static GameLaunchResponse toGameLaunchResponse(String uri) {
        GameLaunchResponse response = new GameLaunchResponse();
        response.setGameUrl(uri);
        return response;
    }

    static Boolean isComplete(String type) {
        return WalletTransactionInfo.TYPE_CREDIT.equalsIgnoreCase(type);
    }

    static String makeUTCTimestamp() {
        return LocalDateTime.now(ZoneOffset.UTC).format(formatter);
    }

    private static String getSessionId(String roundId, String accountId, String operator, int maxSessionIdLength) {
        var sessionId = String.format("%s-%s", roundId, Account.idHash(operator, accountId));
        return WalletSessionUtils.shortenWalletSessionIfExceedsLength(sessionId, maxSessionIdLength);
    }
}
