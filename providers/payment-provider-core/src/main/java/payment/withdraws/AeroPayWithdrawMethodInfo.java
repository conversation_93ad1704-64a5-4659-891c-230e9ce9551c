package payment.withdraws;

import java.nio.charset.StandardCharsets;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.google.common.hash.HashCode;
import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import payment.WithdrawMethodInfo;
import payment.model.WithdrawMethodSpec;

@JsonTypeName("AeroPayWithdraw")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AeroPayWithdrawMethodInfo implements WithdrawMethodInfo, WithdrawalBankDetailsInfo {

    private String userId;
    private String bankAccountId;
    private String bankAccountName;
    private String bankAccountNumber;
    private String bankName;
    private String phoneNumber;

    @Override
    public HashCode hash() {
        Hasher hasher = Hashing.murmur3_128().newHasher();
        hasher.putString(userId, StandardCharsets.UTF_8);
        hasher.putString(bankAccountId, StandardCharsets.UTF_8);
        return hasher.hash();
    }

    @Override
    @JsonIgnore
    public WithdrawMethodSpec getProvider() {
        return WithdrawMethodSpec.AEROPAY;
    }

    public static String methodName() {
        return WithdrawMethodSpec.AEROPAY.code();
    }
}
