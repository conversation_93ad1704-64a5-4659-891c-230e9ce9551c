package identity;

import java.io.BufferedReader;
import java.io.IOException;
import java.lang.reflect.UndeclaredThrowableException;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.time.Duration;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import javax.crypto.SecretKey;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.Strings;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.hash.Hashing;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.boot.DevMode;
import com.turbospaces.cache.BlockhoundCacheWrapper;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ups.RawServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import api.v1.ApplicationException;
import api.v1.PermanentTokenFlatten;
import common.JwtTags;
import core.model.CoreAccount;
import core.model.CoreBrand;
import io.ebean.Transaction;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.JwsHeader;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SigningKeyResolverAdapter;
import io.jsonwebtoken.impl.compression.DefaultCompressionCodecResolver;
import io.jsonwebtoken.io.Deserializer;
import io.jsonwebtoken.jackson.io.JacksonDeserializer;
import io.jsonwebtoken.security.Keys;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.cache.GuavaCacheMetrics;
import io.vavr.CheckedFunction0;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;
import uam.api.v1.IdentityByPermanentToken;
import uam.api.v1.IdentityBySessionToken;

public abstract class AbstractIdentityManager<T extends CoreAccount> implements IdentityManager<T>, InitializingBean, DisposableBean, DevMode {
    private final Deserializer<Map<String, ?>> deserializer;
    private final DefaultCompressionCodecResolver compresor;
    private final ScheduledExecutorService timer;

    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final ApplicationProperties props;
    protected final ApiFactory apiFactory;
    protected final DynamicCloud cloud;
    protected final MeterRegistry meterRegistry;
    protected final Cache<String, SecretKey> encryptors = new BlockhoundCacheWrapper<>(
            CacheBuilder.newBuilder().expireAfterWrite(Duration.ofMinutes(1)).build());

    protected AbstractIdentityManager(ApplicationProperties props, ApiFactory apiFactory, DynamicCloud cloud, MeterRegistry meterRegistry) {
        this.props = Objects.requireNonNull(props);
        this.apiFactory = Objects.requireNonNull(apiFactory);
        this.cloud = Objects.requireNonNull(cloud);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        //
        // ~ just one thread
        //
        this.deserializer = new JacksonDeserializer<>();
        this.compresor = new DefaultCompressionCodecResolver();
        this.timer = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setDaemon(true);
                return t;
            }
        });
    }
    @Override
    public boolean isDevMode() {
        return props.isDevMode();
    }
    @Override
    public void afterPropertiesSet() {
        GuavaCacheMetrics.monitor(meterRegistry, encryptors, "encryptors");

        Duration cleanupInterval = props.APP_TIMER_INTERVAL.get();
        timer.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                long size = encryptors.size();
                if (size > 0) {
                    logger.debug("about to cleanUp encryptors cache of {} items ...", size);
                }
                encryptors.cleanUp();
            }
        }, cleanupInterval.toSeconds(), cleanupInterval.toSeconds(), TimeUnit.SECONDS);
    }
    @Override
    public void destroy() {
        PlatformUtil.shutdownExecutor(timer, props.APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT.get());
    }
    @Override
    public String encrypt(CoreBrand brand, JwtBuilder jwt) throws Throwable {
        return DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedFunction0<>() {
            @Override
            public String apply() throws Throwable {
                var key = encryptors.get(brand.getName(), () -> secretKey(brand));
                return jwt.signWith(key).compact();
            }
        });
    }
    @Override
    public Jws<Claims> decrypt(String jws, Transaction tx) throws Throwable {
        return DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedFunction0<>() {
            @Override
            public Jws<Claims> apply() throws Throwable {
                return Jwts.parserBuilder()
                        .deserializeJsonWith(deserializer)
                        .setCompressionCodecResolver(compresor)
                        .setSigningKeyResolver(new SigningKeyResolverAdapter() {
                            @Override
                            @SuppressWarnings("rawtypes")
                            public Key resolveSigningKey(JwsHeader header, Claims claims) {
                                try {
                                    String brandName = claims.get(JwtTags.JWT_BRAND).toString();
                                    var brand = requiredBrandByName(brandName, tx);
                                    return encryptors.get(brandName, () -> secretKey(brand));
                                } catch (ApplicationException | ExecutionException err) {
                                    throw new UndeclaredThrowableException(err);
                                }
                            }
                        }).build().parseClaimsJws(jws);
            }
        });
    }

    protected CoreBrand requiredBrandByName(String brandName, Transaction tx) throws ApplicationException {
        return brandByName(brandName, tx).orElseThrow(new Supplier<ApplicationException>() {
            @Override
            public ApplicationException get() {
                var fault = apiFactory.toExceptionalNotFoundReply("Brand was not found: " + brandName);
                return fault.toException();
            }
        });
    }

    @Override
    public String hashToken(CoreAccount account, String currency, boolean sweepstake, int expireAt) {
        SecretKey secretKey = secretKey(account.getBrand());

        var hasher = Hashing.murmur3_128().newHasher();
        hasher.putString(account.getHash(), StandardCharsets.UTF_8);
        hasher.putString(currency, StandardCharsets.UTF_8);
        hasher.putBoolean(sweepstake);
        hasher.putInt(expireAt);
        hasher.putBytes(secretKey.getEncoded());

        var hash = hasher.hash();
        var checksum = Math.abs(hash.asInt());
        return String.valueOf(checksum);
    }
    @Override
    @SuppressWarnings("unchecked")
    public Optional<T> account(uam.api.v1.Identity identity, Date when, Transaction tx) throws Throwable {
        if (identity.hasByAccountId()) {
            var account = resolve(identity.getByAccountId().getAccountId(), tx);
            return Optional.ofNullable((T) account);
        } else if (identity.hasByToken()) {
            try {
                var session = getUserSession(identity.getByToken(), when, tx);
                trackAccount(session, when);
                return Optional.ofNullable((T) session.account());
            } catch (io.jsonwebtoken.JwtException err) {
                logger.warn("Error when resolving JWT token", err);
                ResponseStatusFacade fault = apiFactory.toExceptionalAuthReply("Invalid token");
                throw fault.toException();
            }
        } else if (identity.hasByPermanentToken()) {
            var session = getUserSession(identity.getByPermanentToken(), when, tx);
            trackAccount(session, when);
            return Optional.ofNullable((T) session.account());
        }

        return Optional.empty();
    }
    @Override
    public T requiredAccount(uam.api.v1.Identity identity, Date when, Transaction tx) throws Throwable {
        return account(identity, when, tx).orElseThrow(new Supplier<ApplicationException>() {
            @Override
            public ApplicationException get() {
                var fault = apiFactory.toExceptionalBadRequestReply("Unknown identity: " + identity.getTypeCase());
                return fault.toException();
            }
        });
    }
    protected CoreAccount resolve(Long accountId, Transaction tx) throws ApplicationException {
        return account(accountId, tx).orElseThrow(new Supplier<ApplicationException>() {
            @Override
            public ApplicationException get() {
                var fault = apiFactory.toExceptionalAuthReply("Your account is deleted. Please contact {link_support}");
                return fault.toException();
            }
        });
    }
    protected UserSession getUserSession(IdentityBySessionToken byToken, Date when, Transaction tx) throws Throwable {
        try {
            Jws<Claims> jwt = decrypt(byToken.getToken(), tx);
            Claims claims = jwt.getBody();
            Date expireAt = claims.getExpiration();

            if (when.after(expireAt)) {
                var fault = apiFactory.toExceptionalAuthReply("Session has been expired");
                throw fault.toException();
            }

            //
            // ~ account
            //
            Long accountId = Long.parseLong(claims.getSubject());
            if (accountId.equals(byToken.getAccountId())) {
                var account = resolve(accountId, tx);
                return new DefaultUserSession(account, byToken.getToken());
            }

            // ~ must not happen (security penetration attempt)
            var fault = apiFactory.toExceptionalAuthReply("Token was not issued for identity");
            throw fault.toException();
        } catch (ExpiredJwtException err) {
            var fault = apiFactory.toExceptionalAuthReply("Session has been expired");
            throw fault.toException();
        }
    }
    protected ExternalUserSession getUserSession(IdentityByPermanentToken identity, Date when, Transaction tx) throws ApplicationException {
        var accountId = identity.getAccountId();
        var account = account(accountId, tx)
                .orElseThrow(new Supplier<ApplicationException>() {
                    @Override
                    public ApplicationException get() {
                        var fault = apiFactory.toExceptionalAuthReply("Your account is deleted. Please contact {link_support}");
                        return fault.toException();
                    }
                });

        var tkn = PermanentTokenFlatten.standard().readExternal(identity.getToken());
        var sweepstake = tkn.isSweepstake();
        var currency = tkn.getCurrency();
        var expireAfter = tkn.getExpireAfter();

        var checksum = hashToken(account, currency, sweepstake, expireAfter);

        var start = Date.from(YEAR_2020.toInstant());
        var end = DateUtils.addMinutes(start, expireAfter);
        if (when.after(end)) {
            var fault = apiFactory.toExceptionalAuthReply("Session has been expired: " + end);
            throw fault.toException();
        }

        //
        // ~ well we want to ensure that not just token is valid, but also that we have valid identity provided
        //
        if (Long.compare(tkn.getAccountId(), accountId) == 0) {
            if (Strings.CS.equals(checksum, tkn.getChecksum())) {
                return new DefaultExternalUserSession(account, identity.getToken(), sweepstake);
            }
        }

        //
        // ~ must not happen (security penetration attempt)
        //
        var fault = apiFactory.toExceptionalAuthReply("Token was not issued for identity");
        throw fault.toException();
    }
    protected SecretKey requiredSecretKey(String counterparty) throws Exception {
        return secretKey(cloud, counterparty).orElseThrow(() -> new IllegalArgumentException("Unable to find secret for counterparty: " + counterparty));
    }
    protected SecretKey secretKey(CoreBrand brand) {
        Optional<SecretKey> opt = secretKey(cloud, brand.getName());
        if (opt.isPresent()) {
            return opt.get();
        }
        throw new IllegalArgumentException("Unable to find secret for brand: " + brand.getName());
    }

    protected abstract Optional<? extends CoreBrand> brandByName(String brandName, Transaction tx) throws ApplicationException;
    protected abstract Optional<? extends CoreAccount> account(Long accountId, Transaction tx) throws ApplicationException;
    protected void trackAccount(UserSession session, Date when) {

    }
    private static Optional<SecretKey> secretKey(DynamicCloud cloud, String name) {
        Optional<RawServiceInfo> opt = UPSs.findScopedServiceInfoByName(name, cloud, "jwt");
        if (opt.isPresent()) {
            RawServiceInfo ups = opt.get();
            try (BufferedReader reader = ups.openBufferedStream()) {
                byte[] byteArray = IOUtils.toByteArray(reader, StandardCharsets.UTF_8);
                return Optional.of(Keys.hmacShaKeyFor(byteArray));
            } catch (IOException err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        }
        return Optional.empty();
    }
}
