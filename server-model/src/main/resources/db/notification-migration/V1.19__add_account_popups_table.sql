-- apply changes
create table notification.account_popups (
  id                            uuid not null,
  account_id                    bigint not null,
  created_at                    timestamptz not null,
  modified_at                   timestamptz not null,
  version                       integer not null,
  tracked_popups                jsonb,
  constraint uq_account_popups_account_id unique (account_id),
  constraint pk_account_popups primary key (id)
);

-- foreign keys and indices
alter table notification.account_popups add constraint fk_account_popups_account_id foreign key (account_id) references notification.accounts (id) on delete restrict on update restrict;

create index if not exists ix_account_popups_account_id on notification.account_popups (account_id);
