package notification.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

import java.time.Instant;
import java.util.Date;
import java.util.UUID;

@Entity
@Table(name = "account_questline_instance", schema = Schemas.NOTIFICATION,
        uniqueConstraints = { @UniqueConstraint( columnNames = { "code", "status" } ) })
@Getter
@Setter
public class AccountQuestlineInstance {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    @Column(nullable = false)
    private UUID code;

    @ManyToOne(optional = false)
    private Account account;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private QuestlineStatusSpec status;

    private Date expireDate;

    private String title;
    private String description;
    private String icon;

    public boolean isExpiredByTime() {
        return expireDate != null && expireDate.toInstant().isBefore(Instant.now());
    }

}
