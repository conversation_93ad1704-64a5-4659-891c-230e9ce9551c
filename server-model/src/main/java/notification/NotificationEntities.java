package notification;

import model.CoreEntities;
import notification.model.Account;
import notification.model.AccountFreeSpin;
import notification.model.AccountMilestoneUpdateInstance;
import notification.model.AccountNotification;
import notification.model.AccountOfferChainInstance;
import notification.model.AccountPreferences;
import notification.model.AccountPreferencesNotification;
import notification.model.AccountPromotion;
import notification.model.AccountPwaNotification;
import notification.model.AccountQuestlineInstance;
import notification.model.BonusReward;
import notification.model.BonusRewardAccountNotification;
import notification.model.Brand;
import notification.model.FreeSpinAccountNotification;
import notification.model.FreeSpinCampaign;
import notification.model.JackpotAccountFreeContribution;
import notification.model.JackpotAccountFreeContributionNotification;
import notification.model.MilestouneUpdateInstanceAccountNotification;
import notification.model.NotificationCategory;
import notification.model.OfferChainInstanceAccountNotification;
import notification.model.PickemReward;
import notification.model.PickemRewardAccountNotification;
import notification.model.Product;
import notification.model.Promotion;
import notification.model.PromotionAccountNotification;
import notification.model.QuestlineInstanceAccountNotification;
import notification.model.popup.AccountPopup;
import notification.model.popup.Popup;

public class NotificationEntities extends CoreEntities {
    public NotificationEntities() {
        super();

        add(AccountNotification.class);
        add(BonusRewardAccountNotification.class);
        add(FreeSpinAccountNotification.class);
        add(JackpotAccountFreeContributionNotification.class);
        add(NotificationCategory.class);
        add(PromotionAccountNotification.class);
        add(AccountPreferencesNotification.class);
        add(AccountPwaNotification.class);
        add(OfferChainInstanceAccountNotification.class);
        add(QuestlineInstanceAccountNotification.class);
        add(MilestouneUpdateInstanceAccountNotification.class);
        add(PickemRewardAccountNotification.class);

        add(Account.class);
        add(AccountPreferences.class);
        add(AccountFreeSpin.class);
        add(AccountPromotion.class);
        add(AccountOfferChainInstance.class);
        add(BonusReward.class);
        add(Brand.class);
        add(FreeSpinCampaign.class);
        add(JackpotAccountFreeContribution.class);
        add(Promotion.class);
        add(Product.class);
        add(AccountQuestlineInstance.class);
        add(AccountMilestoneUpdateInstance.class);
        add(PickemReward.class);

        add(Popup.class);
        add(AccountPopup.class);
    }
}
