package notification.testutils;

import java.util.List;
import java.util.UUID;
import java.util.function.Consumer;

import com.google.common.hash.Hashing;
import com.turbospaces.common.PlatformUtil;

import io.ebean.Transaction;
import notification.NotificationJpaManager;
import notification.model.Account;
import notification.model.Brand;
import notification.model.NotificationCategory;
import notification.model.NotificationCategoryCodeSpec;
import notification.model.popup.AccountPopup;
import notification.model.popup.HourlyPopupScheduleRule;
import notification.model.popup.Popup;
import notification.model.popup.PopupSchedule;

public class NotificationDataGenerator {

    public static final String TEST_BRAND = "bluedream";
    public static final String SC_CURRENCY = "SC";
    public static final String GC_CURRENCY = "GC";
    public static final String FIAT_CURRENCY = "USD";

    public static Brand genBrand(NotificationJpaManager ebean) throws Throwable {
        return genBrand(ebean, TEST_BRAND);
    }

    public static Brand genBrand(NotificationJpaManager ebean, String name) throws Throwable {
        Brand brand = buildBrand(name);
        save(ebean, brand);
        return brand;
    }

    private static Brand buildBrand(String name) {
        Brand brand = new Brand();
        brand.setName(name);
        brand.setGoldCurrency(GC_CURRENCY);
        brand.setSweepstakeCurrency(SC_CURRENCY);
        brand.setFiatCurrency(FIAT_CURRENCY);
        return brand;
    }

    public static Account genAccount(NotificationJpaManager ebean, Brand brand) throws Throwable {
        Account account = new Account();
        var hash = Hashing.murmur3_32_fixed().hashBytes(PlatformUtil.randomUUID().toString().getBytes()).toString();
        account.setBrand(brand);
        account.setHash(hash);
        save(ebean, account);
        return account;
    }


    public static Account genAccountWithId(NotificationJpaManager ebean, Brand brand) throws Throwable {
        Account account = new Account();
        var hash = Hashing.murmur3_32_fixed().hashBytes(PlatformUtil.randomUUID().toString().getBytes()).toString();
        account.setId(123L);
        account.setBrand(brand);
        account.setHash(hash);
        save(ebean, account);
        return account;
    }

    public static Popup genPopup(NotificationJpaManager ebean, Brand brand) throws Throwable {
        return genPopup(ebean, brand, _ -> {
        });
    }

    public static Popup genPopup(NotificationJpaManager ebean, Brand brand, Consumer<Popup> overrides) throws Throwable {
        var popup = new Popup();
        popup.setBrand(brand);
        popup.setCountry("US");
        popup.setCode(UUID.randomUUID().toString());
        popup.setEnabled(true);
        popup.setSchedule(new PopupSchedule(List.of(new HourlyPopupScheduleRule(4))));
        overrides.accept(popup);
        save(ebean, popup);
        return popup;
    }

    public static AccountPopup genAccountPopup(NotificationJpaManager ebean, Account account) throws Throwable {
        return genAccountPopup(ebean, account, _ -> {});
    }

    public static AccountPopup genAccountPopup(NotificationJpaManager ebean, Account account, Consumer<AccountPopup> overrides) throws Throwable {
        var accountPopup = new AccountPopup(account);
        overrides.accept(accountPopup);
        save(ebean, accountPopup);
        return accountPopup;
    }

    public static NotificationCategory genNotificationCategory(NotificationJpaManager ebean, Brand brand, String code) throws Throwable {
        NotificationCategory category = new NotificationCategory();
        category.setBrand(brand);
        category.setCode(NotificationCategoryCodeSpec.QUESTLINES);
        category.setInactive(false);
        category.setTitle("Test title");
        save(ebean, category);
        return category;
    }

    public static void cleanupEntitiesTables(NotificationJpaManager jpaManager, Class<?>... entities) {
        try {
            jpaManager.execute(() -> {
                for (Class<?> clazz : entities) {
                    List<?> l = jpaManager.find(clazz).findList();
                    l.forEach(jpaManager::delete);
                }
            });
        } finally {
            jpaManager.cacheManager().clearAll();
        }
    }

    private static <E> void save(NotificationJpaManager ebean, E entity) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            ebean.save(entity, tx);
            tx.commit();
        }
    }
}
