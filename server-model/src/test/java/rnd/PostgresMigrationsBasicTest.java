package rnd;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.sql.DataSource;

import org.apache.maven.artifact.versioning.ComparableVersion;
import org.flywaydb.core.api.configuration.FluentConfiguration;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.testcontainers.containers.PostgreSQLContainer;

import com.google.common.collect.ImmutableMap;
import com.turbospaces.boot.MockBootstrap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import com.turbospaces.plugins.FlywayBootstrapInitializer;

import io.micrometer.core.instrument.MeterRegistry;
import model.Schemas;
import rnd.tools.GenerateRandomizerPostgresMigration;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class PostgresMigrationsBasicTest {

    private static final Path MIGRATIONS_PATH = Paths.get("src/main/resources/db/rnd-migration");

    public static final Pattern MIGRATION_FILE_NAME_PATTERN = Pattern.compile("^V(\\d+\\.\\d+)__[^-]+\\.sql$");
    public static final Pattern REPEATABLE_MIGRATION_FILE_NAME_PATTERN = Pattern.compile("^R__\\d+_.+\\.sql$");

    private static PostgreSQLContainer<?> postgreSQLContainer;
    private static PostgresqlServiceInfo postgresqlServiceInfo;

    @BeforeAll
    public static void setUp() {
        postgreSQLContainer = RandomizerContainerUtils.postgreSQLContainerStarted();
        postgresqlServiceInfo = RandomizerContainerUtils.postgresqlServiceInfo(postgreSQLContainer);
    }

    @AfterAll
    public static void cleanUp() {
        if (postgreSQLContainer != null) {
            postgreSQLContainer.stop();
        }
    }

    @ContextConfiguration
    public static class Config {

        @Bean
        public PostgresqlServiceInfo postgresqlServiceInfo() {
            return postgresqlServiceInfo;
        }

        @Bean
        public HikariDataSourceFactoryBean datasource(
                ApplicationProperties props,
                MeterRegistry meterRegistry,
                PostgresqlServiceInfo postgresqlServiceInfo) {
            return new HikariDataSourceFactoryBean(props, meterRegistry, postgresqlServiceInfo);
        }

        @Bean
        public JdbcTemplate jdbcTemplate(DataSource dataSource) {
            return new JdbcTemplate(dataSource);
        }

    }

    private static class FileData {
        Path path;
        Instant modifiedTime;

        FileData(Path path, long modifiedTime) {
            this.path = path;
            this.modifiedTime = Instant.ofEpochMilli(modifiedTime);
        }
    }

    private static Instant getMigrationsDirModificationTime() throws IOException {
        BasicFileAttributes attr = Files.readAttributes(MIGRATIONS_PATH, BasicFileAttributes.class);
        return Instant.ofEpochMilli(attr.lastModifiedTime().toMillis());
    }

    private static RandomizerModelProperties createProps() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        // Override default (if any) with dynamical port from test containers
        cfg.setLocalProperty("service.postgres-owner.uri", postgresqlServiceInfo.getUri());
        RandomizerModelProperties props = new RandomizerModelProperties(cfg.factory());
        cfg.setLocalProperty(props.APP_DB_MIGRATION_PATH.getKey(), "db/rnd-migration");
        return props;
    }

    @Test
    @Order(1)
    public void testNoDuplicateVersions() throws IOException {
        // Regular expression to match the version part of the file names

        // Map to store the versions and their corresponding file paths and creation times
        Map<String, List<FileData>> versionMap = new HashMap<>();

        // List all files in the directory
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(MIGRATIONS_PATH, "*.sql")) {
            for (Path file : stream) {
                String fileName = file.getFileName().toString();
                Matcher matcher = MIGRATION_FILE_NAME_PATTERN.matcher(fileName);
                if (matcher.matches()) {
                    String version = matcher.group(1);
                    BasicFileAttributes attr = Files.readAttributes(file, BasicFileAttributes.class);
                    FileData fileData = new FileData(file, attr.lastModifiedTime().toMillis());

                    versionMap.computeIfAbsent(version, k -> new ArrayList<>()).add(fileData);
                }
            }
        }

        boolean duplicatesFound = false;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneOffset.UTC);

        StringBuilder errorText = new StringBuilder();
        for (Map.Entry<String, List<FileData>> entry : versionMap.entrySet()) {
            List<FileData> files = entry.getValue();
            if (files.size() > 1) {
                duplicatesFound = true;
                errorText.append(String.format("Duplicate version found: %s\n", entry.getKey()));
                files.sort(Comparator.comparingLong(f -> f.modifiedTime.toEpochMilli()));
                for (FileData fileData : files) {
                    errorText.append(String.format("mtime %s: %s\n", formatter.format(fileData.modifiedTime), fileData.path.getFileName()));
                }
                errorText.append(String.format("Candidate for manual version bump: %s\n", files.get(files.size() - 1).path.getFileName()));
                errorText.append("\n");
            }
        }
        assertFalse(duplicatesFound, errorText.toString());
    }

    private static String getLatestFlywayMigrationFileVersion() throws IOException {
        ComparableVersion maxVersion = null;
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(MIGRATIONS_PATH, "*.sql")) {
            for (Path file : stream) {
                String fileName = file.getFileName().toString();
                Matcher matcher = MIGRATION_FILE_NAME_PATTERN.matcher(fileName);
                if (matcher.matches()) {
                    String versionStr = matcher.group(1);

                    ComparableVersion version = new ComparableVersion(versionStr);
                    if (maxVersion == null) {
                        maxVersion = version;
                    } else if (maxVersion.compareTo(version) < 0) {
                        maxVersion = version;
                    }
                }
            }
        }
        return maxVersion.toString();
    }

    @Test
    @Order(2)
    public void testValidMigrationName() throws IOException {
        Set<String> exclusions = Set.of("V1.10.sql");

        DirectoryStream<Path> paths = Files.newDirectoryStream(MIGRATIONS_PATH, "*.sql");
        List<String> invalidFileNames = new ArrayList<>();
        for (Path file : paths) {
            String fileName = file.getFileName().toString();
            if (exclusions.contains(fileName)) {
                continue;
            }
            Matcher matcher = MIGRATION_FILE_NAME_PATTERN.matcher(fileName);
            if (!matcher.matches() && !REPEATABLE_MIGRATION_FILE_NAME_PATTERN.matcher(fileName).matches()) {
                invalidFileNames.add(fileName);
            }
        }
        assertTrue(invalidFileNames.isEmpty(),
                "The following migration files have invalid naming pattern: " + invalidFileNames + "\n" +
                        "The valid name pattern is: " + MIGRATION_FILE_NAME_PATTERN);
    }

    /**
     * It currently requires live DataSource (via Postgres test container), but why?
     */
    @Test
    @Order(3)
    public void testGenerateMigrationRunsOkAndProducesNoPendingChanges() throws Throwable {
        Instant mTimeBefore = getMigrationsDirModificationTime();
        GenerateRandomizerPostgresMigration.generate(createProps());
        Instant mTimeAfter = getMigrationsDirModificationTime();
        assertFalse(mTimeAfter.isAfter(mTimeBefore),
                "Did you forget to run GenerateRandomizerPostgresMigration?\n" +
                        "Check '" + MIGRATIONS_PATH + "' for new files");
    }

    @Test
    @Order(4)
    public void testApplyFlywayMigrations() throws Throwable {
        RandomizerModelProperties props = createProps();
        SimpleBootstrap bootstrap = new MockBootstrap(props, Config.class);
        bootstrap.addBootstrapRegistryInitializer(
                new FlywayBootstrapInitializer(props, props.APP_DB_MIGRATION_PATH, postgresqlServiceInfo, new RandomizerEntities(), Schemas.RND,
                        Schemas.RND_DATA) {
                    @Override
                    protected void configureMigration(FluentConfiguration config) {
                        super.configureMigration(config);

                        ImmutableMap.Builder<String, String> m = ImmutableMap.builder();

                        m.put(props.APP_DB_CDC_ENABLED.getKey(), String.valueOf(props.APP_DB_CDC_ENABLED.get()));
                        m.put("cdc.list", new RandomizerDataEntities().toDbzRootTables());

                        config.placeholders(m.build());
                    }
                });
        ConfigurableApplicationContext applicationContext = bootstrap.run();
        JdbcOperations jdbcOps = applicationContext.getBean(JdbcOperations.class);
        String actualLatestVersionFromFlywayTable = jdbcOps.queryForObject(
                "select version from rnd.flyway_schema_history where script not like 'R__%' order by installed_rank desc limit 1",
                String.class);

        String expectedLatestMigrationVersionFromFile = getLatestFlywayMigrationFileVersion();
        // This check is technically redundant, because migrations failures propagate with error, but just to be sure
        assertEquals(expectedLatestMigrationVersionFromFile, actualLatestVersionFromFlywayTable);
    }

}
