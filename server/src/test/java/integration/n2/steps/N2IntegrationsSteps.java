package integration.n2.steps;

import java.math.BigDecimal;

import org.apache.http.client.utils.URIBuilder;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;

import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.base.Joiner;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.HttpProto;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.bots.DefaultN2Bot;
import aggregator.bots.N2Bot;
import aggregator.listeners.MockDataGenerator;
import aggregator.repo.BrandRepo;
import aggregator.repo.DefaultBrandRepo;
import aggregator.repo.DefaultWalletSessionRepo;
import aggregator.repo.WalletSessionRepo;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.flatten.TempTokenFlatten;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.WalletSessionUtils;
import gaming.n2.b2b.model.AuthBalanceRequest;
import gaming.n2.b2b.model.BetRequest;
import gaming.n2.b2b.model.CancelRequest;
import gaming.n2.b2b.model.CommonResponse;
import gaming.n2.b2b.model.WinRequest;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import okhttp3.OkHttpClient;

public class N2IntegrationsSteps {
    @Autowired
    private AggregatorServerProperties props;
    @Autowired
    private DynamicCloud cloud;
    @Autowired
    private SpannerTemplate spannerTemplate;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private OkHttpClient httpClient;
    private BrandRepo brandRepo;
    private WalletSessionRepo walletSessionRepo;
    private PlainServiceInfo si;
    private PlainServiceInfo siSc;
    private PlainServiceInfo siGc;
    private N2Bot bot;
    private String account;
    private String currency;
    private String gameCode;
    private String roundId;
    private String roundIdShort;
    private String txIdBet;
    private String txIdWin;
    private String txIdLast;
    private String token;
    private AccountRoutingCurrencyFlatten flatten;
    private CommonResponse response;

    @Given("^Initialize properties -n2-$")
    public void initializeProperties() throws Exception {
        PlainServiceInfo siBot = buildPlainServiceInfoForBot();
        siSc = buildPlainServiceInfo("SC", "brand-sc");
        siGc = buildPlainServiceInfo("GC", "brand-gc");
        cloud.addUps(siBot);
        cloud.addUps(siSc);
        cloud.addUps(siGc);
        this.brandRepo = new DefaultBrandRepo(spannerTemplate, cacheManager);
        this.walletSessionRepo = new DefaultWalletSessionRepo(spannerTemplate);
        this.bot = createBot();
    }

    @Given("^New game (.+) with currency (.+) -n2-$")
    public void newGameWithCurrency(String gameCode, String curr) {
        initializeCreds(curr);
        this.currency = curr;
        this.flatten = genAccountRoutingCurrencyFlatten();
        this.account = Joiner.on('_').join(flatten.getRoutingKey(), flatten.getAccountId(), flatten.getCurrency());
        this.token = genTempTokenFlatten(flatten).writeExternal();
        this.gameCode = gameCode;
        Assertions.assertNotNull(this.currency);
        Assertions.assertEquals(this.flatten.getCurrency(), this.currency);
    }

    @When("Authorize player -n2-")
    public void authorizePlayer() throws Exception {
        var request = new AuthBalanceRequest(token, account, currency);

        response = bot.authBalance(request, si);
    }

    @When("Ask the balance -n2-")
    public void getBalance() throws Exception {
        var request = new AuthBalanceRequest(token, account, currency);

        response = bot.authBalance(request, si);
    }

    @Then("^Player has balance (\\d+(?:\\.\\d+)?) and currency (.+) -n2-$")
    public void hasResponse(BigDecimal balance, String currency) {
        Assertions.assertEquals(currency, response.getCurrency());
        Assertions.assertEquals(0, balance.compareTo(response.getBalance()),
                String.format("Expected %.2f but balance %.2f", balance, response.getBalance()));
        Assertions.assertNull(response.getCode());
        Assertions.assertNull(response.getDescription());
        Assertions.assertNull(response.getTraceId());
    }

    @And("^Send bet request with amount (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) -n2-$")
    public void sendBet(BigDecimal amount) throws Exception {
        sendBetWithTxId(amount, PlatformUtil.randomUUID().toString(), PlatformUtil.randomUUID().toString());
    }

    @And("^Send bet request with amount (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?), transactionId (.+), roundId (.+) -n2-$")
    public void sendBetWithTxId(BigDecimal amount, String txId, String roundId) throws Exception {
        txIdBet = txIdLast = txId;
        this.roundId = roundId;
        roundIdShort = shortenRoundId(roundId);
        var request = BetRequest.builder()
                .sessionId(token)
                .playerId(account)
                .amount(amount)
                .currency(currency)
                .gameId(gameCode)
                .gameRoundId(roundId)
                .transactionId(txIdBet)
                .build();

        response = bot.bet(request, si);
    }

    @And("^Send bet request with amount (\\d+(?:\\.\\d+)?) and token (.+) -n2-$")
    public void sendBetWithInvalidToken(BigDecimal amount, String token) throws Exception {
        this.token = token;
        sendBetWithTxId(amount, PlatformUtil.randomUUID().toString(), PlatformUtil.randomUUID().toString());
    }

    @And("^Send win request with amount (\\d+(?:\\.\\d+)?) -n2-$")
    public void sendWin(BigDecimal amount) throws Exception {
        txIdWin = txIdLast = PlatformUtil.randomUUID().toString();
        var request = buildWinRequest(amount, txIdWin);

        response = bot.win(request, si);
    }

    @And("^Send win request with tx id (.+) and amount (\\d+(?:\\.\\d+)?) -n2-$")
    public void sendWinWithTxId(String txId, BigDecimal amount) throws Exception {
        txIdWin = txIdLast = txId;
        var request = buildWinRequest(amount, txIdWin);

        response = bot.win(request, si);
    }

    @Then("^Has (\\d+(?:\\.\\d+)?) amount tx with type (.+) and total number of txs is (.+) -n2-$")
    public void hasTransaction(BigDecimal amount, String type, int transactionCount) {
        var account = brandRepo.requiredAccount(MockDataGenerator.OPERATOR, String.valueOf(flatten.getAccountId()));
        var wsOpt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), ProviderSpec.N2.code(),
                roundIdShort, currency);
        Assertions.assertTrue(wsOpt.isPresent());
        var txs = wsOpt.get().getTransactions();
        Assertions.assertEquals(transactionCount, txs.size());

        var tx = txs.stream()
                .filter(transaction -> transaction.getReference().equals(txIdLast))
                .filter(transaction -> transaction.getSessionId().equals(roundIdShort))
                .filter(transaction -> transaction.getType().equals(type))
                .findFirst().orElseThrow();
        Assertions.assertEquals(0, amount.compareTo(new BigDecimal(tx.getAmount())));
    }

    @Then("^Transactional response has status (.+) -n2-$")
    public void hasTxResponseWithError(String message) {
        Assertions.assertEquals(message, response.getCode());
        Assertions.assertEquals(message, response.getDescription());
        Assertions.assertNotNull(response.getTraceId());
        Assertions.assertNull(response.getBalance());
        Assertions.assertNull(response.getCurrency());
    }

    @Then("^Has no transactions -n2-$")
    public void hasNoTransaction() {
        var account = brandRepo.requiredAccount(MockDataGenerator.OPERATOR, String.valueOf(flatten.getAccountId()));
        var wsOpt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), ProviderSpec.N2.code(), roundIdShort, currency);
        Assertions.assertTrue(wsOpt.isEmpty());
    }

    @And("^Send cancel request -n2-$")
    public void sendCancel() throws Throwable {
        if (txIdBet == null) {
            txIdBet = txIdLast = PlatformUtil.randomUUID().toString();
            roundId = PlatformUtil.randomUUID().toString();
            roundIdShort = shortenRoundId(roundId);
        }
        var request = buildCancelRequest();

        response = bot.cancelBet(request, si);
    }

    @And("^Send cancel request with bet transactionId (.+) and roundId (.+) -n2-$")
    public void sendCancelWithBetTxId(String betTxId, String round) throws Throwable {
        txIdBet = txIdLast = betTxId;
        roundId = round;
        roundIdShort = shortenRoundId(roundId);
        var request = buildCancelRequest();

        response = bot.cancelBet(request, si);
    }

    private CancelRequest buildCancelRequest() {
        return CancelRequest.builder()
                .sessionId(token)
                .playerId(account)
                .gameId(gameCode)
                .gameRoundId(roundId)
                .transactionId(PlatformUtil.randomUUID().toString())
                .withdrawTransactionId(txIdBet)
                .build();
    }

    private PlainServiceInfo buildPlainServiceInfoForBot() throws Exception {
        return new PlainServiceInfo(
                String.format("%s-%s", AggregatorWildcardUPSs.IGP_PREFIX, OperatorSpec.BLUEDREAM.code()),
                new URIBuilder()
                        .setScheme("http")
                        .setHost("localhost")
                        .setPort(props.CLOUD_APP_PORT.get())
                        .setUserInfo("username", "password")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "alpha_numeric")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                        .build().toURL().toExternalForm());
    }

    private N2Bot createBot() {
        var uriBuilder = new URIBuilder()
                .setScheme("http")
                .setHost("localhost")
                .setPort(props.CLOUD_APP_PORT.get());
        return new DefaultN2Bot(httpClient, uriBuilder, OperatorSpec.BLUEDREAM);
    }

    private PlainServiceInfo buildPlainServiceInfo(String currency, String brandId) throws Exception {
        return new PlainServiceInfo(
                OperatorSpec.BLUEDREAM.irgsUps(ProviderSpec.N2.code(), currency),
                new URIBuilder()
                        .setScheme("http")
                        .setHost("localhost")
                        .setUserInfo("somePublicKey", "somePrivateKey")
                        .setParameter("brandId", brandId)
                        .build().toString());
    }

    private void initializeCreds(String currency) {
        si = switch (currency) {
            case "SC" -> siSc;
            case "GC" -> siGc;
            default -> throw new IllegalArgumentException("Wrong currency");
        };
    }

    private AccountRoutingCurrencyFlatten genAccountRoutingCurrencyFlatten() {
        var hash = PlatformUtil.randomAlphanumeric(8);
        var id = String.valueOf(System.currentTimeMillis());
        return AccountRoutingCurrencyFlatten.underscore(hash, id, this.currency);
    }

    private TempTokenFlatten genTempTokenFlatten(AccountRoutingCurrencyFlatten flatten) {
        return TempTokenFlatten.underscore(flatten.getRoutingKey(), flatten.getAccountId(), flatten.getCurrency(), 36);
    }

    private String shortenRoundId(String roundId) {
        return WalletSessionUtils.shortenWalletSession(roundId);
    }

    private WinRequest buildWinRequest(BigDecimal amount, String txIdWin) {
        return WinRequest.builder()
                .sessionId(token)
                .playerId(account)
                .amount(amount)
                .currency(currency)
                .gameId(gameCode)
                .gameRoundId(roundId)
                .transactionId(txIdWin)
                .withdrawTransactionId(txIdBet)
                .build();
    }
}
