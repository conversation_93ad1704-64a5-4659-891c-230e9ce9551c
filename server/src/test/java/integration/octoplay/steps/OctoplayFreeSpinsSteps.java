package integration.octoplay.steps;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.junit.jupiter.api.Assertions;
import org.mockserver.mock.Expectation;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.mockserver.model.MediaType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.hash.Hashing;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.HttpProto;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.bots.freespins.DefaultFreeSpinsBot;
import aggregator.bots.freespins.FreeSpinsBot;
import aggregator.repo.BrandRepo;
import aggregator.repo.DefaultBrandRepo;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.FreeSpinsService;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CancelFreeSpinsRequest;
import common.model.freespins.CancelFreeSpinsResponse;
import common.model.freespins.CancelPlayerFreeSpinRequest;
import common.model.freespins.CancelPlayerFreeSpinResponse;
import common.model.freespins.CreateFreeSpinsCampaignDTO;
import common.model.freespins.CreateFreeSpinsRequest;
import common.model.freespins.CreateFreeSpinsResponse;
import common.model.freespins.DisableFreeSpinsCampaignRequest;
import common.model.freespins.FreeSpinCampaignStatus;
import common.model.freespins.FreeSpinsInfo;
import gaming.octoplay.api.freespins.model.CancelFreeSpinsOctoplayRequest;
import gaming.octoplay.api.freespins.model.CreateFreeSpinsOctoplayRequest;
import integration.MockServer;
import integration.utils.TestUtils;
import io.cucumber.java.After;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.SneakyThrows;
import okhttp3.OkHttpClient;

public class OctoplayFreeSpinsSteps {
    private static final String LOGIN = "name";
    private static final String PASSWORD = "password";
    private static final String FREE_ROUNDS_CREATE_PATH = "/api/free-rounds/create";
    private static final String FREE_ROUNDS_CANCEL_PATH = "/api/free-rounds/cancel";
    private static final String startDateString = "2023-09-03T23:59:59";
    private static final String expirationDateString = "2025-10-06T23:59:59";
    private static final String ERROR_MESSAGE = "Some error message";

    private static final ObjectMapper mapper = new CommonObjectMapper();

    @Autowired
    private AggregatorServerProperties props;
    @Autowired
    private DynamicCloud cloud;
    @Autowired
    private SpannerTemplate spannerTemplate;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private OkHttpClient httpClient;

    private final List<String> mockExpectations = new ArrayList<>();

    private PlainServiceInfo psi;
    private FreeSpinsBot bot;
    private BrandRepo brandRepo;

    private String currency;
    private String providerCurrency;
    private String gameCode;
    private String bonusCode;
    private String requestId;
    private long freeSpinsId;
    private String playerId;
    private BigDecimal betAmount;
    private int spins;
    private LocalDateTime startDate;
    private LocalDateTime expirationDate;
    private CreateFreeSpinsResponse actualCreate;
    private CancelPlayerFreeSpinResponse actualCancel;
    private CancelFreeSpinsResponse actualCancelBatching;

    private final OperatorSpec operator = OperatorSpec.BLUEDREAM;
    private final ProviderSpec provider = ProviderSpec.OCTOPLAY;

    @After("@octoplay-fs")
    public void afterScenario() {
        mockExpectations.forEach(expectationId -> MockServer.mockServer.clear(expectationId));
    }

    @Given("^Initialize properties -octoplay fs-$")
    public void initializeProperties() throws Exception {
        psi = buildPlainServiceInfo();
        PlainServiceInfo psiBot = buildPlainServiceInfoForBot();
        cloud.addUps(psi);
        cloud.addUps(psiBot);
        this.brandRepo = new DefaultBrandRepo(spannerTemplate, cacheManager);

        bot = new DefaultFreeSpinsBot(httpClient, operator, provider, psiBot);
    }

    @Given("^Initialize currency (.+) and game code (.+) -octoplay fs-$")
    public void initializeCurrencyAndGameCode(String currency, String gameCode) {
        this.currency = currency;
        this.providerCurrency = currency + "C";
        this.gameCode = gameCode;
    }

    @Given("^Initialize for create with accountId (.+) -octoplay fs-$")
    public void initializeFreeSpinsCreate(String accountId) {
        this.bonusCode = PlatformUtil.randomAlphanumeric(5);
        this.requestId = PlatformUtil.randomUUID().toString();
        this.freeSpinsId = 12345L;
        this.playerId = TestUtils.buildPlayerId(accountId, currency);
        this.betAmount = new BigDecimal("5");
        this.spins = 10;
        this.startDate = LocalDateTime.parse(startDateString);
        this.expirationDate = LocalDateTime.parse(expirationDateString);
    }

    @Given("^Prepare mockServer for create with status (.+) -octoplay fs-$")
    public void genMockServerForCreate(int status) throws JsonProcessingException {
        String providerGameCode = getProviderGameCode();
        var request = CreateFreeSpinsOctoplayRequest.builder()
                .playerId(playerId)
                .currency(providerCurrency)
                .operator(operator.code())
                .bonusCode(getProviderBonusCode())
                .gameIdList(List.of(providerGameCode))
                .betAmount(betAmount)
                .spins(spins)
                .expirationDate(expirationDate)
                .build();

        String requestBody = mapper.writeValueAsString(request);

        createMockServer(
                requestBody,
                HttpStatus.SC_OK == status ? getValidCreateResponseJson() : getErrorResponseJson(),
                FREE_ROUNDS_CREATE_PATH,
                status);
    }

    @Given("^Prepare mockServer for cancel with status (.+) -octoplay fs-$")
    public void genMockServerForCancel(int status) throws JsonProcessingException {
        var request = new CancelFreeSpinsOctoplayRequest(freeSpinsId);

        String requestBody = mapper.writeValueAsString(request);

        createMockServer(
                requestBody,
                HttpStatus.SC_OK == status ? getValidCancelResponseJson() : getErrorResponseJson(),
                FREE_ROUNDS_CANCEL_PATH,
                status);
    }

    @When("^Send create player free spins -octoplay fs-$")
    public void sendCreateFreeSpins() throws Exception {
        var request = getCreatePlayerFreeSpinRequest();
        actualCreate = bot.createPlayerFreeSpin(request, getCombinedRequestId());
        System.out.println();
    }

    @When("^Send create player free spins by bonus -octoplay fs-$")
    public void sendCreateFreeSpinsByBonus() throws Exception {
        var request = getCreatePlayerFreeSpinByBonusRequest();
        actualCreate = bot.createPlayerFreeSpin(request, getCombinedRequestId());
    }

    @When("^Send cancel player free spins -octoplay fs-$")
    public void sendCancelFreeSpins() throws Exception {
        var request = getCancelPlayerFreeSpinRequest();
        actualCancel = bot.cancelFreeSpins(request);
    }

    @When("^Send cancel free spins batching -octoplay fs-$")
    public void sendCancelFreeSpinsBatching() throws Exception {
        var request = getCancelFreeSpinsRequest();
        actualCancelBatching = bot.cancelFreeSpinsBatching(request);
    }

    @Then("^Has created (.+) free spins -octoplay fs-$")
    public void freeSpinExistedAfterProcessing(int freeSpinsCount) {
        Assertions.assertEquals(bonusCode, actualCreate.getBonusCode());
        Assertions.assertEquals(0, actualCreate.getCode());
        Assertions.assertEquals(playerId, actualCreate.getFreeSpin().getPlayerId());
        Assertions.assertEquals(String.valueOf(freeSpinsId), actualCreate.getFreeSpin().getFreeSpinsId());
        Assertions.assertTrue(actualCreate.getFreeSpin().isApplied());
        Assertions.assertNull(actualCreate.getFreeSpin().getMessage());

        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);
        checkAccountFreeSpinsInDb(freeSpinsCount);
    }

    @Then("^Has canceled player free spins with isApplied (.+) -octoplay fs-$")
    public void hasCancelResponse(boolean isApplied) {
        Assertions.assertEquals(bonusCode, actualCancel.getBonusCode());
        Assertions.assertEquals(0, actualCancel.getCode());
        Assertions.assertEquals(playerId, actualCancel.getFreeSpin().getPlayerId());
        Assertions.assertEquals(String.valueOf(freeSpinsId), actualCancel.getFreeSpin().getFreeSpinsId());
        Assertions.assertEquals(isApplied, actualCancel.getFreeSpin().isApplied());
        Assertions.assertNull(actualCancel.getFreeSpin().getMessage());
    }

    @Then("^Has canceled free spins batching with isApplied (.+) -octoplay fs-$")
    public void hasCancelBatchingResponse(boolean isApplied) {
        Assertions.assertEquals(bonusCode, actualCancelBatching.getBonusCode());
        Assertions.assertEquals(0, actualCancelBatching.getCode());
        Assertions.assertNotNull(actualCancelBatching.getFreeSpinsList());
        Assertions.assertEquals(1, actualCancelBatching.getFreeSpinsList().size());

        var freeSpins = actualCancelBatching.getFreeSpinsList().getFirst();

        Assertions.assertEquals(playerId, freeSpins.getPlayerId());
        Assertions.assertEquals(String.valueOf(freeSpinsId), freeSpins.getFreeSpinsId());
        Assertions.assertEquals(isApplied, freeSpins.isApplied());
        Assertions.assertNull(freeSpins.getMessage());
    }

    @Then("^Has create free spins responses with error -octoplay fs-$")
    public void hasCreateErrorResponse() {
        var expected = CreateFreeSpinsResponse.buildError(400, ERROR_MESSAGE);
        expected.setBonusCode(this.bonusCode);
        Assertions.assertEquals(expected.getError(), actualCreate.getError());
        Assertions.assertEquals(expected.getMsg(), actualCreate.getMsg());
        Assertions.assertNull(actualCreate.getFreeSpin());

        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);
    }

    @Then("^Has cancel free spins batching responses with error -octoplay fs-$")
    public void hasCancelBatchingErrorResponse() {
        Assertions.assertEquals(bonusCode, actualCancelBatching.getBonusCode());
        Assertions.assertEquals(400, actualCancelBatching.getCode());

        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);

        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);
    }

    @When("^Send create free spin spin campaign request -octoplay fs-$")
    public void sendCreateFreeSpinSpinCampaignRequest() throws Exception {
        var createFreeSpinCampaignRequest = CreateFreeSpinsCampaignDTO.builder()
                .campaign(bonusCode)
                .currency(currency)
                .betAmount(betAmount)
                .spins(spins)
                .startDate(startDate)
                .expirationDate(expirationDate)
                .requestId(requestId)
                .betLevel(1)
                .operator(operator.code())
                .provider(provider.code())
                .gameCode(gameCode)
                .build();
        bot.createFreeSpinsCampaign(createFreeSpinCampaignRequest, requestId);
    }

    @Then("Has created free spin campaign -octoplay fs-")
    public void hasCreatedFreeSpinCampaign() {
        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);
    }

    @When("^Send disable free spin spin campaign request -octoplay fs-$")
    public void sendDisableFreeSpinSpinCampaignRequest() throws Exception {
        var request = new DisableFreeSpinsCampaignRequest(bonusCode);

        var response = bot.disableFreeSpinsCampaign(request, requestId);
        Assertions.assertEquals(bonusCode, response.getBonusCode());
    }

    @Then("Has disabled free spin campaign -octoplay fs-")
    public void hasDisabledFreeSpinCampaign() {
        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CANCELED);
    }

    @When("^Check create free spins is disabled -octoplay fs-$")
    public void sendCreateFreeSpinsToDisabledCampaign() throws Exception {
        var request = getCreatePlayerFreeSpinRequest();
        actualCreate = bot.createPlayerFreeSpin(request, requestId);

        Assertions.assertNotNull(actualCreate);
        Assertions.assertEquals(FreeSpinsService.FREE_SPINS_CAMPAIGN_CANCELLED.formatted(bonusCode, provider.code()), actualCreate.getMsg());
    }

    @When("^Check create free spins is disabled async -octoplay fs-$")
    public void sendCreateFreeSpinsToDisabledCampaignAsync() throws Exception {
        var request = new CreateFreeSpinsRequest();
        request.setBonusCode(bonusCode);
        request.setCurrency(currency);
        request.setBetAmount(betAmount);
        request.setSpins(spins);
        request.setStartDate(startDate);
        request.setExpirationDate(expirationDate);
        request.setGames(List.of(gameCode));
        request.setPlayerIdList(List.of(playerId));
        CreateFreeSpinsResponse createFreeSpinsResponse = bot.createV2FreeSpins(request, requestId);

        Assertions.assertNotNull(createFreeSpinsResponse);
        Assertions.assertEquals(FreeSpinsService.FREE_SPINS_CAMPAIGN_CANCELLED.formatted(bonusCode, provider.code()), createFreeSpinsResponse.getMsg());
    }

    private void checkFreeSpinBonusInDb(FreeSpinCampaignStatus status) {
        var freeSpinCampaign = brandRepo.freeSpinCampaign(bonusCode, operator.code(), provider.code());
        Assertions.assertTrue(freeSpinCampaign.isPresent());
        Assertions.assertEquals(currency, freeSpinCampaign.get().getCurrency());
        Assertions.assertEquals(status.code(), freeSpinCampaign.get().getStatus());
    }

    private void checkAccountFreeSpinsInDb(int freeSpinsCount) {
        var freeSpins = brandRepo.accountFreeSpinsByBonusCode(operator.code(), provider.code(), bonusCode);
        Assertions.assertFalse(freeSpins.isEmpty());
        Assertions.assertEquals(freeSpinsCount, freeSpins.get().size());
    }

    private PlainServiceInfo buildPlainServiceInfo() throws Exception {
        return new PlainServiceInfo(
                String.format("%s-%s-%s",
                        AggregatorWildcardUPSs.IRGS_PREFIX, operator.code(), provider.code()),
                new URIBuilder()
                        .setScheme("http")
                        .setHost("localhost")
                        .setPort(MockServer.mockServer.getPort())
                        .setPath("/api/v1")
                        .setUserInfo(LOGIN, PASSWORD)
                        .build().toString());
    }

    private PlainServiceInfo buildPlainServiceInfoForBot() throws Exception {
        return new PlainServiceInfo(
                String.format("%s-%s", AggregatorWildcardUPSs.IGP_PREFIX, operator.code()),
                new URIBuilder()
                        .setScheme("http")
                        .setHost("localhost")
                        .setPort(props.CLOUD_APP_PORT.get())
                        .setUserInfo(LOGIN, PASSWORD)
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "alpha_numeric")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                        .build().toString());
    }

    private void createMockServer(String requestBody, String responseBody, String path, int statusCode) {
        var httpRequest = HttpRequest.request()
                .withMethod(HttpPost.METHOD_NAME)
                .withPath(path)
                .withBody(requestBody);

        var httpResponse = HttpResponse.response()
                .withStatusCode(statusCode)
                .withContentType(MediaType.APPLICATION_JSON)
                .withDelay(TimeUnit.MICROSECONDS, TestUtils.HTTP_RESPONSE_DELAY);

        if (StringUtils.isNotEmpty(responseBody)) {
            httpResponse.withBody(responseBody);
        }

        Expectation[] expectation = MockServer.mockServer.when(httpRequest, TestUtils.expectation()).respond(httpResponse);
        mockExpectations.addAll(Arrays.stream(expectation).map(Expectation::getId).toList());
    }

    private CreateFreeSpinsRequest getCreatePlayerFreeSpinRequest() {
        var request = new CreateFreeSpinsRequest();
        request.setBonusCode(bonusCode);
        request.setCurrency(currency);
        request.setBetAmount(betAmount);
        request.setSpins(spins);
        request.setStartDate(startDate);
        request.setExpirationDate(expirationDate);
        request.setGames(List.of(gameCode));
        request.setRequestId(requestId);
        request.setPlayerId(playerId);
        return request;
    }

    private CreateFreeSpinsRequest getCreatePlayerFreeSpinByBonusRequest() {
        var request = new CreateFreeSpinsRequest();
        request.setBonusCode(bonusCode);
        request.setRequestId(requestId);
        request.setPlayerId(playerId);
        return request;
    }

    private CancelPlayerFreeSpinRequest getCancelPlayerFreeSpinRequest() {
        return CancelPlayerFreeSpinRequest.builder()
                .freeSpin(getFreeSpinsInfo())
                .campaign(bonusCode)
                .currency(currency)
                .cancelBonusCode(false)
                .requestId(requestId)
                .build();
    }

    private CancelFreeSpinsRequest getCancelFreeSpinsRequest() {
        return CancelFreeSpinsRequest.builder()
                .freeSpinsList(List.of(getFreeSpinsInfo()))
                .campaign(bonusCode)
                .currency(currency)
                .cancelBonusCode(false)
                .requestId(requestId)
                .build();
    }

    private FreeSpinsInfo getFreeSpinsInfo() {
        return FreeSpinsInfo.builder()
                .playerId(playerId)
                .freeSpinsId(String.valueOf(freeSpinsId))
                .build();
    }

    private String getProviderBonusCode() {
        return bonusCode + "-" + Hashing.murmur3_128().hashString(getCombinedRequestId(), StandardCharsets.UTF_8);
    }

    private String getProviderGameCode() {
        return gameCode.split("_")[1];
    }

    private String getCombinedRequestId() {
        return requestId + "-" + playerId;
    }

    @SneakyThrows
    private String getValidCreateResponseJson() {
        return String.format(
                """
                        {
                            "id": %d
                        }
                        """, freeSpinsId);
    }

    @SneakyThrows
    private String getValidCancelResponseJson() {
        return "{}";
    }

    @SneakyThrows
    private String getErrorResponseJson() {
        return String.format(
                """
                        {
                            "message": "%s"
                        }
                        """, ERROR_MESSAGE);
    }
}
