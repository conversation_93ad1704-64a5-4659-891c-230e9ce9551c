package integration.infinity.steps;

import static gaming.infingame.b2b.InfingameEndpointV2.GOLD_CURRENCY;
import static gaming.infingame.b2b.InfingameEndpointV2.SWEEPSTAKE_CURRENCY;

import java.io.File;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;

import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.collect.Lists;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.HttpProto;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.RawServiceInfo;

import aggregator.bots.DefaultInfingameBot;
import aggregator.bots.InfingameBot;
import aggregator.endpoints.InMemoryUamOmniGamePlayEndpoint;
import aggregator.listeners.MockDataGenerator;
import aggregator.model.Account;
import aggregator.model.AccountFreeSpin;
import aggregator.model.FreeSpinCampaign;
import aggregator.model.FreeSpinIdempotencyKey;
import aggregator.model.WalletTransaction;
import aggregator.repo.BrandRepo;
import aggregator.repo.DefaultBrandRepo;
import aggregator.repo.DefaultSpannerTransactionalCallback;
import aggregator.repo.DefaultWalletSessionRepo;
import aggregator.repo.WalletSessionRepo;
import api.v1.ApiFactory;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.flatten.TempTokenFlatten;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.FreeSpinCampaignStatus;
import common.model.freespins.FreeSpinIdempotencyStatus;
import common.model.freespins.FreeSpinsStatus;
import gaming.infingame.Mappers;
import gaming.infingame.b2b.model.DepositResponse;
import gaming.infingame.b2b.model.EnterResponse;
import gaming.infingame.b2b.model.GetBalanceResponse;
import gaming.infingame.b2b.model.LogoutResponse;
import gaming.infingame.b2b.model.ReEnterResponse;
import gaming.infingame.b2b.model.RefundResponse;
import gaming.infingame.b2b.model.RoundBetResponse;
import gaming.infingame.b2b.model.RoundWinResponse;
import gaming.infingame.b2b.model.common.ErrorCode;
import gaming.infingame.b2b.model.common.GiftSpin;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import okhttp3.OkHttpClient;

public class InfinGameIntegrationsV2Steps {
    private final static String OK = "ok";
    @Autowired
    private AggregatorServerProperties props;
    @Autowired
    private DynamicCloud cloud;
    @Autowired
    private SpannerTemplate spannerTemplate;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private OkHttpClient httpClient;
    private BrandRepo brandRepo;
    private WalletSessionRepo walletSessionRepo;
    private InfingameBot infingameBot;
    private String currency;
    private String sessionId;
    private String gameCode;
    private String roundId;
    private String lastGiftSpinId;
    private String lastGiftSpinOffer;
    private String lastTransactionIdBet;
    private String lastTournamentTxId;
    private int transactionCount;
    private Account account;
    private AccountRoutingCurrencyFlatten flatten;
    private EnterResponse enterResponse;
    private RoundBetResponse roundBetResponse;
    private RoundWinResponse roundWinResponse;
    private DepositResponse depositResponse;
    private RefundResponse refundResponse;

    private final List<String> listTxIdBet = Lists.newArrayList();
    private final List<String> listTxIdWin = Lists.newArrayList();
    private final List<String> listTxIdDeposit = Lists.newArrayList();
    private final List<String> listTxIdRefund = Lists.newArrayList();

    // error requests
    private ReEnterResponse reEnterResponseError;
    private GetBalanceResponse getBalanceResponseError;
    private RoundBetResponse roundBetResponseError;
    private RoundWinResponse roundWinResponseError;
    private RefundResponse refundResponseError;
    private LogoutResponse logoutResponseError;
    private EnterResponse enterResponseError;

    @Given("^Initialize bootstrap properties -infingame- v2$")
    public void initializeBootstrapProperties() throws Exception {
        PlainServiceInfo si = new PlainServiceInfo(
                String.format("%s-%s", AggregatorWildcardUPSs.IGP_PREFIX, OperatorSpec.BLUEDREAM.code()),
                new URIBuilder()
                        .setScheme("http")
                        .setHost("localhost")
                        .setPort(props.CLOUD_APP_PORT.get())
                        .setUserInfo("admin", "changeit")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "plain")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                        .build().toURL().toExternalForm());

        File publicKey = new File(getClass().getClassLoader().getResource("infingame-public-key.txt").getFile());
        RawServiceInfo signSiV2 = new RawServiceInfo(
                String.format("%s-%s-%s-sign-v2", AggregatorWildcardUPSs.IRGS_PREFIX, OperatorSpec.BLUEDREAM.code(), ProviderSpec.INFINGAME.code()),
                FileUtils.readFileToByteArray(publicKey));

        cloud.addUps(si);
        cloud.addUps(signSiV2);
        this.brandRepo = new DefaultBrandRepo(spannerTemplate, cacheManager);
        this.walletSessionRepo = new DefaultWalletSessionRepo(spannerTemplate);
    }

    @Given("^New game with currency (.+) -infingame- v2$")
    public void newGameWithCurrency(String curr) throws Throwable {
        this.currency = curr;
        this.infingameBot = createBot();
        this.sessionId = PlatformUtil.randomUUID().toString();
        this.flatten = genAccountRoutingCurrencyFlatten();
        Assertions.assertNotNull(this.currency);
        Assertions.assertEquals(this.flatten.getCurrency(), this.currency);
    }

    @When("^Enter the game (.+) -infingame- v2$")
    public void enterTheGame(String gameCode) throws Throwable {
        String token = genTempTokenFlatten(flatten).writeExternal();
        this.enterResponse = this.infingameBot.infingameEnter(
                this.sessionId,
                token,
                gameCode);
        this.account = this.brandRepo.requiredAccount(MockDataGenerator.OPERATOR, String.valueOf(this.flatten.getAccountId()));
        this.gameCode = gameCode;
        Assertions.assertEquals(flatten.writeExternal(), this.enterResponse.user.id);
    }

    @Then("^Has (\\d+(?:\\.\\d+)?) balance -infingame- v2$")
    public void hasBalance(BigDecimal balance) throws Throwable {
        GetBalanceResponse getBalanceResponse = this.infingameBot.infingameGetBalance(
                this.sessionId,
                this.enterResponse.user.id);
        Assertions.assertEquals(toProviderCurrency(flatten.getCurrency()), getBalanceResponse.balance.currency);
        Assertions.assertEquals(0, balance.compareTo(Mappers.fromProviderAmount(getBalanceResponse.balance.value)),
                String.format("Expected %.2f but balance %.2f", balance, Mappers.fromProviderAmount(getBalanceResponse.balance.value)));
    }

    @And("^User game session has valid sessionId -infingame- v2$")
    public void userAccountHasValidSessionId() {
        var gameSession = this.brandRepo.requiredGameSession(this.account.getHash(), this.sessionId, ProviderSpec.INFINGAME.code());
        Assertions.assertEquals(this.sessionId, gameSession.getSessionId());
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) bet -infingame- v2$")
    public void sendBet(BigDecimal bet) throws Throwable {
        if (StringUtils.isEmpty(this.roundId)) {
            this.roundId = Long.toString(ApiFactory.RANDOM.nextLong());
        }

        sendBet(bet, PlatformUtil.randomUUID().toString());
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) bet using previous transactionId -infingame- v2$")
    public void sendBetPrevious(BigDecimal bet) throws Throwable {
        if (StringUtils.isEmpty(this.roundId)) {
            this.roundId = Long.toString(ApiFactory.RANDOM.nextLong());
        }

        sendBet(bet, lastTransactionIdBet);
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) bet with transactionId (.+) -infingame- v2$")
    public void sendBetWithTxId(BigDecimal bet, String transactionId) throws Throwable {
        sendBet(bet, transactionId);
    }

    private void sendBet(BigDecimal bet, String transactionId) throws Exception {
        RoundBetResponse roundBetResponse = this.infingameBot.infingameRoundBet(
                this.sessionId,
                this.roundId,
                transactionId,
                this.enterResponse.user.id,
                bet);

        if (OK.equals(roundBetResponse.result)) {
            if (!this.listTxIdBet.contains(transactionId)) {
                this.roundBetResponse = roundBetResponse;
                this.transactionCount++;
                this.listTxIdBet.add(transactionId);
                this.lastTransactionIdBet = transactionId;
            }

            var wsOpt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), ProviderSpec.INFINGAME.code(), roundId, currency);
            Assertions.assertTrue(wsOpt.isPresent());
            var txs = wsOpt.get().getTransactions();
            Assertions.assertEquals(this.transactionCount, txs.size());
            assertDebitWalletTransaction(bet, txs, transactionId);
        } else {
            this.roundBetResponseError = roundBetResponse;
        }
    }

    @Then("^Add free spins: bonusCode (.+), amount (.+) -infingame-$")
    public void addFreeSpins(String bonusCode, String amount) {
        var requestId = "requestId";
        this.lastGiftSpinId = "296087";
        this.lastGiftSpinOffer = bonusCode;
        var operator = OperatorSpec.BLUEDREAM.code();
        var provider = ProviderSpec.INFINGAME.code();
        spannerTemplate.performReadWriteTransaction(new DefaultSpannerTransactionalCallback<>(cacheManager,
                callback -> {
                    var brandRepo = callback.brandRepo();
                    var product = brandRepo.getOrCreateProduct(operator, this.gameCode);
                    var freeSpinCampaign = new FreeSpinCampaign(
                            bonusCode,
                            operator,
                            provider,
                            product,
                            this.currency,
                            amount,
                            1,
                            LocalDateTime.now(ZoneOffset.UTC),
                            LocalDateTime.now(ZoneOffset.UTC),
                            FreeSpinCampaignStatus.CREATED.code());
                    brandRepo.getOrCreateFreeSpinCampaignWithoutCache(freeSpinCampaign);
                    var freeSpinIdempotencyKey = new FreeSpinIdempotencyKey(
                            operator,
                            provider,
                            requestId,
                            FreeSpinIdempotencyStatus.FINISHED.code(),
                            LocalDateTime.now(ZoneOffset.UTC));
                    brandRepo.getOrCreateFreeSpinIdempotencyKey(freeSpinIdempotencyKey);

                    var accountId = this.flatten.getAccountId();
                    var account = brandRepo.getOrCreateAccount(operator, accountId);
                    var accountFreeSpin = new AccountFreeSpin(
                            account.getHash(),
                            operator,
                            provider,
                            this.lastGiftSpinId,
                            requestId,
                            bonusCode,
                            this.enterResponse.user.id,
                            FreeSpinsStatus.CREATED.code());
                    brandRepo.insert(accountFreeSpin);

                    return new Object();
                }));
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) bet with gift spin -infingame- v2$")
    public void sendBetWithGiftSpin(BigDecimal bet) throws Throwable {
        String transactionId = PlatformUtil.randomUUID().toString();
        this.roundId = Long.toString(ApiFactory.RANDOM.nextLong());
        var giftSpin = new GiftSpin();
        giftSpin.giftId = this.lastGiftSpinId;
        giftSpin.offer = this.lastGiftSpinOffer;
        giftSpin.finished = false;
        RoundBetResponse roundBetResponse = this.infingameBot.infingameRoundBetGiftSpin(
                this.sessionId,
                this.roundId,
                transactionId,
                this.enterResponse.user.id,
                bet,
                giftSpin);

        Assertions.assertNull(roundBetResponse.error);

        if (OK.equals(roundBetResponse.result)) {
            if (!this.listTxIdBet.contains(transactionId)) {
                this.roundBetResponse = roundBetResponse;
                this.transactionCount++;
                this.listTxIdBet.add(transactionId);
                this.lastTransactionIdBet = transactionId;
            }

            var wsOpt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), ProviderSpec.INFINGAME.code(), roundId, currency);
            Assertions.assertTrue(wsOpt.isPresent());
            var txs = wsOpt.get().getTransactions();
            Assertions.assertEquals(this.transactionCount, txs.size());
            assertDebitWalletTransaction(BigDecimal.ZERO, txs, transactionId);
        } else {
            this.roundBetResponseError = roundBetResponse;
        }
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) win request -infingame- v2$")
    public void sendWinRequest(BigDecimal win) throws Throwable {
        String transactionId = PlatformUtil.randomUUID().toString();
        sendWin(win, transactionId);
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) deposit request -infingame- v2$")
    public void sendDepositRequest(BigDecimal deposit) throws Throwable {
        String transactionId = PlatformUtil.randomUUID().toString();
        this.lastTournamentTxId = transactionId;
        sendDeposit(deposit, transactionId);
    }

    @And("^Send duplicate (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) deposit request -infingame- v2$")
    public void sendDuplicateDepositRequest(BigDecimal deposit) throws Throwable {
        sendDeposit(deposit, this.lastTournamentTxId);
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) win request with transactionId (.+) -infingame- v2$")
    public void sendWinRequestWithTxId(BigDecimal win, String transactionId) throws Throwable {
        sendWin(win, transactionId);
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) win request with gift spin -infingame- v2$")
    public void sendWinRequestGiftSpin(BigDecimal win) throws Throwable {
        String transactionId = PlatformUtil.randomUUID().toString();
        var giftSpin = new GiftSpin();
        giftSpin.giftId = this.lastGiftSpinId;
        giftSpin.offer = this.lastGiftSpinOffer;
        giftSpin.finished = true;
        this.roundWinResponse = this.infingameBot.infingameRoundWinGiftSpin(
                this.sessionId,
                this.roundId,
                transactionId,
                this.enterResponse.user.id,
                win,
                giftSpin);

        Assertions.assertNull(roundWinResponse.error);

        if (OK.equals(roundWinResponse.result)) {
            if (!this.listTxIdWin.contains(transactionId)) {
                this.transactionCount++;
                this.listTxIdWin.add(transactionId);
            }
            var wsOpt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), ProviderSpec.INFINGAME.code(), roundId, currency);
            Assertions.assertTrue(wsOpt.isPresent());
            var txs = wsOpt.get().getTransactions();
            Assertions.assertEquals(this.transactionCount, txs.size());
            assertCreditWalletTransaction(win, txs, transactionId);
        } else {
            this.roundWinResponseError = this.roundWinResponse;
        }
    }

    private void sendWin(BigDecimal win, String transactionId) throws Exception {
        this.roundWinResponse = this.infingameBot.infingameRoundWin(
                this.sessionId,
                this.roundId,
                transactionId,
                this.enterResponse.user.id,
                win);

        if (OK.equals(roundWinResponse.result)) {
            if (!this.listTxIdWin.contains(transactionId)) {
                this.transactionCount++;
                this.listTxIdWin.add(transactionId);
            }
            var wsOpt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), ProviderSpec.INFINGAME.code(), roundId, currency);
            Assertions.assertTrue(wsOpt.isPresent());
            var txs = wsOpt.get().getTransactions();
            Assertions.assertEquals(this.transactionCount, txs.size());
            assertCreditWalletTransaction(win, txs, transactionId);
        } else {
            this.roundWinResponseError = this.roundWinResponse;
        }
    }

    private void sendDeposit(BigDecimal deposit, String transactionId) throws Exception {
        this.depositResponse = this.infingameBot.infingameDeposit(
                this.roundId,
                transactionId,
                this.enterResponse.user.id,
                deposit);

        if (OK.equals(depositResponse.result)) {
            if (!this.listTxIdDeposit.contains(transactionId)) {
                this.transactionCount++;
                this.listTxIdDeposit.add(transactionId);
            }
            var wsOpt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), ProviderSpec.INFINGAME.code(), roundId, currency);
            Assertions.assertTrue(wsOpt.isPresent());
            var txs = wsOpt.get().getTransactions();
            Assertions.assertEquals(this.transactionCount, txs.size());
            assertCreditWalletTransaction(deposit, txs, transactionId);
        }
    }

    @And("^ReEnter the game -infingame- v2$")
    public void reEnterTheGame() throws Throwable {
        var newSessionId = PlatformUtil.randomUUID().toString();
        ReEnterResponse reEnterResponse = this.infingameBot.infingameReEnter(
                this.sessionId,
                newSessionId,
                this.enterResponse.user.id,
                this.gameCode);
        Assertions.assertEquals(this.enterResponse.user.id, reEnterResponse.user.id);
        this.sessionId = newSessionId;
    }

    @And("^Send (\\d+(?:\\.\\d+)?) refund -infingame- v2$")
    public void sendRefundRequest(BigDecimal refund) throws Throwable {
        sendRefund(refund, this.lastTransactionIdBet, false);
    }

    @And("^Send (\\d+(?:\\.\\d+)?) refund with transactionId (.+) -infingame- v2$")
    public void sendRefundWithTxId(BigDecimal refund, String transactionId) throws Throwable {
        sendRefund(refund, transactionId, false);
    }

    @And("^Send (\\d+(?:\\.\\d+)?) refund with gift spin -infingame- v2$")
    public void sendRefundWithGiftRequest(BigDecimal refund) throws Throwable {
        sendRefund(refund, this.lastTransactionIdBet, true);
    }

    @And("^Send (\\d+(?:\\.\\d+)?) refund after win -infingame- v2$")
    public void sendRefundAfterWin(BigDecimal refund) throws Throwable {
        this.infingameBot.infingameRefund(
                this.sessionId,
                this.roundId,
                this.lastTransactionIdBet,
                this.enterResponse.user.id,
                refund);
    }

    private void sendRefund(BigDecimal refund, String transactionId, boolean isFreeSpin) throws Exception {
        this.refundResponse = this.infingameBot.infingameRefund(
                this.sessionId,
                this.roundId,
                transactionId,
                this.enterResponse.user.id,
                refund);

        if (OK.equals(refundResponse.result)) {
            if (!this.listTxIdRefund.contains(transactionId)) {
                this.transactionCount++;
                this.listTxIdRefund.add(transactionId);
            }
            var wsOpt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), ProviderSpec.INFINGAME.code(), roundId, currency);
            Assertions.assertTrue(wsOpt.isPresent());
            var txs = wsOpt.get().getTransactions();
            Assertions.assertEquals(this.transactionCount, txs.size());

            if (isFreeSpin) {
                assertRefundWalletTransaction(BigDecimal.ZERO, txs);
            } else {
                assertRefundWalletTransaction(refund, txs);
            }
        } else {
            this.refundResponseError = this.refundResponse;
        }
    }

    @And("^User logout -infingame- v2$")
    public void logout() throws Throwable {
        GetBalanceResponse getBalanceResponse = this.infingameBot.infingameGetBalance(
                this.sessionId,
                this.enterResponse.user.id);
        var balance = Mappers.fromProviderAmount(getBalanceResponse.balance.value);
        LogoutResponse logoutResponse = this.infingameBot.infingameLogout(
                this.sessionId,
                this.enterResponse.user.id,
                this.gameCode);
        var account = this.brandRepo.requiredAccount(MockDataGenerator.OPERATOR, String.valueOf(this.flatten.getAccountId()));
        var wsOpt = this.walletSessionRepo.walletSession(account.getHash(), ProviderSpec.INFINGAME.code(), this.roundId, this.currency);
        Assertions.assertTrue(wsOpt.isPresent());
        var gameSessionAfterLogout = this.brandRepo.gameSession(account.getHash(), this.sessionId, ProviderSpec.INFINGAME.code());
        Assertions.assertFalse(gameSessionAfterLogout.isPresent());
        Assertions.assertEquals(0, balance.compareTo(Mappers.fromProviderAmount(logoutResponse.balance.value)),
                String.format("Expected %.2f but balance %.2f",
                        balance,
                        Mappers.fromProviderAmount(logoutResponse.balance.value)));
    }

    @Then("^Game session after logout is closed -infingame- v2$")
    public void gameSessionAfterLogoutIsClosed() throws Throwable {
        RoundBetResponse roundBetResponse = this.infingameBot.infingameRoundBet(
                this.sessionId,
                PlatformUtil.randomUUID().toString(),
                PlatformUtil.randomUUID().toString(),
                this.enterResponse.user.id,
                BigDecimal.ZERO);
        Assertions.assertEquals(ErrorCode.WL_ERROR.name(), roundBetResponse.error.code);
    }

    @And("^Send get balance request with wrong session -infingame- v2$")
    public void getBalanceWithWrongSession() throws Throwable {
        this.getBalanceResponseError = this.infingameBot.infingameGetBalance(
                PlatformUtil.randomUUID().toString(),
                this.enterResponse.user.id);
        Assertions.assertNotNull(this.getBalanceResponseError);
    }

    @Then("^Get balance response has error code: wl_error -infingame- v2$")
    public void getBalanceResponseHasErrorCode() {
        Assertions.assertEquals(ErrorCode.WL_ERROR.name(), this.getBalanceResponseError.error.code);
    }

    @And("^Send reenter request with wrong session -infingame- v2$")
    public void reEnterWithWrongSession() throws Throwable {
        this.reEnterResponseError = this.infingameBot.infingameReEnter(
                PlatformUtil.randomUUID().toString(),
                PlatformUtil.randomUUID().toString(),
                this.enterResponse.user.id,
                this.gameCode);
        Assertions.assertNotNull(this.reEnterResponseError);
    }

    @Then("^Reenter response has error code -infingame- v2$")
    public void reEnterResponseHasErrorCode() {
        Assertions.assertEquals(ErrorCode.WL_ERROR.name(), this.reEnterResponseError.error.code);
    }

    @And("^Send bet round request with wrong session -infingame- v2$")
    public void sendBetRoundWithWrongSession() throws Throwable {
        this.roundBetResponseError = this.infingameBot.infingameRoundBet(
                PlatformUtil.randomUUID().toString(),
                this.roundId,
                this.lastTransactionIdBet,
                this.enterResponse.user.id,
                new BigDecimal(1));
        Assertions.assertNotNull(this.roundBetResponseError);
    }

    @And("^Send win round request with wrong session -infingame- v2$")
    public void sendWinRoundWithWrongSession() throws Throwable {
        this.roundWinResponseError = this.infingameBot.infingameRoundWin(
                PlatformUtil.randomUUID().toString(),
                this.roundId,
                PlatformUtil.randomUUID().toString(),
                this.enterResponse.user.id,
                new BigDecimal(1));
        Assertions.assertNotNull(this.roundWinResponseError);
    }

    @Then("^Win round response has error code -infingame- v2$")
    public void winRoundResponseHasErrorCode() {
        Assertions.assertEquals(ErrorCode.WL_ERROR.name(), this.roundWinResponseError.error.code);
    }

    @And("^Send refund request with wrong session -infingame- v2$")
    public void sendRefundRequestWithWrongSession() throws Throwable {
        this.refundResponseError = createBot().infingameRefund(
                PlatformUtil.randomUUID().toString(),
                this.roundId,
                this.lastTransactionIdBet,
                this.enterResponse.user.id,
                new BigDecimal(1));
        Assertions.assertNotNull(this.refundResponseError);
    }

    @Then("^Refund response has error code -infingame- v2$")
    public void refundResponseHasErrorCode() {
        Assertions.assertEquals(ErrorCode.WL_ERROR.name(), this.refundResponseError.error.code);
    }

    @And("^Send logout request with wrong session -infingame- v2$")
    public void sendLogoutRequestWithWrongSession() throws Throwable {
        this.logoutResponseError = this.infingameBot.infingameLogout(
                PlatformUtil.randomUUID().toString(),
                this.enterResponse.user.id,
                this.gameCode);
        Assertions.assertNotNull(this.logoutResponseError);
    }

    @Then("^Logout response has error code -infingame- v2$")
    public void betResponseHasErrorCode() {
        Assertions.assertEquals(ErrorCode.WL_ERROR.name(), this.logoutResponseError.error.code);
    }

    @When("^Enter a game with invalid token -infingame- v2$")
    public void enterTheGameWithInvalidToken() throws Throwable {
        this.enterResponseError = this.infingameBot.infingameEnter(
                this.sessionId,
                "",
                "game");
    }

    @When("^Emulate uam (.+) error response by round bet request -infingame- v2$")
    public void emulateUamErrorByEnterRequestCurrency(String errorResponse) throws Throwable {
        this.infingameBot = createBot();
        this.sessionId = PlatformUtil.randomUUID().toString();
        this.currency = InMemoryUamOmniGamePlayEndpoint.GC_CURRENCY;
        var flatten = genAccountRoutingCurrencyFlatten();
        var token = genTempTokenFlatten(flatten).writeExternal();

        EnterResponse enterResponse = this.infingameBot.infingameEnter(
                this.sessionId,
                token,
                errorResponse);

        this.roundBetResponseError = this.infingameBot.infingameRoundBet(
                this.sessionId,
                Long.toString(ApiFactory.RANDOM.nextLong()),
                PlatformUtil.randomUUID().toString(),
                enterResponse.user.id,
                BigDecimal.ZERO);
    }

    @Then("^Enter response has error code: (.+) -infingame- v2$")
    public void enterResponseHasErrorCode(String errorCode) {
        Assertions.assertEquals(ErrorCode.valueOf(errorCode.toUpperCase()).name(), this.enterResponseError.error.code);
    }

    @Then("^Bet round response has error code: (.+) -infingame- v2$")
    public void betRoundResponseHasErrorCodeNotEnoughMoney(String errorCode) {
        Assertions.assertEquals(ErrorCode.valueOf(errorCode.toUpperCase()).name(), this.roundBetResponseError.error.code);
    }

    private InfingameBot createBot() throws URISyntaxException {
        String path = String.format("/v2/%s/infingame", OperatorSpec.BLUEDREAM.code());
        URI uri = new URIBuilder().setScheme("http").setHost("localhost").setPort(props.CLOUD_APP_PORT.get()).setPath(path).build();
        return new DefaultInfingameBot(httpClient, uri);
    }

    private static void assertCreditWalletTransaction(BigDecimal win, List<WalletTransaction> txs, String reference) {
        var creditTx = txs.stream().filter(tx -> tx.getReference().equals(reference)).findFirst();
        Assertions.assertTrue(creditTx.isPresent());
        Assertions.assertTrue(creditTx.get().isCredit());
        Assertions.assertEquals(0, win.compareTo(new BigDecimal(creditTx.get().getAmount())));
    }

    private static void assertDebitWalletTransaction(BigDecimal bet, List<WalletTransaction> txs, String reference) {
        var debitTx = txs.stream().filter(tx -> tx.getReference().equals(reference)).findFirst();
        Assertions.assertTrue(debitTx.isPresent());
        Assertions.assertTrue(debitTx.get().isDebit());
        Assertions.assertEquals(0, bet.compareTo(new BigDecimal(debitTx.get().getAmount())));
    }

    private static void assertRefundWalletTransaction(BigDecimal refund, List<WalletTransaction> txs) {
        var refundTx = txs.stream().filter(WalletTransaction::isRefund).findFirst();
        Assertions.assertTrue(refundTx.isPresent());
        Assertions.assertEquals(0, refund.compareTo(new BigDecimal(refundTx.get().getAmount())));
    }

    private AccountRoutingCurrencyFlatten genAccountRoutingCurrencyFlatten() {
        var hash = PlatformUtil.randomAlphanumeric(8);
        var id = String.valueOf(System.currentTimeMillis());
        return AccountRoutingCurrencyFlatten.underscore(hash, id, this.currency);
    }

    private static String toProviderCurrency(String currency) {
        if (InMemoryUamOmniGamePlayEndpoint.SC_CURRENCY.equals(currency)) {
            return SWEEPSTAKE_CURRENCY;
        } else if (InMemoryUamOmniGamePlayEndpoint.GC_CURRENCY.equals(currency)) {
            return GOLD_CURRENCY;
        }

        return currency;
    }

    private TempTokenFlatten genTempTokenFlatten(AccountRoutingCurrencyFlatten flatten) {
        return TempTokenFlatten.underscore(flatten.getRoutingKey(), flatten.getAccountId(), flatten.getCurrency(), 36);
    }
}
