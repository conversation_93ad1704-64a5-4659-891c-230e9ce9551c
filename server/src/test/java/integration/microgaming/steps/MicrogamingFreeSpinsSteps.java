package integration.microgaming.steps;

import static integration.utils.TestUtils.awaitCreationFSBeforeCheck;

import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.junit.jupiter.api.Assertions;
import org.mockserver.mock.Expectation;
import org.mockserver.model.ExpectationId;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.mockserver.model.MediaType;
import org.mockserver.model.Parameter;
import org.mockserver.model.ParameterBody;
import org.mockserver.model.StringBody;
import org.mockserver.verify.VerificationTimes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;
import org.testcontainers.shaded.org.apache.commons.lang3.RandomStringUtils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.HttpProto;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.bots.freespins.DefaultFreeSpinsBot;
import aggregator.bots.freespins.FreeSpinsBot;
import aggregator.model.AccountFreeSpin;
import aggregator.model.FreeSpinCampaign;
import aggregator.repo.BrandRepo;
import aggregator.repo.DefaultBrandRepo;
import api.v1.ApiFactory;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.FreeSpinsService;
import common.model.Currency;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CancelFreeSpinsRequest;
import common.model.freespins.CancelFreeSpinsResponse;
import common.model.freespins.CancelPlayerFreeSpinRequest;
import common.model.freespins.CancelPlayerFreeSpinResponse;
import common.model.freespins.CreateFreeSpinsCampaignDTO;
import common.model.freespins.CreateFreeSpinsRequest;
import common.model.freespins.CreateFreeSpinsResponse;
import common.model.freespins.DisableFreeSpinsCampaignRequest;
import common.model.freespins.FreeSpinCampaignStatus;
import common.model.freespins.FreeSpinsInfo;
import common.utils.BaseMapper;
import gaming.microgaming.api.freespins.model.AssignedFreeSpinInstance;
import gaming.microgaming.api.freespins.model.OfferGame;
import gaming.microgaming.api.freespins.model.request.AssignPlayerRequest;
import gaming.microgaming.api.freespins.model.request.AssignPlayersRequest;
import gaming.microgaming.api.freespins.model.request.CancelFreeSpinRequest;
import gaming.microgaming.api.freespins.model.request.CreateOfferRequest;
import gaming.microgaming.api.freespins.model.response.AssignPlayerResponse;
import gaming.microgaming.api.freespins.model.response.AssignPlayersResponse;
import gaming.microgaming.api.freespins.model.response.CreateOfferResponse;
import gaming.microgaming.api.model.request.STSTokenRequest;
import gaming.microgaming.api.model.response.STSTokenResponse;
import gaming.microgaming.b2b.model.enums.ErrorCode;
import gaming.microgaming.b2b.model.response.ErrorResponse;
import gaming.microgaming.utility.Mappers;
import integration.MockServer;
import integration.utils.TestUtils;
import io.cucumber.java.After;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.netty.handler.codec.http.HttpMethod;
import io.netty.handler.codec.http.HttpScheme;
import okhttp3.OkHttpClient;

public class MicrogamingFreeSpinsSteps {
    @Autowired
    private AggregatorServerProperties props;
    @Autowired
    private DynamicCloud cloud;
    @Autowired
    private SpannerTemplate spannerTemplate;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private OkHttpClient httpClient;
    private BrandRepo repository;
    private final CommonObjectMapper mapper = new CommonObjectMapper();

    private final List<String> mockExpectations = new ArrayList<>();
    private final ProviderSpec provider = ProviderSpec.MICROGAMING;
    private final OperatorSpec operator = OperatorSpec.BLUEDREAM;
    private final List<Integer> successStatuses = List.of(HttpStatus.SC_OK, HttpStatus.SC_CREATED);

    private FreeSpinsBot freeSpinsBot;
    private String contextCurrency;
    private String contextGameCode;
    private String contextBonusCode;
    private BigDecimal contextBetAmount;
    private Integer contextSpins;
    private LocalDateTime contextStartDate;
    private LocalDateTime contextExpirationDate;
    private String contextRequestId;
    private Integer contextBetLevel;
    private List<String> contextPlayerIds;
    private Map<String, String> contextInstanceIds;
    private String contextCampaignId;
    private STSTokenResponse contextSTSTokenResponse;

    private CreateFreeSpinsResponse contextCreateFreeSpinsResponse;
    private CreateFreeSpinsResponse contextCreatePlayerFreeSpinResponse;
    private CancelPlayerFreeSpinResponse contextCancelPlayerFreeSpinResponse;
    private CancelFreeSpinsResponse contextCancelFreeSpinsResponse;

    private final String FREE_SPINS_BONUS_CODE_EXCEPTION_PREFIX = "Invalid bonus code!";

    private static final String GET_STS_TOKEN_PATH = "/connect/token";

    private static final String CREATE_OFFER_PATH = "/api/v1/agents/%s/offers";
    private static final String ASSIGN_PLAYER_TO_OFFER_PATH = "/api/v1/agents/%s/offers/%s/players/%s/freeGameInstances";
    private static final String ASSIGN_PLAYERS_TO_OFFER_PATH = "/api/v1/agents/%s/offers/%s/freeGameBatchInstances";
    private static final String UPDATE_OFFER_FREE_SPINS_STATUS_PATH = "/api/v1/agents/%s/players/%s/freeGameInstances/%s";

    private static final String AGENT_CODE = RandomStringUtils.randomAlphanumeric(19);
    private static final String AGENT_SECRET = RandomStringUtils.randomAlphanumeric(33);
    private static final String STS_GRANT_TYPE = "client_credentials";

    @After("@microgaming-fs")
    public void after() {
        mockExpectations.forEach(mockExpectation -> MockServer.mockServer.clear(ExpectationId.expectationId(mockExpectation)));
    }

    @Given("^Initialize properties -microgaming fs-$")
    public void initializeProperties() throws Exception {
        var igpServiceInfo = buildPlainServiceInfoForBot();
        cloud.addUps(buildPlainServiceInfo(Currency.GC));
        cloud.addUps(buildPlainServiceInfo(Currency.SC));
        cloud.addUps(buildAuthPlainServiceInfo(Currency.GC));
        cloud.addUps(buildAuthPlainServiceInfo(Currency.SC));
        cloud.addUps(igpServiceInfo);

        contextSTSTokenResponse = buildSTSTokenResponse();

        repository = new DefaultBrandRepo(spannerTemplate, cacheManager);

        freeSpinsBot = new DefaultFreeSpinsBot(httpClient, operator, provider, igpServiceInfo);
    }

    @Given("^Initialize currency (.+) and game code (.+) -microgaming fs-$")
    public void initializeCurrencyAndGameCode(String currency, String gameCode) {
        contextCurrency = currency;
        contextGameCode = gameCode;
    }

    @Given("^Initialize data with playerIdCount (.+) and bonusCode (.+) -microgaming fs-$")
    public void initializeFreeSpinsCreate(int playerIdCount, String bonusCode) {
        contextBonusCode = bonusCode;
        contextRequestId = RandomStringUtils.randomAlphanumeric(11);
        contextBetAmount = new BigDecimal("5.00");
        contextBetLevel = ApiFactory.RANDOM.nextInt(1, 6);
        contextSpins = ApiFactory.RANDOM.nextInt(1, 10);
        contextCampaignId = RandomStringUtils.randomAlphanumeric(11);
        contextStartDate = LocalDateTime.now(ZoneOffset.UTC).plusDays(5).withNano(0);
        contextExpirationDate = LocalDateTime.now(ZoneOffset.UTC).plusDays(9).withNano(0);
        contextPlayerIds = buildPlayers(playerIdCount);
        contextInstanceIds = new LinkedHashMap<>();
    }

    @Given("^Prepare mockServer for authenticating with status (\\d+) -microgaming fs-")
    public void prepareMockServerForAuthenticating(int status) throws Exception {
        createMockServer(HttpMethod.POST,
                GET_STS_TOKEN_PATH,
                buildSTSTokenRequest(),
                successStatuses.contains(status) ? buildSTSTokenResponse() : buildErrorResponse(),
                status,
                true);
    }

    @Given("^Prepare mockServer for creating offer with status (\\d+) -microgaming fs-")
    public void prepareMockServerForCreatingOffer(int status) throws Exception {
        createMockServer(HttpMethod.POST,
                CREATE_OFFER_PATH.formatted(AGENT_CODE),
                buildCreateOfferRequest(),
                successStatuses.contains(status) ? buildCreateOfferResponse() : buildErrorResponse(),
                status,
                false);
    }

    @Given("^Prepare mockServer for creating player free spins with status (\\d+) -microgaming fs-")
    public void prepareMockServerForCreatingPlayerFreeSpins(int status) throws Exception {
        for (String contextPlayerId : contextPlayerIds) {
            createMockServer(HttpMethod.POST,
                    ASSIGN_PLAYER_TO_OFFER_PATH.formatted(AGENT_CODE, contextCampaignId, contextPlayerId),
                    buildAssignPlayerRequest(),
                    successStatuses.contains(status) ? buildAssignPlayerResponse() : buildErrorResponse(),
                    status, false);
        }
    }

    @Given("^Prepare mockServer for creating free spins with status (\\d+) -microgaming fs-")
    public void prepareMockServerForCreatingFreeSpins(int status) throws Exception {
        createMockServer(HttpMethod.POST,
                ASSIGN_PLAYERS_TO_OFFER_PATH.formatted(AGENT_CODE, contextCampaignId),
                buildAssignPlayersRequest(),
                successStatuses.contains(status) ? buildAssignPlayersResponse() : buildErrorResponse(),
                status,
                false);
    }

    @Given("^Prepare mockServer for cancelling free spins with status (\\d+) -microgaming fs-")
    public void prepareMockServerForCancellingFreeSpins(int status) throws Exception {
        for (String contextPlayerId : contextPlayerIds) {
            createMockServer(HttpMethod.PATCH,
                    UPDATE_OFFER_FREE_SPINS_STATUS_PATH.formatted(AGENT_CODE, contextPlayerId, contextInstanceIds.get(contextPlayerId)),
                    buildCancelFreeSpinRequest(contextPlayerId, contextInstanceIds.get(contextPlayerId)),
                    successStatuses.contains(status) ? new Object() : buildErrorResponse(),
                    status,
                    false);
        }
    }

    @When("^Send create free spins -microgaming fs-$")
    public void sendCreateFreeSpins() throws Exception {
        contextCreateFreeSpinsResponse = freeSpinsBot.createFreeSpins(buildCreateFreeSpinsRequest(), contextRequestId);
    }

    @When("^Send create free spins by bonus -microgaming fs-$")
    public void sendCreateFreeSpinsByBonus() throws Exception {
        contextCreateFreeSpinsResponse = freeSpinsBot.createFreeSpins(buildCreateFreeSpinsByBonusRequest(), contextRequestId);
    }

    @When("^Send create free spins by V2 async flow -microgaming fs-$")
    public void sendCreateV2FreeSpins() throws Exception {
        var request = buildCreateFreeSpinsRequest();

        freeSpinsBot.createV2FreeSpins(request, contextRequestId);
    }

    @When("^Send create free spins with invalid bonus code -microgaming fs-$")
    public void sendCreateFreeSpinsWithInvalidBonusCode() throws Exception {
        var request = buildCreateFreeSpinsRequest();
        request.setBonusCode(RandomStringUtils.randomAlphanumeric(16));

        contextCreateFreeSpinsResponse = freeSpinsBot.createFreeSpins(request, contextRequestId);
    }

    @When("^Send create free spin -microgaming fs-$")
    public void sendCreateFreeSpin() throws Exception {
        contextCreatePlayerFreeSpinResponse = freeSpinsBot.createPlayerFreeSpin(buildCreatePlayerFreeSpinRequest(), contextRequestId);
    }

    @When("^Send create free spin by bonus -microgaming fs-$")
    public void sendCreateFreeSpinByBonus() throws Exception {
        contextCreatePlayerFreeSpinResponse = freeSpinsBot.createPlayerFreeSpin(buildCreatePlayerFreeSpinsByBonusRequest(), contextRequestId);
    }

    @When("^Send create free spin with invalid bonus code -microgaming fs-$")
    public void sendCreateFreeSpinWithInvalidBonusCode() throws Exception {
        var request = buildCreatePlayerFreeSpinRequest();
        request.setBonusCode(RandomStringUtils.randomAlphanumeric(16));

        contextCreatePlayerFreeSpinResponse = freeSpinsBot.createPlayerFreeSpin(request, contextRequestId);
    }

    @When("^Send cancel free spin -microgaming fs-$")
    public void sendCancelFreeSpin() throws Exception {
        contextCancelPlayerFreeSpinResponse = freeSpinsBot.cancelFreeSpins(buildCancelPlayerFreeSpinRequest());
    }

    @When("^Send cancel free spins -microgaming fs-$")
    public void sendCancelFreeSpins() throws Exception {
        contextCancelFreeSpinsResponse = freeSpinsBot.cancelFreeSpinsBatching(buildCancelFreeSpinsRequest());
    }

    @When("^Send cancel free spins with invalid list -microgaming fs-$")
    public void sendCancelFreeSpinsWithInvalidList() throws Exception {
        contextCancelFreeSpinsResponse = freeSpinsBot.cancelFreeSpinsBatching(buildCancelFreeSpinsRequestWithInvalidList());
    }

    @Then("^Has created (.*) free spins -microgaming fs-$")
    public void hasCreateFreeSpinsResponse(int freeSpinsCount) {
        Assertions.assertNotNull(contextCreateFreeSpinsResponse);

        Assertions.assertEquals(contextBonusCode, contextCreateFreeSpinsResponse.getBonusCode());
        Assertions.assertNull(contextCreateFreeSpinsResponse.getMessage());
        Assertions.assertEquals(freeSpinsCount, contextCreateFreeSpinsResponse.getFreeSpinsList()
                .stream()
                .filter(FreeSpinsInfo::isApplied)
                .count());
    }

    @Then("^Has created free spin -microgaming fs-$")
    public void hasCreateFreeSpinResponse() {
        Assertions.assertNotNull(contextCreatePlayerFreeSpinResponse);

        Assertions.assertEquals(contextBonusCode, contextCreatePlayerFreeSpinResponse.getBonusCode());
        Assertions.assertNull(contextCreatePlayerFreeSpinResponse.getMessage());
        Assertions.assertNotNull(contextCreatePlayerFreeSpinResponse.getFreeSpin());
    }

    @Then("^Has created (.*) free spins by V2 async flow -microgaming fs-$")
    public void freeSpinExistedAfterAsyncProcessing(int freeSpinsCount) throws InterruptedException {
        awaitCreationFSBeforeCheck(() -> repository.freeSpinCampaignExists(contextBonusCode, operator.code(), provider.code()) &&
                !repository.accountFreeSpinsByBonusCode(operator.code(), provider.code(), contextBonusCode).get().isEmpty());

        validateFreeSpinCampaign();
        validateAccountFreeSpins(freeSpinsCount);
    }

    @Then("^Has free spins bonus code exception -microgaming fs-$")
    public void hasCreateFreeSpinsResponseWithFreeSpinsException() {
        Assertions.assertNotNull(contextCreateFreeSpinsResponse);

        Assertions.assertEquals(String.valueOf(HttpStatus.SC_BAD_REQUEST), contextCreateFreeSpinsResponse.getError());
        Assertions.assertTrue(contextCreateFreeSpinsResponse.getMsg()
                .startsWith(FREE_SPINS_BONUS_CODE_EXCEPTION_PREFIX));
        Assertions.assertEquals(0, contextCreateFreeSpinsResponse.getFreeSpinsList().size());
    }

    @Then("^Has create free spin bonus code exception -microgaming fs-$")
    public void hasCreateFreeSpinResponseWithFreeSpinsException() {
        Assertions.assertNotNull(contextCreatePlayerFreeSpinResponse);

        Assertions.assertEquals(String.valueOf(HttpStatus.SC_BAD_REQUEST), contextCreatePlayerFreeSpinResponse.getError());
        Assertions.assertTrue(contextCreatePlayerFreeSpinResponse.getMsg()
                .startsWith(FREE_SPINS_BONUS_CODE_EXCEPTION_PREFIX));
        Assertions.assertNull(contextCreatePlayerFreeSpinResponse.getFreeSpin());
    }

    @Then("^Has internal exception on create free spins -microgaming fs-$")
    public void hasCreateFreeSpinsResponseWithProviderException() {
        Assertions.assertNotNull(contextCreateFreeSpinsResponse);

        Assertions.assertEquals(contextBonusCode, contextCreateFreeSpinsResponse.getBonusCode());
        Assertions.assertTrue(contextCreateFreeSpinsResponse.getFreeSpinsList()
                .stream()
                .noneMatch(FreeSpinsInfo::isApplied));
    }

    @Then("^Has create free spins exception response with status (\\d+) -microgaming fs-$")
    public void hasCreateFreeSpinsResponseWithException(int status) {
        Assertions.assertNotNull(contextCreateFreeSpinsResponse);
        Assertions.assertEquals(status, contextCreateFreeSpinsResponse.getCode());
    }

    @Then("^Has cancel free spins exception response with status (\\d+) -microgaming fs-$")
    public void hasCancelFreeSpinsResponseWithException(int status) {
        Assertions.assertNotNull(contextCancelFreeSpinsResponse);
        Assertions.assertEquals(status, contextCancelFreeSpinsResponse.getCode());
    }

    @Then("^Has cancel player free spins exception response with status (\\d+) -microgaming fs-$")
    public void hasCancelPlayerFreeSpinsResponseWithException(int status) {
        Assertions.assertNotNull(contextCancelPlayerFreeSpinResponse);
        Assertions.assertEquals(status, contextCancelPlayerFreeSpinResponse.getCode());
    }

    @Then("^Has created (\\d+) free spins without duplicating provider requests -microgaming fs-")
    public void hasCreateFreeSpinsResponseWithoutRequestsDuplication(int freeSpinsCount) {
        hasCreateFreeSpinsResponse(freeSpinsCount);

        mockExpectations.forEach(mockExpectation -> MockServer.mockServer.verify(mockExpectation, VerificationTimes.exactly(1)));
    }

    @Then("^Has created player free spin without duplicating provider requests -microgaming fs-")
    public void hasCreatePlayerFreeSpinsResponseWithoutRequestsDuplication() {
        hasCreateFreeSpinResponse();

        mockExpectations.forEach(mockExpectation -> MockServer.mockServer.verify(mockExpectation, VerificationTimes.exactly(1)));
    }

    @Then("^Has cancelled free spin -microgaming fs-$")
    public void hasCancelFreeSpinResponse() {
        Assertions.assertNotNull(contextCancelPlayerFreeSpinResponse);

        Assertions.assertEquals(contextBonusCode, contextCancelPlayerFreeSpinResponse.getBonusCode());
        Assertions.assertTrue(contextCancelPlayerFreeSpinResponse.getFreeSpin().isApplied());
    }

    @Then("^Has cancelled free spins -microgaming fs-$")
    public void hasCancelFreeSpinsResponse() {
        Assertions.assertNotNull(contextCancelFreeSpinsResponse);

        Assertions.assertEquals(contextBonusCode, contextCancelFreeSpinsResponse.getBonusCode());
        Assertions.assertEquals(contextPlayerIds.size(),
                contextCancelFreeSpinsResponse.getFreeSpinsList()
                        .stream()
                        .filter(FreeSpinsInfo::isApplied)
                        .count());
    }

    @Then("^Has cancel free spin bonus code exception -microgaming fs-$")
    public void hasCancelFreeSpinResponseWithFreeSpinsException() {
        Assertions.assertNotNull(contextCancelPlayerFreeSpinResponse);

        Assertions.assertEquals(HttpStatus.SC_NOT_FOUND, contextCancelPlayerFreeSpinResponse.getCode());
        Assertions.assertEquals(FreeSpinsService.BONUS_CODE_NOT_FOUND_MSG, contextCancelPlayerFreeSpinResponse.getMessage());
        Assertions.assertNull(contextCancelPlayerFreeSpinResponse.getFreeSpin());
    }

    @Then("^Has cancel free spins bonus code exception -microgaming fs-$")
    public void hasCancelFreeSpinsResponseWithFreeSpinsException() {
        Assertions.assertNotNull(contextCancelFreeSpinsResponse);

        Assertions.assertEquals(HttpStatus.SC_NOT_FOUND, contextCancelFreeSpinsResponse.getCode());
        Assertions.assertEquals(FreeSpinsService.BONUS_CODE_NOT_FOUND_MSG, contextCancelFreeSpinsResponse.getMessage());
        Assertions.assertEquals(0, contextCancelFreeSpinsResponse.getFreeSpinsList().size());
    }

    @Then("^Has not applied cancel free spin -microgaming fs-$")
    public void hasNotAppliedCancelFreeSpinResponse() {
        Assertions.assertNotNull(contextCancelPlayerFreeSpinResponse);

        Assertions.assertNotNull(contextCancelPlayerFreeSpinResponse.getFreeSpin());

        var freeSpin = contextCancelPlayerFreeSpinResponse.getFreeSpin();

        Assertions.assertFalse(freeSpin.isApplied());
        Assertions.assertEquals(contextPlayerIds.getFirst(), freeSpin.getPlayerId());
    }

    @Then("^Has not applied cancel free spins -microgaming fs-$")
    public void hasNotAppliedCancelFreeSpinsResponse() {
        Assertions.assertNotNull(contextCancelFreeSpinsResponse);

        Assertions.assertNotNull(contextCancelFreeSpinsResponse.getFreeSpinsList());

        var freeSpins = contextCancelFreeSpinsResponse.getFreeSpinsList();

        Assertions.assertEquals(contextPlayerIds.size(), freeSpins.stream()
                .filter(freeSpin -> !freeSpin.isApplied())
                .count());
    }

    @Then("^Has invalid free spins list on cancel free spins exception -microgaming fs-$")
    public void hasCancelFreeSpinsResponseWithInvalidFreeSpinsListException() {
        Assertions.assertNotNull(contextCancelFreeSpinsResponse);

        Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, contextCancelFreeSpinsResponse.getCode());
        Assertions.assertEquals("Free spins batching cancel list is invalid",
                contextCancelFreeSpinsResponse.getMessage());
    }

    @When("^Send create free spin spin campaign request -microgaming fs-$")
    public void sendCreateFreeSpinSpinCampaignRequest() throws Exception {
        var createFreeSpinCampaignRequest = CreateFreeSpinsCampaignDTO.builder()
                .campaign(contextBonusCode)
                .currency(contextCurrency)
                .betAmount(contextBetAmount)
                .spins(contextSpins)
                .startDate(contextStartDate)
                .expirationDate(contextExpirationDate)
                .requestId(contextRequestId)
                .betLevel(contextBetLevel)
                .operator(operator.code())
                .provider(provider.code())
                .games(List.of(contextGameCode))
                .build();
        freeSpinsBot.createFreeSpinsCampaign(createFreeSpinCampaignRequest, contextRequestId);
    }

    @Then("Has created free spin campaign -microgaming fs-")
    public void hasCreatedFreeSpinCampaign() {
        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);
    }

    @When("^Send disable free spin spin campaign request -microgaming fs-$")
    public void sendDisableFreeSpinSpinCampaignRequest() throws Exception {
        var request = new DisableFreeSpinsCampaignRequest(contextBonusCode);

        var response = freeSpinsBot.disableFreeSpinsCampaign(request, contextRequestId);
        Assertions.assertEquals(contextBonusCode, response.getBonusCode());
    }

    @Then("Has disabled free spin campaign -microgaming fs-")
    public void hasDisabledFreeSpinCampaign() {
        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CANCELED);
    }

    @When("^Check create free spins is disabled -microgaming fs-$")
    public void sendCreateFreeSpinsToDisabledCampaign() throws Exception {
        var request = new CreateFreeSpinsRequest();
        request.setBonusCode(contextBonusCode);
        request.setCurrency(contextCurrency);
        request.setBetAmount(contextBetAmount);
        request.setSpins(contextSpins);
        request.setStartDate(contextStartDate);
        request.setExpirationDate(contextExpirationDate);
        request.setGames(List.of(contextGameCode));
        request.setPlayerIdList(List.of(contextPlayerIds.getFirst()));
        CreateFreeSpinsResponse createFreeSpinsResponse = freeSpinsBot.createFreeSpins(request, contextRequestId);

        Assertions.assertNotNull(createFreeSpinsResponse);
        Assertions.assertEquals(FreeSpinsService.FREE_SPINS_CAMPAIGN_CANCELLED.formatted(contextBonusCode, provider.code()), createFreeSpinsResponse.getMsg());
    }

    @When("^Check create free spins is disabled async -microgaming fs-$")
    public void sendCreateFreeSpinsToDisabledCampaignAsync() throws Exception {
        var request = new CreateFreeSpinsRequest();
        request.setBonusCode(contextBonusCode);
        request.setCurrency(contextCurrency);
        request.setBetAmount(contextBetAmount);
        request.setSpins(contextSpins);
        request.setStartDate(contextStartDate);
        request.setExpirationDate(contextExpirationDate);
        request.setGames(List.of(contextGameCode));
        request.setPlayerIdList(List.of(contextPlayerIds.getFirst()));
        CreateFreeSpinsResponse createFreeSpinsResponse = freeSpinsBot.createV2FreeSpins(request, contextRequestId);

        Assertions.assertNotNull(createFreeSpinsResponse);
        Assertions.assertEquals(FreeSpinsService.FREE_SPINS_CAMPAIGN_CANCELLED.formatted(contextBonusCode, provider.code()), createFreeSpinsResponse.getMsg());
    }

    private void checkFreeSpinBonusInDb(FreeSpinCampaignStatus status) {
        var freeSpinCampaign = repository.freeSpinCampaign(contextBonusCode, operator.code(), provider.code());
        Assertions.assertTrue(freeSpinCampaign.isPresent());
        Assertions.assertEquals(contextCurrency, freeSpinCampaign.get().getCurrency());
        Assertions.assertEquals(status.code(), freeSpinCampaign.get().getStatus());
    }

    private PlainServiceInfo buildPlainServiceInfo(Currency currency) throws URISyntaxException, MalformedURLException {
        return new PlainServiceInfo(
                operator.irgsUps(provider.code(), currency.name().toLowerCase()),
                new URIBuilder()
                        .setScheme(HttpScheme.HTTP.name().toString())
                        .setHost("localhost")
                        .setPort(MockServer.mockServer.getPort())
                        .setUserInfo(AGENT_CODE, AGENT_SECRET)
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "alpha_numeric")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                        .build()
                        .toURL()
                        .toExternalForm());
    }

    private PlainServiceInfo buildAuthPlainServiceInfo(Currency currency) throws URISyntaxException, MalformedURLException {
        return new PlainServiceInfo(
                String.format("%s-auth", operator.irgsUps(provider.code(), currency.name().toLowerCase())),
                new URIBuilder()
                        .setScheme(HttpScheme.HTTP.name().toString())
                        .setHost("localhost")
                        .setPort(MockServer.mockServer.getPort())
                        .setUserInfo(AGENT_CODE, AGENT_SECRET)
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "alpha_numeric")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                        .build()
                        .toURL()
                        .toExternalForm());
    }

    private PlainServiceInfo buildPlainServiceInfoForBot() throws Exception {
        return new PlainServiceInfo(
                String.format("%s-%s", AggregatorWildcardUPSs.IGP_PREFIX, operator.code()),
                new URIBuilder()
                        .setScheme(HttpScheme.HTTP.name().toString())
                        .setHost("localhost")
                        .setPort(props.CLOUD_APP_PORT.get())
                        .setUserInfo(operator.code(), "changeit")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "alpha_numeric")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                        .build().toString());
    }

    private List<String> buildPlayers(int quantity) {
        return IntStream.range(1, quantity + 1).mapToObj(ignored -> {
            String randomAccountId = RandomStringUtils.randomAlphanumeric(16);

            return "%s_%s_%s".formatted(RandomStringUtils.randomAlphanumeric(6), randomAccountId, contextCurrency);
        })
                .collect(Collectors.toCollection(LinkedList::new));
    }

    @SuppressWarnings("unchecked")
    private void createMockServer(HttpMethod method,
            String path,
            Object request,
            Object response,
            int status,
            boolean isFormData) throws Exception {
        String requestSerialized = mapper.writeValueAsString(request);
        String responseSerialized = mapper.writeValueAsString(response);

        var httpRequest = HttpRequest.request()
                .withMethod(method.name())
                .withPath(path)
                .withHeader(HttpHeaders.ACCEPT, ContentType.APPLICATION_JSON.getMimeType())
                .withHeader(HttpHeaders.AUTHORIZATION, contextSTSTokenResponse.getAuthenticationHeader())
                .withBody(isFormData ? ParameterBody.parameterBody(((Map<String, String>) mapper.convertValue(request, new TypeReference<>() {}))
                        .entrySet()
                        .stream()
                        .map(entry -> Parameter.param(entry.getKey(), entry.getValue()))
                        .toList())
                        : StringBody.exact(requestSerialized));

        if (List.of(GET_STS_TOKEN_PATH, CREATE_OFFER_PATH, UPDATE_OFFER_FREE_SPINS_STATUS_PATH, ASSIGN_PLAYER_TO_OFFER_PATH, ASSIGN_PLAYERS_TO_OFFER_PATH)
                .contains(path)) {
            httpRequest.removeHeader(HttpHeaders.AUTHORIZATION);
        }

        var httpResponse = HttpResponse.response()
                .withStatusCode(status)
                .withContentType(MediaType.APPLICATION_JSON)
                .withDelay(TimeUnit.MICROSECONDS, TestUtils.HTTP_RESPONSE_DELAY);

        if (StringUtils.isNotEmpty(responseSerialized)) {
            httpResponse.withBody(responseSerialized);
        }

        Expectation[] expectation = MockServer.mockServer.when(httpRequest, TestUtils.expectation()).respond(httpResponse);
        mockExpectations.addAll(Arrays.stream(expectation).map(Expectation::getId).toList());
    }

    private CreateFreeSpinsRequest buildCreateFreeSpinsRequest() {
        return CreateFreeSpinsRequest.builder()
                .bonusCode(contextBonusCode)
                .currency(contextCurrency)
                .betAmount(contextBetAmount)
                .spins(contextSpins)
                .startDate(contextStartDate)
                .expirationDate(contextExpirationDate)
                .games(List.of(contextGameCode))
                .requestId(contextRequestId)
                .playerIdList(new ArrayList<>(contextPlayerIds))
                .operator(operator.code())
                .provider(provider.code())
                .betLevel(contextBetLevel)
                .build();
    }

    private CreateFreeSpinsRequest buildCreateFreeSpinsByBonusRequest() {
        return CreateFreeSpinsRequest.builder()
                .bonusCode(contextBonusCode)
                .requestId(contextRequestId)
                .playerIdList(new ArrayList<>(contextPlayerIds))
                .operator(operator.code())
                .provider(provider.code())
                .build();
    }

    private CreateFreeSpinsRequest buildCreatePlayerFreeSpinRequest() {
        var request = new CreateFreeSpinsRequest();

        request.setPlayerId(contextPlayerIds.getFirst());
        request.setOperator(operator.code());
        request.setProvider(provider.code());
        request.setBonusCode(contextBonusCode);
        request.setCurrency(contextCurrency);
        request.setBetAmount(contextBetAmount);
        request.setSpins(contextSpins);
        request.setStartDate(contextStartDate);
        request.setExpirationDate(contextExpirationDate);
        request.setGames(List.of(contextGameCode));
        request.setRequestId(contextRequestId);
        request.setBetLevel(contextBetLevel);

        return request;
    }

    private CreateFreeSpinsRequest buildCreatePlayerFreeSpinsByBonusRequest() {
        var request = new CreateFreeSpinsRequest();

        request.setPlayerId(contextPlayerIds.getFirst());
        request.setOperator(operator.code());
        request.setProvider(provider.code());
        request.setBonusCode(contextBonusCode);
        request.setRequestId(contextRequestId);

        return request;
    }

    private CancelPlayerFreeSpinRequest buildCancelPlayerFreeSpinRequest() {
        return CancelPlayerFreeSpinRequest.builder()
                .requestId(contextRequestId)
                .campaign(contextBonusCode)
                .currency(contextCurrency)
                .freeSpin(FreeSpinsInfo.builder()
                        .playerId(contextPlayerIds.getFirst())
                        .freeSpinsId(contextInstanceIds.get(contextPlayerIds.getFirst()))
                        .isApplied(false)
                        .build())
                .build();
    }

    private CancelFreeSpinsRequest buildCancelFreeSpinsRequest() {
        return CancelFreeSpinsRequest.builder()
                .requestId(contextRequestId)
                .campaign(contextBonusCode)
                .currency(contextCurrency)
                .freeSpinsList(contextPlayerIds.stream()
                        .map(playerId -> FreeSpinsInfo.builder()
                                .playerId(playerId)
                                .freeSpinsId(contextInstanceIds.get(playerId))
                                .isApplied(false)
                                .build())
                        .toList())
                .build();
    }

    private CancelFreeSpinsRequest buildCancelFreeSpinsRequestWithInvalidList() {
        return CancelFreeSpinsRequest.builder()
                .requestId(contextRequestId)
                .campaign(contextBonusCode)
                .currency(contextCurrency)
                .freeSpinsList(new ArrayList<>())
                .build();
    }

    private STSTokenRequest buildSTSTokenRequest() {
        return STSTokenRequest.builder()
                .clientId(AGENT_CODE)
                .clientSecret(AGENT_SECRET)
                .grantType(STS_GRANT_TYPE)
                .build();
    }

    private STSTokenResponse buildSTSTokenResponse() {
        return STSTokenResponse.builder()
                .accessToken(
                        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c")
                .tokenType("Bearer")
                .expiresIn(36000)
                .expiresAt(LocalDateTime.now().plusSeconds(36000))
                .scope("OIDC")
                .build();
    }

    private CreateOfferRequest buildCreateOfferRequest() {
        return CreateOfferRequest.builder()
                .offerName(contextBonusCode)
                .offerGames(List.of(new OfferGame(BaseMapper.removePrefixFromGameId(contextGameCode, provider))))
                .durationAvailableInDaysAfterAwarded(Duration.between(contextStartDate, contextExpirationDate).toDays())
                .numberOfRounds(contextSpins)
                .offerAvailableFromDateUTC(Mappers.toDateTimeFreeSpinsFormat(contextStartDate))
                .offerAvailableToDateUTC(Mappers.toDateTimeFreeSpinsFormat(contextExpirationDate))
                .operator(operator.code())
                .currency(contextCurrency)
                .build();
    }

    private CreateOfferResponse buildCreateOfferResponse() {
        return CreateOfferResponse.builder()
                .offerId(contextCampaignId)
                .reuse(false)
                .build();
    }

    private AssignPlayerRequest buildAssignPlayerRequest() {
        return AssignPlayerRequest.builder()
                .offerAvailableToPlayerFromDateUTC(Mappers.toDateTimeFreeSpinsFormat(contextStartDate))
                .offerId(contextCampaignId)
                .playerId(contextPlayerIds.getFirst())
                .operator(operator.code())
                .currency(contextCurrency)
                .build();
    }

    private AssignPlayerResponse buildAssignPlayerResponse() {
        return AssignPlayerResponse.builder()
                .instanceId(PlatformUtil.randomUUIDv7().toString())
                .build();
    }

    private AssignPlayersRequest buildAssignPlayersRequest() {
        return AssignPlayersRequest.builder()
                .offerAvailableToPlayerFromDateUTC(Mappers.toDateTimeFreeSpinsFormat(contextStartDate))
                .offerId(contextCampaignId)
                .players(contextPlayerIds)
                .operator(operator.code())
                .currency(contextCurrency)
                .build();
    }

    private AssignPlayersResponse buildAssignPlayersResponse() {
        return AssignPlayersResponse.builder()
                .instances(contextPlayerIds.stream().map(playerId -> {
                    String instance = PlatformUtil.randomUUIDv7().toString();
                    contextInstanceIds.put(playerId, instance);

                    return new AssignedFreeSpinInstance(instance, playerId);
                }).toList())
                .build();
    }

    private CancelFreeSpinRequest buildCancelFreeSpinRequest(String playerId, String instanceId) {
        return CancelFreeSpinRequest.builder()
                .playerId(playerId)
                .instanceId(instanceId)
                .operator(operator.code())
                .currency(contextCurrency)
                .build();
    }

    private ErrorResponse buildErrorResponse() {
        return ErrorResponse.builder()
                .code(ErrorCode.INTERNAL_SERVER_ERROR.name())
                .message(ErrorCode.INTERNAL_SERVER_ERROR.getMessage())
                .status(ErrorCode.INTERNAL_SERVER_ERROR.getHttpStatus())
                .build();
    }

    private void validateFreeSpinCampaign() {
        Optional<FreeSpinCampaign> freeSpinCampaignOptional = repository.freeSpinCampaign(contextBonusCode, operator.code(), provider.code());

        Assertions.assertTrue(freeSpinCampaignOptional.isPresent());

        FreeSpinCampaign freeSpinCampaign = freeSpinCampaignOptional.get();

        Assertions.assertEquals(contextCurrency, freeSpinCampaign.getCurrency());
        Assertions.assertEquals(FreeSpinCampaignStatus.CREATED.code(), freeSpinCampaign.getStatus());
    }

    private void validateAccountFreeSpins(int freeSpinsCount) {
        Optional<List<AccountFreeSpin>> freeSpins = repository.accountFreeSpinsByBonusCode(operator.code(), provider.code(), contextBonusCode);

        Assertions.assertTrue(freeSpins.isPresent());
        Assertions.assertEquals(freeSpinsCount, freeSpins.get().size());
    }
}
