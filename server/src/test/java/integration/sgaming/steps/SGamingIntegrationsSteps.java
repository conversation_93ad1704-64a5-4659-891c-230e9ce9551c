package integration.sgaming.steps;

import static aggregator.wallet.patrianna.types.WalletTransactionInfo.TYPE_CREDIT;
import static aggregator.wallet.patrianna.types.WalletTransactionInfo.TYPE_DEBIT;
import static integration.utils.TestUtils.awaitCreationFSBeforeCheck;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.jboss.resteasy.util.BasicAuthHelper;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.HttpProto;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.bots.DefaultSGamingBot;
import aggregator.bots.SGamingBot;
import aggregator.listeners.MockDataGenerator;
import aggregator.model.Account;
import aggregator.model.WalletTransaction;
import aggregator.repo.BrandRepo;
import aggregator.repo.DefaultBrandRepo;
import aggregator.repo.DefaultWalletSessionRepo;
import aggregator.repo.WalletSessionRepo;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.flatten.TempTokenFlatten;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import gaming.sgaming.b2b.model.AuthRequest;
import gaming.sgaming.b2b.model.AuthResponse;
import gaming.sgaming.b2b.model.BalanceRequest;
import gaming.sgaming.b2b.model.BalanceResponse;
import gaming.sgaming.b2b.model.ErrorResponse;
import gaming.sgaming.b2b.model.TransactionRequest;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import okhttp3.OkHttpClient;

public class SGamingIntegrationsSteps {
    private static final String DEFAULT_COUNTRY = "US";
    private static final String PROVIDER_DEBIT = "WITHDRAW";
    private static final String PROVIDER_CREDIT = "DEPOSIT";
    private final OperatorSpec operator = OperatorSpec.BLUEDREAM;
    private final ProviderSpec provider = ProviderSpec.SGAMING;
    @Autowired
    private AggregatorServerProperties props;
    @Autowired
    private DynamicCloud cloud;
    @Autowired
    private SpannerTemplate spannerTemplate;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private OkHttpClient httpClient;
    @Autowired
    private CommonObjectMapper mapper;
    private BrandRepo brandRepo;
    private WalletSessionRepo walletSessionRepo;
    private SGamingBot bot;
    private String currency;
    private String gameCode;
    private String roundId;
    private String txIdBet;
    private String txIdWin;
    private String tempToken;
    private String permToken;
    private AccountRoutingCurrencyFlatten flatten;
    private String playerId;
    private String response;

    @Given("^Initialize properties -sgaming-$")
    public void initializeProperties() throws Exception {
        PlainServiceInfo si = buildPlainServiceInfo();
        cloud.addUps(si);
        cloud.addUps(buildPlainServiceInfoForBot());
        this.brandRepo = new DefaultBrandRepo(spannerTemplate, cacheManager);
        this.walletSessionRepo = new DefaultWalletSessionRepo(spannerTemplate);
        this.bot = createBot(si);
    }

    @Given("^New game (.+) with currency (.+) -sgaming-$")
    public void newGameWithCurrency(String gameCode, String curr) {
        this.currency = curr;
        this.flatten = genAccountRoutingCurrencyFlatten();
        this.playerId = flatten.writeExternal();
        this.tempToken = genTempTokenFlatten(flatten).writeExternal();
        this.gameCode = gameCode;
        Assertions.assertNotNull(currency);
        Assertions.assertEquals(flatten.getCurrency(), currency);
    }

    @When("Authorize player -sgaming-$")
    public void authorizePlayer() throws Exception {
        response = bot.auth(new AuthRequest(tempToken + "_" + gameCode));
    }

    @Then("^Has auth with balance (.+) -sgaming-$")
    public void hasAuthResponse(int balance) throws JsonProcessingException {
        var authRespActual = mapper.readValue(response, AuthResponse.class);
        permToken = authRespActual.getToken();

        Assertions.assertTrue(StringUtils.isNotEmpty(authRespActual.getToken()));
        Assertions.assertEquals(playerId, authRespActual.getPlayerId());
        Assertions.assertEquals(toProviderCurrency(currency), authRespActual.getCurrency());
        Assertions.assertEquals(DEFAULT_COUNTRY, authRespActual.getCountry());
        Assertions.assertEquals(balance, authRespActual.getBalance());
        Assertions.assertEquals(operator.code(), authRespActual.getOperator());
    }

    @Then("^Wait before second auth -sgaming-$")
    public void waitBeforeSecondAuth() {
        var accountId = flatten.getAccountId();
        awaitCreationFSBeforeCheck(() -> {
            Optional<Account> account = brandRepo.getAccount(operator.code(), accountId);
            return account.isPresent() &&
                    brandRepo.gameSession(account.get().getHash(), tempToken, provider.code()).isPresent() &&
                    brandRepo.gameSession(account.get().getHash(), permToken, provider.code()).isPresent();
        });
    }

    @When("Ask the balance -sgaming-$")
    public void getBalance() throws Exception {
        response = bot.getBalance(new BalanceRequest(permToken));
    }

    @Then("^Player has balance (.+) -sgaming-$")
    public void hasBalance(long balance) throws JsonProcessingException {
        var balanceRespExpected = new BalanceResponse(balance);
        var balanceRespActual = mapper.readValue(response, BalanceResponse.class);

        Assertions.assertEquals(balanceRespExpected, balanceRespActual);
    }

    @When("^Send bet (.+) request with txId (.+) -sgaming-$")
    public void sendBetRequest(long betAmount, String txId) throws Exception {
        this.txIdBet = txId;
        this.roundId = roundId == null ? PlatformUtil.randomUUID() + "round" : roundId;
        var request = new TransactionRequest(permToken, roundId, txIdBet, PROVIDER_DEBIT, betAmount);

        response = bot.submit(request);
    }

    @When("^Send win (.+) request with txId (.+) -sgaming-$")
    public void sendWinRequest(long winAmount, String txId) throws Exception {
        this.txIdWin = txId;
        this.roundId = roundId == null ? PlatformUtil.randomUUID() + "round" : roundId;
        var request = new TransactionRequest(permToken, roundId, txIdWin, PROVIDER_CREDIT, winAmount);

        response = bot.submit(request);
    }

    @Then("^Has only bet (\\d+(?:\\.\\d+)?) and win (\\d+(?:\\.\\d+)?) txs -sgaming-$")
    public void hasBetWinTxs(BigDecimal betAmount, BigDecimal winAmount) {
        var txs = readTxs(roundId);
        Assertions.assertEquals(2, txs.size());

        var txBet = getTx(txs, txIdBet, TYPE_DEBIT);
        Assertions.assertEquals(0, betAmount.compareTo(new BigDecimal(txBet.getAmount())));

        var txWin = getTx(txs, txIdWin, TYPE_CREDIT);
        Assertions.assertEquals(0, winAmount.compareTo(new BigDecimal(txWin.getAmount())));
    }

    @Then("^Response has status (.+) -sgaming-$")
    public void hasResponseWithError(int code) throws JsonProcessingException {
        var errorRespActual = mapper.readValue(response, ErrorResponse.class);

        Assertions.assertEquals(code, errorRespActual.getCode());
        Assertions.assertNotNull(errorRespActual.getMessage());
    }

    @Then("^Has no transactions -sgaming-$")
    public void hasNoTransaction() {
        var account = brandRepo.requiredAccount(MockDataGenerator.OPERATOR, String.valueOf(flatten.getAccountId()));
        var wsOpt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), provider.code(), roundId, currency);
        Assertions.assertTrue(wsOpt.isEmpty());
    }

    private List<WalletTransaction> readTxs(String roundId) {
        var account = brandRepo.requiredAccount(MockDataGenerator.OPERATOR, String.valueOf(flatten.getAccountId()));
        var wsOpt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), provider.code(),
                roundId, currency);
        Assertions.assertTrue(wsOpt.isPresent());
        return wsOpt.get().getTransactions();
    }

    private WalletTransaction getTx(List<WalletTransaction> txs, String txId, String type) {
        return txs.stream()
                .filter(transaction -> transaction.getReference().equals(txId))
                .filter(transaction -> transaction.getType().equals(type))
                .findAny().orElseThrow();
    }

    private AccountRoutingCurrencyFlatten genAccountRoutingCurrencyFlatten() {
        var hash = RandomStringUtils.randomAlphanumeric(8);
        var id = String.valueOf(System.currentTimeMillis());
        return AccountRoutingCurrencyFlatten.underscore(hash, id, this.currency);
    }

    private TempTokenFlatten genTempTokenFlatten(AccountRoutingCurrencyFlatten flatten) {
        return TempTokenFlatten.underscore(flatten.getRoutingKey(), flatten.getAccountId(), flatten.getCurrency(), 36);
    }

    private String toProviderCurrency(String currency) {
        return switch (currency) {
            case "SC" -> "VSC";
            case "GC" -> "VGC";
            default -> currency;
        };
    }

    private PlainServiceInfo buildPlainServiceInfo() throws Exception {
        return new PlainServiceInfo(
                operator.irgsUps(provider.code()),
                new URIBuilder()
                        .setScheme("http")
                        .setHost("localhost")
                        .setUserInfo("someOperatorId", "key")
                        .build().toString());
    }

    private PlainServiceInfo buildPlainServiceInfoForBot() throws Exception {
        return new PlainServiceInfo(
                String.format("%s-%s", AggregatorWildcardUPSs.IGP_PREFIX, operator.code()),
                new URIBuilder()
                        .setScheme("http")
                        .setHost("localhost")
                        .setPort(props.CLOUD_APP_PORT.get())
                        .setUserInfo("username", "password")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "alpha_numeric")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                        .build().toURL().toExternalForm());
    }

    private SGamingBot createBot(PlainServiceInfo si) {
        var uriBuilder = new URIBuilder()
                .setScheme("http")
                .setHost("localhost")
                .setPort(props.CLOUD_APP_PORT.get());
        var authHeader = BasicAuthHelper.createHeader(si.getUserName(), si.getPassword());
        return new DefaultSGamingBot(httpClient, uriBuilder, operator, mapper, authHeader);
    }
}
