package integration.netgaming.steps;

import static integration.utils.TestUtils.awaitCreationFSBeforeCheck;

import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.utils.URIBuilder;
import org.junit.jupiter.api.Assertions;
import org.mockserver.mock.Expectation;
import org.mockserver.model.ExpectationId;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.mockserver.model.MediaType;
import org.mockserver.verify.VerificationTimes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;
import org.testcontainers.shaded.org.apache.commons.lang3.RandomStringUtils;

import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.HttpProto;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.bots.freespins.DefaultFreeSpinsBot;
import aggregator.bots.freespins.FreeSpinsBot;
import aggregator.model.AccountFreeSpin;
import aggregator.model.FreeSpinCampaign;
import aggregator.repo.BrandRepo;
import aggregator.repo.DefaultBrandRepo;
import api.v1.ApiFactory;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.FreeSpinsService;
import common.model.Currency;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CancelFreeSpinsRequest;
import common.model.freespins.CancelFreeSpinsResponse;
import common.model.freespins.CancelPlayerFreeSpinRequest;
import common.model.freespins.CancelPlayerFreeSpinResponse;
import common.model.freespins.CreateFreeSpinsCampaignDTO;
import common.model.freespins.CreateFreeSpinsRequest;
import common.model.freespins.CreateFreeSpinsResponse;
import common.model.freespins.DisableFreeSpinsCampaignRequest;
import common.model.freespins.FreeSpinCampaignStatus;
import common.model.freespins.FreeSpinsInfo;
import common.utils.BaseMapper;
import gaming.netgaming.api.freespins.model.CoinLevelResponseResult;
import gaming.netgaming.api.freespins.model.FreeSpinsResponseResult;
import gaming.netgaming.api.freespins.model.request.CancelFreeSpinRequest;
import gaming.netgaming.api.freespins.model.request.GetCoinLevelsRequest;
import gaming.netgaming.api.freespins.model.request.GrantBonusRequest;
import gaming.netgaming.api.freespins.model.response.CancelFreeSpinResponse;
import gaming.netgaming.api.freespins.model.response.GetCoinLevelsResponse;
import gaming.netgaming.api.freespins.model.response.GrantBonusResponse;
import gaming.netgaming.b2b.model.response.ErrorCode;
import gaming.netgaming.b2b.model.response.ErrorResponse;
import gaming.netgaming.utility.Mappers;
import integration.MockServer;
import integration.utils.TestUtils;
import io.cucumber.java.After;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.netty.handler.codec.http.HttpMethod;
import io.netty.handler.codec.http.HttpScheme;
import okhttp3.OkHttpClient;

public class NetgamingFreeSpinsSteps {
    @Autowired
    private AggregatorServerProperties props;
    @Autowired
    private DynamicCloud cloud;
    @Autowired
    private SpannerTemplate spannerTemplate;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private OkHttpClient httpClient;
    private BrandRepo repository;
    private final CommonObjectMapper mapper = new CommonObjectMapper();

    private final List<String> mockExpectations = new ArrayList<>();
    private final ProviderSpec provider = ProviderSpec.NETGAMING;
    private final OperatorSpec operator = OperatorSpec.BLUEDREAM;

    private FreeSpinsBot freeSpinsBot;
    private String contextCurrency;
    private String contextGameCode;
    private String contextBonusCode;
    private BigDecimal contextBetAmount;
    private Integer contextSpins;
    private LocalDateTime contextStartDate;
    private LocalDateTime contextExpirationDate;
    private String contextRequestId;
    private Integer contextBetLevel;
    private String contextFreeSpinsId;
    private List<String> contextPlayerIds;
    private CreateFreeSpinsResponse contextCreateFreeSpinsResponse;
    private CreateFreeSpinsResponse contextCreatePlayerFreeSpinResponse;
    private CancelPlayerFreeSpinResponse contextCancelPlayerFreeSpinResponse;
    private CancelFreeSpinsResponse contextCancelFreeSpinsResponse;

    private final String FREE_SPINS_BONUS_CODE_EXCEPTION_PREFIX = "Invalid bonus code!";
    private final String FREE_SPINS_BET_LEVEL_EXCEPTION_PREFIX = "Bet Level";

    private static final String GRANT_BONUS = "/v1/wallet/grant-bonus/";
    private static final String CANCEL_FREE_SPINS = "/v1/more/cancel-promotion/";
    private static final String GET_COIN_LEVELS = "/v1/more/pfs-bet-values/";

    private static final String SUCCESS_STATUS = "success";
    private static final String API_KEY = "API_KEY";

    @After("@netgaming-fs")
    public void after() {
        mockExpectations.forEach(mockExpectation -> MockServer.mockServer.clear(ExpectationId.expectationId(mockExpectation)));
    }

    @Given("^Initialize properties -netgaming fs-$")
    public void initializeProperties() throws Exception {
        var igpServiceInfo = buildPlainServiceInfoForBot();
        cloud.addUps(buildPlainServiceInfo());
        cloud.addUps(igpServiceInfo);

        repository = new DefaultBrandRepo(spannerTemplate, cacheManager);

        freeSpinsBot = new DefaultFreeSpinsBot(httpClient, operator, provider, igpServiceInfo);
    }

    @Given("^Initialize currency (.+) and game code (.+) -netgaming fs-$")
    public void initializeCurrencyAndGameCode(String currency, String gameCode) {
        contextCurrency = currency;
        contextGameCode = gameCode;
    }

    @Given("^Initialize data with playerIdCount (.+) and bonusCode (.+) -netgaming fs-$")
    public void initializeFreeSpinsCreate(int playerIdCount, String bonusCode) {
        contextBonusCode = bonusCode;
        contextRequestId = RandomStringUtils.randomAlphanumeric(11);
        contextBetAmount = new BigDecimal("5.00");
        contextBetLevel = ApiFactory.RANDOM.nextInt(1, 6);
        contextSpins = ApiFactory.RANDOM.nextInt(1, 10);
        contextStartDate = LocalDateTime.now(ZoneOffset.UTC).plusDays(5).withNano(0);
        contextExpirationDate = LocalDateTime.now(ZoneOffset.UTC).plusDays(9).withNano(0);
        contextFreeSpinsId = RandomStringUtils.randomAlphanumeric(16);
        contextPlayerIds = buildPlayers(playerIdCount);
    }

    @Given("^Prepare mockServer for coin levels with status (\\d+) -netgaming fs-$")
    public void generateMockServerForCoinLevels(int status) throws Exception {
        createMockServer(HttpMethod.POST,
                GET_COIN_LEVELS,
                buildGetCoinLevelsRequest(),
                buildGetCoinLevelsResponse(),
                status);
    }

    @Given("^Prepare mockServer for create with status (\\d+) -netgaming fs-$")
    public void generateMockServerForCreate(int status) throws Exception {
        createMockServer(HttpMethod.POST,
                GRANT_BONUS,
                buildGrantBonusRequest(),
                status == HttpStatus.SC_INTERNAL_SERVER_ERROR ? buildErrorResponse() : buildGrantBonusResponse(),
                status);
    }

    @Given("^Prepare mockServer for coin level validation exception with status (\\d+) -netgaming fs-$")
    public void generateMockServerForValidationException(int status) throws Exception {
        createMockServer(HttpMethod.POST,
                GET_COIN_LEVELS,
                buildGetCoinLevelsRequest(),
                buildGetCoinLevelsResponseValidationException(),
                status);
    }

    @Given("^Prepare mockServer for cancel with status (\\d+) -netgaming fs-$")
    public void generateMockServerForCancel(int status) throws Exception {
        createMockServer(HttpMethod.POST,
                CANCEL_FREE_SPINS,
                buildCancelFreeSpinRequest(),
                status == HttpStatus.SC_INTERNAL_SERVER_ERROR ? buildErrorResponse() : buildCancelFreeSpinResponse(),
                status);
    }

    @When("^Send create free spins -netgaming fs-$")
    public void sendCreateFreeSpins() throws Exception {
        contextCreateFreeSpinsResponse = freeSpinsBot.createFreeSpins(buildCreateFreeSpinsRequest(), contextRequestId);
    }

    @When("^Send create free spins by bonus -netgaming fs-$")
    public void sendCreateFreeSpinsByBonus() throws Exception {
        contextCreateFreeSpinsResponse = freeSpinsBot.createFreeSpins(buildCreateFreeSpinsByBonusRequest(), contextRequestId);
    }

    @When("^Send create free spins by V2 async flow -netgaming fs-$")
    public void sendCreateV2FreeSpins() throws Exception {
        var request = buildCreateFreeSpinsRequest();

        freeSpinsBot.createV2FreeSpins(request, contextRequestId);
    }

    @When("^Send create free spins with invalid bonus code -netgaming fs-$")
    public void sendCreateFreeSpinsWithInvalidBonusCode() throws Exception {
        var request = buildCreateFreeSpinsRequest();
        request.setBonusCode(RandomStringUtils.randomAlphanumeric(16));

        contextCreateFreeSpinsResponse = freeSpinsBot.createFreeSpins(request, contextRequestId);
    }

    @When("^Send create free spin -netgaming fs-$")
    public void sendCreateFreeSpin() throws Exception {
        contextCreatePlayerFreeSpinResponse = freeSpinsBot.createPlayerFreeSpin(buildCreatePlayerFreeSpinRequest(), contextRequestId);
    }

    @When("^Send create free spin by bonus -netgaming fs-$")
    public void sendCreateFreeSpinByBonus() throws Exception {
        contextCreatePlayerFreeSpinResponse = freeSpinsBot.createPlayerFreeSpin(buildCreatePlayerFreeSpinByBonusRequest(), contextRequestId);
    }

    @When("^Send create free spin with invalid bonus code -netgaming fs-$")
    public void sendCreateFreeSpinWithInvalidBonusCode() throws Exception {
        var request = buildCreatePlayerFreeSpinRequest();
        request.setBonusCode(RandomStringUtils.randomAlphanumeric(16));

        contextCreatePlayerFreeSpinResponse = freeSpinsBot.createPlayerFreeSpin(request, contextRequestId);
    }

    @When("^Send create free spin without bet level -netgaming fs-$")
    public void sendCreateFreeSpinWithoutBetLevel() throws Exception {
        var request = buildCreatePlayerFreeSpinRequest();
        request.setBetLevel(null);

        contextCreatePlayerFreeSpinResponse = freeSpinsBot.createPlayerFreeSpin(request, contextRequestId);
    }

    @When("^Send create free spin with invalid bet level -netgaming fs-$")
    public void sendCreateFreeSpinWithInvalidBetLevel() throws Exception {
        var request = buildCreatePlayerFreeSpinRequest();
        request.setBetLevel(0);

        contextCreatePlayerFreeSpinResponse = freeSpinsBot.createPlayerFreeSpin(request, contextRequestId);
    }

    @When("^Send cancel free spin -netgaming fs-$")
    public void sendCancelFreeSpin() throws Exception {
        contextCancelPlayerFreeSpinResponse = freeSpinsBot.cancelFreeSpins(buildCancelPlayerFreeSpinRequest());
    }

    @When("^Send cancel free spins -netgaming fs-$")
    public void sendCancelFreeSpins() throws Exception {
        contextCancelFreeSpinsResponse = freeSpinsBot.cancelFreeSpinsBatching(buildCancelFreeSpinsRequest());
    }

    @When("^Send cancel free spins with invalid list -netgaming fs-$")
    public void sendCancelFreeSpinsWithInvalidList() throws Exception {
        contextCancelFreeSpinsResponse = freeSpinsBot.cancelFreeSpinsBatching(buildCancelFreeSpinsRequestWithInvalidList());
    }

    @Then("^Has created (\\d+) free spins -netgaming fs-$")
    public void hasCreateFreeSpinsResponse(int freeSpinsCount) {
        Assertions.assertNotNull(contextCreateFreeSpinsResponse);

        Assertions.assertEquals(contextBonusCode, contextCreateFreeSpinsResponse.getBonusCode());
        Assertions.assertNull(contextCreateFreeSpinsResponse.getMessage());
        Assertions.assertEquals(freeSpinsCount, contextCreateFreeSpinsResponse.getFreeSpinsList()
                .stream()
                .filter(FreeSpinsInfo::isApplied)
                .count());
    }

    @Then("^Has created free spin -netgaming fs-$")
    public void hasCreateFreeSpinResponse() {
        Assertions.assertNotNull(contextCreatePlayerFreeSpinResponse);

        Assertions.assertEquals(contextBonusCode, contextCreatePlayerFreeSpinResponse.getBonusCode());
        Assertions.assertNull(contextCreatePlayerFreeSpinResponse.getMessage());
        Assertions.assertNotNull(contextCreatePlayerFreeSpinResponse.getFreeSpin());
    }

    @Then("^Has created (\\d+) free spins by V2 async flow -netgaming fs-$")
    public void freeSpinExistedAfterAsyncProcessing(int freeSpinsCount) throws InterruptedException {
        awaitCreationFSBeforeCheck(() -> repository.freeSpinCampaignExists(contextBonusCode, operator.code(), provider.code()) &&
                !repository.accountFreeSpinsByBonusCode(operator.code(), provider.code(), contextBonusCode).get().isEmpty());

        validateFreeSpinCampaign();
        validateAccountFreeSpins(freeSpinsCount);
    }

    @Then("^Has validation exception on create free spins -netgaming fs-$")
    public void hasCreateFreeSpinsResponseWithValidationException() {
        Assertions.assertNotNull(contextCreateFreeSpinsResponse);

        Assertions.assertEquals(String.valueOf(HttpStatus.SC_BAD_REQUEST), contextCreateFreeSpinsResponse.getError());
        Assertions.assertTrue(contextCreateFreeSpinsResponse.getMsg()
                .startsWith(FREE_SPINS_BET_LEVEL_EXCEPTION_PREFIX));
    }

    @Then("^Has free spins bonus code exception -netgaming fs-$")
    public void hasCreateFreeSpinsResponseWithFreeSpinsException() {
        Assertions.assertNotNull(contextCreateFreeSpinsResponse);

        Assertions.assertEquals(String.valueOf(HttpStatus.SC_BAD_REQUEST), contextCreateFreeSpinsResponse.getError());
        Assertions.assertTrue(contextCreateFreeSpinsResponse.getMsg()
                .startsWith(FREE_SPINS_BONUS_CODE_EXCEPTION_PREFIX));
        Assertions.assertEquals(0, contextCreateFreeSpinsResponse.getFreeSpinsList().size());
    }

    @Then("^Has create free spin bonus code exception -netgaming fs-$")
    public void hasCreateFreeSpinResponseWithFreeSpinsException() {
        Assertions.assertNotNull(contextCreatePlayerFreeSpinResponse);

        Assertions.assertEquals(String.valueOf(HttpStatus.SC_BAD_REQUEST), contextCreatePlayerFreeSpinResponse.getError());
        Assertions.assertTrue(contextCreatePlayerFreeSpinResponse.getMsg()
                .startsWith(FREE_SPINS_BONUS_CODE_EXCEPTION_PREFIX));
        Assertions.assertNull(contextCreatePlayerFreeSpinResponse.getFreeSpin());
    }

    @Then("^Has create free spin bet level exception -netgaming fs-$")
    public void hasCreateFreeSpinResponseWithBetLevelException() {
        Assertions.assertNotNull(contextCreatePlayerFreeSpinResponse);

        Assertions.assertEquals(String.valueOf(HttpStatus.SC_BAD_REQUEST), contextCreatePlayerFreeSpinResponse.getError());
        Assertions.assertTrue(contextCreatePlayerFreeSpinResponse.getMsg()
                .startsWith(FREE_SPINS_BET_LEVEL_EXCEPTION_PREFIX));
        Assertions.assertNull(contextCreatePlayerFreeSpinResponse.getFreeSpin());
    }

    @Then("^Has provider exception on create free spins -netgaming fs-$")
    public void hasCreateFreeSpinsResponseWithInternalException() {
        Assertions.assertNotNull(contextCreateFreeSpinsResponse);

        Assertions.assertEquals(contextBonusCode, contextCreateFreeSpinsResponse.getBonusCode());
        Assertions.assertEquals(contextPlayerIds.size(), contextCreateFreeSpinsResponse.getFreeSpinsList()
                .stream()
                .filter(freeSpinsInfo -> !freeSpinsInfo.isApplied())
                .count());
    }

    @Then("^Has exception on create free spins with status (\\d+) -netgaming fs-$")
    public void hasCreateFreeSpinsResponseWithInternalException(int status) {
        Assertions.assertNotNull(contextCreateFreeSpinsResponse);
        Assertions.assertEquals(status, contextCreateFreeSpinsResponse.getCode());
    }

    @Then("^Has exception on cancel free spins with status (\\d+) -netgaming fs-$")
    public void hasCancelFreeSpinsResponseWithInternalException(int status) {
        Assertions.assertNotNull(contextCancelFreeSpinsResponse);
        Assertions.assertEquals(status, contextCancelFreeSpinsResponse.getCode());
    }

    @Then("^Has exception on cancel player free spins with status (\\d+) -netgaming fs-$")
    public void hasCancelPlayerFreeSpinsResponseWithInternalException(int status) {
        Assertions.assertNotNull(contextCancelPlayerFreeSpinResponse);
        Assertions.assertEquals(status, contextCancelPlayerFreeSpinResponse.getCode());
    }

    @Then("^Has created (\\d+) free spins without duplicating provider requests -netgaming fs-")
    public void hasCreateFreeSpinsResponseWithoutRequestsDuplication(int freeSpinsCount) {
        hasCreateFreeSpinsResponse(freeSpinsCount);

        mockExpectations.forEach(mockExpectation -> MockServer.mockServer.verify(mockExpectation, VerificationTimes.exactly(1)));
    }

    @Then("^Has created 1 free spin without duplicating provider requests -netgaming fs-")
    public void hasCreateFreeSpinResponseWithoutRequestsDuplication() {
        hasCreateFreeSpinResponse();

        mockExpectations.forEach(mockExpectation -> MockServer.mockServer.verify(mockExpectation, VerificationTimes.exactly(1)));
    }

    @Then("^Has cancelled free spin -netgaming fs-$")
    public void hasCancelFreeSpinResponse() {
        Assertions.assertNotNull(contextCancelPlayerFreeSpinResponse);

        Assertions.assertEquals(contextBonusCode, contextCancelPlayerFreeSpinResponse.getBonusCode());
        Assertions.assertTrue(contextCancelPlayerFreeSpinResponse.getFreeSpin().isApplied());
    }

    @Then("^Has cancelled free spins -netgaming fs-$")
    public void hasCancelFreeSpinsResponse() {
        Assertions.assertNotNull(contextCancelFreeSpinsResponse);

        Assertions.assertEquals(contextBonusCode, contextCancelFreeSpinsResponse.getBonusCode());
        Assertions.assertEquals(contextPlayerIds.size(),
                contextCancelFreeSpinsResponse.getFreeSpinsList()
                        .stream()
                        .filter(freeSpin -> freeSpin.isApplied()
                                && freeSpin.getFreeSpinsId().equals(contextFreeSpinsId))
                        .count());
    }

    @Then("^Has cancel free spin bonus code exception -netgaming fs-$")
    public void hasCancelFreeSpinResponseWithFreeSpinsException() {
        Assertions.assertNotNull(contextCancelPlayerFreeSpinResponse);

        Assertions.assertEquals(HttpStatus.SC_NOT_FOUND, contextCancelPlayerFreeSpinResponse.getCode());
        Assertions.assertEquals(FreeSpinsService.BONUS_CODE_NOT_FOUND_MSG, contextCancelPlayerFreeSpinResponse.getMessage());
        Assertions.assertNull(contextCancelPlayerFreeSpinResponse.getFreeSpin());
    }

    @Then("^Has cancel free spins bonus code exception -netgaming fs-$")
    public void hasCancelFreeSpinsResponseWithFreeSpinsException() {
        Assertions.assertNotNull(contextCancelFreeSpinsResponse);

        Assertions.assertEquals(HttpStatus.SC_NOT_FOUND, contextCancelFreeSpinsResponse.getCode());
        Assertions.assertEquals(FreeSpinsService.BONUS_CODE_NOT_FOUND_MSG, contextCancelFreeSpinsResponse.getMessage());
        Assertions.assertEquals(0, contextCancelFreeSpinsResponse.getFreeSpinsList().size());
    }

    @Then("^Has not applied cancel free spin -netgaming fs-$")
    public void hasNotAppliedCancelFreeSpinResponse() {
        Assertions.assertNotNull(contextCancelPlayerFreeSpinResponse);

        Assertions.assertNotNull(contextCancelPlayerFreeSpinResponse.getFreeSpin());

        var freeSpin = contextCancelPlayerFreeSpinResponse.getFreeSpin();

        Assertions.assertFalse(freeSpin.isApplied());
        Assertions.assertEquals(contextPlayerIds.getFirst(), freeSpin.getPlayerId());
    }

    @Then("^Has not applied cancel free spins -netgaming fs-$")
    public void hasNotAppliedCancelFreeSpinsResponse() {
        Assertions.assertNotNull(contextCancelFreeSpinsResponse);

        Assertions.assertNotNull(contextCancelFreeSpinsResponse.getFreeSpinsList());

        var freeSpins = contextCancelFreeSpinsResponse.getFreeSpinsList();

        Assertions.assertEquals(contextPlayerIds.size(), freeSpins.stream()
                .filter(freeSpin -> !freeSpin.isApplied()
                        && freeSpin.getFreeSpinsId().equals(contextFreeSpinsId))
                .count());
    }

    @Then("^Has invalid free spins list on cancel free spins exception -netgaming fs-$")
    public void hasCancelFreeSpinsResponseWithInvalidFreeSpinsListException() {
        Assertions.assertNotNull(contextCancelFreeSpinsResponse);

        Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, contextCancelFreeSpinsResponse.getCode());
        Assertions.assertEquals("Free spins batching cancel list is invalid",
                contextCancelFreeSpinsResponse.getMessage());
    }

    @When("^Send create free spin spin campaign request -netgaming fs-$")
    public void sendCreateFreeSpinSpinCampaignRequest() throws Exception {
        var createFreeSpinCampaignRequest = CreateFreeSpinsCampaignDTO.builder()
                .campaign(contextBonusCode)
                .currency(contextCurrency)
                .betAmount(contextBetAmount)
                .spins(contextSpins)
                .startDate(contextStartDate)
                .expirationDate(contextExpirationDate)
                .requestId(contextRequestId)
                .betLevel(contextBetLevel)
                .operator(operator.code())
                .provider(provider.code())
                .games(List.of(contextGameCode))
                .build();
        freeSpinsBot.createFreeSpinsCampaign(createFreeSpinCampaignRequest, contextRequestId);
    }

    @Then("Has created free spin campaign -netgaming fs-")
    public void hasCreatedFreeSpinCampaign() {
        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);
    }

    @When("^Send disable free spin spin campaign request -netgaming fs-$")
    public void sendDisableFreeSpinSpinCampaignRequest() throws Exception {
        var request = new DisableFreeSpinsCampaignRequest(contextBonusCode);

        var response = freeSpinsBot.disableFreeSpinsCampaign(request, contextRequestId);
        Assertions.assertEquals(contextBonusCode, response.getBonusCode());
    }

    @Then("Has disabled free spin campaign -netgaming fs-")
    public void hasDisabledFreeSpinCampaign() {
        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CANCELED);
    }

    @When("^Check create free spins is disabled -netgaming fs-$")
    public void sendCreateFreeSpinsToDisabledCampaign() throws Exception {
        var request = new CreateFreeSpinsRequest();
        request.setBonusCode(contextBonusCode);
        request.setCurrency(contextCurrency);
        request.setBetAmount(contextBetAmount);
        request.setSpins(contextSpins);
        request.setStartDate(contextStartDate);
        request.setExpirationDate(contextExpirationDate);
        request.setGames(List.of(contextGameCode));
        request.setPlayerIdList(List.of(contextPlayerIds.getFirst()));
        CreateFreeSpinsResponse createFreeSpinsResponse = freeSpinsBot.createFreeSpins(request, contextRequestId);

        Assertions.assertNotNull(createFreeSpinsResponse);
        Assertions.assertEquals(FreeSpinsService.FREE_SPINS_CAMPAIGN_CANCELLED.formatted(contextBonusCode, provider.code()), createFreeSpinsResponse.getMsg());
    }

    @When("^Check create free spins is disabled async -netgaming fs-$")
    public void sendCreateFreeSpinsToDisabledCampaignAsync() throws Exception {
        var request = new CreateFreeSpinsRequest();
        request.setBonusCode(contextBonusCode);
        request.setCurrency(contextCurrency);
        request.setBetAmount(contextBetAmount);
        request.setSpins(contextSpins);
        request.setStartDate(contextStartDate);
        request.setExpirationDate(contextExpirationDate);
        request.setGames(List.of(contextGameCode));
        request.setPlayerIdList(List.of(contextPlayerIds.getFirst()));
        CreateFreeSpinsResponse createFreeSpinsResponse = freeSpinsBot.createV2FreeSpins(request, contextRequestId);

        Assertions.assertNotNull(createFreeSpinsResponse);
        Assertions.assertEquals(FreeSpinsService.FREE_SPINS_CAMPAIGN_CANCELLED.formatted(contextBonusCode, provider.code()), createFreeSpinsResponse.getMsg());
    }

    private void checkFreeSpinBonusInDb(FreeSpinCampaignStatus status) {
        Assertions.assertTrue(repository.freeSpinCampaignExists(contextBonusCode, operator.code(), provider.code()));
        var freeSpinCampaign = repository.freeSpinCampaign(contextBonusCode, operator.code(), provider.code());
        Assertions.assertEquals(contextCurrency, freeSpinCampaign.get().getCurrency());
        Assertions.assertEquals(status.code(), freeSpinCampaign.get().getStatus());
    }

    private PlainServiceInfo buildPlainServiceInfo() throws URISyntaxException, MalformedURLException {
        return new PlainServiceInfo(
                String.format("%s-%s-%s",
                        AggregatorWildcardUPSs.IRGS_PREFIX, operator.code(), provider.code()),
                new URIBuilder()
                        .setScheme(HttpScheme.HTTP.name().toString())
                        .setHost("localhost")
                        .setPort(MockServer.mockServer.getPort())
                        .setUserInfo("ANY", API_KEY)
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "alpha_numeric")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                        .build()
                        .toURL()
                        .toExternalForm());
    }

    private PlainServiceInfo buildPlainServiceInfoForBot() throws Exception {
        return new PlainServiceInfo(
                String.format("%s-%s", AggregatorWildcardUPSs.IGP_PREFIX, operator.code()),
                new URIBuilder()
                        .setScheme(HttpScheme.HTTP.name().toString())
                        .setHost("localhost")
                        .setPort(props.CLOUD_APP_PORT.get())
                        .setUserInfo(operator.code(), "changeit")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "alpha_numeric")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                        .build().toString());
    }

    private List<String> buildPlayers(int quantity) {
        return IntStream.range(1, quantity + 1).mapToObj(ignored -> {
            String randomAccountId = RandomStringUtils.randomAlphanumeric(16);

            return "%s_%s_%s".formatted(RandomStringUtils.randomAlphanumeric(6), randomAccountId, contextCurrency);
        })
                .toList();
    }

    private void createMockServer(HttpMethod method,
            String path,
            Object request,
            Object response,
            int status) throws Exception {
        String requestSerialized = mapper.writeValueAsString(request);
        String responseSerialized = mapper.writeValueAsString(response);

        var httpRequest = HttpRequest.request()
                .withMethod(method.name())
                .withPath(path)
                .withBody(requestSerialized);

        var httpResponse = HttpResponse.response()
                .withStatusCode(status)
                .withContentType(MediaType.APPLICATION_JSON)
                .withDelay(TimeUnit.MICROSECONDS, TestUtils.HTTP_RESPONSE_DELAY);

        if (StringUtils.isNotEmpty(responseSerialized)) {
            httpResponse.withBody(responseSerialized);
        }

        Expectation[] expectation = MockServer.mockServer.when(httpRequest, TestUtils.expectation()).respond(httpResponse);
        mockExpectations.addAll(Arrays.stream(expectation).map(Expectation::getId).toList());
    }

    private CreateFreeSpinsRequest buildCreateFreeSpinsRequest() {
        return CreateFreeSpinsRequest.builder()
                .bonusCode(contextBonusCode)
                .currency(contextCurrency)
                .betAmount(contextBetAmount)
                .spins(contextSpins)
                .startDate(contextStartDate)
                .expirationDate(contextExpirationDate)
                .games(List.of(contextGameCode))
                .requestId(contextRequestId)
                .playerIdList(new ArrayList<>(contextPlayerIds))
                .operator(operator.code())
                .provider(provider.code())
                .betLevel(contextBetLevel)
                .build();
    }

    private CreateFreeSpinsRequest buildCreateFreeSpinsByBonusRequest() {
        return CreateFreeSpinsRequest.builder()
                .bonusCode(contextBonusCode)
                .requestId(contextRequestId)
                .playerIdList(new ArrayList<>(contextPlayerIds))
                .operator(operator.code())
                .provider(provider.code())
                .build();
    }

    private CreateFreeSpinsRequest buildCreatePlayerFreeSpinRequest() {
        var request = new CreateFreeSpinsRequest();

        request.setPlayerId(contextPlayerIds.getFirst());
        request.setOperator(operator.code());
        request.setProvider(provider.code());
        request.setBonusCode(contextBonusCode);
        request.setCurrency(contextCurrency);
        request.setBetAmount(contextBetAmount);
        request.setSpins(contextSpins);
        request.setStartDate(contextStartDate);
        request.setExpirationDate(contextExpirationDate);
        request.setGames(List.of(contextGameCode));
        request.setRequestId(contextRequestId);
        request.setBetLevel(contextBetLevel);

        return request;
    }

    private CreateFreeSpinsRequest buildCreatePlayerFreeSpinByBonusRequest() {
        var request = new CreateFreeSpinsRequest();

        request.setPlayerId(contextPlayerIds.getFirst());
        request.setOperator(operator.code());
        request.setProvider(provider.code());
        request.setBonusCode(contextBonusCode);
        request.setRequestId(contextRequestId);

        return request;
    }

    private CancelPlayerFreeSpinRequest buildCancelPlayerFreeSpinRequest() {
        return CancelPlayerFreeSpinRequest.builder()
                .requestId(contextRequestId)
                .campaign(contextBonusCode)
                .currency(contextCurrency)
                .freeSpin(FreeSpinsInfo.builder()
                        .playerId(contextPlayerIds.getFirst())
                        .freeSpinsId(contextFreeSpinsId)
                        .isApplied(false)
                        .build())
                .build();
    }

    private CancelFreeSpinsRequest buildCancelFreeSpinsRequest() {
        return CancelFreeSpinsRequest.builder()
                .requestId(contextRequestId)
                .campaign(contextBonusCode)
                .currency(contextCurrency)
                .freeSpinsList(contextPlayerIds.stream()
                        .map(playerId -> FreeSpinsInfo.builder()
                                .playerId(playerId)
                                .freeSpinsId(contextFreeSpinsId)
                                .isApplied(false)
                                .build())
                        .toList())
                .build();
    }

    private CancelFreeSpinsRequest buildCancelFreeSpinsRequestWithInvalidList() {
        return CancelFreeSpinsRequest.builder()
                .requestId(contextRequestId)
                .campaign(contextBonusCode)
                .currency(contextCurrency)
                .freeSpinsList(new ArrayList<>())
                .build();
    }

    private GrantBonusRequest buildGrantBonusRequest() {
        return GrantBonusRequest.builder()
                .apiKey(API_KEY)
                .amountType("promo_freespin")
                .campaignId(contextBonusCode)
                .gameIds(List.of(Integer.valueOf(BaseMapper.removePrefixFromGameId(contextGameCode, ProviderSpec.NETGAMING))))
                .coinValueLevel(contextBetLevel)
                .numRounds(contextSpins)
                .playerIds(contextPlayerIds)
                .currencies(contextPlayerIds.stream().map(playerId -> contextCurrency).toList())
                .startDate(Mappers.toDateTimeFreeSpinsFormat(contextStartDate))
                .endDate(Mappers.toDateTimeFreeSpinsFormat(contextExpirationDate))
                .build();
    }

    private GrantBonusResponse buildGrantBonusResponse() {
        return GrantBonusResponse.builder()
                .result(FreeSpinsResponseResult.builder()
                        .status(SUCCESS_STATUS)
                        .promoFreeSpinId(contextFreeSpinsId)
                        .build())
                .build();
    }

    private GetCoinLevelsRequest buildGetCoinLevelsRequest() {
        return GetCoinLevelsRequest.builder()
                .apiKey(API_KEY)
                .gameId(BaseMapper.removePrefixFromGameId(contextGameCode, ProviderSpec.NETGAMING))
                .currency(Currency.valueOf(contextCurrency))
                .build();
    }

    private GetCoinLevelsResponse buildGetCoinLevelsResponse() {
        return GetCoinLevelsResponse.builder()
                .result(CoinLevelResponseResult.builder()
                        .gameName(RandomStringUtils.randomAlphanumeric(10))
                        .gameId(Integer.parseInt(BaseMapper.removePrefixFromGameId(contextGameCode, ProviderSpec.NETGAMING)))
                        .title(RandomStringUtils.randomAlphanumeric(10))
                        .betValues(buildBetValues())
                        .build())
                .build();
    }

    private GetCoinLevelsResponse buildGetCoinLevelsResponseValidationException() {
        return GetCoinLevelsResponse.builder()
                .result(CoinLevelResponseResult.builder()
                        .gameName(RandomStringUtils.randomAlphanumeric(10))
                        .gameId(Integer.parseInt(BaseMapper.removePrefixFromGameId(contextGameCode, ProviderSpec.NETGAMING)))
                        .title(RandomStringUtils.randomAlphanumeric(10))
                        .betValues(List.of(Map.of(contextCurrency, IntStream.range(1, contextBetLevel).mapToObj(playerId -> BigDecimal.ONE).toList())))
                        .build())
                .build();
    }

    private ErrorResponse buildErrorResponse() {
        return ErrorResponse.builder()
                .error(1)
                .errorId(ErrorCode.INTERNAL_SERVER_ERROR.getCode())
                .errorCode(ErrorCode.INTERNAL_SERVER_ERROR.name())
                .errorMessage(ErrorCode.INTERNAL_SERVER_ERROR.getMessage())
                .build();
    }

    private CancelFreeSpinRequest buildCancelFreeSpinRequest() {
        return CancelFreeSpinRequest.builder()
                .apiKey(API_KEY)
                .promoFreeSpinId(contextFreeSpinsId)
                .build();
    }

    private CancelFreeSpinResponse buildCancelFreeSpinResponse() {
        return CancelFreeSpinResponse.builder()
                .result(FreeSpinsResponseResult.builder()
                        .status(SUCCESS_STATUS)
                        .promoFreeSpinId(contextFreeSpinsId)
                        .campaignId(contextBonusCode)
                        .build())
                .build();
    }

    private List<Map<String, List<BigDecimal>>> buildBetValues() {
        return List.of(
                Map.of(contextCurrency, IntStream.range(0, contextBetLevel)
                        .mapToObj(ignored -> ignored == contextBetLevel - 1 ? contextBetAmount : new BigDecimal(ignored + 1))
                        .toList()));
    }

    private void validateFreeSpinCampaign() {
        Optional<FreeSpinCampaign> freeSpinCampaignOptional = repository.freeSpinCampaign(contextBonusCode, operator.code(), provider.code());

        Assertions.assertTrue(freeSpinCampaignOptional.isPresent());

        FreeSpinCampaign freeSpinCampaign = freeSpinCampaignOptional.get();

        Assertions.assertEquals(contextCurrency, freeSpinCampaign.getCurrency());
        Assertions.assertEquals(FreeSpinCampaignStatus.CREATED.code(), freeSpinCampaign.getStatus());
    }

    private void validateAccountFreeSpins(int freeSpinsCount) {
        Optional<List<AccountFreeSpin>> freeSpins = repository.accountFreeSpinsByBonusCode(operator.code(), provider.code(), contextBonusCode);

        Assertions.assertTrue(freeSpins.isPresent());
        Assertions.assertEquals(freeSpinsCount, freeSpins.get().size());
    }
}
