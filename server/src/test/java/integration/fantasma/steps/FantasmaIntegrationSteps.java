package integration.fantasma.steps;

import java.math.BigDecimal;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;

import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.collect.Lists;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.HttpProto;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.bots.DefaultFantasmaBot;
import aggregator.bots.FantasmaBot;
import aggregator.listeners.MockDataGenerator;
import aggregator.model.Account;
import aggregator.model.WalletTransaction;
import aggregator.repo.BrandRepo;
import aggregator.repo.DefaultBrandRepo;
import aggregator.repo.DefaultWalletSessionRepo;
import aggregator.repo.WalletSessionRepo;
import common.AggregatorServerProperties;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.flatten.TempTokenFlatten;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import gaming.fantasma.b2b.model.AuthenticateRequest;
import gaming.fantasma.b2b.model.AuthenticateResponse;
import gaming.fantasma.b2b.model.BalanceRequest;
import gaming.fantasma.b2b.model.BalanceResponse;
import gaming.fantasma.b2b.model.CancelRequest;
import gaming.fantasma.b2b.model.ErrorResponse;
import gaming.fantasma.b2b.model.TransactionRequest;
import gaming.fantasma.utils.Mappers;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import okhttp3.OkHttpClient;

public class FantasmaIntegrationSteps {
    @Autowired
    private AggregatorServerProperties props;
    @Autowired
    private DynamicCloud cloud;
    @Autowired
    private SpannerTemplate spannerTemplate;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private OkHttpClient httpClient;
    private final CommonObjectMapper mapper = new CommonObjectMapper();

    private BrandRepo brandRepo;
    private WalletSessionRepo walletSessionRepo;
    private Account account;
    private FantasmaBot fantasmaBot;
    private String gameCode;
    private String roundId;
    private AccountRoutingCurrencyFlatten flatten;
    private String playerId;
    private String currency;
    private String lastTransactionIdBet;
    private String lastTransactionIdWin;
    private String lastTransactionIdRefund;
    private int transactionCount;
    private AuthenticateResponse authResponse;
    private ErrorResponse responseError;

    private final List<String> listTxIdBet = Lists.newArrayList();
    private final List<String> listTxIdWin = Lists.newArrayList();
    private final List<String> listTxIdRefund = Lists.newArrayList();
    private static final String AUTH_KEY = "someAuthKeyFromProviderSide";

    @Given("^Initialize bootstrap properties -fantasma-$")
    public void initializeBootstrapProperties() throws Exception {
        String uri = new URIBuilder()
                .setScheme("http")
                .setHost("localhost")
                .setPort(props.CLOUD_APP_PORT.get())
                .setUserInfo("admin", AUTH_KEY)
                .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "alpha_numeric")
                .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                .build().toURL().toExternalForm();

        PlainServiceInfo si = new PlainServiceInfo(OperatorSpec.BLUEDREAM.igpUps(), uri);

        PlainServiceInfo signSi = new PlainServiceInfo(
                OperatorSpec.BLUEDREAM.irgsUps(ProviderSpec.FANTASMA.code()), uri);

        cloud.addUps(si);
        cloud.addUps(signSi);
        this.brandRepo = new DefaultBrandRepo(spannerTemplate, cacheManager);
        this.walletSessionRepo = new DefaultWalletSessionRepo(spannerTemplate);
    }

    @Given("^New game with currency (.+) -fantasma-$")
    public void newGameWithCurrency(String currency) throws Throwable {
        this.currency = currency;
        this.fantasmaBot = createBot();
        this.flatten = generateFlatten();
        this.playerId = flatten.writeExternal();

        Assertions.assertNotNull(this.currency);
        Assertions.assertEquals(this.flatten.getCurrency(), this.currency);
    }

    @When("^Enter the game (.+) -fantasma-$")
    public void enterTheGame(String gameCode) throws Throwable {
        String token = generateTempFlatten(this.flatten).writeExternal();
        AuthenticateRequest request = new AuthenticateRequest();
        request.setOperator(OperatorSpec.BLUEDREAM.code());
        request.setGame(gameCode);
        request.setTemporaryToken(token);
        this.gameCode = gameCode;

        String json = this.fantasmaBot.authenticate(request, "");
        this.authResponse = this.mapper.readValue(json, AuthenticateResponse.class);

        this.account = this.brandRepo.requiredAccount(MockDataGenerator.OPERATOR, String.valueOf(this.flatten.getAccountId()));

        Assertions.assertEquals(this.playerId, this.authResponse.getPlayerId());
    }

    @When("^Enter a game (.+) with invalid token -fantasma-$")
    public void enterTheGameWithInvalidToken(String gameCode) throws Throwable {
        String token = "_invalid_";
        AuthenticateRequest request = new AuthenticateRequest();
        request.setOperator(OperatorSpec.BLUEDREAM.code());
        request.setGame(gameCode);
        request.setTemporaryToken(token);
        this.gameCode = gameCode;

        String json = this.fantasmaBot.authenticate(request, "");
        this.responseError = this.mapper.readValue(json, ErrorResponse.class);

        Assertions.assertInstanceOf(ErrorResponse.class, this.responseError);
    }

    @When("^Renter the game (.+) with authorization header -fantasma-$")
    public void enterTheGameWithDuplicatedToken(String gameCode) throws Throwable {
        String token = generateTempFlatten(this.flatten).writeExternal();
        AuthenticateRequest request = new AuthenticateRequest();
        request.setOperator(OperatorSpec.BLUEDREAM.code());
        request.setGame(gameCode);
        request.setTemporaryToken(token);
        this.gameCode = gameCode;

        String json = this.fantasmaBot.authenticate(request, this.authResponse.getAuth());
        this.authResponse = this.mapper.readValue(json, AuthenticateResponse.class);

        this.account = this.brandRepo.requiredAccount(MockDataGenerator.OPERATOR, String.valueOf(this.flatten.getAccountId()));

        Assertions.assertEquals(this.playerId, this.authResponse.getPlayerId());
    }

    @Then("^Response has error code: (.+) -fantasma-$")
    public void responseHasErrorCode(String error) {
        Assertions.assertEquals(error, this.responseError.getError().getCode());
    }

    @Then("^No error -fantasma-$")
    public void responseHasNoError() {
        Assertions.assertNull(this.responseError.getError());
    }

    @Then("^Has (\\d+(?:\\.\\d+)?) balance -fantasma-$")
    public void hasBalance(BigDecimal balance) throws Throwable {
        BalanceRequest request = new BalanceRequest();
        request.setPlayerId(this.playerId);

        String json = this.fantasmaBot.getBalance(request, this.authResponse.getAuth());
        var response = this.mapper.readValue(json, BalanceResponse.class);
        validateAmounts(balance, response.getBalance());
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) bet -fantasma-$")
    public void sendBet(BigDecimal bet) throws Throwable {
        if (StringUtils.isEmpty(this.roundId)) {
            this.roundId = PlatformUtil.randomUUID().toString();
        }
        sendBet(createTransactionRequest(bet, PlatformUtil.randomUUID().toString(), Mappers.WITHDRAW));
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) negative bet -fantasma-$")
    public void sendNegativeBet(BigDecimal bet) throws Throwable {
        if (StringUtils.isEmpty(this.roundId)) {
            this.roundId = PlatformUtil.randomUUID().toString();
        }
        var request = createTransactionRequest(bet, PlatformUtil.randomUUID().toString(), Mappers.WITHDRAW);
        String json = this.fantasmaBot.transaction(request, this.authResponse.getAuth());
        this.responseError = this.mapper.readValue(json, ErrorResponse.class);

        Assertions.assertInstanceOf(ErrorResponse.class, this.responseError);
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) bet using previous transactionId -fantasma-$")
    public void sendBetPrevious(BigDecimal bet) throws Throwable {
        if (StringUtils.isEmpty(this.roundId)) {
            this.roundId = PlatformUtil.randomUUID().toString();
        }
        sendBet(createTransactionRequest(bet, this.lastTransactionIdBet, Mappers.WITHDRAW));
    }

    @When("^Send bet for the game with not enough money -fantasma-$")
    public void sendBetRequestNotEnoughMoney() throws Throwable {
        if (StringUtils.isEmpty(this.roundId)) {
            this.roundId = PlatformUtil.randomUUID().toString();
        }
        var request = createTransactionRequest(new BigDecimal("11.00"), PlatformUtil.randomUUID().toString(), Mappers.WITHDRAW);
        String json = this.fantasmaBot.transaction(request, this.authResponse.getAuth());
        this.responseError = this.mapper.readValue(json, ErrorResponse.class);

        Assertions.assertInstanceOf(ErrorResponse.class, this.responseError);
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) win request -fantasma-$")
    public void sendWinRequest(BigDecimal win) throws Throwable {
        sendWin(createTransactionRequest(win, PlatformUtil.randomUUID().toString(), Mappers.DEPOSIT));
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) win request with transactionId (.+) -fantasma-$")
    public void sendWinRequestWithTxId(BigDecimal win, String transactionId) throws Throwable {
        sendWin(createTransactionRequest(win, transactionId, Mappers.DEPOSIT));
    }

    @And("^Send (\\d+(?:\\.\\d+)?) last win refund -fantasma-$")
    public void sendWinRefundRequest(BigDecimal refund) throws Throwable {
        sendRefund(createCancelRequest(this.lastTransactionIdWin), refund);
    }

    @And("^Send (\\d+(?:\\.\\d+)?) last bet refund -fantasma-$")
    public void sendBetRefundRequest(BigDecimal refund) throws Throwable {
        sendRefund(createCancelRequest(this.lastTransactionIdBet), refund);
    }

    @And("^Send refund before bet -fantasma-$")
    public void sendRefundBeforeBetRequest() throws Throwable {
        if (StringUtils.isEmpty(this.roundId)) {
            this.roundId = PlatformUtil.randomUUID().toString();
        }
        sendRefund(createCancelRequest(PlatformUtil.randomUUID().toString()), BigDecimal.ZERO);
    }

    @And("^Send (\\d+(?:\\.\\d+)?) bet after refund -fantasma-$")
    public void sendBetAfterRefundRequest(BigDecimal bet) throws Throwable {

        var request = createTransactionRequest(bet, this.lastTransactionIdRefund, Mappers.WITHDRAW);
        String json = this.fantasmaBot.transaction(request, this.authResponse.getAuth());
        this.responseError = mapper.readValue(json, ErrorResponse.class);

        var wsOpt = walletSessionRepo.walletSessionWithTransactions(
                this.account.getHash(), ProviderSpec.FANTASMA.code(), this.roundId, this.currency);
        Assertions.assertTrue(wsOpt.isPresent());
        var transactions = wsOpt.get().getTransactions();
        Assertions.assertEquals(this.transactionCount, transactions.size());
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) win after finished the game -fantasma-$")
    public void sendWinRequestAfterRoundFinished(BigDecimal win) throws Throwable {
        var request = createTransactionRequest(win, PlatformUtil.randomUUID().toString(), Mappers.DEPOSIT);
        String json = this.fantasmaBot.transaction(request, this.authResponse.getAuth());
        this.responseError = mapper.readValue(json, ErrorResponse.class);
    }

    private void sendRefund(CancelRequest request, BigDecimal refund) throws Exception {

        String json = this.fantasmaBot.cancel(request, this.authResponse.getAuth());

        this.responseError = this.mapper.readValue(json, ErrorResponse.class);

        var transactionId = request.getTransactionId();

        if (!this.listTxIdRefund.contains(transactionId)) {
            this.transactionCount++;
            this.listTxIdRefund.add(transactionId);
            this.lastTransactionIdRefund = transactionId;
        }
        var wsOpt = walletSessionRepo.walletSessionWithTransactions(
                this.account.getHash(), ProviderSpec.FANTASMA.code(), this.roundId, this.currency);
        Assertions.assertTrue(wsOpt.isPresent());
        var transactions = wsOpt.get().getTransactions();
        Assertions.assertEquals(this.transactionCount, transactions.size());
        assertRefundWalletTransaction(refund, transactions);
    }

    private CancelRequest createCancelRequest(String transactionId) {
        var request = new CancelRequest();
        request.setPlayerId(this.playerId);
        request.setTransactionId(transactionId);
        request.setRoundId(this.roundId);
        return request;
    }

    private TransactionRequest createTransactionRequest(BigDecimal amount, String transactionId, String type) {
        var request = new TransactionRequest();
        request.setPlayerId(this.playerId);
        request.setTransactionId(transactionId);
        request.setType(type);
        request.setAmount(amount);
        request.setGame(this.gameCode);
        request.setRoundId(this.roundId);
        request.setFinished(Mappers.DEPOSIT.equals(type));
        return request;
    }

    private void sendBet(TransactionRequest request) throws Exception {

        String json = this.fantasmaBot.transaction(request, this.authResponse.getAuth());

        this.responseError = this.mapper.readValue(json, ErrorResponse.class);
        Assertions.assertNull(this.responseError.getError());

        var transactionId = request.getTransactionId();

        if (!this.listTxIdBet.contains(transactionId)) {
            this.transactionCount++;
            this.listTxIdBet.add(transactionId);
            this.lastTransactionIdBet = transactionId;
        }
        var wsOpt = walletSessionRepo.walletSessionWithTransactions(
                this.account.getHash(), ProviderSpec.FANTASMA.code(), this.roundId, this.currency);
        Assertions.assertTrue(wsOpt.isPresent());
        var transactions = wsOpt.get().getTransactions();
        Assertions.assertEquals(this.transactionCount, transactions.size());
        assertDebitWalletTransaction(request.getAmount(), transactions, transactionId);
    }

    private void sendWin(TransactionRequest request) throws Exception {

        String json = this.fantasmaBot.transaction(request, this.authResponse.getAuth());

        this.responseError = this.mapper.readValue(json, ErrorResponse.class);
        Assertions.assertNull(this.responseError.getError());

        var transactionId = request.getTransactionId();

        if (!this.listTxIdWin.contains(transactionId)) {
            this.transactionCount++;
            this.listTxIdWin.add(transactionId);
            this.lastTransactionIdWin = transactionId;
        }
        var wsOpt = walletSessionRepo.walletSessionWithTransactions(
                this.account.getHash(), ProviderSpec.FANTASMA.code(), this.roundId, this.currency);
        Assertions.assertTrue(wsOpt.isPresent());
        var transactions = wsOpt.get().getTransactions();
        Assertions.assertEquals(this.transactionCount, transactions.size());
        assertCreditWalletTransaction(request.getAmount(), transactions, transactionId);
    }

    private FantasmaBot createBot() {
        URIBuilder uriBuilder = new URIBuilder()
                .setScheme("http")
                .setHost("localhost")
                .setPort(props.CLOUD_APP_PORT.get());
        return new DefaultFantasmaBot(httpClient, uriBuilder, AUTH_KEY);
    }

    private AccountRoutingCurrencyFlatten generateFlatten() {
        var hash = PlatformUtil.randomAlphanumeric(8);
        var id = String.valueOf(System.currentTimeMillis());
        return AccountRoutingCurrencyFlatten.underscore(hash, id, this.currency);
    }

    private TempTokenFlatten generateTempFlatten(AccountRoutingCurrencyFlatten flatten) {
        return TempTokenFlatten.underscore(flatten.getRoutingKey(), flatten.getAccountId(), flatten.getCurrency(), 36);
    }

    private static void validateAmounts(BigDecimal expected, BigDecimal actual) {
        Assertions.assertEquals(0, expected.compareTo(actual), String.format("Expected %.2f but balance %.2f", expected, actual));
    }

    private static void assertDebitWalletTransaction(BigDecimal bet, List<WalletTransaction> txs, String reference) {
        var debitTx = txs.stream().filter(tx -> tx.getReference().equals(reference)).findFirst();
        Assertions.assertTrue(debitTx.isPresent());
        Assertions.assertTrue(debitTx.get().isDebit());
        validateAmounts(bet, new BigDecimal(debitTx.get().getAmount()));
    }

    private static void assertCreditWalletTransaction(BigDecimal win, List<WalletTransaction> txs, String reference) {
        var creditTx = txs.stream().filter(tx -> tx.getReference().equals(reference)).findFirst();
        Assertions.assertTrue(creditTx.isPresent());
        Assertions.assertTrue(creditTx.get().isCredit());
        validateAmounts(win, new BigDecimal(creditTx.get().getAmount()));
    }

    private static void assertRefundWalletTransaction(BigDecimal refund, List<WalletTransaction> txs) {
        var refundTx = txs.stream().filter(WalletTransaction::isRefund).findFirst();
        Assertions.assertTrue(refundTx.isPresent());
        Assertions.assertTrue(refundTx.get().isRefund());
        validateAmounts(refund, new BigDecimal(refundTx.get().getAmount()));
    }
}
