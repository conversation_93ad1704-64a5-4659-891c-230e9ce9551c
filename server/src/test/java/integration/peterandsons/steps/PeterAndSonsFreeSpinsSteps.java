package integration.peterandsons.steps;

import static gaming.peterandsons.api.PeterAndSonsLaunchApi.PETERANDSONS;
import static gaming.peterandsons.utils.Mappers.toEpochMilli;
import static gaming.peterandsons.utils.Mappers.toProviderCurrency;
import static integration.utils.TestUtils.awaitCreationFSBeforeCheck;
import static integration.utils.TestUtils.buildPlayerId;
import static integration.utils.TestUtils.getPlayerIdToFreeSpinsIdMap;
import static org.mockserver.matchers.Times.exactly;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.junit.jupiter.api.Assertions;
import org.mockserver.mock.Expectation;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;
import org.mockserver.model.MediaType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.net.HttpHeaders;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.HttpProto;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.bots.freespins.DefaultFreeSpinsBot;
import aggregator.bots.freespins.FreeSpinsBot;
import aggregator.repo.BrandRepo;
import aggregator.repo.DefaultBrandRepo;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.FreeSpinsService;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CancelFreeSpinsRequest;
import common.model.freespins.CancelFreeSpinsResponse;
import common.model.freespins.CancelPlayerFreeSpinRequest;
import common.model.freespins.CancelPlayerFreeSpinResponse;
import common.model.freespins.CreateFreeSpinsCampaignDTO;
import common.model.freespins.CreateFreeSpinsRequest;
import common.model.freespins.CreateFreeSpinsResponse;
import common.model.freespins.DisableFreeSpinsCampaignRequest;
import common.model.freespins.FreeSpinCampaignStatus;
import common.model.freespins.FreeSpinsInfo;
import gaming.peterandsons.api.freespins.model.Campaign;
import gaming.peterandsons.api.freespins.model.CampaignMutationType;
import gaming.peterandsons.api.freespins.model.CampaignsQuery;
import gaming.peterandsons.api.freespins.model.CreateCampaignQuery;
import gaming.peterandsons.api.freespins.model.DeleteCampaignQuery;
import gaming.peterandsons.api.freespins.model.EditCampaignQuery;
import gaming.peterandsons.api.freespins.model.LoginQuery;
import gaming.peterandsons.api.freespins.model.Query;
import integration.MockServer;
import integration.utils.TestUtils;
import io.cucumber.java.After;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.SneakyThrows;
import okhttp3.OkHttpClient;

public class PeterAndSonsFreeSpinsSteps {
    private static final String GRAPHQL_API_PATH = "/graphql";
    private static final String LOGIN = "name";
    private static final String PASSWORD = "password";
    private static final String TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9." +
            "************************************************************************************************." +
            "lnkucAaSQAbmbQBeIaT7i3skVYyzLR6HiWnbQjdet9w";
    private static final String DELETE_CAMPAIGN = "deleteCampaign";
    private static final String EDIT_CAMPAIGN = "editCampaign";
    private static final String ERROR_MESSAGE = "Some error message";
    private static final String startDateString = "2025-09-03T23:59:59";
    private static final String expirationDateString = "2025-10-06T23:59:59";
    private static final ObjectMapper mapper = new CommonObjectMapper();
    private static final OperatorSpec operator = OperatorSpec.BLUEDREAM;
    private static final ProviderSpec provider = ProviderSpec.PETERANDSONS;
    private static final List<String> mockExpectations = new ArrayList<>();

    @Autowired
    private AggregatorServerProperties props;
    @Autowired
    private DynamicCloud cloud;
    @Autowired
    private SpannerTemplate spannerTemplate;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private OkHttpClient httpClient;

    private BrandRepo brandRepo;

    private FreeSpinsBot bot;
    private PlainServiceInfo psi;

    private String currency;
    private String gameCode;
    private String bonusCode;
    private String requestId;
    private String campaignId;
    private BigDecimal betAmount;
    private int spins;
    private LocalDateTime startDate;
    private LocalDateTime expirationDate;
    private Map<String, String> playerIdToFreeSpinsIdMap;
    private CreateFreeSpinsResponse actualCreate;
    private CancelPlayerFreeSpinResponse actualCancel;
    private CancelFreeSpinsResponse actualCancelBatching;

    @After("@peterandsons-fs")
    public void afterScenario() {
        mockExpectations.forEach(expectationId -> MockServer.mockServer.clear(expectationId));
    }

    @Given("^Initialize properties -peterandsons fs-$")
    public void initializeProperties() throws Exception {
        psi = buildPlainServiceInfo();
        PlainServiceInfo psiBot = buildPlainServiceInfoForBot();
        cloud.addUps(psi);
        cloud.addUps(psiBot);
        this.brandRepo = new DefaultBrandRepo(spannerTemplate, cacheManager);

        bot = new DefaultFreeSpinsBot(httpClient, operator, provider, psiBot);
    }

    @Given("^Initialize currency (.+) and game code (.+) -peterandsons fs-$")
    public void initializeCurrencyAndGameCode(
            String currency,
            String gameCode) {
        this.currency = currency;
        this.gameCode = gameCode;
    }

    @Given("^Initialize for create with playerIds (.+) -peterandsons fs-$")
    public void initializeFreeSpinsCreate(String playerIds) {
        this.bonusCode = PlatformUtil.randomAlphanumeric(10);
        this.requestId = PlatformUtil.randomUUID().toString();
        this.campaignId = this.bonusCode + "-" + this.requestId;
        this.betAmount = new BigDecimal("5");
        this.spins = 10;
        this.startDate = LocalDateTime.parse(startDateString);
        this.expirationDate = LocalDateTime.parse(expirationDateString);
        this.playerIdToFreeSpinsIdMap = getPlayerIdToFreeSpinsIdMap(playerIds, () -> campaignId, currency);
    }

    @Given("^Prepare mockServer for login with status (.+) for (.+) calling -peterandsons fs-$")
    public void genMockServerForLogin(int status, int times) throws JsonProcessingException {
        createLoginMockServer(
                HttpStatus.SC_OK == status
                        ? getValidLoginResponseJson()
                        : getErrorLoginResponseJson(),
                times);
    }

    @Given("^Prepare mockServer for create with status (.+) -peterandsons fs-$")
    public void genMockServerForCreate(int status) throws JsonProcessingException {
        String providerGameCode = gameCode.split("_")[1];
        CreateCampaignQuery createCampaignQuery = CreateCampaignQuery.builder()
                .bonusCode(bonusCode)
                .requestId(requestId)
                .start(toEpochMilli(startDate))
                .end(toEpochMilli(expirationDate))
                .wallet(psi.getUserName())
                .operator(operator.code())
                .brand(operator.code())
                .provider(PETERANDSONS)
                .gameId(providerGameCode)
                .nativeIds(playerIdToFreeSpinsIdMap.keySet().stream().toList())
                .betCount(spins)
                .amount(betAmount)
                .currency(toProviderCurrency(currency))
                .build();

        String requestBody = mapper.writeValueAsString(new Query(createCampaignQuery.getQueryString()));

        createMockServer(
                requestBody,
                HttpStatus.SC_OK == status
                        ? getValidBatchResponseJson(campaignId)
                        : getErrorBatchResponseJson());
    }

    @Given("^Prepare mockServer for V2 create with status (.+) -peterandsons fs-$")
    public void genV2MockServerForCreate(int status) throws JsonProcessingException {
        String providerGameCode = gameCode.split("_")[1];
        CreateCampaignQuery createCampaignQuery = CreateCampaignQuery.builder()
                .bonusCode(bonusCode)
                .requestId(requestId + "-1")
                .start(toEpochMilli(startDate))
                .end(toEpochMilli(expirationDate))
                .wallet(psi.getUserName())
                .operator(operator.code())
                .brand(operator.code())
                .provider(PETERANDSONS)
                .gameId(providerGameCode)
                .nativeIds(playerIdToFreeSpinsIdMap.keySet().stream().toList())
                .betCount(spins)
                .amount(betAmount)
                .currency(toProviderCurrency(currency))
                .build();

        String requestBody = mapper.writeValueAsString(new Query(createCampaignQuery.getQueryString()));

        createMockServer(
                requestBody,
                HttpStatus.SC_OK == status
                        ? getValidBatchResponseJson(campaignId)
                        : getErrorBatchResponseJson());
    }

    @Given("^Prepare mockServer for search by bonus code with status (.+) -peterandsons fs-$")
    public void genMockServerForSearchByBonusCode(int status) throws JsonProcessingException {
        var campaignsQuery = new CampaignsQuery(bonusCode);
        var requestBody = mapper.writeValueAsString(new Query(campaignsQuery.getQueryString()));
        var responseCampaignIdsString = mapper.writeValueAsString(
                playerIdToFreeSpinsIdMap.values().stream()
                        .map(campaignId -> Campaign.builder().campaignId(campaignId).build())
                        .toList());

        createMockServer(
                requestBody,
                HttpStatus.SC_OK == status
                        ? getValidCampaignsByBonusCodeResponse(responseCampaignIdsString)
                        : getErrorCampaignsResponse());
    }

    @Given("^Prepare mockServer for search by bonus code and campaignId with status (.+) -peterandsons fs-$")
    public void genMockServerForSearchByBonusCodeAndCampaignId(int status) throws JsonProcessingException {
        var campaignsQuery = new CampaignsQuery(bonusCode, campaignId);
        var requestBody = mapper.writeValueAsString(new Query(campaignsQuery.getQueryString()));
        var responseCampaignsString = mapper.writeValueAsString(
                playerIdToFreeSpinsIdMap.values().stream()
                        .map(campaignId -> Campaign.builder()
                                .campaignId(campaignId)
                                .nativeIds(playerIdToFreeSpinsIdMap.keySet().stream().toList())
                                .build())
                        .toList());

        createMockServer(
                requestBody,
                HttpStatus.SC_OK == status
                        ? getValidCampaignsByBonusCodeAndCampaignIdResponse(responseCampaignsString)
                        : getErrorCampaignsResponse());
    }

    @Given("^Prepare mockServer for cancel with status (.+) -peterandsons fs-$")
    public void genMockServerForCancel(int status) {
        playerIdToFreeSpinsIdMap.values().forEach(campaignId -> {
            var deleteCampaignQuery = new DeleteCampaignQuery(campaignId);
            try {
                var requestBody = mapper.writeValueAsString(new Query(deleteCampaignQuery.getQueryString()));
                createMockServer(
                        requestBody,
                        HttpStatus.SC_OK == status
                                ? getValidDeleteCampaignResponse()
                                : getErrorDeleteCampaignResponse());
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Given("^Prepare mockServer for remove playerId (.+) from campaign with status (.+) -peterandsons fs-$")
    public void genMockServerForEditCampaignPlayerIdList(String accountIdForRemoving, int status) throws JsonProcessingException {
        var playerIdForRemoving = buildPlayerId(accountIdForRemoving, currency);
        List<String> playerIdsForSaving = playerIdToFreeSpinsIdMap.keySet().stream()
                .filter(playerId -> !playerId.equals(playerIdForRemoving))
                .toList();
        var editQuery = new EditCampaignQuery(
                campaignId,
                playerIdsForSaving,
                CampaignMutationType.EDIT_PLAYER_ID_LIST);
        var requestBody = mapper.writeValueAsString(new Query(editQuery.getQueryString()));

        createMockServer(
                requestBody,
                HttpStatus.SC_OK == status
                        ? getValidEditCampaignResponse()
                        : getErrorEditCampaignResponse());
    }

    @When("^Send create free spins -peterandsons fs-$")
    public void sendCreateFreeSpins() throws Exception {
        var request = CreateFreeSpinsRequest.builder()
                .bonusCode(bonusCode)
                .currency(currency)
                .betAmount(betAmount)
                .spins(spins)
                .startDate(startDate)
                .expirationDate(expirationDate)
                .games(List.of(gameCode))
                .requestId(PlatformUtil.randomUUID().toString())
                .playerIdList(playerIdToFreeSpinsIdMap.keySet().stream().toList())
                .build();
        actualCreate = bot.createFreeSpins(request, this.requestId);
        System.out.println();
    }

    @When("^Send create free spins by bonus -peterandsons fs-$")
    public void sendCreateFreeSpinsByBonus() throws Exception {
        var request = CreateFreeSpinsRequest.builder()
                .bonusCode(bonusCode)
                .requestId(PlatformUtil.randomUUID().toString())
                .playerIdList(playerIdToFreeSpinsIdMap.keySet().stream().toList())
                .build();
        actualCreate = bot.createFreeSpins(request, this.requestId);
        System.out.println();
    }

    @When("^Send cancel player free spins for playerId (.+) -peterandsons fs-$")
    public void sendCancelPlayerFreeSpins(String accountIdForCancel) throws Exception {
        var freeSpin = getFreeSpinsInfoByPlayerId(buildPlayerId(accountIdForCancel, currency), false);
        var request = CancelPlayerFreeSpinRequest.builder()
                .campaign(bonusCode)
                .currency(currency)
                .cancelBonusCode(false)
                .requestId(PlatformUtil.randomUUID().toString())
                .freeSpin(freeSpin)
                .build();

        actualCancel = bot.cancelFreeSpins(request);
    }

    @When("^Send cancel free spins by bonus code -peterandsons fs-$")
    public void sendCancelFreeSpinsByBonusCode() throws Exception {
        var freeSpin = getFreeSpinsInfoList(false).getFirst();
        var request = CancelFreeSpinsRequest.builder()
                .campaign(bonusCode)
                .currency(currency)
                .cancelBonusCode(false)
                .requestId(PlatformUtil.randomUUID().toString())
                .freeSpinsList(List.of(freeSpin))
                .build();

        actualCancelBatching = bot.cancelFreeSpinsBatching(request);
        System.out.println();
    }

    @Then("^Has created (.+) free spins -peterandsons fs-$")
    public void hasCreateResponse(int freeSpinsCount) {
        CreateFreeSpinsResponse expected = new CreateFreeSpinsResponse(bonusCode, getFreeSpinsInfoList(true));

        Assertions.assertEquals(expected.getBonusCode(), actualCreate.getBonusCode());
        Assertions.assertEquals(expected.getCode(), actualCreate.getCode());
        Assertions.assertTrue(CollectionUtils.isEqualCollection(expected.getFreeSpinsList(), actualCreate.getFreeSpinsList()));
        Assertions.assertEquals(freeSpinsCount, actualCreate.getFreeSpinsList().size());
    }

    @Then("^Has received spins responses with error -peterandsons fs-$")
    public void hasCreateErrorResponse() {
        CreateFreeSpinsResponse expected = CreateFreeSpinsResponse.buildError(400,
                "Error of free spins operation on provider side. " + ERROR_MESSAGE + ".");
        expected.setBonusCode(this.bonusCode);

        Assertions.assertEquals(expected.getBonusCode(), actualCreate.getBonusCode());
        Assertions.assertEquals(expected.getCode(), actualCreate.getCode());
        Assertions.assertEquals(expected.getMessage(), actualCreate.getMessage());
        Assertions.assertEquals(1, actualCreate.getFreeSpinsList().size());
        Assertions.assertFalse(actualCreate.getFreeSpinsList().getFirst().isApplied());

        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);
    }

    @Then("^Has canceled batching free spins with isApplied (.+) -peterandsons fs-$")
    public void hasCancelBatchingFreeSpinsResponse(boolean isApplied) {
        var freeSpinsInfoList = getFreeSpinsInfoList(isApplied);
        CancelFreeSpinsResponse expected = new CancelFreeSpinsResponse(bonusCode, freeSpinsInfoList);

        if (!isApplied) {
            expected.setMessage(ERROR_MESSAGE);
        }

        Assertions.assertEquals(expected.getBonusCode(), actualCancelBatching.getBonusCode());
        Assertions.assertEquals(expected.getCode(), actualCancelBatching.getCode());
        Assertions.assertEquals(expected.getMessage(), actualCancelBatching.getMessage());
        Assertions.assertEquals(expected.getFreeSpinsList().size(), actualCancelBatching.getFreeSpinsList().size());

        expected.getFreeSpinsList().forEach(expectedFreeSpin -> {
            Assertions.assertTrue(actualCancelBatching.getFreeSpinsList().stream()
                    .anyMatch(actualFreeSpin -> expectedFreeSpin.getFreeSpinsId().equals(actualFreeSpin.getFreeSpinsId()) &&
                            expectedFreeSpin.isApplied() == actualFreeSpin.isApplied() &&
                            Objects.equals(expectedFreeSpin.getMessage(), actualFreeSpin.getMessage())));
        });

        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);
    }

    @Then("^Has canceled player free spins with playerId (.+) and isApplied (.+) -peterandsons fs-$")
    public void hasCancelPlayerFreeSpinsResponse(String accountId, boolean isApplied) {
        var freeSpin = getFreeSpinsInfoByPlayerId(buildPlayerId(accountId, currency), isApplied);
        CancelPlayerFreeSpinResponse expected = new CancelPlayerFreeSpinResponse(bonusCode, freeSpin);

        if (!isApplied) {
            expected.getFreeSpin().setMessage(ERROR_MESSAGE);
        }

        Assertions.assertEquals(expected.getBonusCode(), actualCancel.getBonusCode());
        Assertions.assertEquals(expected.getCode(), actualCancel.getCode());
        Assertions.assertEquals(expected.getMessage(), actualCancel.getMessage());
        Assertions.assertEquals(expected.getFreeSpin(), actualCancel.getFreeSpin());

        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);
    }

    @When("^Send create free spins by V2 async flow -peterandsons fs-$")
    public void sendCreateV2FreeSpins() throws Exception {
        var request = CreateFreeSpinsRequest.builder()
                .bonusCode(bonusCode)
                .currency(currency)
                .betAmount(betAmount)
                .spins(spins)
                .startDate(startDate)
                .expirationDate(expirationDate)
                .games(List.of(gameCode))
                .requestId(PlatformUtil.randomUUID().toString())
                .playerIdList(playerIdToFreeSpinsIdMap.keySet().stream().toList())
                .build();
        actualCreate = bot.createV2FreeSpins(request, this.requestId);
    }

    @Then("^Has created (.+) free spins by V2 async flow -peterandsons fs-$")
    public void freeSpinExistedAfterAsyncProcessing(int freeSpinsCount) throws InterruptedException {
        awaitCreationFSBeforeCheck(() -> brandRepo.freeSpinCampaignExists(bonusCode, operator.code(), provider.code()) &&
                !brandRepo.accountFreeSpinsByBonusCode(operator.code(), provider.code(), bonusCode).get().isEmpty());

        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);
        checkAccountFreeSpinsInDb(freeSpinsCount);

        Assertions.assertEquals(bonusCode, actualCreate.getBonusCode());
        Assertions.assertEquals(0, actualCreate.getCode());
    }

    @When("^Send create free spin spin campaign request -peterandsons fs-$")
    public void sendCreateFreeSpinSpinCampaignRequest() throws Exception {
        var createFreeSpinCampaignRequest = CreateFreeSpinsCampaignDTO.builder()
                .campaign(bonusCode)
                .currency(currency)
                .betAmount(betAmount)
                .spins(spins)
                .startDate(startDate)
                .expirationDate(expirationDate)
                .requestId(requestId)
                .betLevel(1)
                .operator(operator.code())
                .provider(provider.code())
                .gameCode(gameCode)
                .build();
        bot.createFreeSpinsCampaign(createFreeSpinCampaignRequest, requestId);
    }

    @Then("Has created free spin campaign -peterandsons fs-")
    public void hasCreatedFreeSpinCampaign() {
        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CREATED);
    }

    @When("^Send disable free spin spin campaign request -peterandsons fs-$")
    public void sendDisableFreeSpinSpinCampaignRequest() throws Exception {
        var request = new DisableFreeSpinsCampaignRequest(bonusCode);

        var response = bot.disableFreeSpinsCampaign(request, requestId);
        Assertions.assertEquals(bonusCode, response.getBonusCode());
    }

    @Then("Has disabled free spin campaign -peterandsons fs-")
    public void hasDisabledFreeSpinCampaign() {
        checkFreeSpinBonusInDb(FreeSpinCampaignStatus.CANCELED);
    }

    @When("^Check create free spins is disabled -peterandsons fs-$")
    public void sendCreateFreeSpinsToDisabledCampaign() throws Exception {
        var request = CreateFreeSpinsRequest.builder()
                .bonusCode(bonusCode)
                .currency(currency)
                .betAmount(betAmount)
                .spins(spins)
                .startDate(startDate)
                .expirationDate(expirationDate)
                .games(List.of(gameCode))
                .requestId(PlatformUtil.randomUUID().toString())
                .playerIdList(playerIdToFreeSpinsIdMap.keySet().stream().toList())
                .build();
        actualCreate = bot.createV2FreeSpins(request, this.requestId);

        Assertions.assertNotNull(actualCreate);
        Assertions.assertEquals(FreeSpinsService.FREE_SPINS_CAMPAIGN_CANCELLED.formatted(bonusCode, provider.code()), actualCreate.getMsg());
    }

    @When("^Check create free spins is disabled async -peterandsons fs-$")
    public void sendCreateFreeSpinsToDisabledCampaignAsync() throws Exception {
        var request = CreateFreeSpinsRequest.builder()
                .bonusCode(bonusCode)
                .currency(currency)
                .betAmount(betAmount)
                .spins(spins)
                .startDate(startDate)
                .expirationDate(expirationDate)
                .games(List.of(gameCode))
                .requestId(PlatformUtil.randomUUID().toString())
                .playerIdList(playerIdToFreeSpinsIdMap.keySet().stream().toList())
                .build();
        actualCreate = bot.createV2FreeSpins(request, this.requestId);

        Assertions.assertNotNull(actualCreate);
        Assertions.assertEquals(FreeSpinsService.FREE_SPINS_CAMPAIGN_CANCELLED.formatted(bonusCode, provider.code()), actualCreate.getMsg());
    }

    private void checkAccountFreeSpinsInDb(int freeSpinsCount) {
        var freeSpins = brandRepo.accountFreeSpinsByBonusCode(operator.code(), provider.code(), bonusCode);
        Assertions.assertFalse(freeSpins.isEmpty());
        Assertions.assertEquals(freeSpinsCount, freeSpins.get().size());
    }

    private void checkFreeSpinBonusInDb(FreeSpinCampaignStatus status) {
        var freeSpinCampaign = brandRepo.freeSpinCampaign(bonusCode, operator.code(), provider.code());
        Assertions.assertTrue(freeSpinCampaign.isPresent());
        Assertions.assertEquals(currency, freeSpinCampaign.get().getCurrency());
        Assertions.assertEquals(status.code(), freeSpinCampaign.get().getStatus());
    }

    private List<FreeSpinsInfo> getFreeSpinsInfoList(boolean isApplied) {
        return playerIdToFreeSpinsIdMap.entrySet().stream()
                .map(entry -> FreeSpinsInfo.builder()
                        .isApplied(true)
                        .playerId(entry.getKey())
                        .freeSpinsId(entry.getValue())
                        .isApplied(isApplied)
                        .build())
                .toList();
    }

    private FreeSpinsInfo getFreeSpinsInfoByPlayerId(String playerId, boolean isApplied) {
        return playerIdToFreeSpinsIdMap.entrySet().stream()
                .filter(entry -> entry.getKey().equals(playerId))
                .map(entry -> FreeSpinsInfo.builder()
                        .isApplied(true)
                        .playerId(entry.getKey())
                        .freeSpinsId(entry.getValue())
                        .isApplied(isApplied)
                        .build())
                .findFirst()
                .get();
    }

    private PlainServiceInfo buildPlainServiceInfo() throws Exception {
        return new PlainServiceInfo(
                String.format("%s-%s-%s",
                        AggregatorWildcardUPSs.IRGS_PREFIX, operator.code(), provider.code()),
                new URIBuilder()
                        .setScheme("http")
                        .setHost("localhost")
                        .setPort(MockServer.mockServer.getPort())
                        .setPath(GRAPHQL_API_PATH)
                        .setUserInfo("user-name", "user-password")
                        .setParameters(List.of(
                                new BasicNameValuePair("backoffice-login", LOGIN),
                                new BasicNameValuePair("backoffice-password", PASSWORD)))
                        .build().toString());
    }

    private PlainServiceInfo buildPlainServiceInfoForBot() throws Exception {
        return new PlainServiceInfo(
                String.format("%s-%s", AggregatorWildcardUPSs.IGP_PREFIX, operator.code()),
                new URIBuilder()
                        .setScheme("http")
                        .setHost("localhost")
                        .setPort(props.CLOUD_APP_PORT.get())
                        .setUserInfo(LOGIN, PASSWORD)
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "alpha_numeric")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                        .build().toString());
    }

    private void createMockServer(String requestBody, String responseBody) {
        var httpRequest = HttpRequest.request()
                .withMethod(HttpPost.METHOD_NAME)
                .withPath(GRAPHQL_API_PATH)
                .withHeader(HttpHeaders.AUTHORIZATION, "Bearer " + TOKEN)
                .withBody(requestBody);

        var httpResponse = HttpResponse.response()
                .withStatusCode(200)
                .withContentType(MediaType.APPLICATION_JSON)
                .withDelay(TimeUnit.MICROSECONDS, TestUtils.HTTP_RESPONSE_DELAY);

        if (StringUtils.isNotEmpty(responseBody)) {
            httpResponse.withBody(responseBody);
        }

        Expectation[] expectation = MockServer.mockServer.when(httpRequest, TestUtils.expectation()).respond(httpResponse);
        mockExpectations.addAll(Arrays.stream(expectation).map(Expectation::getId).toList());
    }

    private void createLoginMockServer(String response, int times) throws JsonProcessingException {
        var loginQuery = new LoginQuery(LOGIN, PASSWORD);
        var requestBody = mapper.writeValueAsString(new Query(loginQuery.getQueryString()));

        var httpRequest = HttpRequest.request()
                .withMethod(HttpPost.METHOD_NAME)
                .withPath(GRAPHQL_API_PATH)
                .withBody(requestBody);

        var httpResponse = HttpResponse.response()
                .withStatusCode(200)
                .withContentType(MediaType.APPLICATION_JSON)
                .withDelay(TimeUnit.MICROSECONDS, TestUtils.HTTP_RESPONSE_DELAY);

        if (StringUtils.isNotEmpty(response)) {
            httpResponse.withBody(response);
        }
        Expectation[] expectation = MockServer.mockServer.when(httpRequest, exactly(times)).respond(httpResponse);
        mockExpectations.addAll(Arrays.stream(expectation).map(Expectation::getId).toList());
    }

    @SneakyThrows
    private String getValidBatchResponseJson(String campaignId) {
        return String.format(
                """
                        {
                            "data": {
                                "addCampaign":  "%s"
                            }
                        }
                        """, campaignId);
    }

    @SneakyThrows
    private String getErrorBatchResponseJson() {
        return String.format(
                """
                        {
                            "errors": [
                                {
                                    "message": "%s",
                                    "path": [
                                        "addCampaign"
                                    ]
                                }
                            ],
                            "data": {
                                "addCampaign": null
                            }
                        }
                        """, ERROR_MESSAGE);
    }

    @SneakyThrows
    private String getValidCampaignMutationResponse(String mutationType) {
        return String.format(
                """
                        {
                            "data": {
                                "%s":  %s
                            }
                        }
                        """, mutationType, true);
    }

    @SneakyThrows
    private String getErrorCampaignMutationResponse(String mutationType) {
        return String.format(
                """
                        {
                            "errors": [
                                {
                                    "message": "%s",
                                    "path": [
                                        "%s"
                                    ]
                                }
                            ],
                            "data": {
                                "%s": null
                            }
                        }
                        """, ERROR_MESSAGE, mutationType, mutationType);
    }

    @SneakyThrows
    private String getValidDeleteCampaignResponse() {
        return getValidCampaignMutationResponse(DELETE_CAMPAIGN);
    }

    @SneakyThrows
    private String getErrorDeleteCampaignResponse() {
        return getErrorCampaignMutationResponse(DELETE_CAMPAIGN);
    }

    @SneakyThrows
    private String getValidEditCampaignResponse() {
        return getValidCampaignMutationResponse(EDIT_CAMPAIGN);
    }

    @SneakyThrows
    private String getErrorEditCampaignResponse() {
        return getErrorCampaignMutationResponse(EDIT_CAMPAIGN);
    }

    @SneakyThrows
    private String getValidCampaignsByBonusCodeResponse(String campaignIdList) {
        return String.format(
                """
                        {
                          "data": {
                            "campaigns": {
                              "items": %s
                            }
                          }
                        }
                        """, campaignIdList);
    }

    @SneakyThrows
    private String getValidCampaignsByBonusCodeAndCampaignIdResponse(String campaignIdList) {
        return String.format(
                """
                        {
                          "data": {
                            "campaigns": {
                              "items": %s
                            }
                          }
                        }
                        """, campaignIdList);
    }

    @SneakyThrows
    private String getErrorCampaignsResponse() {
        return String.format(
                """
                        {
                            "errors": [
                                {
                                    "message": "%s",
                                    "path": [
                                        "campaigns"
                                    ]
                                }
                            ],
                            "data": null
                        }
                        """, ERROR_MESSAGE);
    }

    @SneakyThrows
    private String getValidLoginResponseJson() {
        return String.format(
                """
                        {
                            "data": {
                                "login": {
                                    "token": "%s"
                                }
                            }
                        }
                        """, TOKEN);
    }

    @SneakyThrows
    private String getErrorLoginResponseJson() {
        return """
                {
                    "errors": [
                        {
                            "message": "Incorrect email or password",
                            "path": [
                                "login"
                            ]
                        }
                    ],
                    "data": {
                        "login": null
                    }
                }
                """;
    }
}
