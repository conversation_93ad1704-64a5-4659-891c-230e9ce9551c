package integration.playtech.steps;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

import org.apache.http.client.utils.URIBuilder;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.DynamicCloud;

import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.collect.Lists;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.HttpProto;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.bots.DefaultPlaytechBot;
import aggregator.bots.PlaytechBot;
import aggregator.listeners.MockDataGenerator;
import aggregator.model.Account;
import aggregator.model.WalletTransaction;
import aggregator.repo.BrandRepo;
import aggregator.repo.DefaultBrandRepo;
import aggregator.repo.DefaultWalletSessionRepo;
import aggregator.repo.WalletSessionRepo;
import api.v1.ApiFactory;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.flatten.TempTokenFlatten;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.BaseMapper;
import gaming.playtech.Mappers;
import gaming.playtech.b2b.model.AuthenticateRequest;
import gaming.playtech.b2b.model.AuthenticateResponse;
import gaming.playtech.b2b.model.BetRequest;
import gaming.playtech.b2b.model.BetResponse;
import gaming.playtech.b2b.model.BonusInfo;
import gaming.playtech.b2b.model.GameRoundResultRequest;
import gaming.playtech.b2b.model.GameRoundResultResponse;
import gaming.playtech.b2b.model.GetBalanceRequest;
import gaming.playtech.b2b.model.GetBalanceResponse;
import gaming.playtech.b2b.model.LiveTipRequest;
import gaming.playtech.b2b.model.LiveTipResponse;
import gaming.playtech.b2b.model.LogoutRequest;
import gaming.playtech.b2b.model.LogoutResponse;
import gaming.playtech.b2b.model.TransferFundsRequest;
import gaming.playtech.b2b.model.TransferFundsResponse;
import gaming.playtech.b2b.model.common.ErrorCode;
import gaming.playtech.b2b.model.common.GameContext;
import gaming.playtech.b2b.model.common.GameRoundClose;
import gaming.playtech.b2b.model.common.Pay;
import gaming.playtech.b2b.model.common.PayType;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import okhttp3.OkHttpClient;

public class PlaytechIntegrationsSteps {
    @Autowired
    private AggregatorServerProperties props;
    @Autowired
    private DynamicCloud cloud;
    @Autowired
    private SpannerTemplate spannerTemplate;
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private OkHttpClient httpClient;
    private BrandRepo brandRepo;
    private WalletSessionRepo walletSessionRepo;
    private PlaytechBot playtechBot;
    private String currency;
    private String providerCurrency;
    private String gameCode;
    private String roundId;
    private AuthenticateResponse authenticateResponse;
    private BetResponse betResponse;
    private GameRoundResultResponse resultResponse;
    private TransferFundsResponse transferFundsResponse;
    private LiveTipResponse liveTipResponse;
    private LogoutResponse logoutResponse;
    private String lastTransactionIdBet;
    private String lastTransactionIdResult;
    private String lastTransactionIdTransfer;
    private int transactionCount;
    private Account account;
    private AccountRoutingCurrencyFlatten flatten;

    private final List<String> listTxIdBet = Lists.newArrayList();
    private final List<String> listTxIdResult = Lists.newArrayList();
    private final List<String> listTxIdRefund = Lists.newArrayList();

    private PlainServiceInfo si;
    private static final String PROVIDER_SPEC_CODE = ProviderSpec.PLAYTECH.code();
    private static final String OPERATOR_SPEC_CODE = OperatorSpec.BLUEDREAM.code();
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    @Given("^Initialize bootstrap properties -playtech-$")
    public void initializeBootstrapProperties() throws Exception {
        this.si = new PlainServiceInfo(
                String.format("%s-%s", AggregatorWildcardUPSs.IGP_PREFIX, OPERATOR_SPEC_CODE),
                new URIBuilder()
                        .setScheme("http")
                        .setHost("localhost")
                        .setPort(props.CLOUD_APP_PORT.get())
                        .setUserInfo("admin", "changeit")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_ALG, "HmacKECCAK512")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_METHOD, "alpha_numeric")
                        .addParameter(HttpProto.QUERY_PARAM_HMAC_KEY, "d2e91091ac2a6d37522ec92bf11cc1bd")
                        .build().toURL().toExternalForm());
        cloud.addUps(si);
        this.brandRepo = new DefaultBrandRepo(spannerTemplate, cacheManager);
        this.walletSessionRepo = new DefaultWalletSessionRepo(spannerTemplate);
    }

    @Given("^New game with currency (.+) -playtech-$")
    public void newGameWithCurrency(String curr) {
        this.currency = curr;
        this.providerCurrency = Mappers.toProviderCurrency(curr);
        this.playtechBot = createBot();
        this.flatten = genAccountRoutingCurrencyFlatten();
        Assertions.assertNotNull(this.currency);
        Assertions.assertEquals(this.flatten.getCurrency(), this.currency);
    }

    @When("^Enter the game (.+) -playtech-$")
    public void enterTheGame(String gameCode) throws Throwable {
        var playerId = BaseMapper.toPlayerIdWithOperatorShortCode(this.flatten.writeExternal(), OPERATOR_SPEC_CODE);
        sendAuthenticate(gameCode, playerId);
        Assertions.assertEquals(this.providerCurrency, this.authenticateResponse.getCurrencyCode());
    }

    @When("^Authorize with invalid username -playtech-$")
    public void authorizeWithInvalidUsername() throws Throwable {
        sendAuthenticate("invalid_username_game", PlatformUtil.randomUUID().toString());
    }

    @Then("^Has (\\d+(?:\\.\\d+)?) balance -playtech-$")
    public void hasBalance(BigDecimal balance) throws Throwable {
        var getBalanceRequest = new GetBalanceRequest();
        getBalanceRequest.setExternalToken(this.authenticateResponse.getPermanentExternalToken());
        getBalanceRequest.setRequestId(PlatformUtil.randomUUID().toString());
        getBalanceRequest.setUsername(BaseMapper.toPlayerIdWithOperatorShortCode(this.flatten.writeExternal(), OPERATOR_SPEC_CODE));
        GetBalanceResponse response = this.playtechBot.getBalance(getBalanceRequest);

        Assertions.assertEquals(0, balance.compareTo(response.getBalance().getReal()),
                String.format("Expected %.2f but balance %.2f", balance, response.getBalance().getReal()));
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) bet -playtech-$")
    public void sendBet(BigDecimal bet) throws Throwable {
        String transactionId = PlatformUtil.randomUUID().toString();
        sendBet(bet, this.roundId, transactionId);
        this.transactionCount++;
        this.lastTransactionIdBet = transactionId;
        this.listTxIdBet.add(transactionId);

        if (Objects.isNull(this.account)) {
            this.account = this.brandRepo.requiredAccount(MockDataGenerator.OPERATOR, String.valueOf(this.flatten.getAccountId()));
        }

        var wsOpt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), PROVIDER_SPEC_CODE, this.roundId, this.currency);
        Assertions.assertTrue(wsOpt.isPresent());
        var txs = wsOpt.get().getTransactions();
        Assertions.assertEquals(this.transactionCount, txs.size());
        assertDebitWalletTransaction(bet, txs, transactionId);
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) bet using previous transactionId -playtech-$")
    public void sendBetPreviousTxId(BigDecimal bet) throws Throwable {
        sendBet(bet, this.roundId, this.lastTransactionIdBet);
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) bet with transactionId (.+) -playtech-$")
    public void sendBetWithTxId(BigDecimal bet, String transactionId) throws Throwable {
        sendBet(bet, this.roundId, transactionId);
        if (!this.listTxIdBet.contains(transactionId)) {
            this.transactionCount++;
            this.lastTransactionIdBet = transactionId;
            this.listTxIdBet.add(transactionId);

            if (Objects.isNull(this.account)) {
                this.account = this.brandRepo.requiredAccount(MockDataGenerator.OPERATOR, String.valueOf(this.flatten.getAccountId()));
            }

            var wsOpt = walletSessionRepo.walletSessionWithTransactions(this.account.getHash(), PROVIDER_SPEC_CODE, this.roundId, this.currency);
            Assertions.assertTrue(wsOpt.isPresent());
            var txs = wsOpt.get().getTransactions();
            Assertions.assertEquals(this.transactionCount, txs.size());
            assertDebitWalletTransaction(bet, txs, transactionId);
        }
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) win request -playtech-$")
    public void sendWin(BigDecimal win) throws Throwable {
        String transactionId = PlatformUtil.randomUUID().toString();
        sendResult(PayType.WIN, win, this.roundId, transactionId);

        this.transactionCount++;
        this.lastTransactionIdResult = transactionId;
        this.listTxIdResult.add(transactionId);

        var wsOpt = walletSessionRepo.walletSessionWithTransactions(this.account.getHash(),
                PROVIDER_SPEC_CODE, this.roundId, this.currency);
        Assertions.assertTrue(wsOpt.isPresent());
        var txs = wsOpt.get().getTransactions();
        Assertions.assertEquals(this.transactionCount, txs.size());
        assertCreditWalletTransaction(win, txs, transactionId);
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) transfer funds request with transaction id (.+) -playtech-$")
    public void sendTransferFunds(BigDecimal amount, String transactionId) throws Throwable {
        var transferFundsRequest = new TransferFundsRequest();
        transferFundsRequest.setRequestId(PlatformUtil.randomUUID().toString());
        transferFundsRequest.setAmount(amount);
        transferFundsRequest.setUsername(BaseMapper.toPlayerIdWithOperatorShortCode(this.flatten.writeExternal(), OPERATOR_SPEC_CODE));
        transferFundsRequest.setTransactionCode(transactionId);
        transferFundsRequest.setTransactionDate(LocalDateTime.now(ZoneOffset.UTC).format(dateTimeFormatter));
        transferFundsRequest.setType("Bonus");
        var bonusInfo = new BonusInfo();
        bonusInfo.setRemoteBonusCode("campaignId");
        bonusInfo.setBonusInstanceCode("freeSpinId");
        transferFundsRequest.setBonusInfo(bonusInfo);
        this.transferFundsResponse = this.playtechBot.trunsferFunds(transferFundsRequest);

        this.lastTransactionIdTransfer = transactionId;
        this.listTxIdResult.add(transactionId);

        var wsOpt = walletSessionRepo.walletSessionWithTransactions(this.account.getHash(),
                PROVIDER_SPEC_CODE, transactionId, this.currency);
        Assertions.assertTrue(wsOpt.isPresent());
        var txs = wsOpt.get().getTransactions();
        Assertions.assertEquals(1, txs.size());
        assertCreditWalletTransaction(amount, txs, transactionId);
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) live tip -playtech-$")
    public void sendLiveTip(BigDecimal tip) throws Throwable {
        String transactionId = PlatformUtil.randomUUID().toString();
        var liveTipRequest = new LiveTipRequest();
        liveTipRequest.setExternalToken(this.authenticateResponse.getPermanentExternalToken());
        liveTipRequest.setAmount(tip);
        liveTipRequest.setRequestId(PlatformUtil.randomUUID().toString());
        liveTipRequest.setUsername(BaseMapper.toPlayerIdWithOperatorShortCode(this.flatten.writeExternal(), OPERATOR_SPEC_CODE));
        liveTipRequest.setGameCodeName(this.gameCode);
        liveTipRequest.setTransactionCode(transactionId);
        liveTipRequest.setTransactionDate(LocalDateTime.now(ZoneOffset.UTC).format(dateTimeFormatter));
        this.liveTipResponse = this.playtechBot.liveTip(liveTipRequest);

        if (!this.listTxIdBet.contains(transactionId)) {
            this.transactionCount++;
            this.lastTransactionIdBet = transactionId;
            this.listTxIdBet.add(transactionId);

            if (Objects.isNull(this.account)) {
                this.account = this.brandRepo.requiredAccount(MockDataGenerator.OPERATOR, String.valueOf(this.flatten.getAccountId()));
            }

            var wsOpt = walletSessionRepo.walletSessionWithTransactions(this.account.getHash(), PROVIDER_SPEC_CODE, this.roundId, this.currency);
            Assertions.assertTrue(wsOpt.isPresent());
            var txs = wsOpt.get().getTransactions();
            Assertions.assertEquals(this.transactionCount, txs.size());
            assertDebitWalletTransaction(tip, txs, transactionId);
        }
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) win request with transactionId (.+) -playtech-$")
    public void sendWinWithTxId(BigDecimal win, String transactionId) throws Throwable {
        sendResult(PayType.WIN, win, this.roundId, transactionId);
        if (!this.listTxIdResult.contains(transactionId)) {
            this.transactionCount++;
            this.lastTransactionIdResult = transactionId;
            this.listTxIdResult.add(transactionId);

            var wsOpt = walletSessionRepo.walletSessionWithTransactions(this.account.getHash(),
                    PROVIDER_SPEC_CODE, this.roundId, this.currency);
            Assertions.assertTrue(wsOpt.isPresent());
            var txs = wsOpt.get().getTransactions();
            Assertions.assertEquals(this.transactionCount, txs.size());
            assertCreditWalletTransaction(win, txs, transactionId);
        }
    }

    @And("^Send (-\\d+(?:\\.\\d+)?|\\d+(?:\\.\\d+)?) win using previous transactionId -playtech-$")
    public void sendWinWithPreviousTxId(BigDecimal win) throws Throwable {
        sendResult(PayType.WIN, win, this.roundId, this.lastTransactionIdResult);
    }

    @And("^Send game round close request -playtech-$")
    public void sendGameRoundClose() throws Throwable {
        this.transactionCount++;
        var gameRoundClose = new GameRoundClose();
        gameRoundClose.setDate(LocalDateTime.now(ZoneOffset.UTC).format(dateTimeFormatter));
        var resultRequest = new GameRoundResultRequest();
        resultRequest.setRequestId(PlatformUtil.randomUUID().toString());
        resultRequest.setExternalToken(this.authenticateResponse.getPermanentExternalToken());
        resultRequest.setUsername(BaseMapper.toPlayerIdWithOperatorShortCode(this.flatten.writeExternal(), OPERATOR_SPEC_CODE));
        resultRequest.setGameCodeName(this.gameCode);
        resultRequest.setGameRoundCode(roundId);
        resultRequest.setGameRoundClose(gameRoundClose);
        this.resultResponse = this.playtechBot.result(resultRequest);
    }

    @And("^Send bet (\\d+(?:\\.\\d+)?) refund -playtech-$")
    public void sendBetRefund(BigDecimal refund) throws Throwable {
        sendRefund(refund, this.roundId, this.lastTransactionIdBet);
    }

    @And("^Send win (\\d+(?:\\.\\d+)?) refund -playtech-")
    public void sendWinRefund(BigDecimal refund) throws Throwable {
        sendRefund(refund, this.roundId, this.lastTransactionIdResult);
    }

    @And("^Send (\\d+(?:\\.\\d+)?) refund with transactionId (.+) -playtech-$")
    public void sendRefundWithTxId(BigDecimal refund, String transactionId) throws Throwable {
        sendRefund(refund, this.roundId, transactionId);
    }

    @And("^User logout -playtech-$")
    public void logout() throws Throwable {
        var logoutReq = new LogoutRequest();
        logoutReq.setUsername(BaseMapper.toPlayerIdWithOperatorShortCode(this.flatten.writeExternal(), OPERATOR_SPEC_CODE));
        logoutReq.setExternalToken(authenticateResponse.getPermanentExternalToken());
        logoutReq.setRequestId(PlatformUtil.randomUUID().toString());
        this.logoutResponse = this.playtechBot.logout(logoutReq);
    }

    @Then("^Game session after logout is closed -playtech-$")
    public void gameSessionAfterLogoutIsClosed() throws Throwable {
        var getBalanceRequest = new GetBalanceRequest();
        getBalanceRequest.setExternalToken(this.authenticateResponse.getPermanentExternalToken());
        getBalanceRequest.setRequestId(PlatformUtil.randomUUID().toString());
        getBalanceRequest.setUsername(BaseMapper.toPlayerIdWithOperatorShortCode(this.flatten.writeExternal(), OPERATOR_SPEC_CODE));
        GetBalanceResponse response = this.playtechBot.getBalance(getBalanceRequest);

        Assertions.assertEquals(ErrorCode.ERR_AUTHENTICATION_FAILED.toString(), response.getError().getCode());
    }

    @When("^Enter a game with invalid token -playtech-$")
    public void enterTheGameWithInvalidToken() throws Throwable {
        this.roundId = Long.toString(ApiFactory.RANDOM.nextLong());
        var gameContext = new GameContext();
        gameContext.setGameCodeName("gameCode");
        var authenticateRequest = new AuthenticateRequest();
        authenticateRequest.setExternalToken("invalid_token");
        authenticateRequest.setGameContext(gameContext);
        authenticateRequest.setRequestId(PlatformUtil.randomUUID().toString());
        authenticateRequest.setUsername(BaseMapper.toPlayerIdWithOperatorShortCode(this.flatten.writeExternal(), OPERATOR_SPEC_CODE));
        this.authenticateResponse = this.playtechBot.authenticate(authenticateRequest);
    }

    @Then("^Authenticate response has error code: (.+) -playtech-$")
    public void authenticateResponseHasErrorCode(String errorStatusCode) {
        Assertions.assertEquals(errorStatusCode, this.authenticateResponse.getError().getCode());
    }

    @When("^Send bet for the game with not enough money -playtech-$")
    public void sendBetRequestNotEnoughMoney() throws Throwable {
        sendBet(new BigDecimal("11.00"), this.roundId, PlatformUtil.randomUUID().toString());
    }

    @When("^Send negative bet for the game -playtech-$")
    public void sendNegativeBetRequest() throws Throwable {
        sendBet(new BigDecimal("-11.00"), this.roundId, PlatformUtil.randomUUID().toString());
    }

    @When("^Send bet with empty game code -playtech-$")
    public void sendBetRequestWithEmptyGameCode() throws Throwable {
        this.gameCode = "";
        sendBet(new BigDecimal("1.00"), this.roundId, PlatformUtil.randomUUID().toString());
    }

    @When("^Send bet with empty transaction code -playtech-$")
    public void sendBetRequestWithEmptyTransactionCode() throws Throwable {
        sendBet(new BigDecimal("1.00"), this.roundId, "");
    }

    @Then("^Bet response has error code: (.+) -playtech-$")
    public void betResponseHasErrorCode(String errorStatusCode) {
        Assertions.assertEquals(errorStatusCode, this.betResponse.getError().getCode());
    }

    private void sendAuthenticate(String gameCode, String username) throws Exception {
        String token = genTempTokenFlatten(flatten).writeExternal();
        this.roundId = Long.toString(ApiFactory.RANDOM.nextLong());
        var gameContext = new GameContext();
        gameContext.setGameCodeName(gameCode);
        var authenticateRequest = new AuthenticateRequest();
        authenticateRequest.setExternalToken(token);
        authenticateRequest.setGameContext(gameContext);
        authenticateRequest.setRequestId(PlatformUtil.randomUUID().toString());
        authenticateRequest.setUsername(username);
        this.gameCode = gameCode;
        this.authenticateResponse = this.playtechBot.authenticate(authenticateRequest);
    }

    private void sendBet(BigDecimal bet, String roundId, String transactionId) throws Throwable {
        var betRequest = new BetRequest();
        betRequest.setExternalToken(this.authenticateResponse.getPermanentExternalToken());
        betRequest.setAmount(bet);
        betRequest.setRequestId(PlatformUtil.randomUUID().toString());
        betRequest.setUsername(BaseMapper.toPlayerIdWithOperatorShortCode(this.flatten.writeExternal(), OPERATOR_SPEC_CODE));
        betRequest.setGameCodeName(this.gameCode);
        betRequest.setGameRoundCode(roundId);
        betRequest.setTransactionCode(transactionId);
        betRequest.setTransactionDate(LocalDateTime.now(ZoneOffset.UTC).format(dateTimeFormatter));
        this.betResponse = this.playtechBot.bet(betRequest);
    }

    private void sendResult(PayType payType, BigDecimal amount, String roundId, String transactionId) throws Throwable {
        var pay = new Pay();
        pay.setAmount(amount);
        pay.setType(payType);
        pay.setTransactionDate(LocalDateTime.now(ZoneOffset.UTC).format(dateTimeFormatter));
        var resultRequest = new GameRoundResultRequest();
        resultRequest.setRequestId(PlatformUtil.randomUUID().toString());
        if (PayType.WIN.equals(payType)) {
            // no need to have pay model if amount = 0 --> NO WIN CASE
            if (BigDecimal.ZERO.compareTo(amount) == 0) {
                resultRequest.setRequestId(transactionId);
            } else {
                pay.setTransactionCode(transactionId);
                resultRequest.setPay(pay);
            }
        } else if (PayType.REFUND.equals(payType)) {
            pay.setRelatedTransactionCode(transactionId);
            resultRequest.setPay(pay);
        }
        resultRequest.setExternalToken(this.authenticateResponse.getPermanentExternalToken());
        resultRequest.setUsername(BaseMapper.toPlayerIdWithOperatorShortCode(this.flatten.writeExternal(), OPERATOR_SPEC_CODE));
        resultRequest.setGameCodeName(this.gameCode);
        resultRequest.setGameRoundCode(roundId);
        this.resultResponse = this.playtechBot.result(resultRequest);
    }

    private void sendRefund(BigDecimal refund, String roundId, String transactionId) throws Throwable {
        sendResult(PayType.REFUND, refund, roundId, transactionId);
        if (!listTxIdRefund.contains(transactionId)) {
            this.transactionCount++;
            this.listTxIdRefund.add(transactionId);

            var wsOpt = walletSessionRepo.walletSessionWithTransactions(this.account.getHash(),
                    PROVIDER_SPEC_CODE, this.roundId, this.currency);
            Assertions.assertTrue(wsOpt.isPresent());
            var txs = wsOpt.get().getTransactions();
            Assertions.assertEquals(this.transactionCount, txs.size());
            assertRefundWalletTransaction(refund, txs);
        }
    }

    private PlaytechBot createBot() {
        var uriBuilder = new URIBuilder()
                .setScheme("http")
                .setHost("localhost")
                .setPort(props.CLOUD_APP_PORT.get());

        return new DefaultPlaytechBot(httpClient, this.si, uriBuilder, OperatorSpec.BLUEDREAM);
    }

    private static void assertCreditWalletTransaction(BigDecimal win, List<WalletTransaction> txs, String reference) {
        var creditTx = txs.stream().filter(tx -> tx.getReference().equals(reference)).findFirst();
        Assertions.assertTrue(creditTx.isPresent());
        Assertions.assertTrue(creditTx.get().isCredit());
        Assertions.assertEquals(0, win.compareTo(new BigDecimal(creditTx.get().getAmount())));
    }

    private static void assertDebitWalletTransaction(BigDecimal bet, List<WalletTransaction> txs, String reference) {
        var debitTx = txs.stream().filter(tx -> tx.getReference().equals(reference)).findFirst();
        Assertions.assertTrue(debitTx.isPresent());
        Assertions.assertTrue(debitTx.get().isDebit());
        Assertions.assertEquals(0, bet.compareTo(new BigDecimal(debitTx.get().getAmount())));
    }

    private static void assertRefundWalletTransaction(BigDecimal refund, List<WalletTransaction> txs) {
        var refundTx = txs.stream().filter(WalletTransaction::isRefund).findFirst();
        Assertions.assertTrue(refundTx.isPresent());
        Assertions.assertEquals(0, refund.compareTo(new BigDecimal(refundTx.get().getAmount())));
    }

    private AccountRoutingCurrencyFlatten genAccountRoutingCurrencyFlatten() {
        var hash = PlatformUtil.randomAlphanumeric(8);
        var id = String.valueOf(System.currentTimeMillis());
        return AccountRoutingCurrencyFlatten.underscore(hash, id, this.currency);
    }

    private TempTokenFlatten genTempTokenFlatten(AccountRoutingCurrencyFlatten flatten) {
        return TempTokenFlatten.underscore(flatten.getRoutingKey(), flatten.getAccountId(), flatten.getCurrency(), 36);
    }
}
