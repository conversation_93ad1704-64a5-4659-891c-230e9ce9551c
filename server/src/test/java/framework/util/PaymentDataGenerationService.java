package framework.util;

import static payment.model.PaymentMethodVerificationStatusSpec.UNVERIFIED;
import static payment.type.PaymentMethodTypeSpec.SPREEDLY_GATEWAY;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Collections;
import java.util.Date;
import java.util.UUID;
import java.util.function.Consumer;

import org.testcontainers.shaded.org.apache.commons.lang3.RandomStringUtils;

import com.google.common.hash.Hashing;
import com.turbospaces.common.PlatformUtil;

import api.v1.AccountRoutingInfo;
import api.v1.AccountStatusSpec;
import api.v1.ApiFactory;
import api.v1.ForcementModeSpec;
import api.v1.KYCStatusSpec;
import api.v1.PlatformSpec;
import framework.listeners.PaymentBrandDataGenerator;
import framework.listeners.PaymentProviderDataGenerator;
import framework.listeners.WithdrawProviderDataGenerator;
import io.ebean.Transaction;
import payment.PaymentJpaManager;
import payment.PaymentOrderSeq;
import payment.WithdrawMethodInfo;
import payment.card.AchPaymentMethod;
import payment.card.BillingDetails;
import payment.card.CardPaymentMethod;
import payment.card.CryptoPaymentMethod;
import payment.card.ETransferPaymentMethod;
import payment.crypto.BlockchainTransactionData;
import payment.crypto.CryptoCurrency;
import payment.crypto.CryptoNetwork;
import payment.crypto.FeeData;
import payment.crypto.RateData;
import payment.model.AccountPaymentMethod;
import payment.model.AccountPaymentSetting;
import payment.model.AccountPurchaseLimit;
import payment.model.AccountPurchaseLimitPeriodSpec;
import payment.model.AccountWithdrawMethod;
import payment.model.AchAccountTypeSpec;
import payment.model.ChargebackHistory;
import payment.model.ChargebackStatusSpec;
import payment.model.CryptoPurchaseDetail;
import payment.model.CryptoPurchaseInboxNotification;
import payment.model.CurrencyRate;
import payment.model.DailyRedeemLimit;
import payment.model.InboxNotificationStatusSpec;
import payment.model.OfferInboxNotification;
import payment.model.OfferTemplate;
import payment.model.OrderStatusSpec;
import payment.model.PaymentMethodMetaInfo;
import payment.model.PaymentMethodVerificationStatusSpec;
import payment.model.PaymentOrder;
import payment.model.PaymentOrderError;
import payment.model.Provider;
import payment.model.PurchaseLimitReasonSpec;
import payment.model.RedeemStatusSpec;
import payment.model.Secure3dAction;
import payment.model.WithdrawMethodSpec;
import payment.model.WithdrawMoneyRequest;
import payment.model.immutable.ImmutableAccount;
import payment.model.immutable.ImmutableBrand;
import payment.model.util.WithdrawError;
import payment.type.PaymentMethodTypeSpec;
import payment.type.PurchaseProviderSpec;
import payment.type.RedeemProviderSpec;
import payment.withdraws.CryptoWithdrawMethodInfo;
import payment.withdraws.NuveiMazoomaWithdrawMethodInfo;
import payment.withdraws.StandardAchWithdrawMethodInfo;
import uam.api.v1.PaymentProvider;
import uam.api.v1.internal.AccountPaymentRoutingInfo;

public class PaymentDataGenerationService {
    public static ImmutableAccount genAccount(PaymentJpaManager ebean) throws Throwable {
        return genAccount(ebean, "US");
    }

    public static ImmutableAccount genAccountWith3dsRequired(PaymentJpaManager ebean) throws Throwable {
        return genAccountWith3dsRequired(ebean, "US");
    }

    public static ImmutableAccount genAccountWith3dsRequired(PaymentJpaManager ebean, String country) throws Throwable {
        ImmutableAccount account = genAccount(ebean, country);
        getOrCreateAccountPaymentSetting(ebean, account, s -> s.setSecure3dAction(Secure3dAction.FORCE));
        return account;
    }

    public static ImmutableAccount genAccount(PaymentJpaManager ebean, String country) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            String hash = generateHash();
            ImmutableAccount account = createImmutableAccount(ebean, tx, country, hash);
            tx.commit();
            return account;
        }
    }


    public static AccountPurchaseLimit genAccountPurchaseLimit(PaymentJpaManager ebean, ImmutableAccount account, AccountPurchaseLimitPeriodSpec accountPurchaseLimitPeriodSpec, BigDecimal threshold,LocalDate limitEndDate) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            AccountPurchaseLimit accountPurchaseLimit = new AccountPurchaseLimit();
            accountPurchaseLimit.setPeriod(accountPurchaseLimitPeriodSpec);
            accountPurchaseLimit.setAccount(account);
            accountPurchaseLimit.setDays(accountPurchaseLimitPeriodSpec.getDays());
            accountPurchaseLimit.setLimitEnd(limitEndDate);
            accountPurchaseLimit.setReason(PurchaseLimitReasonSpec.MEMBER_REQUEST);
            accountPurchaseLimit.setInactive(false);
            accountPurchaseLimit.setVersion(1);
            accountPurchaseLimit.setThreshold(threshold);
            ebean.save(accountPurchaseLimit, tx);
            tx.commit();
            return accountPurchaseLimit;

        }
    }

    public static DailyRedeemLimit genDailyRedeemLimit(long id, PaymentJpaManager ebean, String state, ImmutableBrand brand, int limit) throws Throwable {
        DailyRedeemLimit dailyRedeemLimit = new DailyRedeemLimit(
                id, brand, state, BigDecimal.valueOf(limit), 1, Date.from(Instant.now()), Date.from(Instant.now()));
        try (Transaction tx = ebean.newTransaction()) {
            ebean.save(dailyRedeemLimit, tx);
            tx.commit();
        }
        return dailyRedeemLimit;
    }

    public static ImmutableAccount genAccountWithInvitedBy(PaymentJpaManager ebean) throws Throwable {
        var account = genAccount(ebean);
        var invitedBy = genAccount(ebean);
        try (Transaction tx = ebean.newTransaction()) {
            account.setInvitedBy(invitedBy);
            ebean.save(account);
            tx.commit();
        }
        return account;
    }

    public static PaymentOrder genSuccessfulOrder(PaymentJpaManager ebean, ImmutableAccount account, OfferTemplate offerTemplate, PaymentProvider provider) throws Throwable {
        return genOrder(ebean,
                provider,
                account,
                offerTemplate,
                PlatformSpec.WEB,
                PlatformUtil.randomUUID().toString(),
                PlatformUtil.randomUUID(),
                true,
                OrderStatusSpec.SUCCESS);
    }

    public static PaymentOrder genSuccessfulOrder(PaymentJpaManager ebean, ImmutableAccount account, OfferTemplate offerTemplate, PaymentProvider provider, LocalDate at) throws Throwable {
        return genOrder(ebean,
                provider,
                account,
                offerTemplate,
                PlatformSpec.WEB,
                PlatformUtil.randomUUID().toString(),
                PlatformUtil.randomUUID(),
                null,
                true,
                offerTemplate.getPrice(),
                OrderStatusSpec.SUCCESS,
                at);
    }

    public static OfferInboxNotification genOfferInboxNotification(PaymentJpaManager ebean, ImmutableAccount account, OfferTemplate offerTemplate) {
        var ntf = new OfferInboxNotification();
        ntf.setOfferTemplate(offerTemplate);
        ntf.setStatus(InboxNotificationStatusSpec.UNREAD);
        ntf.setToken(PlatformUtil.randomUUID());
        ntf.setAccount(account);
        ebean.save(ntf);
        return ntf;
    }

    public static CryptoPurchaseInboxNotification genOfferInboxNotificationCrypto(PaymentJpaManager ebean, ImmutableAccount account, PaymentOrder order) {
        var ntf = new CryptoPurchaseInboxNotification();
        ntf.setStatus(InboxNotificationStatusSpec.UNREAD);
        ntf.setToken(PlatformUtil.randomUUID());
        ntf.setCryptoPaymentOrder(order);
        ntf.setAccount(account);
        ebean.save(ntf);
        return ntf;
    }

    public static PaymentOrder genFailedOrder(
            PaymentJpaManager ebean,
            ImmutableAccount account,
            OfferTemplate offerTemplate,
            PaymentProvider provider) throws Throwable {
        return genOrder(ebean, provider, account, offerTemplate, PlatformSpec.WEB, PlatformUtil.randomUUID().toString(),
                PlatformUtil.randomUUID(), false, OrderStatusSpec.FAILED);
    }

    public static PaymentOrder getNetworkFailedOrder(
            PaymentJpaManager ebean,
            ImmutableAccount account,
            OfferTemplate offerTemplate,
            PaymentProvider provider) throws Throwable {
        var cardNetworkFailed = PaymentOrderError.builder().cardNetworkError("card network failed").build();
        return genOrder(ebean, provider, account, offerTemplate, PlatformSpec.WEB, PlatformUtil.randomUUID().toString(),
                PlatformUtil.randomUUID(), cardNetworkFailed, false, offerTemplate.getPrice(), OrderStatusSpec.FAILED,PlatformUtil.toLocalUTCDate());
    }

    public static PaymentOrder genOrderWithCardPaymentMethod(PaymentJpaManager ebean, ImmutableAccount account, OfferTemplate offerTemplate,
            PaymentProvider provider, String zip, String city) throws Throwable {
        return genOrderWithCardPaymentMethod(ebean, provider, account, offerTemplate, PlatformSpec.WEB, PlatformUtil.randomUUID().toString(),
                PlatformUtil.randomUUID(), true, zip, city);
    }

    public static PaymentOrder genOrderWithCardPaymentMethod(PaymentJpaManager ebean, ImmutableAccount account, OfferTemplate offerTemplate,
            PaymentProvider provider, AccountPaymentMethod paymentMethod) throws Throwable {
        return genOrderWithCardPaymentMethod(ebean, provider, account, offerTemplate, PlatformSpec.WEB, PlatformUtil.randomUUID().toString(),
                PlatformUtil.randomUUID(), true, paymentMethod);
    }

    public static PaymentOrder genRefundedOrder(
            PaymentJpaManager ebean,
            ImmutableAccount account,
            OfferTemplate offerTemplate,
            PaymentProvider provider) throws Throwable {
        var generatedOrder = genOrder(ebean, provider, account, offerTemplate, PlatformSpec.WEB, PlatformUtil.randomUUID().toString(),
                PlatformUtil.randomUUID(), true, OrderStatusSpec.SUCCESS);
        PaymentOrder refundedOrder;
        try (Transaction tx = ebean.newTransaction()) {
            refundedOrder = ebean.paymentRepo().requiredOrder(generatedOrder.getId(), tx);
            refundedOrder.setRefunded(true);
            ebean.save(refundedOrder);
            tx.commit();
        }
        return refundedOrder;
    }

    public static AccountPaymentMethod genCardPaymentMethod(PaymentJpaManager ebean,
            ImmutableAccount account,
            String token,
            PaymentMethodTypeSpec spec) throws Throwable {
        AccountPaymentMethod pm = new AccountPaymentMethod();
        try (Transaction tx = ebean.newTransaction()) {
            pm.setCode(token);
            pm.setRemember(false);
            pm.setType(spec.getCode());
            pm.setFingerprint(PlatformUtil.randomUUID().toString());
            var cardMethod = new CardPaymentMethod();
            cardMethod.setBin("123456");
            cardMethod.setFirstEight("********");
            cardMethod.setLastFour("1234");
            cardMethod.setHolderName("Moby Dick");
            pm.setCardPaymentMethod(cardMethod);
            pm.setAccount(account);

            ebean.save(cardMethod);
            ebean.save(pm);

            tx.commit();
        }
        return pm;
    }

    public static AccountPaymentMethod genCardPaymentMethod(PaymentJpaManager ebean, PaymentOrder order) throws Throwable {
        AccountPaymentMethod pm;
        try (Transaction tx = ebean.newTransaction()) {
            pm = createCardPaymentMethodEntityForOrder(order);
            ebean.save(pm);
            ebean.save(order);
            tx.commit();
        }
        return pm;
    }

    public static AccountPaymentMethod createCardPaymentMethodEntityForOrder(PaymentOrder order) {
        AccountPaymentMethod pm = new AccountPaymentMethod(order.getAccount());
        pm.setCode("code");
        PaymentMethodTypeSpec methodType = PurchaseProviderSpec.fromServerApi(order.getProviderName()).getMethodType();
        pm.setType(methodType.getCode());
        pm.setFingerprint(PlatformUtil.randomUUID().toString());
        var cardMethod = new CardPaymentMethod();
        cardMethod.setBin("123456");
        cardMethod.setLastFour("1234");
        cardMethod.setHolderName("Moby Dick");
        BillingDetails billingAddress = new BillingDetails();
        billingAddress.setAddress1("Address1");
        cardMethod.setBillingAddress(billingAddress);
        pm.setCardPaymentMethod(cardMethod);
        order.setPaymentMethod(pm);
        return pm;
    }

    public static AccountPaymentMethod genSkrillPaymentMethod(PaymentJpaManager ebean, PaymentOrder order) throws Throwable {
        AccountPaymentMethod pm = new AccountPaymentMethod(order.getAccount());
        try (Transaction tx = ebean.newTransaction()) {
            pm.setCode("code");
            pm.setType(PaymentMethodTypeSpec.SKRILL.getCode());
            pm.setFingerprint("<EMAIL>");
            pm.setRemember(true);
            order.setPaymentMethod(pm);
            ebean.save(pm);
            ebean.save(order);
            tx.commit();
        }
        return pm;
    }

    public static AccountPaymentMethod genPurchaseMethod(PaymentJpaManager ebean, ImmutableAccount account,
            PaymentMethodTypeSpec type, String code,
            String fingerprint, boolean remember) throws Throwable {
        var pm = new AccountPaymentMethod(account);
        try (Transaction tx = ebean.newTransaction()) {
            pm.setCode(code);
            pm.setType(type.code());
            pm.setFingerprint(fingerprint);
            pm.setRemember(remember);
            ebean.save(pm);
            tx.commit();
        }
        return pm;
    }

    public static WithdrawMoneyRequest genWithdraw(ImmutableAccount account, PaymentJpaManager ebean) throws Throwable {
        return genWithdraw(account, ebean, RedeemStatusSpec.PRE_AUTHORIZED);
    }

    public static PaymentOrder genOrder(PaymentJpaManager ebean, PaymentProvider provider, ImmutableAccount account,
            OfferTemplate offerTemplate, PlatformSpec platformSpec, String code, UUID transactionId,
            boolean isSuccess, OrderStatusSpec status) throws Throwable {
        return genOrder(ebean, provider, account, offerTemplate, platformSpec, code, transactionId, null, isSuccess, offerTemplate.getPrice(), status, PlatformUtil.toLocalUTCDate());
    }

    public static PaymentOrder genDepositOrder(PaymentJpaManager ebean, PaymentProvider provider,
            ImmutableAccount account, PlatformSpec platformSpec, String code,
            UUID transactionId, PaymentOrderError orderError, boolean isSuccess,
            BigDecimal amount, OrderStatusSpec status) throws Throwable {
        return genOrder(ebean, provider, account, null, platformSpec, code, transactionId, orderError, isSuccess, amount, status, PlatformUtil.toLocalUTCDate());
    }

    public static PaymentOrder genDepositOrder(PaymentJpaManager ebean, PaymentProvider provider,
                                               ImmutableAccount account, PlatformSpec platformSpec, String code,
                                               UUID transactionId, PaymentOrderError orderError, boolean isSuccess,
                                               BigDecimal amount, OrderStatusSpec status, LocalDate at) throws Throwable {
        return genOrder(ebean, provider, account, null, platformSpec, code, transactionId, orderError, isSuccess, amount, status, at);
    }

    public static PaymentOrder genOrder(PaymentJpaManager ebean, PaymentProvider provider, ImmutableAccount account,
            OfferTemplate offerTemplate, PlatformSpec platformSpec, String code, UUID transactionId,
            PaymentOrderError orderError, boolean isSuccess, BigDecimal amount, OrderStatusSpec status, LocalDate at) throws Throwable {
        PaymentOrder paymentOrder = new PaymentOrder();
        try (Transaction tx = ebean.newTransaction()) {
            paymentOrder.setId((long) ebean.idGenerator(PaymentOrderSeq.class).nextId(tx));
            Provider paymentProvider = PaymentProviderDataGenerator.getPaymentProvider(ebean, provider);
            paymentOrder.setProvider(paymentProvider);
            paymentOrder.setCode(code);
            paymentOrder.setOrderSn(PlatformUtil.randomUUID().toString());
            paymentOrder.setTransactionId(transactionId);
            paymentOrder.setAccount(account);
            paymentOrder.setAmount(amount);
            paymentOrder.setInitialAmount(amount);
            paymentOrder.setCurrency("USD");
            paymentOrder.setBaseAmount(offerTemplate != null ? offerTemplate.getPrice() : amount);
            paymentOrder.setInitialBaseAmount(paymentOrder.getBaseAmount());
            paymentOrder.setUserAgent("User Agent");
            paymentOrder.setPlatform(platformSpec);
            paymentOrder.setDescription(offerTemplate != null ? offerTemplate.getTitle() : "deposit");
            paymentOrder.setOffer(offerTemplate);
            paymentOrder.setSuccess(isSuccess);
            paymentOrder.setInternalStatus(status);
            if (offerTemplate != null) {
                paymentOrder.setGcAmount(offerTemplate.getGoldAmount());
                paymentOrder.setScAmount(offerTemplate.sweepstakeMoneyAmount());

                paymentOrder.setInitialGcAmount(offerTemplate.getGoldAmount());
                paymentOrder.setInitialScAmount(offerTemplate.sweepstakeMoneyAmount());
            }
            paymentOrder.setRemoteIp("127.0.0.1");
            paymentOrder.setAt(at);
            paymentOrder.setFraudRequestId(PlatformUtil.randomUUID().toString());
            paymentOrder.setError(orderError);
            ebean.save(paymentOrder);
            tx.commit();
        }
        return paymentOrder;
    }

    public static CryptoPurchaseDetail genCryptoPurchaseDetail() {
        var det = new CryptoPurchaseDetail();
        det.setKytAlertLevel("unknown");
        det.setKytStatus("no_review");
        det.setSourceAmount(BigDecimal.TEN);
        det.setSourceCurrency(CryptoCurrency.USDT);
        det.setSourceNetwork(CryptoNetwork.ETH);
        det.setPaidAmountInBaseCurrency(BigDecimal.TEN);
        det.setPaidAmountInLocalCurrency(BigDecimal.TEN);
        det.setPaidAmount(new BigDecimal("10.77847000000"));
        det.setSourceAmount(new BigDecimal("13.654684787"));
        det.setStatus("paid");
        det.setBlockChains(Collections.singletonList(new BlockchainTransactionData(
                "0xc419f2ef97c67589e653824415462b70911978325b5c2b14d9d4bb9a3ca98037",
                "******************************************",
                "******************************************")));
        det.setFees(Collections.singletonList(new FeeData(BigDecimal.ONE, "USD")));
        det.setRates(Collections.singletonList(new RateData(BigDecimal.ONE, "TST_USD")));
        return det;
    }

    public static PaymentOrder genOrderWithCardPaymentMethod(
            PaymentJpaManager ebean,
            PaymentProvider provider,
            ImmutableAccount account,
            OfferTemplate offerTemplate,
            PlatformSpec platformSpec,
            String code,
            UUID transactionId,
            boolean isSuccess,
            String zip,
            String city) throws Throwable {
        PaymentOrder paymentOrder = new PaymentOrder();
        Provider paymentProvider = PaymentProviderDataGenerator.getPaymentProvider(ebean, provider);
        paymentOrder.setProvider(paymentProvider);
        paymentOrder.setAccount(account);
        AccountPaymentMethod paymentMethod = createCardPaymentMethodEntityForOrder(paymentOrder);
        BillingDetails billingAddress = new BillingDetails();
        billingAddress.setCity(city);
        billingAddress.setZip(zip);
        CardPaymentMethod cardPaymentMethod = paymentMethod.getCardPaymentMethod();
        cardPaymentMethod.setBillingAddress(billingAddress);
        paymentMethod.setCardPaymentMethod(cardPaymentMethod);
        return genOrderWithCardPaymentMethod(ebean, provider, account, offerTemplate, platformSpec, code, transactionId, isSuccess, paymentOrder);
    }

    public static PaymentOrder genOrderWithCardPaymentMethod(
            PaymentJpaManager ebean,
            PaymentProvider provider,
            ImmutableAccount account,
            OfferTemplate offerTemplate,
            PlatformSpec platformSpec,
            String code,
            UUID transactionId,
            boolean isSuccess,
            AccountPaymentMethod paymentMethod) throws Throwable {
        PaymentOrder paymentOrder = new PaymentOrder();
        paymentOrder.setPaymentMethod(paymentMethod);
        return genOrderWithCardPaymentMethod(ebean, provider, account, offerTemplate, platformSpec, code, transactionId, isSuccess, paymentOrder);
    }

    private static PaymentOrder genOrderWithCardPaymentMethod(
            PaymentJpaManager ebean,
            PaymentProvider provider,
            ImmutableAccount account,
            OfferTemplate offerTemplate,
            PlatformSpec platformSpec,
            String code,
            UUID transactionId,
            boolean isSuccess,
            PaymentOrder paymentOrder) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            Provider paymentProvider = PaymentProviderDataGenerator.getPaymentProvider(ebean, provider);
            paymentOrder.setId((long) ebean.idGenerator(PaymentOrderSeq.class).nextId(tx));
            paymentOrder.setProvider(paymentProvider);
            paymentOrder.setCode(code);
            paymentOrder.setOrderSn(PlatformUtil.randomUUID().toString());
            paymentOrder.setTransactionId(transactionId);
            paymentOrder.setAccount(account);
            paymentOrder.setAmount(offerTemplate.getPrice());
            paymentOrder.setBaseAmount(offerTemplate.getPrice());

            paymentOrder.setInitialBaseAmount(paymentOrder.getBaseAmount());
            paymentOrder.setInitialAmount(paymentOrder.getAmount());

            paymentOrder.setGcAmount(offerTemplate.getGoldAmount());
            paymentOrder.setScAmount(offerTemplate.sweepstakeMoneyAmount());

            paymentOrder.setInitialGcAmount(offerTemplate.getGoldAmount());
            paymentOrder.setInitialScAmount(offerTemplate.sweepstakeMoneyAmount());

            paymentOrder.setCurrency("USD");
            paymentOrder.setUserAgent("User Agent");
            paymentOrder.setPlatform(platformSpec);
            paymentOrder.setDescription(offerTemplate.getTitle());
            paymentOrder.setOffer(offerTemplate);
            paymentOrder.setSuccess(isSuccess);
            paymentOrder.setInternalStatus(PaymentOrder.resolveInternalStatus(isSuccess));
            paymentOrder.setRemoteIp("127.0.0.1");
            paymentOrder.setAt(PlatformUtil.toLocalUTCDate());
            paymentOrder.setFraudRequestId(PlatformUtil.randomUUID().toString());
            ebean.save(paymentOrder.getPaymentMethod());
            ebean.save(paymentOrder);
            tx.commit();
        }
        return paymentOrder;
    }

    public static void genUnverifiedPaymentMethodMetaInfo(PaymentJpaManager ebean, ImmutableAccount account,
            String fingerPrint, PaymentMethodVerificationStatusSpec status) {
        var metaInfo = PaymentMethodMetaInfo.builder()
                .account(account)
                .type(SPREEDLY_GATEWAY.getCode())
                .fingerprint(fingerPrint)
                .verificationStatus(status)
                .build();
        ebean.save(metaInfo);
    }

    public static WithdrawMoneyRequest getWithdrawMonetReqById(PaymentJpaManager ebean, long id) {
        return ebean.find(WithdrawMoneyRequest.class).where().eq("id", id).findOne();
    }

    public static WithdrawMoneyRequest genLockedWithdrawMoneyReq(PaymentJpaManager ebean, ImmutableAccount account) throws Throwable {
        return genWithdrawMoneyReq(ebean, account, RedeemStatusSpec.LOCKED, RedeemStatusSpec.LOCKED);
    }

    public static WithdrawMoneyRequest genNewWithdrawMoneyReq(PaymentJpaManager ebean, ImmutableAccount account) throws Throwable {
        return genWithdrawMoneyReq(ebean, account, RedeemStatusSpec.NEW, RedeemStatusSpec.NEW);
    }

    public static WithdrawMoneyRequest genDeclinedWithdrawMoneyReq(PaymentJpaManager ebean, ImmutableAccount account) throws Throwable {
        return genWithdrawMoneyReq(ebean, account, RedeemStatusSpec.DECLINED, RedeemStatusSpec.DECLINED);
    }

    public static WithdrawMoneyRequest genCancelledWithdrawMoneyReq(PaymentJpaManager ebean, ImmutableAccount account) throws Throwable {
        return genWithdrawMoneyReq(ebean, account, RedeemStatusSpec.CANCELLED, RedeemStatusSpec.CANCELLED);
    }

    public static WithdrawMoneyRequest genConfirmedWithdrawMoneyReqWithStatus(PaymentJpaManager ebean, ImmutableAccount account, RedeemStatusSpec status,
            RedeemStatusSpec uamStatus, Date date) throws Throwable {

        var result = genWithdrawMoneyReq(ebean, account, status, uamStatus);
        try (Transaction tx = ebean.newTransaction()) {
            result.setPreConfirmedAt(date);
            ebean.save(result, tx);
            tx.commit();
        }
        return result;
    }

    private static WithdrawMoneyRequest genWithdrawMoneyReq(PaymentJpaManager ebean, ImmutableAccount account, RedeemStatusSpec status,
            RedeemStatusSpec uamStatus) throws Throwable {

        var result = genWithdraw(account, ebean, status);
        try (Transaction tx = ebean.newTransaction()) {
            result.setUamStatus(uamStatus);
            ebean.save(result, tx);
            tx.commit();
        }
        return result;
    }

    public static WithdrawMoneyRequest genWithdraw(ImmutableAccount account, PaymentJpaManager ebean, RedeemStatusSpec status) throws Throwable {
        return genWithdraw(account, ebean, status, BigDecimal.ONE, BigDecimal.ONE, account.getBrand().getSweepstakeCurrency());
    }

    public static WithdrawMoneyRequest genFiatWithdraw(ImmutableAccount account, PaymentJpaManager ebean, RedeemStatusSpec status) throws Throwable {
        return genWithdraw(account, ebean, status, BigDecimal.TEN, BigDecimal.ONE, account.getBrand().getFiatCurrency());
    }

    public static WithdrawMoneyRequest genWithdraw(ImmutableAccount account, PaymentJpaManager ebean,
            RedeemStatusSpec status, BigDecimal amount) throws Throwable {
        return genWithdraw(account, ebean, status, amount, amount, account.getBrand().getSweepstakeCurrency());
    }

    public static WithdrawMoneyRequest genWithdraw(ImmutableAccount account, PaymentJpaManager ebean,
            RedeemStatusSpec status, BigDecimal amount, BigDecimal baseAmount, String currency) throws Throwable {
        return genWithdraw(account, ebean, status, amount, baseAmount, currency, RedeemProviderSpec.NUVEI_MAZOOMA_ACH.name().toLowerCase());
    }

    public static WithdrawMoneyRequest genWithdraw(ImmutableAccount account, PaymentJpaManager ebean,
            RedeemStatusSpec status, BigDecimal amount, BigDecimal baseAmount, String currency, String provider) throws Throwable {
        WithdrawMoneyRequest wmr = new WithdrawMoneyRequest();
        try (Transaction tx = ebean.newTransaction()) {
            Provider paymentProvider = WithdrawProviderDataGenerator.getWithdrawProvider(ebean, RedeemProviderSpec.fromString(provider));
            wmr.setProvider(paymentProvider);
            AccountWithdrawMethod method = ebean.withdrawMethodRepo()
                    .lastMethod(account, WithdrawMethodSpec.fromString(provider), tx)
                    .orElseGet(() -> {
                        var m = new AccountWithdrawMethod();
                        m.setAccount(account);
                        m.setCode(provider);
                        m.setType(WithdrawMethodSpec.fromString(provider));
                        var methodInfo = new NuveiMazoomaWithdrawMethodInfo();
                        methodInfo.setFiAccountNumber(PlatformUtil.randomUUID().toString());
                        m.setMethod(methodInfo);
                        ebean.save(m, tx);
                        return m;
                    });

            wmr.setMethod(method);
            wmr.setCode(PlatformUtil.randomUUID().toString());
            wmr.setTransactionId(PlatformUtil.randomUUID());
            wmr.setAccount(account);
            wmr.setCurrency(currency);
            wmr.setLocalCurrency("USD");
            wmr.setStatus(status);
            wmr.setUamStatus(status);
            wmr.setAmount(amount);
            wmr.setBaseAmount(baseAmount);
            wmr.setAt(PlatformUtil.toLocalUTCDate());
            wmr.setManualInterventionNeeded(true);
            ebean.save(wmr, tx);
            tx.commit();
        }
        return wmr;
    }

    public static WithdrawMoneyRequest genWithdraw(PaymentJpaManager ebean, ImmutableAccount account,
            RedeemProviderSpec provider, AccountWithdrawMethod method,
            RedeemStatusSpec status, BigDecimal amount) throws Throwable {
        return genWithdraw(ebean, account, provider, method, status, amount, null);
    }

    public static WithdrawMoneyRequest genWithdraw(PaymentJpaManager ebean, ImmutableAccount account,
                                                   RedeemProviderSpec provider, AccountWithdrawMethod method,
                                                   RedeemStatusSpec status, BigDecimal amount, WithdrawError error) throws Throwable {
        WithdrawMoneyRequest wmr = new WithdrawMoneyRequest();
        try (Transaction tx = ebean.newTransaction()) {
            Provider paymentProvider = WithdrawProviderDataGenerator.getWithdrawProvider(ebean, provider);
            wmr.setProvider(paymentProvider);
            wmr.setMethod(method);
            wmr.setCode(PlatformUtil.randomUUID().toString());
            wmr.setTransactionId(PlatformUtil.randomUUID());
            wmr.setAccount(account);
            wmr.setCurrency(account.getBrand().getSweepstakeCurrency());
            wmr.setStatus(status);
            wmr.setUamStatus(status);
            wmr.setAmount(amount);
            wmr.setBaseAmount(amount);
            wmr.setEmail(account.getEmail());
            wmr.setAt(PlatformUtil.toLocalUTCDate());
            wmr.setError(error);
            ebean.save(wmr, tx);
            tx.commit();
        }
        return wmr;
    }

    public static AccountPaymentSetting getOrCreateAccountPaymentSetting(PaymentJpaManager ebean, ImmutableAccount account,
            Consumer<AccountPaymentSetting> enhancer) {
        return ebean.paymentRepo().getAccountPaymentSetting(account.getId(), null).orElseGet(() -> {
            AccountPaymentSetting ps = new AccountPaymentSetting();
            ps.setAccount(account);
            enhancer.accept(ps);
            ebean.paymentRepo().saveAccountPaymentSetting(ps, null);
            return ps;
        });
    }

    public static AccountWithdrawMethod genWithdrawMethod(PaymentJpaManager ebean, ImmutableAccount account,
            WithdrawMethodSpec type, WithdrawMethodInfo methodInfo, String code, boolean remember) throws Throwable {

        try (Transaction tx = ebean.newTransaction()) {
            var method = new AccountWithdrawMethod(account, false);
            method.setType(type);
            method.setCode(code);
            method.setRemember(remember);
            method.setMethod(methodInfo);
            ebean.save(method);
            tx.commit();
            return method;
        }
    }

    public static void addCurrencyRate(PaymentJpaManager ebean, String curr, double v) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            CurrencyRate currencyRate = CurrencyRate.builder()
                    .brand(PaymentBrandDataGenerator.getBrand(ebean))
                    .currencyCode(curr)
                    .rate(new BigDecimal(v))
                    .modifiedBy("test")
                    .build();
            ebean.save(currencyRate);
            tx.commit();
        }
    }

    public static AccountPaymentMethod createCardPaymentMethod(PaymentJpaManager ebean, ImmutableAccount account, String bin, String lastFour, String code,
            boolean remember, String fingerprint) throws Throwable {
        return createPaymentMethod(ebean, account, bin, lastFour, code, remember, fingerprint, PaymentMethodTypeSpec.SPREEDLY_GATEWAY);
    }

    public static AccountPaymentMethod createGpPaymentMethod(PaymentJpaManager ebean, ImmutableAccount account, String bin, String lastFour, String code,
            boolean remember, String fingerprint) throws Throwable {
        return createPaymentMethod(ebean, account, bin, lastFour, code, remember, fingerprint, PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY);
    }

    public static AccountPaymentMethod createPaymentMethod(PaymentJpaManager ebean, ImmutableAccount account, String bin, String lastFour, String code,
            boolean remember, String fingerprint, PaymentMethodTypeSpec type) throws Throwable {
        AccountPaymentMethod paymentMethod = new AccountPaymentMethod();
        try (Transaction tx = ebean.newTransaction()) {
            paymentMethod.setType(type.getCode());
            paymentMethod.setCode(code);
            paymentMethod.setAccount(account);
            paymentMethod.setRemember(remember);
            CardPaymentMethod cardPaymentMethod = new CardPaymentMethod();
            cardPaymentMethod.setBin(bin.substring(0, Math.min(bin.length(), 6)));
            cardPaymentMethod.setFirstEight(bin);
            cardPaymentMethod.setLastFour(lastFour);
            paymentMethod.setCardPaymentMethod(cardPaymentMethod);
            paymentMethod.setFingerprint(fingerprint);
            ebean.save(paymentMethod);
            tx.commit();
        }
        return paymentMethod;
    }

    public static AccountPaymentMethod createCryptoPaymentMethod(PaymentJpaManager ebean, PaymentOrder orderWithCryptoDetails) throws Throwable {
        return createCryptoPaymentMethod(ebean, orderWithCryptoDetails.getAccount(), orderWithCryptoDetails.getCryptoPurchaseDetail().getSourceCurrency(),
                orderWithCryptoDetails.getCryptoPurchaseDetail().getSourceNetwork(), true);
    }

    public static AccountPaymentMethod createCryptoPaymentMethod(PaymentJpaManager ebean, ImmutableAccount account, CryptoCurrency curr, CryptoNetwork ntw,
            boolean remember) throws Throwable {
        AccountPaymentMethod paymentMethod = new AccountPaymentMethod();
        try (Transaction tx = ebean.newTransaction()) {
            paymentMethod.setType(PaymentMethodTypeSpec.CRYPTO.getCode());
            paymentMethod.setCode("CRYPTO-" + curr + ":" + ntw + "t245ty92435");
            paymentMethod.setAccount(account);
            paymentMethod.setRemember(remember);
            CryptoPaymentMethod method = new CryptoPaymentMethod();
            method.setWallet("******************************************");
            method.setNetwork(ntw);
            method.setCurrency(curr);

            paymentMethod.setFingerprint(method.getWallet());
            paymentMethod.setCryptoPaymentMethod(method);
            ebean.save(paymentMethod);
            tx.commit();
        }
        return paymentMethod;
    }

    public static AccountWithdrawMethod genStandardAchMethod(PaymentJpaManager ebean, ImmutableAccount account,
            String code, AchAccountTypeSpec accountType) {
        var method = new AccountWithdrawMethod();
        method.setAccount(account);
        method.setCode(code);
        method.setType(WithdrawMethodSpec.STANDARD_ACH);
        var methodInfo = new StandardAchWithdrawMethodInfo();
        methodInfo.setBankAccountNumber(PlatformUtil.randomUUID().toString());
        methodInfo.setBankAccountName(PlatformUtil.randomUUID().toString());
        methodInfo.setBankAccountType(accountType.name().toLowerCase());
        method.setMethod(methodInfo);
        ebean.save(method);
        return method;
    }

    public static AccountWithdrawMethod genCryptoMethod(PaymentJpaManager ebean, ImmutableAccount account) {
        var mi = new CryptoWithdrawMethodInfo();
        mi.setNetwork(CryptoNetwork.TETH);
        mi.setCurrency(CryptoCurrency.TST);
        mi.setWallet("******************************************");

        var method = new AccountWithdrawMethod();
        method.setAccount(account);
        method.setCode(mi.toCode());
        method.setType(WithdrawMethodSpec.CRYPTO);
        method.setMethod(mi);

        ebean.save(method);
        return method;
    }

    private static ImmutableAccount createImmutableAccount(PaymentJpaManager ebean, Transaction tx, String country,  String hash)
            throws Exception {
        ImmutableAccount account = new ImmutableAccount();
        account.setBrand(ebean.brandRepo().requiredBrandByName(PaymentBrandDataGenerator.TEST_BRAND_CODE, tx));
        account.setUsername("userName");
        account.setEmail("test" + hash + "@gmail.com");
        account.setRealEmail(account.getEmail());
        account.setKyc(KYCStatusSpec.CONFIRMED);
        account.setHash(hash);
        account.setMode(ForcementModeSpec.SWEEPSTAKE);
        account.setStatus(AccountStatusSpec.DEFAULT);
        account.setEmailVerified(true);
        account.setCountry(country);
        account.setCustomerId(RandomStringUtils.randomAlphabetic(6));
        ebean.save(account, tx);
        return account;
    }

    private static String generateHash() {
        return Hashing.murmur3_32_fixed().hashBytes(PlatformUtil.randomUUID().toString().getBytes()).toString();
    }

    public static PaymentMethodMetaInfo genPaymentMethodMetaInfo(PaymentJpaManager ebean, ImmutableAccount account, String fingerPrint) {
        var meta = PaymentMethodMetaInfo.builder()
                .account(account)
                .type(SPREEDLY_GATEWAY.getCode())
                .fingerprint(fingerPrint)
                .cardBin("123456")
                .lastFour("7890")
                .verificationStatus(UNVERIFIED)
                .build();
        ebean.save(meta);
        return meta;
    }

    public static AccountPaymentMethod genAchPaymentMethod(PaymentJpaManager ebean, PaymentOrder order,
            PaymentMethodTypeSpec type, String code, String fingerprint,
            AchPaymentMethod achDetails) {
        var pm = new AccountPaymentMethod(order.getAccount());
        pm.setType(type.code());
        pm.setCode(code);
        pm.setFingerprint(fingerprint);
        pm.setAchPaymentMethod(achDetails);
        order.setPaymentMethod(pm);
        ebean.save(pm);
        ebean.save(order);
        return pm;
    }

    public static AccountPaymentMethod genETransferPaymentMethod(PaymentJpaManager ebean, PaymentOrder order,
            PaymentMethodTypeSpec type, String code, String fingerprint,
            ETransferPaymentMethod eTransferPaymentMethod) {
        var pm = new AccountPaymentMethod(order.getAccount());
        pm.setType(type.code());
        pm.setCode(code);
        pm.setFingerprint(fingerprint);
        pm.setETransferPaymentMethod(eTransferPaymentMethod);
        order.setPaymentMethod(pm);
        ebean.save(pm);
        ebean.save(order);
        return pm;
    }

    public static AccountRoutingInfo getAccountRoutingInfo(ImmutableAccount account) {
        return AccountRoutingInfo.newBuilder()
                .setHash(account.getHash())
                .setId(account.getId())
                .setCountry(account.getCountry())
                .setRemoteIp("127.0.0.1")
                .setBrand(account.getBrand().getName())
                .build();
    }

    public static AccountPaymentRoutingInfo getAccountPaymentRoutingInfo(ImmutableAccount account) {
        return AccountPaymentRoutingInfo.newBuilder()
                .setHash(account.getHash())
                .setId(account.getId())
                .setCountry(account.getCountry())
                .setRemoteIp("127.0.0.1")
                .setBrand(account.getBrand().getName())
                .build();
    }

    public static void genFraudResponseId(PaymentJpaManager ebean, PaymentOrder order) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            order.setFraudResponseId(ApiFactory.RANDOM.nextLong());
            ebean.save(order);
            tx.commit();
        }
    }

    public static PaymentOrder genChargeback(PaymentJpaManager ebean, ImmutableAccount account, OfferTemplate offerTemplate,
            PaymentProvider provider, ChargebackStatusSpec status) throws Throwable {
        var order = genSuccessfulOrder(ebean, account, offerTemplate, provider);
        order.setChargebackAt(LocalDate.now());
        order.setOriginalChargebackAt(LocalDate.now());
        order.setChargebackStatus(status);
        ebean.save(order);
        genChargebackHistory(ebean, order);
        return order;
    }

    private static void genChargebackHistory(PaymentJpaManager ebean, PaymentOrder chargeback) {
        var history = new ChargebackHistory();
        history.setStatus(chargeback.getChargebackStatus());
        history.setDate(new Date());
        history.setOrder(chargeback);
        ebean.save(history);
    }

}
