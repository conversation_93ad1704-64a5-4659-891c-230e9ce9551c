package payment.withdraw;

import static framework.util.Mapper.toAccountInfoCtx;
import static framework.util.PaymentDataGenerationService.getOrCreateAccountPaymentSetting;
import static framework.util.TestOperationUtil.createRedeemMoneyRequest;
import static framework.util.TestOperationUtil.getAccountPersonalInfo;
import static framework.util.TestOperationUtil.mockOnWithdrawRequest;
import static framework.util.TestOperationUtil.mockRedeemMoneyEvent;
import static framework.util.TestOperationUtil.mockSendFraudCheckWithFraudResponseId;
import static framework.util.TestOperationUtil.onWithdraw;
import static framework.util.TestOperationUtil.preparePreConfirmReq;
import static framework.util.TestOperationUtil.sendFraudProviderCtx;
import static framework.util.TestOperationUtil.setAccountInfo;
import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static payment.model.PaymentMethodVerificationStatusSpec.REQUIRE_VERIFICATION;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Date;
import java.util.Map;
import java.util.Set;

import org.apache.commons.math3.random.RandomDataGenerator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.TransactionalRequest;
import com.turbospaces.resteasy.MockClientResponse;
import com.turbospaces.rpc.QueuePostTemplate;
import com.turbospaces.ups.PlainServiceInfo;

import api.facade.DefaultResponseStatusFacade;
import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.Status;
import di.PaymentInMemoryDatabaseDiModule;
import di.mock.MockCommonDiModule;
import di.service.MockExternalApiDiModule;
import framework.PaymentSpringBootTestContextBootstrapper;
import framework.listeners.PaymentBrandDataGenerator;
import framework.listeners.WithdrawProviderDataGenerator;
import framework.util.IdentityUtil;
import framework.util.Mapper;
import framework.util.PaymentDataGenerationService;
import framework.util.Responses;
import framework.util.TestOperationUtil;
import fraud.api.FraudServiceApi;
import fraud.api.v1.SendFraudProviderCtxResponse;
import identity.EmptyGeoLocatorManager;
import identity.ExternalUserSession;
import io.ebean.Transaction;
import io.netty.util.AsciiString;
import payment.CommonMockTest;
import payment.PaymentIdentityManager;
import payment.api.PaymentServiceApi;
import payment.cloudflaregatewayhook.CloudflareGatewayHookService;
import payment.context.account.AccountInfoContext;
import payment.di.CommonPaymentDiModule;
import payment.di.PaymentRedeemDiModule;
import payment.di.PaymentServicesDiModule;
import payment.dto.DailyStateRemainsLimit;
import payment.handlers.modify.withdraw.PreConfirmRedeemMoneyRequestHandler;
import payment.handlers.modify.withdraw.RedeemMoneyRequestHandler;
import payment.handlers.modify.withdraw.SetRedeemProcessingDataHandler;
import payment.jobs.CancelWithdrawJob;
import payment.model.AccountProviderBlackList;
import payment.model.RedeemStatusSpec;
import payment.model.WithdrawMethodSpec;
import payment.model.WithdrawMoneyRequest;
import payment.model.immutable.ImmutableAccount;
import payment.model.util.WithdrawError;
import payment.prizeout.DefaultPrizeoutService;
import payment.prizeout.PrizeoutApi;
import payment.prizeout.PrizeoutApiJaxRsClient;
import payment.prizeout.b2b.PrizeoutRejectRequest;
import payment.quartz.PaymentQuartzFactoryBean;
import payment.services.CommonWithdrawService;
import payment.services.WithdrawLimitService;
import payment.services.WithdrawalStatusCalcType;
import payment.services.ctx.redeem.DefaultWithdrawContext;
import payment.skrill.DefaultSkrillService;
import payment.util.WithdrawErrorType;
import uam.api.UamServiceApi;
import uam.api.v1.Identity;
import uam.api.v1.OnWithdrawResponse;
import uam.api.v1.RedeemMoneyRequest;
import uam.api.v1.RedeemMoneyResponse;
import uam.api.v1.SetAccountInfoResponse;
import uam.api.v1.WithdrawMethod;

@ExtendWith(SpringExtension.class)
@BootstrapWith(PaymentSpringBootTestContextBootstrapper.class)
@ContextConfiguration(classes = {
        MockExternalApiDiModule.class,
        MockCommonDiModule.class,
        PaymentInMemoryDatabaseDiModule.class,
        PaymentRedeemDiModule.class,
        PaymentServicesDiModule.class
})
@TestExecutionListeners(mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS, listeners = {PaymentBrandDataGenerator.class, WithdrawProviderDataGenerator.class})
@Import({
        DefaultSkrillService.class,
        CancelWithdrawJob.class,
        RedeemMoneyRequestHandler.class,
        PreConfirmRedeemMoneyRequestHandler.class,
        DefaultPrizeoutService.class,
        SetRedeemProcessingDataHandler.class,
        PaymentIdentityManager.class,
})
class WithdrawTests extends CommonMockTest {
    @MockitoSpyBean
    private CommonWithdrawService withdrawService;
    @MockitoSpyBean
    private WithdrawLimitService withdrawLimitService;
    @MockitoSpyBean
    private DefaultSkrillService skrillService;
    @MockitoSpyBean
    private CancelWithdrawJob cancelRedeemJob;
    @Autowired
    private PaymentIdentityManager identityManager;
    @MockitoSpyBean
    private RedeemMoneyRequestHandler redeemMoneyRequestHandler;
    @MockitoSpyBean
    private PreConfirmRedeemMoneyRequestHandler preConfirmRedeemMoneyRequestHandler;
    @MockitoBean
    private QueuePostTemplate<?> postTemplate;
    @MockitoSpyBean
    private DefaultPrizeoutService prizeoutService;
    @MockitoBean
    private EmptyGeoLocatorManager mockGeoLocatorManager;
    @MockitoBean
    private CloudflareGatewayHookService cloudflareGatewayHookService;
    @MockitoSpyBean
    private SetRedeemProcessingDataHandler setRedeemProcessingDataHandler;

    @BeforeEach
    void before() throws Exception {
        cfg.setLocalProperty(props.SWEEPSTAKE_MONEY_REDEEM_MAX_AMOUNT_BY_STATE_PER_DAY_ENABLED.getKey(), false);
        doNothing().when(prizeoutService).confirm(any());
    }

    @Test
    void withdrawFailedForLockedUser() throws Throwable {
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        account.setLocked(true);
        ebean.save(account);
        uamMocks(account);

        final ExternalUserSession session = mock(ExternalUserSession.class);
        when(session.account()).thenReturn(account);
        TestOperationUtil.mockGetAccountInfo(mockUtil, uamServiceApi, account);
        TestOperationUtil.mockGetConfirmedKYCInfo(mockUtil, fraudServiceApi, account);

        var code = PlatformUtil.randomUUID().toString();

        assertThrows(ApplicationException.class, () -> {
            var amount = 101.0;
            var identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();
            var cmd = createRedeemMoneyRequest(mockUtil, identity, account, code, amount);
            redeemMoneyRequestHandler.apply(cmd);
        });
        var withdraw = getWithdraw(WithdrawMethodSpec.PRIZEOUT.code(), code);
        Assertions.assertEquals(RedeemStatusSpec.FAILED, withdraw.getStatus());
        Assertions.assertNotNull(withdraw.getMethod(), "method exists");
    }

    @Test
    void withdrawFailedForAccountProviderBlacklist() throws Throwable {
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        ebean.save(account);

        uamMocks(account);
        try (Transaction tx = ebean.newTransaction()) {
            AccountProviderBlackList accountProviderBlackList = new AccountProviderBlackList();
            accountProviderBlackList.setAccount(account);
            accountProviderBlackList.setWithdrawMethods(Set.of(WithdrawMethodSpec.PRIZEOUT));
            ebean.save(accountProviderBlackList, tx);
            tx.commit();
        }

        final ExternalUserSession session = mock(ExternalUserSession.class);
        when(session.account()).thenReturn(account);
        TestOperationUtil.mockGetAccountInfo(mockUtil, uamServiceApi, account);

        var code = PlatformUtil.randomUUID().toString();
        assertThrows(ApplicationException.class, () -> {
            var amount = 101.0;
            var identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();
            var cmd = createRedeemMoneyRequest(mockUtil, identity, account, code, amount);
            redeemMoneyRequestHandler.apply(cmd);
        });
        var withdraw = getWithdraw(WithdrawMethodSpec.PRIZEOUT.code(), code);
        Assertions.assertEquals(RedeemStatusSpec.FAILED, withdraw.getStatus());
        Assertions.assertNotNull(withdraw.getMethod(), "method exists");
    }

    @Test
    void testHappyPath() throws Throwable {
        var redeemDefaultDelay = props.REDEEM_DEFAULT_DELAY.get();

        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        uamMocks(account);

        final ExternalUserSession session = mock(ExternalUserSession.class);
        when(session.account()).thenReturn(account);

        var fraudResponseId = new RandomDataGenerator().nextLong(0, Long.MAX_VALUE);
        var onWithdraw = mockOnWithdrawRequest(mockUtil, uamServiceApi, account);
        var redeemMoneyEvent = mockRedeemMoneyEvent(postTemplate, account);

        String code = "1";
        var amount = 101.0;
        var identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();
        var cmd = createRedeemMoneyRequest(mockUtil, identity, account, code, amount);
        redeemMoneyRequestHandler.apply(cmd);

        verifySendFraudCheckJobSchedule();
        onWithdraw.verify();
        redeemMoneyEvent.verify();

        var withdraw = getWithdraw(WithdrawMethodSpec.PRIZEOUT.code(), "1");
        Assertions.assertEquals(RedeemStatusSpec.PRE_AUTHORIZED, withdraw.getStatus());
        Assertions.assertEquals(RedeemStatusSpec.PRE_AUTHORIZED, withdraw.getUamStatus());
        Assertions.assertEquals(Date.from(withdraw.getCreatedAt().toInstant().plus(redeemDefaultDelay)), withdraw.getProcessingStartAt());
    }

    @Test
    void testStateIsPassedInRedeemLimitError_preConfirm_by_agent() throws Throwable {
        cfg.setLocalProperty(props.SWEEPSTAKE_MONEY_REDEEM_MAX_AMOUNT_BY_STATE_PER_DAY_ENABLED.getKey(), true);
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        uamMocks(account);
        AccountInfoContext accCtx = toAccountInfoCtx(account);
        final ExternalUserSession session = mock(ExternalUserSession.class);
        when(session.account()).thenReturn(account);

        var fraudResponseId = new RandomDataGenerator().nextLong(0, Long.MAX_VALUE);
        mockOnWithdrawRequest(mockUtil, uamServiceApi, account);
        mockRedeemMoneyEvent(postTemplate, account);

        String code = PlatformUtil.randomUUID().toString();
        var amount = 101.0;
        var identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();
        var cmd = createRedeemMoneyRequest(mockUtil, identity, account, code, amount);

        doReturn(DailyStateRemainsLimit.builder()
                .state("CA")
                .limit(BigDecimal.valueOf(Long.MAX_VALUE))
                .remainsLimit(BigDecimal.valueOf(Long.MAX_VALUE))
                .build()).when(withdrawLimitService).getDailyRemainsLimit(identity, account, WithdrawalStatusCalcType.CREATED);
        redeemMoneyRequestHandler.apply(cmd);

        var withdraw = getWithdraw(WithdrawMethodSpec.PRIZEOUT.code(), code);
        Assertions.assertEquals(RedeemStatusSpec.PRE_AUTHORIZED, withdraw.getStatus());

        var ctx = new DefaultWithdrawContext(props, withdraw, ebean, toAccountInfoCtx(account), getOrCreateAccountPaymentSetting(ebean, account, _ -> {}));

        withdrawService.lock(AsciiString.cached("key"), identity, ctx, "system", new Date(), "comment1");
        ebean.save(ctx.withdrawRequestContext.getUpdates().apply(ebean, null));

        withdraw = getWithdraw(WithdrawMethodSpec.PRIZEOUT.code(), code);
        Assertions.assertEquals(RedeemStatusSpec.LOCKED, withdraw.getStatus());
        Assertions.assertEquals("comment1", withdraw.getComments());

        doReturn(DailyStateRemainsLimit.builder()
                .state("CA")
                .remainsLimit(BigDecimal.TEN)
                .limit(BigDecimal.valueOf(100))
                .build()).when(withdrawLimitService).getDailyRemainsLimit(any(), any(), eq(WithdrawalStatusCalcType.PRE_CONFIRMED));

        var preConfirmCmdByAgent = preparePreConfirmReq(mockUtil, account, withdraw, r -> r.setPreConfirmedByAgent("agent"));
        Exception error = assertThrows(ApplicationException.class, () -> {
            preConfirmRedeemMoneyRequestHandler.apply(preConfirmCmdByAgent);
        });
        assertEquals("Sorry, your redemption request cannot be processed as the maximum amount available for redemption today is 10 and the daily redemption limit in CA is 100", error.getMessage());

        var preConfirmCmdByjob = preparePreConfirmReq(mockUtil, account, withdraw, r -> {
        });
        preConfirmRedeemMoneyRequestHandler.apply(preConfirmCmdByjob);
        withdraw = getWithdraw(WithdrawMethodSpec.PRIZEOUT.code(), code);
        Assertions.assertNotNull(withdraw.getPreConfirmedAt());
        verifySendFraudCheckJobSchedule();
    }

    @Test
    void testStateIsPassedInRedeemLimitError() throws Throwable {
        cfg.setLocalProperty(props.SWEEPSTAKE_MONEY_REDEEM_MAX_AMOUNT_BY_STATE_PER_DAY_ENABLED.getKey(), true);
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        uamMocks(account);
        final ExternalUserSession session = mock(ExternalUserSession.class);
        when(session.account()).thenReturn(account);

        var fraudResponseId = new RandomDataGenerator().nextLong(0, Long.MAX_VALUE);
        mockOnWithdrawRequest(mockUtil, uamServiceApi, account);
        mockRedeemMoneyEvent(postTemplate, account);

        var identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();
        doReturn(DailyStateRemainsLimit.builder()
                .state("CA")
                .remainsLimit(BigDecimal.TEN)
                .limit(BigDecimal.valueOf(100))
                .build()).when(withdrawLimitService).getDailyRemainsLimit(identity, account, WithdrawalStatusCalcType.CREATED);

        Exception error = assertThrows(ApplicationException.class, () -> {
            var code = PlatformUtil.randomUUID().toString();
            var amount = 101.0;
            var cmd = createRedeemMoneyRequest(mockUtil, identity, account, code, amount);
            redeemMoneyRequestHandler.apply(cmd);
        });
        assertEquals("Sorry, your redemption request cannot be processed as the maximum amount available for redemption today is 10 and the daily redemption limit in CA is 100", error.getMessage());
    }

    @Test
    void testBigWinnerStatusGtThresholdSum() throws Throwable {
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);

        final ExternalUserSession session = mock(ExternalUserSession.class);
        when(session.account()).thenReturn(account);

        var getPersonalInfo = uamMocks(account);
        mockSendFraudCheck(account);
        mockOnWithdrawRequest(mockUtil, uamServiceApi, account);
        mockRedeemMoneyEvent(postTemplate, account);

        var setBwStatus = mockSetAccountInfoRequest(account);

        var code = "7";
        var amount = 5000;

        var cmd = createRedeemMoneyRequestWithSkril(account, code, amount);
        redeemMoneyRequestHandler.apply(cmd);
        setBwStatus.verify();
        getPersonalInfo.verifyAccountPersonalInfo();
    }

    @Test
    void testBigWinnerStatusLtThreshold() throws Throwable {
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        uamMocks(account);

        final ExternalUserSession session = mock(ExternalUserSession.class);
        when(session.account()).thenReturn(account);

        mockPaymentFraudCheck(account);
        mockSendFraudCheck(account);
        mockOnWithdrawRequest(mockUtil, uamServiceApi, account);
        mockRedeemMoneyEvent(postTemplate, account);

        var setBwStatus = mockSetAccountInfoRequest(account);

        var code = "6";
        var amount = 101.11;
        var identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();
        var cmd = createRedeemMoneyRequest(mockUtil, identity, account, code, amount);

        redeemMoneyRequestHandler.apply(cmd);

        setBwStatus.verify(never());
    }

    @Test
    void testUamTimeout() throws Throwable {
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        uamMocks(account);

        final ExternalUserSession session = mock(ExternalUserSession.class);
        when(session.account()).thenReturn(account);

        var onWithdraw = mockOnWithdrawRequestError(account, Code.ERR_TIMEOUT);

        var code = PlatformUtil.randomUUID().toString();
        assertThrows(ApplicationException.class, () -> {
            var amount = 101.0;
            var identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();
            var cmd = createRedeemMoneyRequest(mockUtil, identity, account, code, amount);

            redeemMoneyRequestHandler.apply(cmd);
        });

        verifySendFraudCheckJobSchedule();
        onWithdraw.verify(times(1));

        var withdraw = getWithdraw(WithdrawMethodSpec.PRIZEOUT.code(), code);
        Assertions.assertEquals(RedeemStatusSpec.PRE_AUTHORIZED, withdraw.getStatus());
        Assertions.assertNull(withdraw.getUamStatus());
    }

    @Test
    void testAccountHasAnyUnverifiedCard() throws Throwable {
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        account.setAdmin(true);
        ebean.save(account);
        uamMocks(account);
        final ExternalUserSession session = mock(ExternalUserSession.class);
        when(session.account()).thenReturn(account);

        var fraudResponseId = new RandomDataGenerator().nextLong(0, Long.MAX_VALUE);
        var onWithdraw = mockOnWithdrawRequest(mockUtil, uamServiceApi, account);
        var redeemMoneyEvent = mockRedeemMoneyEvent(postTemplate, account);

        PaymentDataGenerationService.genUnverifiedPaymentMethodMetaInfo(ebean, account, PlatformUtil.randomUUID().toString(), REQUIRE_VERIFICATION);

        String code = "1";
        var amount = 101.0;
        var identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();
        var cmd = createRedeemMoneyRequest(mockUtil, identity, account, code, amount);

        redeemMoneyRequestHandler.apply(cmd);
        verifySendFraudCheckJobSchedule();
        onWithdraw.verify();
        redeemMoneyEvent.verify();

        var withdraw = getWithdraw(WithdrawMethodSpec.PRIZEOUT.code(), "1");
        Assertions.assertEquals(RedeemStatusSpec.PRE_AUTHORIZED, withdraw.getStatus());
        Assertions.assertEquals(RedeemStatusSpec.PRE_AUTHORIZED, withdraw.getUamStatus());
    }

    @Test
    void testAccountHasAnyUnverifiedCardWhenDeclineIsDisabled() throws Throwable {
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        account.setAdmin(true);
        ebean.save(account);
        uamMocks(account);
        final ExternalUserSession session = mock(ExternalUserSession.class);
        when(session.account()).thenReturn(account);

        var fraudResponseId = new RandomDataGenerator().nextLong(0, Long.MAX_VALUE);
        var onWithdraw = mockOnWithdrawRequest(mockUtil, uamServiceApi, account);
        var redeemMoneyEvent = mockRedeemMoneyEvent(postTemplate, account);

        mockUtil.mockServiceCall(
                uamServiceApi,
                s -> getAccountPersonalInfo(s, account),
                Responses.getAccountPersonalInfo(account));
        PaymentDataGenerationService.genUnverifiedPaymentMethodMetaInfo(ebean, account, PlatformUtil.randomUUID().toString(), REQUIRE_VERIFICATION);

        String code = "1";
        var amount = 101.0;
        var identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();
        var cmd = createRedeemMoneyRequest(mockUtil, identity, account, code, amount);

        redeemMoneyRequestHandler.apply(cmd);
        verifySendFraudCheckJobSchedule();
        onWithdraw.verify();
        redeemMoneyEvent.verify();

        var withdraw = getWithdraw(WithdrawMethodSpec.PRIZEOUT.code(), "1");
        Assertions.assertEquals(RedeemStatusSpec.PRE_AUTHORIZED, withdraw.getStatus());
        Assertions.assertEquals(RedeemStatusSpec.PRE_AUTHORIZED, withdraw.getUamStatus());
    }

    @Test
    void testGetWithdrawMoneyRequestWithError() throws Throwable {
        var account = PaymentDataGenerationService.genAccount(ebean);
        uamMocks(account);
        var wr = WithdrawError.builder().message("Test message")
                .code("R01")
                .type(WithdrawErrorType.DECLINE.code())
                .additionalCodes(Map.of("testKey", "testValue")).build();
        var withdraw = saveAndGetWithdraw("5", account, wr);

        Assertions.assertEquals(wr.getMessage(), withdraw.getError().getMessage());
        Assertions.assertEquals(wr.getCode(), withdraw.getError().getCode());
        Assertions.assertEquals(wr.getType(), withdraw.getError().getType());
        Assertions.assertEquals(wr.getAdditionalCodes().size(), withdraw.getError().getAdditionalCodes().size());
    }

    @Test
    void testDuplicateErrorCode() throws Throwable {
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        uamMocks(account);

        final ExternalUserSession session = mock(ExternalUserSession.class);
        when(session.account()).thenReturn(account);

        var fraudResponseId = new RandomDataGenerator().nextLong(0, Long.MAX_VALUE);

        var onWithdraw = mockOnWithdrawRequestError(account, Code.ERR_DUPLICATE);
        var redeemMoneyEvent = mockRedeemMoneyEvent(postTemplate, account);

        String code = "1";
        var amount = 101.0;
        var identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();
        var cmd = createRedeemMoneyRequest(mockUtil, identity, account, code, amount);
        redeemMoneyRequestHandler.apply(cmd);

        verifySendFraudCheckJobSchedule();
        onWithdraw.verify();
        redeemMoneyEvent.verify();

        var withdraw = getWithdraw(WithdrawMethodSpec.PRIZEOUT.code(), "1");
        Assertions.assertEquals(RedeemStatusSpec.PRE_AUTHORIZED, withdraw.getStatus());
        Assertions.assertEquals(RedeemStatusSpec.PRE_AUTHORIZED, withdraw.getUamStatus());
    }

    private TransactionalRequest<RedeemMoneyRequest, RedeemMoneyResponse.Builder> createRedeemMoneyRequestWithSkril(
            ImmutableAccount account,
            String code,
            double amount) throws Throwable {
        RedeemMoneyRequest.Builder req = RedeemMoneyRequest.newBuilder();
        req.setIdentity(Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)));
        req.setCode(code);
        req.setCurrency("SC");
        req.setAmount(String.valueOf(amount));
        req.setSession(PlatformUtil.randomUUID().toString());
        req.setMethod(WithdrawMethod.newBuilder().setSkrill(uam.api.v1.SkrillWithdrawMethod.newBuilder().setEmail("<EMAIL>").build()));

        var cmd = mockUtil.toTransactionalRequest(RedeemMoneyRequest.class, req.build(), RedeemMoneyResponse.newBuilder());
        cmd.setRoutingKey(account.routingKey());
        return cmd;
    }

    private MockUtil.MockCall mockSetAccountInfoRequest(ImmutableAccount account) throws Throwable {
        return mockUtil.mockServiceCall(uamServiceApi,
                s -> setAccountInfo(s, account),
                SetAccountInfoResponse.getDefaultInstance());
    }

    private WithdrawMoneyRequest getWithdraw(String method, String code) throws Throwable {
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            return ebean.paymentRepo().withdrawRequest(method, code, tx).get();
        }
    }

    private WithdrawMoneyRequest saveAndGetWithdraw(String code, ImmutableAccount acc, WithdrawError wr) throws Throwable {
        var wmr = PaymentDataGenerationService.genWithdraw(acc, ebean, RedeemStatusSpec.DECLINED);
        wmr.setCode(code);
        wmr.setError(wr);
        return wmr;

    }

    private MockUtil.MockCall mockOnWithdrawRequestError(ImmutableAccount account, Code errCode) throws Throwable {
        TestOperationUtil.mockGetAccountInfo(mockUtil, uamServiceApi, account);
        return mockUtil.mockServiceCall(uamServiceApi,
                s -> onWithdraw(s, account),
                OnWithdrawResponse.getDefaultInstance(),
                new DefaultResponseStatusFacade(Status.newBuilder().setErrorCode(errCode).build()));

    }

}
