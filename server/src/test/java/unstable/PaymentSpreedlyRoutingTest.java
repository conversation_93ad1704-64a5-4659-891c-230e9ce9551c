package unstable;

import api.v1.ApplicationException;
import api.v1.Code;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.spreedly.sdk.gateway.SpreedlyApiJaxRsClient;
import com.spreedly.sdk.gateway.SpreedlyPurchaseService;
import com.spreedly.sdk.gateway.SpreedlyServiceApi;
import com.spreedly.sdk.gateway.model.SpreedlyPaymentMethod;
import com.spreedly.sdk.gateway.model.SpreedlyTransactionResponse;
import com.spreedly.sdk.receiver.SpreedlyDeliverRequest;
import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ups.PlainServiceInfo;
import framework.kafka.Mocks;
import framework.listeners.PaymentBrandDataGenerator;
import framework.listeners.PaymentOfferDataGenerator;
import framework.util.CardService;
import framework.util.DataAccessService;
import framework.util.PaymentDataGenerationService;
import framework.util.IdentityUtil;
import framework.util.Responses;
import framework.util.UtilConstant;
import fraud.api.FraudServiceApi;
import fraud.api.v1.FraudInfo;
import fraud.api.v1.GetCardVerificationsResponse;
import fraud.api.v1.LabelTransactionRequest;
import fraud.api.v1.LabelTransactionResponse;
import fraud.api.v1.PaymentFraudCheckResponse;
import jakarta.ws.rs.core.Response;
import org.jboss.resteasy.util.BasicAuthHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.platform.commons.util.StringUtils;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.ConfigurableCloudConnector;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import payment.PaymentEbeanJpaManager;
import payment.PaymentProto;
import payment.card.RoutingProviderCardToken;
import payment.handlers.read.GetPaymentMethodsRequestHandler;
import payment.model.AccountPaymentMethod;
import payment.model.ErrorMapping;
import payment.model.PaymentAggregatedMetaInfo;
import payment.model.PaymentOrder;
import payment.model.PaymentOrderError;
import payment.model.Provider;
import payment.model.ProviderIntegrationTypeSpec;
import payment.model.ProviderTypeSpec;
import payment.model.RoutingErrorConfig;
import payment.model.RoutingErrorTypeSpec;
import payment.model.RoutingRule;
import payment.model.RoutingRuleTypeSpec;
import payment.model.RoutingTypeSpec;
import payment.model.VolumeAllocationConfig;
import payment.model.immutable.ImmutableAccount;
import payment.model.immutable.ImmutableBrand;
import payment.repo.ProviderRepo;
import payment.repo.RoutingRepo;
import payment.type.PaymentMode;
import payment.type.PurchaseProviderSpec;
import uam.api.UamServiceApi;
import uam.api.v1.CreatePaymentOrderRequest;
import uam.api.v1.CreatePaymentOrderResponse;
import uam.api.v1.GetPaymentMethodsRequest;
import uam.api.v1.GetPaymentMethodsResponse;
import uam.api.v1.Identity;
import uam.api.v1.PaymentProvider;

import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Stream;

import static api.v1.CodeSpec.ERR_PAYMENT_INPUT_CVV;
import static framework.listeners.PaymentOfferDataGenerator.OFFER_10_DOLLARS;
import static framework.listeners.PaymentOfferDataGenerator.OFFER_5000_DOLLARS;
import static framework.util.TestOperationUtil.getAccountPaymentInfo;
import static framework.util.TestOperationUtil.getAccountPersonalInfo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.withSettings;
import static payment.DataGeneration.paymentContext;
import static payment.model.PaymentOrder.FAILED;
import static payment.model.ProviderIntegrationTypeSpec.SPREEDLY;
import static payment.model.ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY;
import static payment.model.ProviderIntegrationTypeSpec.SPREEDLY_RAPYD;
import static payment.model.RoutingErrorFailPolicy.INTEGRATION_TYPE;
import static payment.model.RoutingErrorFailPolicy.MERCHANT_ID;
import static payment.model.RoutingRuleTypeSpec.EXCLUDE;
import static payment.model.RoutingRuleTypeSpec.STICK;
import static payment.model.RoutingRuleTypeSpec.STICK_WITH_FALLBACK;

@Import({UamServiceApi.class, GetPaymentMethodsRequestHandler.class})
@TestExecutionListeners(mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS, listeners = { PaymentBrandDataGenerator.class })
class PaymentSpreedlyRoutingTest extends AbstractSpreedlyPaymentTest {
    @MockitoSpyBean
    protected UamServiceApi uamServiceApi;
    @Autowired
    protected FraudServiceApi fraudServiceApi;
    @Autowired
    private ObjectMapper mapper;
    @MockitoSpyBean
    private GetPaymentMethodsRequestHandler getPaymentMethodsRequestHandler;
    @MockitoSpyBean
    private SpreedlyPurchaseService spreedlyPurchaseService;
    @MockitoSpyBean
    private SpreedlyApiJaxRsClient spreedlyApiJaxRsClient;

    private Provider spreedly;

    @BeforeEach
    public void before() throws Exception {
        cloud.addUps(PaymentProviderUtils.SPREEDLY);
        cloud.addUps(PaymentProviderUtils.SPREEDLY_FISERV);
        cloud.addUps(PaymentProviderUtils.SPREEDLY_RAPYD);
        cloud.addUps(PaymentProviderUtils.SPREEDLY_EMERCHANTPAY);
        cloud.addUps(PaymentProviderUtils.SPREEDLY_WORLD_PAY);
        cloud.addUps(PaymentProviderUtils.SPREEDLY_FISERV_2);
        spreedly = PaymentProviderUtils.addPurchaseProvider(ebean, PurchaseProviderSpec.SPREEDLY, SPREEDLY);
        cfg.setLocalProperty(props.ROUTING_CHAIN_MAX_SIZE.getKey(), 10);
        cfg.setLocalProperty(props.PROVIDER_COUNTRY_ENABLED.getKey(), true);
        ebean.paymentRepo().saveOrUpdate(ErrorMapping.builder().errorCode("01")
                .type(RoutingErrorTypeSpec.CARD_NETWORK)
                .description("Something went wrong with your card. Please contact your bank.")
                .message("Refer to card issuer")
                .build(), null);
    }

    @Test
    void testInputCvvRequriedForExcludeInSameTokenization() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        var fiserv = PaymentProviderUtils.addPurchaseProvider(ebean, PurchaseProviderSpec.SPREEDLY_FISERV, ProviderIntegrationTypeSpec.SPREEDLY_FISERV);

        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4111, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);
        mockRequiredPaymentCalls(account, card);

        var order = createSuccesfulPaymentOrder(account, card, PaymentProvider.SPREEDLY, true, PlatformUtil.randomUUID(), PaymentOfferDataGenerator.OFFER_3000_SC);
        assertSame(PaymentProvider.SPREEDLY_FISERV, order.getProvider().toPaymentProvider());

        var routingTokens = order.getPaymentMethod().getCardPaymentMethod().getRoutingProviderCardTokens();
        assertThat(routingTokens)
                .hasSize(1)
                .anyMatch(token -> token.getProvider().toPaymentProvider() == PaymentProvider.SPREEDLY_FISERV);

        PaymentProviderUtils.deactivateProvider(ebean, fiserv);
        fiserv.setExcludeInSameTokenization(true);
        ebean.save(fiserv);

        var fiserv2 = PaymentProviderUtils.addPurchaseProvider(ebean, PurchaseProviderSpec.SPREEDLY_FISERV_2, ProviderIntegrationTypeSpec.SPREEDLY_FISERV);
        Mockito.reset(uamServiceApi, fraudServiceApi, paymentServiceApi);

        // err cvv due ExcludeInSameTokenization on SPREEDLY_FISERV PP
        order = makeFailedPaymentInputCvv(account, card, (err) -> assertEquals(Code.ERR_PAYMENT_INPUT_CVV, err.getCode()));
        assertSame(PaymentProvider.SPREEDLY_FISERV_2, order.getProvider().toPaymentProvider());

        routingTokens = order.getPaymentMethod().getCardPaymentMethod().getRoutingProviderCardTokens();
        // not changed
        assertThat(routingTokens)
                .hasSize(1)
                .anyMatch(token -> token.getProvider().toPaymentProvider() == PaymentProvider.SPREEDLY_FISERV);

        // enablle SPREEDLY_FISERV for same tokenization support
        fiserv.setExcludeInSameTokenization(false);
        ebean.save(fiserv);

        // will pickup token from SPREEDLY_FISERV
        order = createSuccesfulPaymentOrder(account, card, PaymentProvider.SPREEDLY, false, PlatformUtil.randomUUID(), PaymentOfferDataGenerator.OFFER_3000_SC);
        assertSame(PaymentProvider.SPREEDLY_FISERV_2, order.getProvider().toPaymentProvider());
        routingTokens = order.getPaymentMethod().getCardPaymentMethod().getRoutingProviderCardTokens();

        // SPREEDLY_FISERV_2 token auto added, old one persisted
        assertThat(routingTokens)
                .hasSize(2)
                .anyMatch(token -> token.getProvider().toPaymentProvider() == PaymentProvider.SPREEDLY_FISERV)
                .anyMatch(token -> token.getProvider().toPaymentProvider() == PaymentProvider.SPREEDLY_FISERV_2);
    }

    @Test
    void testRoutingErrorEvaluationConditionAdvancedRouting() throws Throwable {

        var re = RoutingErrorConfig.builder()
                .providerType(RoutingTypeSpec.SPREEDLY)
                .errorType(RoutingErrorTypeSpec.CARD_NETWORK)
                .errorCode("51")
                .failPolicy(MERCHANT_ID)
                .build();
        ebean.save(re);

        testRoutingErrorEvaluationConditionCommon((condition) -> {
            re.setRetryCondition(condition);
            ebean.save(re);
        });
    }
    @Test
    void testRoutingErrorEvaluationCondition() throws Throwable {

        var re = RoutingErrorConfig.builder()
                .errorType(RoutingErrorTypeSpec.CARD_NETWORK)
                .providerType(RoutingTypeSpec.SPREEDLY)
                .failPolicy(INTEGRATION_TYPE)
                .errorCode("51")
                .build();
        ebean.save(re);

        testRoutingErrorEvaluationConditionCommon((condition) -> {
            re.setRetryCondition(condition);
            ebean.save(re);
        });
    }

    @Test
    void testCountryEmptyRoutingChain() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        PaymentProviderUtils.addPurchaseProvider(ebean, PurchaseProviderSpec.SPREEDLY_FISERV, ProviderIntegrationTypeSpec.SPREEDLY_FISERV, List.of("IT"));
        PaymentProviderUtils.addPurchaseProvider(ebean, PurchaseProviderSpec.SPREEDLY_FISERV_2, ProviderIntegrationTypeSpec.SPREEDLY_FISERV, List.of("US"));

        ebean.save(RoutingErrorConfig.builder()
                .errorCode("5005")
                .failPolicy(INTEGRATION_TYPE)
                .errorType(RoutingErrorTypeSpec.PROVIDER_CUSTOM)
                .description("The credit card you are using has been temporarily blocked")
                .integrationType(ProviderIntegrationTypeSpec.SPREEDLY_FISERV)
                .build());

        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4111, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        // mock call
        Response resp = mock(Response.class);
        when(resp.getStatus()).thenReturn(422);
        String payload = Files.readString(Path.of("src", "test", "resources", "spreedly-5005.json"));
        when(resp.readEntity(any(Class.class))).thenReturn(payload);

        SpreedlyServiceApi serviceApi = mock(SpreedlyServiceApi.class);
        when(serviceApi.postDeliver(any(String.class), any(String.class), any(String.class), any(String.class), any(SpreedlyDeliverRequest.class))).thenReturn(resp);
        SpreedlyTransactionResponse response = new SpreedlyTransactionResponse();
        response.setPaymentMethod(card);
        doReturn(response).when(serviceApi).resolve(anyString(), anyString(), anyString(), anyString());
        doReturn(serviceApi).when(spreedlyApiJaxRsClient).proxy(any());
        UUID id = PlatformUtil.randomUUID();

        makeFailedPaymentWithTransactionId(account, card, id, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        });

        Mockito.clearInvocations(uamServiceApi, fraudServiceApi);

        makeFailedPaymentWithoutFraudCheck(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT_ROUTING_EMPTY_CHAIN, err.getCode());
            assertEquals("There seems to be an issue with the card. Please retry after sometime or use a different payment method", err.getMessage());
        });
    }
    @Test
    void testRouteLimit() throws Throwable {
        cfg.setLocalProperty(props.ROUTING_CHAIN_MAX_SIZE.getKey(), 1);
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        prepareProvidersForRouting(brand);
        addVolumeAllocation(brand, 70, ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY);
        addVolumeAllocation(brand, 30, ProviderIntegrationTypeSpec.SPREEDLY_RAPYD);
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4111, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        cloud.addUps(PaymentProviderUtils.SPREEDLY_EMERCHANTPAY_WRONG_TERMINAL);
        String sourceId = PlatformUtil.randomUUID().toString();

        var failedOrder = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT_ROUTING, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        }, sourceId);
        assertEquals(failedOrder.getProviderName(), PaymentProvider.SPREEDLY_EMERCHANTPAY.toString().toLowerCase());
        PaymentOrderError error = new PaymentOrderError(Code.ERR_PAYMENT_ROUTING.toString().toLowerCase(),
                "SpreedlyException",
                null,
                "220",
                null,
                List.of(),
                null,
                "Payment has failed, please try again.", null);
        assertErrorIgnoreMessage(failedOrder, error);
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        failedOrder = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, err -> {
            assertEquals(Code.ERR_PAYMENT, err.getCode());
            assertEquals("Exceeded allowed routing chain size", err.getMessage());
        }, sourceId);
        assertEquals(failedOrder.getProviderName(), PaymentProvider.SPREEDLY_RAPYD.toString().toLowerCase());
        error = new PaymentOrderError(Code.ERR_PAYMENT.toString().toLowerCase(),
                "ApplicationException",
                null,
                null,
                null,
                List.of(),
                null,
                "Exceeded allowed routing chain size", null);
        assertErrorIgnoreMessage(failedOrder, error);

        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);
    }

    @Test
    void testCardNetworkRouting() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand(ebean);
        prepareCardNetworkRouting(brand);
        addVolumeAllocation(brand, 70, ProviderIntegrationTypeSpec.SPREEDLY_FISERV);
        addVolumeAllocation(brand, 30, ProviderIntegrationTypeSpec.SPREEDLY_RAPYD);
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_FISERV_51_ERROR, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);
        Identity identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();

        var failedOrder = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, true, PlatformUtil.randomUUID(), (err) -> {
            assertEquals(Code.ERR_PAYMENT_ROUTING, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        }, null, OFFER_5000_DOLLARS, r -> {});

        mockUtil.mockServiceCall(
                uamServiceApi,
                s -> getAccountPersonalInfo(s, account),
                Responses.getAccountPersonalInfo(account));

        assertEquals(failedOrder.getProviderName(), PaymentProvider.SPREEDLY_FISERV.toString().toLowerCase());
        assertFalse(failedOrder.isSuccess());
        final PaymentOrderError error = new PaymentOrderError(
                Code.ERR_PAYMENT_ROUTING.toString().toLowerCase(),
                "SpreedlyException",
                null,
                "051",
                "51",
                List.of(),
                "DECLINED: ",
                "Payment has failed, please try again.", null);
        assertError(failedOrder, error);
        assertNotNullCardTransactionDetails(failedOrder);

        // verify method is not shown to user
        var cmd = mockUtil.toTransactionalRequest(
                GetPaymentMethodsRequest.class,
                GetPaymentMethodsRequest.newBuilder().setIdentity(identity).build(),
                GetPaymentMethodsResponse.newBuilder(),
                account.routingKey());
        getPaymentMethodsRequestHandler.apply(cmd);
        var reply = cmd.reply().build();
        Assertions.assertEquals(0, reply.getDataList().size());

        // verify saved method
        assertEquals(failedOrder.getAccount().getPaymentMethods().size(), 1);
        var failedMethod = failedOrder.getAccount().getPaymentMethods().get(0);
        assertFalse(failedMethod.isRemember());
        assertEquals("SpreedlyGateway", failedMethod.getType());
        assertEquals(FINGERPRINT_SPREEDLY_FISERV_ERROR_51_CARD, failedMethod.getFingerprint());
        assertEquals(2, failedMethod.getErrors().size()); // fiserv returns 2 error codes
        assertTrue(failedMethod.getErrors().stream().anyMatch(e -> e.getErrorCode().equals("51")));
        assertTrue(failedMethod.getCardPaymentMethod().getRoutingProviderCardTokens().isEmpty());

        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        var successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_RAPYD, successOrder);

        mockUtil.mockServiceCall(
                fraudServiceApi,
                s -> s.getCardVerifications(any(), any()),
                GetCardVerificationsResponse.getDefaultInstance());

        // verify saved method is shown to user
        cmd = mockUtil.toTransactionalRequest(
                GetPaymentMethodsRequest.class,
                GetPaymentMethodsRequest.newBuilder().setIdentity(identity).build(),
                GetPaymentMethodsResponse.newBuilder(),
                account.routingKey());
        getPaymentMethodsRequestHandler.apply(cmd);
        reply = cmd.reply().build();
        Assertions.assertEquals(1, reply.getDataList().size());
        Assertions.assertTrue(reply.getDataList().get(0).hasCard());
        Assertions.assertEquals(card.getFirstSixDigits(), reply.getDataList().get(0).getCard().getCardNumber());
        Assertions.assertEquals(card.getLastFourDigits(), reply.getDataList().get(0).getCard().getMaskedCardNumber());
        Assertions.assertEquals(card.getMonth(), reply.getDataList().get(0).getCard().getExpiryMonth());
        Assertions.assertEquals(card.getYear(), reply.getDataList().get(0).getCard().getExpiryYear());
        Assertions.assertEquals(card.getFullName(), reply.getDataList().get(0).getCard().getName());

        // verify saved method is marked as saved
        assertEquals(1, successOrder.getAccount().getPaymentMethods().size());
        assertEquals(failedMethod, successOrder.getAccount().getPaymentMethods().get(0));
        var savedPaymentMethod = successOrder.getAccount().getPaymentMethods().get(0);
        assertTrue(savedPaymentMethod.isRemember());
        assertEquals("SpreedlyGateway", savedPaymentMethod.getType());
        assertEquals(FINGERPRINT_SPREEDLY_FISERV_ERROR_51_CARD, savedPaymentMethod.getFingerprint());
        assertEquals(2, savedPaymentMethod.getErrors().size()); // fiserv returns 2 error codes
        // verify saved new RoutingMemberCardToken for first successful trx
        assertEquals(1, savedPaymentMethod.getCardPaymentMethod().getRoutingProviderCardTokens().size());
        assertEquals(savedPaymentMethod.getCardPaymentMethod().getRoutingProviderCardTokens().get(0).getProvider().getCode(),
                PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase());

        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        // This one doesn't require CVV card is already tokenized on provider, first failed provider is out of chain now
        var successOrder2 = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, false);
        RoutingProviderCardToken expectedCardTokens = savedPaymentMethod.getCardPaymentMethod().getRoutingProviderCardTokens().get(0);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_RAPYD, successOrder2);
        var savedPaymentMethod2 = successOrder2.getAccount().getPaymentMethods().get(0);
        var actualRoutingMemberCardToken = savedPaymentMethod2.getCardPaymentMethod().getRoutingProviderCardTokens().get(0);

        // verify saved method is shown to user after error
        cmd = mockUtil.toTransactionalRequest(
                GetPaymentMethodsRequest.class,
                GetPaymentMethodsRequest.newBuilder().setIdentity(identity).build(),
                GetPaymentMethodsResponse.newBuilder(),
                account.routingKey());
        getPaymentMethodsRequestHandler.apply(cmd);
        reply = cmd.reply().build();
        Assertions.assertEquals(1, reply.getDataList().size());
        Assertions.assertTrue(reply.getDataList().get(0).hasCard());
        Assertions.assertEquals(card.getFirstSixDigits(), reply.getDataList().get(0).getCard().getCardNumber());
        Assertions.assertEquals(card.getLastFourDigits(), reply.getDataList().get(0).getCard().getMaskedCardNumber());
        Assertions.assertEquals(card.getMonth(), reply.getDataList().get(0).getCard().getExpiryMonth());
        Assertions.assertEquals(card.getYear(), reply.getDataList().get(0).getCard().getExpiryYear());
        Assertions.assertEquals(card.getFullName(), reply.getDataList().get(0).getCard().getName());
        // verify saved method is marked as saved
        assertEquals(successOrder2.getAccount().getPaymentMethods().size(), 1);
        // only one is remembered
        assertEquals(1, (int) successOrder2.getAccount().getPaymentMethods().stream().filter(AccountPaymentMethod::isRemember).count());
        assertEquals(1, savedPaymentMethod2.getCardPaymentMethod().getRoutingProviderCardTokens().size());
        // verify saved new RoutingMemberCardToken for second successful trx with same token
        assertEquals(PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase(), actualRoutingMemberCardToken.getProvider().getCode());
        assertEquals(expectedCardTokens.getToken(), actualRoutingMemberCardToken.getToken());
        assertEquals(expectedCardTokens.getId(), actualRoutingMemberCardToken.getId());
    }

    @Test
    void testVolumeAllocationRoutingFreshAggregatedMetaInfo() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);

        saveVolumeAllocation(ProviderIntegrationTypeSpec.SPREEDLY_RAPYD, 30, brand);
        saveVolumeAllocation(ProviderIntegrationTypeSpec.SPREEDLY_FISERV, 50, brand);
        saveVolumeAllocation(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY, 20, brand);

        prepareCardNetworkRouting(brand);
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_FISERV_SUCCESS, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        var successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_FISERV, successOrder);
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);
        savePaymentAggregatedMetaInfo(ebean.providerRepo().provider(ProviderRepo.FindProviderParams.create()
                .withCodes(PaymentProvider.SPREEDLY_FISERV.name().toLowerCase())
                .withBrands(brand.getName())
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withIntegrationTypes(ProviderIntegrationTypeSpec.SPREEDLY_FISERV)).orElseThrow(), BigDecimal.TEN);

        successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_RAPYD, successOrder);
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);
        savePaymentAggregatedMetaInfo(ebean.providerRepo().provider(ProviderRepo.FindProviderParams.create()
                .withCodes(PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase())
                .withBrands(brand.getName())
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withIntegrationTypes(ProviderIntegrationTypeSpec.SPREEDLY_RAPYD)).orElseThrow(), BigDecimal.TEN);

        successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_EMERCHANTPAY, successOrder);
    }

    @Test
    void testVolumeAllocationRoutingEqualSplitBetweenProviders() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);

        saveVolumeAllocation(ProviderIntegrationTypeSpec.SPREEDLY_RAPYD, 30, brand);
        saveVolumeAllocation(ProviderIntegrationTypeSpec.SPREEDLY_FISERV, 50, brand);
        saveVolumeAllocation(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY, 20, brand);

        prepareCardNetworkRouting(brand);
        ebean.save(
                Provider.builder()
                        .code(PaymentProvider.SPREEDLY_FISERV_2.name().toLowerCase())
                        .brand(brand)
                        .type(ProviderTypeSpec.PURCHASE)
                        .paymentMode(PaymentMode.CARD)
                        .integrationType(ProviderIntegrationTypeSpec.SPREEDLY_FISERV)
                        .description(UtilConstant.DESCRIPTION)
                        .version(1)
                        .build());
        savePaymentAggregatedMetaInfo(ebean.providerRepo().provider(ProviderRepo.FindProviderParams.create()
                .withCodes(PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase())
                .withBrands(brand.getName())
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withIntegrationTypes(ProviderIntegrationTypeSpec.SPREEDLY_RAPYD)).orElseThrow(), new BigDecimal("1000"));
        savePaymentAggregatedMetaInfo(ebean.providerRepo().provider(ProviderRepo.FindProviderParams.create()
                .withCodes(PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase())
                .withBrands(brand.getName())
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withIntegrationTypes(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY)).orElseThrow(), new BigDecimal("1000"));
        savePaymentAggregatedMetaInfo(ebean.providerRepo().provider(ProviderRepo.FindProviderParams.create()
                .withCodes(PaymentProvider.SPREEDLY_FISERV.name().toLowerCase())
                .withBrands(brand.getName())
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withIntegrationTypes(ProviderIntegrationTypeSpec.SPREEDLY_FISERV)).orElseThrow(), new BigDecimal("1"));

        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_FISERV_SUCCESS, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        var successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_FISERV_2, successOrder);
        savePaymentAggregatedMetaInfo(ebean.providerRepo().provider(ProviderRepo.FindProviderParams.create()
                .withCodes(PaymentProvider.SPREEDLY_FISERV_2.name().toLowerCase())
                .withBrands(brand.getName())
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withIntegrationTypes(ProviderIntegrationTypeSpec.SPREEDLY_FISERV)).orElseThrow(), BigDecimal.TEN);
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_FISERV, successOrder);
    }

    @Test
    void testVolumeAllocationRoutingFirstByAggregationMetaInfo() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);

        saveVolumeAllocation(ProviderIntegrationTypeSpec.SPREEDLY_RAPYD, 30, brand);
        saveVolumeAllocation(ProviderIntegrationTypeSpec.SPREEDLY_FISERV, 50, brand);
        saveVolumeAllocation(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY, 20, brand);

        prepareCardNetworkRouting(brand);
        savePaymentAggregatedMetaInfo(ebean.providerRepo().provider(ProviderRepo.FindProviderParams.create()
                .withCodes(PaymentProvider.SPREEDLY_FISERV.name().toLowerCase())
                .withBrands(brand.getName())
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withIntegrationTypes(ProviderIntegrationTypeSpec.SPREEDLY_FISERV)).orElseThrow(), BigDecimal.TEN);

        savePaymentAggregatedMetaInfo(ebean.providerRepo().provider(ProviderRepo.FindProviderParams.create()
                .withCodes(PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase())
                .withBrands(brand.getName())
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withIntegrationTypes(ProviderIntegrationTypeSpec.SPREEDLY_RAPYD)).orElseThrow(), BigDecimal.TEN);

        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_FISERV_SUCCESS, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        var successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_EMERCHANTPAY, successOrder);
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);
        savePaymentAggregatedMetaInfo(ebean.providerRepo().provider(ProviderRepo.FindProviderParams.create()
                .withCodes(PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase())
                .withBrands(brand.getName())
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withIntegrationTypes(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY)).orElseThrow(), BigDecimal.TEN);

        successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_FISERV, successOrder);
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);
        addToPaymentAggregatedMetaInfo(ebean.providerRepo().provider(ProviderRepo.FindProviderParams.create()
                .withCodes(PaymentProvider.SPREEDLY_FISERV.name().toLowerCase())
                .withBrands(brand.getName())
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withIntegrationTypes(ProviderIntegrationTypeSpec.SPREEDLY_FISERV)).orElseThrow(), BigDecimal.TEN);

        successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_RAPYD, successOrder);
    }

    @Test
    void testHttpRouting() throws Throwable {
        short httpStatusError = 500;
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        prepareHttpRouting(brand, httpStatusError);

        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4111, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);
        Identity identity = Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)).build();

        // mock call
        Response resp = mock(Response.class);
        when(resp.getStatus()).thenReturn((int) httpStatusError);
        String payload = Files.readString(Path.of("src", "test", "resources", "spreedly_http_error_response.json"));
        when(resp.readEntity(any(Class.class))).thenReturn(payload);
        doReturn(resp).when(spreedlyPurchaseService).purchaseTransaction(any(), any(), any());
        mockUtil.mockServiceCall(
                uamServiceApi,
                s -> getAccountPersonalInfo(s, account),
                Responses.getAccountPersonalInfo(account));

        var sourceId = PlatformUtil.randomAlphanumeric(12);

        // 1st attempt
        var failedOrder1 = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT_ROUTING, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        }, sourceId);
        assertEquals(failedOrder1.getProviderName(), PaymentProvider.SPREEDLY_WORLDPAY.toString().toLowerCase());
        assertFalse(failedOrder1.isSuccess());
        final PaymentOrderError error1 = new PaymentOrderError(
                Code.ERR_PAYMENT_ROUTING.toString().toLowerCase(),
                "SpreedlyException",
                httpStatusError,
                null,
                null,
                List.of(),
                payload,
                "Payment has failed, please try again.", null);
        assertError(failedOrder1, error1);

        // 2nd attempt
        // mock emerchantpay to 500 response
        SpreedlyServiceApi serviceApi = mock(SpreedlyServiceApi.class);
        when(serviceApi.postDeliver(any(String.class), any(String.class), any(String.class), any(String.class), any(SpreedlyDeliverRequest.class))).thenReturn(resp);
        doReturn(serviceApi).when(spreedlyApiJaxRsClient).proxy(any());
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        var failedOrder2 = makeFailedPaymentWithoutFraudCheck(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT, err.getCode());// no next provider, not routable error
            assertEquals("Payment has failed, please try again.", err.getMessage());
        }, sourceId);
        assertEquals(failedOrder2.getProviderName(), PaymentProvider.SPREEDLY_EMERCHANTPAY.toString().toLowerCase());
        assertFalse(failedOrder2.isSuccess());

        final PaymentOrderError error2 = new PaymentOrderError(
                Code.ERR_PAYMENT.toString().toLowerCase(),
                "SpreedlyException",
                httpStatusError,
                null,
                null,
                List.of(),
                payload,
                "Payment has failed, please try again.", null);
        assertError(failedOrder2, error2);

        // empty chain error checking - when no available provider
        Mockito.clearInvocations(uamServiceApi, fraudServiceApi);
        var failedOrder3 = makeFailedPaymentWithoutFraudCheck(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT_ROUTING_EMPTY_CHAIN, err.getCode());
            assertEquals("There seems to be an issue with the card. Please retry after sometime or use a different payment method", err.getMessage());
        }, sourceId);
        final PaymentOrderError error3 = new PaymentOrderError(
                Code.ERR_PAYMENT_ROUTING_EMPTY_CHAIN.toString().toLowerCase(),
                "ApplicationException",
                null,
                null,
                null,
                List.of(),
                "There seems to be an issue with the card. Please retry after sometime or use a different payment method",
                "There seems to be an issue with the card. Please retry after sometime or use a different payment method", null);
        assertError(failedOrder3, error3);

        // verify method is not shown to user
        var cmd = mockUtil.toTransactionalRequest(
                GetPaymentMethodsRequest.class,
                GetPaymentMethodsRequest.newBuilder().setIdentity(identity).build(),
                GetPaymentMethodsResponse.newBuilder(),
                account.routingKey());
        getPaymentMethodsRequestHandler.apply(cmd);
        var reply = cmd.reply().build();
        Assertions.assertEquals(0, reply.getDataList().size());

        // verify saved method
        assertEquals(failedOrder1.getAccount().getPaymentMethods().size(), 1);
        var failedMethod = failedOrder1.getAccount().getPaymentMethods().get(0);
        assertFalse(failedMethod.isRemember());
        assertEquals("SpreedlyGateway", failedMethod.getType());
        assertEquals(FINGERPRINT_4111, failedMethod.getFingerprint());
        assertEquals(2, failedMethod.getErrors().size());
        assertEquals("HTTP-500", failedMethod.getErrors().get(0).getErrorCode());
        assertTrue(failedMethod.getCardPaymentMethod().getRoutingProviderCardTokens().isEmpty());
    }

    @Test
    void testProviderCustomRouting() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        prepareProvidersForRouting(brand);
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4200, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        cloud.addUps(PaymentProviderUtils.SPREEDLY_EMERCHANTPAY_WRONG_TERMINAL);
        var failedOrder = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT_ROUTING, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        });
        assertEquals(failedOrder.getProviderName(), PaymentProvider.SPREEDLY_EMERCHANTPAY.toString().toLowerCase());
        PaymentOrderError error = new PaymentOrderError(Code.ERR_PAYMENT_ROUTING.toString().toLowerCase(),
                "SpreedlyException",
                null,
                "220",
                null,
                List.of(),
                null,
                "Payment has failed, please try again.", null);
        assertErrorIgnoreMessage(failedOrder, error);
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        var successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_RAPYD, successOrder);
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        Optional<RoutingErrorConfig> e = ebean.find(RoutingErrorConfig.class).where().eq("error_code", "220").findOneOrEmpty();
        assertTrue(e.isPresent());
        assertEquals("added by system", e.get().getDescription());
        assertEquals(RoutingErrorTypeSpec.PROVIDER_CUSTOM, e.get().getErrorType());
        assertEquals(MERCHANT_ID, e.get().getFailPolicy());
        assertNull(e.get().getIntegrationType());
    }

    // rule1 condition true -> routableError -> emptyChain
    @Test
    void testProviderCustomAdvancedRoutingOneConditionTriggered() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        var account = PaymentDataGenerationService.genAccount(ebean);

        addVolumeAllocation(brand, 90, SPREEDLY_EMERCHANTPAY);
        addVolumeAllocation(brand, 10, SPREEDLY_RAPYD);
        Provider emp = addProvider(brand, 10, SPREEDLY_EMERCHANTPAY, PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase());
        addRule(STICK, "card.brand == 'visa'", List.of(emp));

        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4111, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        cloud.addUps(PaymentProviderUtils.SPREEDLY_EMERCHANTPAY_WRONG_TERMINAL);
        var failedOrder = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        });
        assertEquals(PaymentProvider.SPREEDLY_EMERCHANTPAY.toString().toLowerCase(), failedOrder.getProviderName());

        PaymentOrderError error = new PaymentOrderError(Code.ERR_PAYMENT.toString().toLowerCase(),
                "SpreedlyException",
                null,
                "220",
                null,
                List.of(),
                null,
                "Payment has failed, please try again.", null);
        assertErrorIgnoreMessage(failedOrder, error);
    }

    // rule1 condition true -> emptyChain
    @Test
    void testStickRuleTriggeredEmptyChain() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        var account = PaymentDataGenerationService.genAccount(ebean);

        addVolumeAllocation(brand, 10, SPREEDLY_EMERCHANTPAY);
        addVolumeAllocation(brand, 90, SPREEDLY_RAPYD);
        Provider emp = addProvider(brand, 10, SPREEDLY_EMERCHANTPAY, PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase());
        addProvider(brand, 0, SPREEDLY_RAPYD, PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase());
        addRule(STICK, "card.brand == 'visa'", List.of(emp));

        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4111, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        cloud.addUps(PaymentProviderUtils.SPREEDLY_EMERCHANTPAY_WRONG_TERMINAL);
        var failedOrder = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        });
        assertEquals(PaymentProvider.SPREEDLY_EMERCHANTPAY.toString().toLowerCase(), failedOrder.getProviderName());
        assertEquals(1, failedOrder.getAppliedRoutingRules().size());
        assertEquals(Set.of("1"), failedOrder.getAppliedRoutingRules());
        PaymentOrderError error = new PaymentOrderError(Code.ERR_PAYMENT.toString().toLowerCase(),
                "SpreedlyException",
                null,
                "220",
                null,
                List.of(),
                null,
                "Payment has failed, please try again.", null);
        assertErrorIgnoreMessage(failedOrder, error);
    }

    // rule1 condition true -> routableError -> not emptyChain
    @Test
    void testProviderCustomAdvancedRoutingOneConditionTriggeredStickFallback() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        var account = PaymentDataGenerationService.genAccount(ebean);

        addVolumeAllocation(brand, 10, SPREEDLY_EMERCHANTPAY);
        addVolumeAllocation(brand, 90, SPREEDLY_RAPYD);
        Provider emp = addProvider(brand, 10, SPREEDLY_EMERCHANTPAY, PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase());
        addProvider(brand, 10, SPREEDLY_RAPYD, PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase());
        addRule(STICK_WITH_FALLBACK, "card.brand == 'visa'", List.of(emp));

        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4200, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        cloud.addUps(PaymentProviderUtils.SPREEDLY_EMERCHANTPAY_WRONG_TERMINAL);
        var failedOrder = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT_ROUTING, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        });
        assertEquals(PaymentProvider.SPREEDLY_EMERCHANTPAY.toString().toLowerCase(), failedOrder.getProviderName());
        assertEquals(1, failedOrder.getAppliedRoutingRules().size());
        assertEquals(Set.of("1"), failedOrder.getAppliedRoutingRules());

        PaymentOrderError error = new PaymentOrderError(Code.ERR_PAYMENT_ROUTING.toString().toLowerCase(),
                "SpreedlyException",
                null,
                "220",
                null,
                List.of(),
                null,
                "Payment has failed, please try again.", null);
        assertErrorIgnoreMessage(failedOrder, error);

        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);
        var successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        assertTrue(successOrder.getAppliedRoutingRules().isEmpty());
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_RAPYD, successOrder);
    }
    // rule1 -> rapyd then rule2 -> emp
    @Test
    void testRuleAmount() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        var account = PaymentDataGenerationService.genAccount(ebean);

        var emp = addProvider(brand, 0, SPREEDLY_EMERCHANTPAY, PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase());
        var rapyd = addProvider(brand, 10, SPREEDLY_RAPYD, PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase());

        addRule(STICK, "offer.amount <= 100", List.of(rapyd));
        addRule(STICK, "offer.amount > 100", List.of(emp));

        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4200, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        var successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true, PlatformUtil.randomUUID(), PaymentOfferDataGenerator.OFFER_100_DOLLARS);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_RAPYD, successOrder);
        assertEquals(1, successOrder.getAppliedRoutingRules().size());
        assertEquals(Set.of("1"), successOrder.getAppliedRoutingRules());

        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        var successOrder2 = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true, PlatformUtil.randomUUID(),
                PaymentOfferDataGenerator.OFFER_9999_DOLLARS);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_EMERCHANTPAY, successOrder2);
        assertEquals(1, successOrder2.getAppliedRoutingRules().size());
        assertEquals(Set.of("2"), successOrder2.getAppliedRoutingRules());
    }

    // rules condition false -> rapyd
    @ParameterizedTest
    @MethodSource("testRuleTypes")
    void testProviderCustomAdvancedRoutingNoConditionTriggered(RoutingRuleTypeSpec ruleType) throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        var account = PaymentDataGenerationService.genAccount(ebean);

        Provider emp = addProvider(brand, 0, SPREEDLY_EMERCHANTPAY, PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase());
        Provider emp2 = addProvider(brand, 10, SPREEDLY_EMERCHANTPAY, PaymentProvider.SPREEDLY_EMERCHANTPAY_2.name().toLowerCase());
        addProvider(brand, 0, SPREEDLY_RAPYD, PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase());
        addRule(ruleType, "card.brand == 'master'", List.of(emp));
        addRule(ruleType, "card.brand == 'master'", List.of(emp2));

        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4200, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        var successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        assertTrue(successOrder.getAppliedRoutingRules().isEmpty());
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_RAPYD, successOrder);
    }

    @Test
    void testProviderCustomRoutingSkipAdding() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        prepareProvidersForRouting(brand);
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4200, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);
        prepareCustomError(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY);

        cloud.addUps(PaymentProviderUtils.SPREEDLY_EMERCHANTPAY_WRONG_TERMINAL);
        var failedOrder = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT_ROUTING, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        });
        assertEquals(failedOrder.getProviderName(), PaymentProvider.SPREEDLY_EMERCHANTPAY.toString().toLowerCase());
        PaymentOrderError error = new PaymentOrderError(Code.ERR_PAYMENT_ROUTING.toString().toLowerCase(),
                "SpreedlyException",
                null,
                "220",
                null,
                List.of(),
                null,
                "Payment has failed, please try again.", null);
        assertErrorIgnoreMessage(failedOrder, error);
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        var successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_RAPYD, successOrder);
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        List<RoutingErrorConfig> routingErrorsConfigs = ebean.routingRepo().getRoutingErrorConfigs(RoutingRepo.FindRoutingErrorsParams.builder()
                .codes(Lists.newArrayList("220"))
                .routingType(RoutingTypeSpec.SPREEDLY)
                .country("US")
                .brand(brand.getName())
                .build());

        assertEquals(1, routingErrorsConfigs.size());
        assertEquals("220", routingErrorsConfigs.get(0).getErrorCode());
        assertNotNull(routingErrorsConfigs.get(0).getDescription());
        assertEquals(RoutingErrorTypeSpec.PROVIDER_CUSTOM, routingErrorsConfigs.get(0).getErrorType());
        assertEquals(MERCHANT_ID, routingErrorsConfigs.get(0).getFailPolicy());
        assertEquals(SPREEDLY_EMERCHANTPAY, routingErrorsConfigs.get(0).getIntegrationType());
    }

    @Test
    void testProviderCustomRoutingForbiddenForAutomatisation() throws Throwable {
        cfg.setLocalProperty(props.AUTOMATED_CUSTOM_ERROR_ROUTING_FORBIDDEN_ERRORS.getKey(), "220");
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        prepareProvidersForRouting(brand);
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4111, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        cloud.addUps(PaymentProviderUtils.SPREEDLY_EMERCHANTPAY_WRONG_TERMINAL);
        var failedOrder = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        });
        assertEquals(failedOrder.getProviderName(), PaymentProvider.SPREEDLY_EMERCHANTPAY.toString().toLowerCase());
        PaymentOrderError error = new PaymentOrderError(Code.ERR_PAYMENT.toString().toLowerCase(),
                "SpreedlyException",
                null,
                "220",
                null,
                List.of(),
                null,
                "Payment has failed, please try again.", null);
        assertErrorIgnoreMessage(failedOrder, error);
        cfg.setLocalProperty(props.AUTOMATED_CUSTOM_ERROR_ROUTING_FORBIDDEN_ERRORS.getKey(), List.of());
    }

    @Test
    void testRoutingChainIsEmptyAfterRouting() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        ebean.save(RoutingErrorConfig.builder().errorCode("220").errorType(RoutingErrorTypeSpec.PROVIDER_CUSTOM).failPolicy(MERCHANT_ID)
                .providerType(RoutingTypeSpec.SPREEDLY).build());
        ebean.save(
                Provider.builder()
                        .code(PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase())
                        .paymentMode(PaymentMode.CARD)
                        .integrationType(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY)
                        .brand(brand)
                        .type(ProviderTypeSpec.PURCHASE)
                        .description(UtilConstant.DESCRIPTION)
                        .version(1)
                        .build());
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4111, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        cloud.addUps(PaymentProviderUtils.SPREEDLY_EMERCHANTPAY_WRONG_TERMINAL);
        var failedOrder = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        });
        assertEquals(failedOrder.getProviderName(), PaymentProvider.SPREEDLY_EMERCHANTPAY.toString().toLowerCase());
        PaymentOrderError error = new PaymentOrderError(Code.ERR_PAYMENT.toString().toLowerCase(),
                "SpreedlyException",
                null,
                "220",
                null,
                List.of(),
                "Invalid Terminal",
                "Payment has failed, please try again.", null);
        assertError(failedOrder, error);
    }

    @Test
    void testFraudRuleRouting() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);

        addProvider(brand, 0, SPREEDLY_EMERCHANTPAY, PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase());
        addProvider(brand, 0, SPREEDLY_RAPYD, PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase());
        prepareFraudRuleRouting();

        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4200, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        mockUtil.mockServiceCall(
                uamServiceApi,
                s -> getAccountPaymentInfo(s, account),
                Responses.getAccountPurchaseInfoResponse(account));

        var failedOrder = makeFailedPayment(account, card, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT_ROUTING, err.getCode());
            assertEquals("Payment has failed, please try again.", err.getMessage());
        });
        assertEquals(failedOrder.getProviderName(), PaymentProvider.SPREEDLY_EMERCHANTPAY.toString().toLowerCase());
        PaymentOrderError error = new PaymentOrderError(Code.ERR_PAYMENT_ROUTING.toString().toLowerCase(),
                null,
                null,
                null,
                null,
                List.of(APPLIED_FRAUD_RULE),
                "Routing rules triggered",
                "Payment has failed, please try again.", null);
        assertErrorIgnoreMessage(failedOrder, error);
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        var successOrder = makeSuccesfulPayment(account, card, PaymentProvider.SPREEDLY, true);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_RAPYD, successOrder);

        // rule is not applied anymore next time it will go back to emerchant pay
        mockUtil.mockServiceCall(
                fraudServiceApi,
                s -> s.paymentFraudCheck(any(), any()),
                PaymentFraudCheckResponse.newBuilder().setIsFraud(false).build());
        successOrder = createSuccesfulPaymentOrder(account, card, PaymentProvider.SPREEDLY, true, PlatformUtil.randomUUID(), PaymentOfferDataGenerator.OFFER_3000_SC);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_RAPYD, successOrder);

        // fraud rule is not applied anymore can go back to emerchantpay
        successOrder = createSuccesfulPaymentOrder(account, card, PaymentProvider.SPREEDLY, true, PlatformUtil.randomUUID(), PaymentOfferDataGenerator.OFFER_3000_SC);
        checkSuccessfulPayment(card, PaymentProvider.SPREEDLY_EMERCHANTPAY, successOrder);

        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);
    }

    @Test
    void testTwoCreatePaymentOrderRequestWithTheSameTransactionId() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        prepareCardNetworkRouting(brand);
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4111, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        var trxId = PlatformUtil.randomUUID();

        var failedOrder = makeFailedPaymentWithTransactionId(account, card, trxId, PaymentProvider.SPREEDLY, (err) -> {
            assertEquals(Code.ERR_PAYMENT_ROUTING, err.getCode());
            assertEquals("Something went wrong with your card. Please contact your bank.", err.getMessage());
        });
        Mockito.clearInvocations(uamServiceApi);
        Mockito.clearInvocations(fraudServiceApi);

        assertEquals(failedOrder.getProviderName(), PaymentProvider.SPREEDLY_EMERCHANTPAY.toString().toLowerCase());
        assertFalse(failedOrder.isSuccess());
        final PaymentOrderError error = new PaymentOrderError(Code.ERR_PAYMENT_ROUTING.toString().toLowerCase(),
                "SpreedlyException",
                null,
                null,
                "01",
                List.of(),
                "card_number is invalid or missing",
                "Something went wrong with your card. Please contact your bank.", null);
        assertError(failedOrder, error);
        assertNotNullCardTransactionDetails(failedOrder);

        ApplicationException err = assertThrows(
                ApplicationException.class,
                () -> {
                    CreatePaymentOrderRequest.Builder req = CreatePaymentOrderRequest.newBuilder();
                    req.setIdentity(Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)));
                    req.setTransactionId(trxId.toString());
                    req.setOffer(PaymentOfferDataGenerator.OFFER_3000_SC);
                    req.setProvider(PaymentProvider.SPREEDLY);
                    req.setToken(PlatformUtil.randomUUID().toString());
                    var cmd = mockUtil.toTransactionalRequest(CreatePaymentOrderRequest.class, req.build(), CreatePaymentOrderResponse.newBuilder());
                    cmd.setRoutingKey(account.routingKey());
                    createPaymentOrderRequestHandler.apply(cmd);
                });
        assertEquals(Code.ERR_DUPLICATE, err.getCode());
        assertEquals("Transaction already processed - Please contact {link_support}", err.getMessage());
        var order = ebean.paymentRepo().orderByTransactionId(trxId, null);
        assertTrue(order.isPresent());
        assertEquals("card_number is invalid or missing", order.get().getError().getMessage());
    }

    @Test
    void testInputCvvIsRequriedForNewTokenization() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        prepareCardNetworkRouting(brand);
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4111, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);
        var mocks = mockRequiredPaymentCalls(account, card);

        // Spreedly needs cvv (cvv is not stored) to be entered to tokenize card in provider
        var order = makeFailedPaymentInputCvv(account, card, (err) -> assertEquals(Code.ERR_PAYMENT_INPUT_CVV, err.getCode()));
        assertInputCvvError(order);
        mocks.verify();
    }

    @Test
    void testInputCvvFraudLabel() throws Throwable {
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        prepareCardNetworkRouting(brand);
        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_4111, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        var mocks = new Mocks(uamMocks(account).asList());
        mocks.add(mockUtil.mockServiceCall(
                fraudServiceApi,
                s -> s.paymentFraudCheck(any(), any()),
                PaymentFraudCheckResponse.newBuilder().setInfo(FraudInfo.newBuilder().setRequestId("123").build()).build()));
        var labelCapture = ArgumentCaptor.forClass(LabelTransactionRequest.class);
        mocks.add(mockUtil.mockServiceCall(
                fraudServiceApi,
                s -> s.labelTransaction(labelCapture.capture(), eq(account.routingKey())),
                LabelTransactionResponse.getDefaultInstance()));
        mocks.add(new MockUtil.MockCall(m -> Assertions.assertEquals(FAILED + ": " + ERR_PAYMENT_INPUT_CVV.getValue(), labelCapture.getValue().getLabel())));

        ApplicationException err = assertThrows(
                ApplicationException.class,
                () -> {
                    CreatePaymentOrderRequest.Builder req = CreatePaymentOrderRequest.newBuilder();
                    req.setIdentity(Identity.newBuilder().setByToken(IdentityUtil.getIdentityByToken(identityManager, account)));
                    req.setTransactionId(PlatformUtil.randomUUID().toString());
                    req.setOffer(PaymentOfferDataGenerator.OFFER_3000_SC);
                    req.setToken(card.getToken());
                    req.setCvvEntered(false);
                    req.setProvider(PaymentProvider.SPREEDLY);
                    var cmd = mockUtil.toTransactionalRequest(CreatePaymentOrderRequest.class, req.build(), CreatePaymentOrderResponse.newBuilder());
                    cmd.setRoutingKey(account.routingKey());
                    createPaymentOrderRequestHandler.apply(cmd);
                });
        Assertions.assertEquals(Code.ERR_PAYMENT_INPUT_CVV, err.getCode());
        mocks.verify();
    }

    private void assertInputCvvError(PaymentOrder savedOrder) {
        // source id is set for all transaction with routing
        assertTrue(StringUtils.isNotBlank(savedOrder.getSourceId()));
        assertFalse(savedOrder.isSuccess());
        // assert correct provider is set due to routing configuration
        assertEquals(PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase(), savedOrder.getProviderName());
        final PaymentOrderError expected = new PaymentOrderError(
                Code.ERR_PAYMENT_INPUT_CVV.toString().toLowerCase(),
                "IllegalArgumentException",
                null,
                null,
                null,
                List.of(),
                "Missing CVV",
                "Payment has failed, please try again.", null);
        assertError(savedOrder, expected);
    }

    @Test
    void testPaymentMethodAndScopeWithErrorDeserialization() throws Throwable {
        String uri = "https://name:<EMAIL>";
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        var si = new PlainServiceInfo("brand-spreedly", uri);
        cloud.addUps(si);

        String payload = Files.readString(Path.of("src", "test", "resources", "spreedly_payment_method_with_error.json"));
        SpreedlyApiJaxRsClient client = mock(SpreedlyApiJaxRsClient.class, withSettings().withoutAnnotations());
        SpreedlyServiceApi serviceApi = mock(SpreedlyServiceApi.class);
        when(client.proxy(any())).thenReturn(serviceApi);

        SpreedlyPurchaseService purchaseService = new SpreedlyPurchaseService(cloud, client);

        when(serviceApi.resolve(any(String.class), any(String.class), any(String.class), any(String.class))).thenReturn(mapper.readValue(payload, SpreedlyTransactionResponse.class));
        ArgumentCaptor<String> captor = ArgumentCaptor.forClass(String.class);

        SpreedlyPaymentMethod token = purchaseService.resolve("token", paymentContext("brand"), PaymentProto.UPS_SPREEDLY);
        verify(serviceApi).resolve(captor.capture(), anyString(), anyString(), anyString());

        assertFalse(token.getErrors().isEmpty());
        assertEquals("Year is expired", token.getErrors().get(0).getMessage());
        assertEquals(captor.getValue(), BasicAuthHelper.createHeader(si.getUserName(), si.getPassword()));

        Mockito.clearInvocations(serviceApi);

        var siUs = new PlainServiceInfo("brand-us-spreedly", "https://name-us:<EMAIL>");
        cloud.addUps(siUs);
        purchaseService.resolve("token", paymentContext("brand", "US"), PaymentProto.UPS_SPREEDLY);
        verify(serviceApi).resolve(captor.capture(), anyString(), anyString(), anyString());
        assertEquals(captor.getValue(), BasicAuthHelper.createHeader(siUs.getUserName(), siUs.getPassword()));

    }

    private void prepareCardNetworkRouting(ImmutableBrand brand) {
        ebean.save(RoutingErrorConfig.builder()
                .errorCode("01")
                .errorType(RoutingErrorTypeSpec.CARD_NETWORK)
                .failPolicy(INTEGRATION_TYPE)
                .providerType(RoutingTypeSpec.SPREEDLY).build());
        ebean.save(RoutingErrorConfig.builder()
                .errorCode("51")
                .errorType(RoutingErrorTypeSpec.CARD_NETWORK)
                .failPolicy(INTEGRATION_TYPE)
                .providerType(RoutingTypeSpec.SPREEDLY).build());
        ebean.save(
                Provider.builder()
                        .code(PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase())
                        .brand(brand)
                        .type(ProviderTypeSpec.PURCHASE)
                        .paymentMode(PaymentMode.CARD)
                        .integrationType(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY)
                        .description(UtilConstant.DESCRIPTION)
                        .version(1)
                        .build());
        ebean.save(
                Provider.builder()
                        .code(PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase())
                        .brand(brand)
                        .paymentMode(PaymentMode.CARD)
                        .type(ProviderTypeSpec.PURCHASE)
                        .integrationType(ProviderIntegrationTypeSpec.SPREEDLY_RAPYD)
                        .description(UtilConstant.DESCRIPTION)
                        .version(1)
                        .build());
        ebean.save(
                Provider.builder()
                        .code(PaymentProvider.SPREEDLY_FISERV.name().toLowerCase())
                        .brand(brand)
                        .paymentMode(PaymentMode.CARD)
                        .type(ProviderTypeSpec.PURCHASE)
                        .integrationType(ProviderIntegrationTypeSpec.SPREEDLY_FISERV)
                        .description(UtilConstant.DESCRIPTION)
                        .version(1)
                        .build());
    }

    private void prepareHttpRouting(ImmutableBrand brand, int httpStatusError) {
        ebean.save(RoutingErrorConfig.builder()
                .errorCode("HTTP-" + httpStatusError)
                .errorType(RoutingErrorTypeSpec.HTTP_CODE)
                .providerType(RoutingTypeSpec.SPREEDLY)
                .failPolicy(INTEGRATION_TYPE)
                .build());
        ebean.save(Provider.builder()
                .code(PaymentProvider.SPREEDLY_WORLDPAY.name().toLowerCase())
                .brand(brand)
                .paymentMode(PaymentMode.CARD)
                .type(ProviderTypeSpec.PURCHASE)
                .integrationType(ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY)
                .description(UtilConstant.DESCRIPTION)
                .version(1)
                .build());

        ebean.save(Provider.builder()
                .code(PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase())
                .brand(brand)
                .paymentMode(PaymentMode.CARD)
                .type(ProviderTypeSpec.PURCHASE)
                .integrationType(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY)
                .description(UtilConstant.DESCRIPTION)
                .version(1)
                .build());
    }

    private void prepareProvidersForRouting(ImmutableBrand brand) {
        ebean.save(
                Provider.builder()
                        .code(PaymentProvider.SPREEDLY_EMERCHANTPAY.name().toLowerCase())
                        .brand(brand)
                        .type(ProviderTypeSpec.PURCHASE)
                        .version(1)
                        .paymentMode(PaymentMode.CARD)
                        .integrationType(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY)
                        .countries(List.of("US", "IT", "GB"))
                        .description(UtilConstant.DESCRIPTION)
                        .build());
        ebean.save(
                Provider.builder()
                        .code(PaymentProvider.SPREEDLY_EMERCHANTPAY_2.name().toLowerCase())
                        .brand(brand)
                        .type(ProviderTypeSpec.PURCHASE)
                        .version(1)
                        .paymentMode(PaymentMode.CARD)
                        .integrationType(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY)
                        .countries(List.of("IT"))
                        .description(UtilConstant.DESCRIPTION)
                        .build());
        ebean.save(
                Provider.builder()
                        .code(PaymentProvider.SPREEDLY_RAPYD.name().toLowerCase())
                        .brand(brand)
                        .paymentMode(PaymentMode.CARD)
                        .type(ProviderTypeSpec.PURCHASE)
                        .version(1)
                        .integrationType(ProviderIntegrationTypeSpec.SPREEDLY_RAPYD)
                        .description(UtilConstant.DESCRIPTION)
                        .build());
    }

    private void addVolumeAllocation(ImmutableBrand brand, int allocation, ProviderIntegrationTypeSpec integrationTypeSpec) {
        ebean.save(VolumeAllocationConfig.builder()
                .brand(brand)
                .currency("USD")
                .country("US")
                .allocation(allocation)
                .paymentMode(PaymentMode.CARD)
                .integrationType(integrationTypeSpec)
                .build(), null);
    }

    private Provider addProvider(ImmutableBrand brand, int providerVolume, ProviderIntegrationTypeSpec integrationTypeSpec, String code) {
        var provider = Provider.builder()
                .code(code)
                .brand(brand)
                .type(ProviderTypeSpec.PURCHASE)
                .version(1)
                .paymentMode(PaymentMode.CARD)
                .integrationType(integrationTypeSpec)
                .description(UtilConstant.DESCRIPTION)
                .build();
        ebean.save(provider);
        if (providerVolume != 0) {
            ebean.save(PaymentAggregatedMetaInfo.builder()
                    .provider(provider)
                    .at(PlatformUtil.toLocalUTCDate())
                    .amount(BigDecimal.valueOf(providerVolume))
                    .purchaseCount(10L)
                    .country("US")
                    .currency("USD")
                    .build());
        }
        return provider;
    }

    private void addRoutableError(String code) {
        ebean.save(RoutingErrorConfig.builder()
                .providerType(RoutingTypeSpec.SPREEDLY)
                .errorType(RoutingErrorTypeSpec.PROVIDER_CUSTOM)
                .errorCode(code)
                .failPolicy(MERCHANT_ID)
                .build());
    }

    private void addCardNetworkError(String code) {
        ebean.save(RoutingErrorConfig.builder()
                .providerType(RoutingTypeSpec.SPREEDLY)
                .errorType(RoutingErrorTypeSpec.CARD_NETWORK)
                .errorCode(code)
                .failPolicy(INTEGRATION_TYPE)
                .build());
    }

    private RoutingRule addRule(RoutingRuleTypeSpec ruleType, String condition, List<Provider> providers) {
        var rule = RoutingRule.builder()
                .ruleType(ruleType)
                .routingType(RoutingTypeSpec.SPREEDLY)
                .priority(1)
                .providers(providers)
                .condition(condition)
                .build();
        ebean.save(rule);
        return rule;
    }

    void testRoutingErrorEvaluationConditionCommon(Consumer<String> retryConditionApplier) throws Throwable {
        PaymentProviderUtils.addPurchaseProvider(ebean, PurchaseProviderSpec.SPREEDLY_FISERV, ProviderIntegrationTypeSpec.SPREEDLY_FISERV);
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);

        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_FISERV_51_ERROR, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        // do success payment
        var successOrder = makePayment(account, card, PaymentProvider.SPREEDLY, true, PlatformUtil.randomUUID(), OFFER_10_DOLLARS);
        assertFalse(successOrder.isRetryConditionTriggered(), "Retry condition is not used");

        // ERR_PAYMENT - no other providers
        final UUID failedTrxId = PlatformUtil.randomUUID();
        Assertions.assertThrows(
                ApplicationException.class,
                () -> makePayment(account, card, PaymentProvider.SPREEDLY, true, failedTrxId, OFFER_5000_DOLLARS));
        var failedOrder = DataAccessService.getOrder(ebean, failedTrxId);
        assertEquals(Code.ERR_PAYMENT.toString().toLowerCase(), failedOrder.getError().getErrorCode());
        assertEquals("DECLINED: ", failedOrder.getError().getMessage());
        assertFalse(failedOrder.isRetryConditionTriggered(), "Retry condition is not used");

        // ERR_PAYMENT_ROUTING_EMPTY_CHAIN - providers skips due prev error,
        final UUID failedTrxId2 = PlatformUtil.randomUUID();
        Assertions.assertThrows(
                ApplicationException.class,
                () -> makePayment(account, card, PaymentProvider.SPREEDLY, true, failedTrxId2, OFFER_5000_DOLLARS));
        var failedOrder2 = DataAccessService.getOrder(ebean, failedTrxId2);
        assertEquals(Code.ERR_PAYMENT_ROUTING_EMPTY_CHAIN.toString().toLowerCase(), failedOrder2.getError().getErrorCode());
        assertFalse(failedOrder2.isRetryConditionTriggered(), "Retry condition is not used");

        // ERR_PAYMENT_ROUTING - has success Purchase With Same Integration, endless cycle
        retryConditionApplier.accept("hasPurchaseWithSameIntegration()");

        final UUID failedTrxId3 = PlatformUtil.randomUUID();
        Assertions.assertThrows(
                ApplicationException.class,
                () -> makePayment(account, card, PaymentProvider.SPREEDLY, true, failedTrxId3, OFFER_5000_DOLLARS));
        var failedOrder3 = DataAccessService.getOrder(ebean, failedTrxId3);
        assertEquals(Code.ERR_PAYMENT_ROUTING.toString().toLowerCase(), failedOrder3.getError().getErrorCode());
        assertTrue(failedOrder3.isRetryConditionTriggered(), "Retry condition used");

        // ERR_PAYMENT_ROUTING_EMPTY_CHAIN - 120sec hasn't passed
        retryConditionApplier.accept("hasPurchaseWithSameIntegration() && routingErrorHasExpiredSec(120)");

        final UUID failedTrxId4 = PlatformUtil.randomUUID();
        Assertions.assertThrows(
                ApplicationException.class,
                () -> makePayment(account, card, PaymentProvider.SPREEDLY, true, failedTrxId4, OFFER_5000_DOLLARS));
        var failedOrder4 = DataAccessService.getOrder(ebean, failedTrxId4);
        assertEquals(Code.ERR_PAYMENT_ROUTING_EMPTY_CHAIN.toString().toLowerCase(), failedOrder4.getError().getErrorCode());
        assertFalse(failedOrder4.isRetryConditionTriggered(), "Retry condition is not used");

        // update error last seen to met retryCondition
        var acc = ebean.accountRepo().requiredAccount(account.getId(), null);
        for (var m : acc.getPaymentMethods()) {
            m.getErrors().forEach(e -> {
                e.setLastSeen(new Date(e.getLastSeen().getTime() - TimeUnit.DAYS.toMillis(5)));
                ebean.save(e);
            });
        }

        // ERR_PAYMENT - 120sec passed before prev error, trying to make real payment
        final UUID failedTrxId5 = PlatformUtil.randomUUID();
        Assertions.assertThrows(
                ApplicationException.class,
                () -> makePayment(account, card, PaymentProvider.SPREEDLY, true, failedTrxId5, OFFER_5000_DOLLARS));
        var failedOrder5 = DataAccessService.getOrder(ebean, failedTrxId5);
        assertEquals(Code.ERR_PAYMENT.toString().toLowerCase(), failedOrder5.getError().getErrorCode());
        assertTrue(failedOrder5.isRetryConditionTriggered(), "Retry condition used");
    }

    @Test
    void testRuleTriggeredByFiservAssociatedError() throws Throwable {
        PaymentProviderUtils.addPurchaseProvider(ebean, PurchaseProviderSpec.SPREEDLY_FISERV, ProviderIntegrationTypeSpec.SPREEDLY_FISERV);
        var rapyd = PaymentProviderUtils.addPurchaseProvider(ebean, PurchaseProviderSpec.SPREEDLY_RAPYD, SPREEDLY_RAPYD);
        ImmutableBrand brand = PaymentBrandDataGenerator.getBrand((PaymentEbeanJpaManager) ebean);
        addRoutableError("051");
        addCardNetworkError("51");
        long ruleId = addRule(EXCLUDE, "hasCardNetworkError(\"51\") && hasProviderError(\"051\",\"spreedly_fiserv\")", List.of(rapyd)).getId();

        ImmutableAccount account = PaymentDataGenerationService.genAccount(ebean);
        SpreedlyPaymentMethod card = cardService.tokenize(CardService.SPREEDLY_FISERV_51_ERROR, paymentContext(brand.getName()),
                PaymentProto.UPS_SPREEDLY);

        final UUID failedTrxId = PlatformUtil.randomUUID();
        Assertions.assertThrows(
                ApplicationException.class,
                () -> makePayment(account, card, PaymentProvider.SPREEDLY, true, failedTrxId, OFFER_5000_DOLLARS));
        var failedOrder = DataAccessService.getOrder(ebean, failedTrxId);

        assertEquals(Code.ERR_PAYMENT.toString().toLowerCase(), failedOrder.getError().getErrorCode());
        assertEquals(1, failedOrder.getAppliedRoutingRules().size());
        assertTrue(failedOrder.getAppliedRoutingRules().contains(String.valueOf(ruleId)));
    }

    private void prepareFraudRuleRouting() {
        ebean.save(RoutingRule.builder().ruleId(AbstractSpreedlyPaymentTest.APPLIED_FRAUD_RULE)
                .ruleType(RoutingRuleTypeSpec.EXCLUDE)
                .routingType(RoutingTypeSpec.SPREEDLY)
                .integrationTypes(List.of(SPREEDLY_EMERCHANTPAY)).build());
    }

    private void prepareCustomError(ProviderIntegrationTypeSpec providerIntegrationType) {
        ebean.save(RoutingErrorConfig.builder().errorCode("220").errorType(RoutingErrorTypeSpec.PROVIDER_CUSTOM)
                .description("test description")
                .integrationType(providerIntegrationType).failPolicy(MERCHANT_ID).build());
    }

    private void savePaymentAggregatedMetaInfo(Provider provider, BigDecimal amount) {
        ebean.save(PaymentAggregatedMetaInfo.builder()
                .provider(provider)
                .at(PlatformUtil.toLocalUTCDate())
                .amount(amount)
                .purchaseCount(10L)
                .country("US")
                .currency("USD")
                .build());
    }

    private void addToPaymentAggregatedMetaInfo(Provider provider, BigDecimal amount) {
        PaymentAggregatedMetaInfo paymentAggregatedMetaInfo = ebean.paymentRepo().aggregatedMetaInfo("US", provider, "USD", PlatformUtil.toLocalUTCDate(), null)
                .orElseThrow();
        paymentAggregatedMetaInfo.setAmount(paymentAggregatedMetaInfo.getAmount().add(amount));
        ebean.save(paymentAggregatedMetaInfo);
    }

    private void saveVolumeAllocation(ProviderIntegrationTypeSpec integrationType, int allocation, ImmutableBrand brand) {
        ebean.save(VolumeAllocationConfig.builder()
                .brand(brand)
                .currency("USD")
                .country("US")
                .allocation(allocation)
                .integrationType(integrationType)
                .paymentMode(PaymentMode.CARD)
                .build(), null);
    }

    public static Stream<Arguments> testRuleTypes() {
        return Stream.of(
                Arguments.of(STICK),
                Arguments.of(STICK_WITH_FALLBACK));
    }
}
