package rnd.services;

import static java.util.Arrays.asList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static rnd.BigDecimalUtil.bd;
import static rnd.MatchUtil.match;
import static rnd.model.Clause.Yes.yes;
import static rnd.model.alert.TradingThresholds.Subject.*;
import static rnd.model.main.OverUnder.OVER;
import static rnd.model.main.OverUnder.UNDER;
import static rnd.utils.MarketUtil.absoluteDiscountedLineFor;
import static rnd.utils.MarketUtil.altBalancedBrandProp;
import static rnd.utils.MarketUtil.altBrandProp;
import static rnd.utils.MarketUtil.discountedPropFor;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

import org.apache.commons.collections4.ListUtils;
import org.assertj.core.api.AbstractObjectAssert;
import org.assertj.core.api.ObjectAssert;
import org.assertj.core.api.ThrowingConsumer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;

import io.ebeaninternal.server.util.Str;
import rnd.DynamicDataTest;
import rnd.model.TestEpl;
import rnd.model.TestNba;
import rnd.model.alert.TradingAlertConfig;
import rnd.model.alert.TradingMarketStats;
import rnd.model.alert.TradingThresholds;
import rnd.model.alert.query.QTradingMarketStats;
import rnd.model.data.BrandProp;
import rnd.model.data.LineType;
import rnd.model.data.Match;
import rnd.model.data.Player;
import rnd.model.main.Brand;
import rnd.model.main.OverUnder;
import rnd.model.main.UserSegment;
import rnd.model.main.pickem.DiscountedLine;
import rnd.model.main.pickem.Entry;
import rnd.model.main.pickem.Pick;
import rnd.utils.MarketUtil;

class TradingStatsTest extends DynamicDataTest {
    @Autowired
    private DefaultTradingAlertsService tradingAlertsService;

    protected Match lakersVsKnicks;
    protected Match bullsVsCeltics;
    private Match liverpoolVsMancity;

    private TestNba swishNba;
    private TestEpl ooEpl;

    private UserSegment whateverSegment;
    private TradingAlertConfig defaultTradingConfig;

    @BeforeEach
    void setUp() throws Throwable {
        when(pickEmConfig.alternativeTradingMarketStatsEnabled()).thenReturn(true);

        swishNba = new TestNba(td.swishAnalytics, td.nba);
        lakersVsKnicks = match(swishNba.lakers, swishNba.knicks, "lakers_nicks");
        bullsVsCeltics = match(swishNba.bulls, swishNba.celtics, "bulls_celtics");
        ooEpl = new TestEpl(td.opticOdds3, td.engPremier);
        liverpoolVsMancity = match(ooEpl.liverpool, ooEpl.manCity, "liverpool_mancity");
        whateverSegment = MarketUtil.userSegment(td.brandMain, yes());
        defaultTradingConfig = TradingAlertConfig.builder()
                .league(td.nba)
                .brand(td.brandMain)
                .wagerThreshold1(bd(100))
                .wagerThreshold2(bd(200))
                .wagerThreshold3(bd(300))
                .exposureThreshold1(bd(1000))
                .exposureThreshold2(bd(2000))
                .exposureThreshold3(bd(3000))
                .build();

        saveAllInTx(
                swishNba.all(),
                lakersVsKnicks, bullsVsCeltics,
                ooEpl.all(),
                liverpoolVsMancity,
                whateverSegment);
    }

    private List<TradingMarketStats> stats;

    @ParameterizedTest
    @ValueSource(booleans = { true, false })
    public void balancedBrandProps(boolean altStatsEnabled) throws Throwable {
        when(pickEmConfig.alternativeTradingMarketStatsEnabled()).thenReturn(altStatsEnabled);
        BrandProp lebronAssists1_5Balanced = altBalancedBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersLebron,
                "assists", 1.5);
        BrandProp lebronPoints2_5Balanced = altBalancedBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersLebron,
                "points", 2.5);
        saveAllInTx(defaultTradingConfig, lebronAssists1_5Balanced, lebronPoints2_5Balanced);
        entry(2, 10)
                .pick(lebronAssists1_5Balanced, OVER)
                .pick(lebronPoints2_5Balanced, UNDER)
                .submit();

        entry(3, 10)
                .pick(lebronPoints2_5Balanced, UNDER)
                .submit();

        assertThat(stats).hasSize(altStatsEnabled ? 4 : 2);
        assertStandardStat(lakersVsKnicks, swishNba.lakersLebron, "assists", OVER).satisfies(
                stats(2, 20, 1), nothingTriggered(), nothingReported());
        assertStandardStat(lakersVsKnicks, swishNba.lakersLebron, "points", UNDER).satisfies(
                stats(5, 50, 2), nothingTriggered(), nothingReported());
        if (altStatsEnabled) {
            assertAltStat(lakersVsKnicks, swishNba.lakersLebron, "assists", OVER, 1.5).satisfies(
                    stats(2, 20, 1), nothingTriggered(), nothingReported());
            assertAltStat(lakersVsKnicks, swishNba.lakersLebron, "points", UNDER, 2.5).satisfies(
                    stats(5, 50, 2), nothingTriggered(), nothingReported());
        }
    }

    @Test
    public void balancedAndUnbalanced() throws Throwable {
        when(pickEmConfig.alternativeTradingMarketStatsEnabled()).thenReturn(true);
        BrandProp lebronAssists1_5 = altBalancedBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersLebron,
                "assists", 1.5);
        BrandProp lebronPoints2_5Balanced = altBalancedBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersLebron,
                "points", 2.5);
        saveAllInTx(defaultTradingConfig, lebronAssists1_5, lebronPoints2_5Balanced);
        entry(2, 10)
                .pick(lebronAssists1_5, OVER)
                .pick(lebronPoints2_5Balanced, UNDER)
                .submit();

        lebronAssists1_5.setProbsProportionally(bd(0.3));
        lebronAssists1_5.setBalanced(false);

        entry(2, 10)
                .pick(lebronAssists1_5, OVER)
                .pick(lebronPoints2_5Balanced, UNDER)
                .submit();

        assertThat(stats).hasSize(4);
        // the unbalanced pick doesn't count
        assertStandardStat(lakersVsKnicks, swishNba.lakersLebron, "assists", OVER).satisfies(
                stats(2, 20, 1), nothingTriggered(), nothingReported());
        assertStandardStat(lakersVsKnicks, swishNba.lakersLebron, "points", UNDER).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
        assertAltStat(lakersVsKnicks, swishNba.lakersLebron, "assists", OVER, 1.5).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
        assertAltStat(lakersVsKnicks, swishNba.lakersLebron, "points", UNDER, 2.5).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
    }

    @Test
    public void discountedStats() throws Throwable {
        when(pickEmConfig.alternativeTradingMarketStatsEnabled()).thenReturn(true);
        BrandProp lebronAssists1_5Unbalanced = altBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersLebron,
                "assists", 1.5, false);
        BrandProp lebronPoints2_5Balanced = altBalancedBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersLebron,
                "points", 2.5);
        DiscountedLine discountedLine = absoluteDiscountedLineFor(lebronPoints2_5Balanced, whateverSegment, 0.5);
        BrandProp lebronPoints0_5Discounted = discountedPropFor(lebronPoints2_5Balanced, discountedLine, 0.5);

        saveAllInTx(defaultTradingConfig, lebronAssists1_5Unbalanced, lebronPoints2_5Balanced, lebronPoints0_5Discounted);

        entry(2, 10)
                .pick(lebronAssists1_5Unbalanced, OVER)
                .pick(lebronPoints2_5Balanced, UNDER)
                .pick(lebronPoints0_5Discounted, OVER)
                .submitTimes(2);

        assertThat(stats).hasSize(4);
        assertStandardStat(lakersVsKnicks, swishNba.lakersLebron, "points", UNDER).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
        assertAltStat(lakersVsKnicks, swishNba.lakersLebron, "points", UNDER, 2.5).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
        assertAltStat(lakersVsKnicks, swishNba.lakersLebron, "assists", OVER, 1.5).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
        assertDiscountedStat(lakersVsKnicks, swishNba.lakersLebron, "points", OVER).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
    }

    @Test
    public void differentLines() throws Throwable {
        when(pickEmConfig.alternativeTradingMarketStatsEnabled()).thenReturn(true);
        BrandProp lebronAssists1_5 = altBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersLebron,
                "assists", 1.5, false);
        lebronAssists1_5.setProbsProportionally(bd(0.4));
        BrandProp lebronAssists2_5 = altBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersLebron,
                "assists", 2.5, true);
        BrandProp lebronAssists3_5 = altBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersLebron,
                "assists", 3.5, false);
        lebronAssists3_5.setProbsProportionally(bd(0.6));

        saveAllInTx(defaultTradingConfig, lebronAssists1_5, lebronAssists2_5, lebronAssists3_5);
        entry(2, 10)
                .pick(lebronAssists1_5, OVER)
                .pick(lebronAssists2_5, OVER)
                .pick(lebronAssists3_5, OVER)
                .submitTimes(2);

        assertThat(stats).hasSize(4);
        // the unbalanced pick doesn't count
        assertStandardStat(lakersVsKnicks, swishNba.lakersLebron, "assists", OVER).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
        assertAltStat(lakersVsKnicks, swishNba.lakersLebron, "assists", OVER, 1.5).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
        assertAltStat(lakersVsKnicks, swishNba.lakersLebron, "assists", OVER, 2.5).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
        assertAltStat(lakersVsKnicks, swishNba.lakersLebron, "assists", OVER, 3.5).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
    }

    @Test
    public void overUnderSeparate() throws Throwable {
        when(pickEmConfig.alternativeTradingMarketStatsEnabled()).thenReturn(true);
        BrandProp lebronPoints2_5Balanced = altBalancedBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersLebron,
                "points", 2.5);

        saveAllInTx(lebronPoints2_5Balanced);

        entry(2, 10)
                .pick(lebronPoints2_5Balanced, UNDER)
                .pick(lebronPoints2_5Balanced, OVER)
                .submitTimes(2);

        assertThat(stats).hasSize(4);
        assertStandardStat(lakersVsKnicks, swishNba.lakersLebron, "points", UNDER).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
        assertAltStat(lakersVsKnicks, swishNba.lakersLebron, "points", UNDER, 2.5).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
        assertStandardStat(lakersVsKnicks, swishNba.lakersLebron, "points", OVER).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
        assertAltStat(lakersVsKnicks, swishNba.lakersLebron, "points", OVER, 2.5).satisfies(
                stats(4, 40, 2), nothingTriggered(), nothingReported());
    }

    private void testRank(ThresholdRank rank, LineType type, BrandProp testBp, BrandProp controlBp, Consumer<Integer> addEntriesFn) {
        AtomicInteger entriesSoFar = new AtomicInteger(0);
        AtomicInteger countSoFar = new AtomicInteger(0);
        Consumer<Integer> addEntriesTill = (target) -> {
            int step = target - entriesSoFar.get();
            addEntriesFn.accept(step);
            entriesSoFar.addAndGet(step);
            countSoFar.incrementAndGet();
        };

        for (TradingThresholds.Subject subject : asList(WAGER, EXPOSURE)) {
            for (int i = 1; i < 4; i++) {
                int targetBalanced = testThreshold(rank, subject, i, type);

                addEntriesTill.accept(targetBalanced - 1);
                assertStats(testBp, OVER, type, "Just before " + subject + " threshold level " + i).satisfies(
                        stats(entriesSoFar.get(), entriesSoFar.get(), countSoFar.get()),
                        triggeredLevel(WAGER, subject == WAGER ? i - 1 : 3),
                        triggeredLevel(EXPOSURE, subject == WAGER ? 0 : i - 1),
                        nothingReported());

                addEntriesTill.accept(targetBalanced);
                assertStats(testBp, OVER, type, "Passed balanced " + subject + " threshold level " + i).satisfies(
                        stats(entriesSoFar.get(), entriesSoFar.get(), countSoFar.get()),
                        triggeredLevel(WAGER, subject == WAGER ? i : 3),
                        triggeredLevel(EXPOSURE, subject == WAGER ? 0 : i),
                        nothingReported());

                assertStats(controlBp, OVER, type, "Control stats after test " + subject + " threshold level " + i + " passed").satisfies(
                        stats(entriesSoFar.get(), entriesSoFar.get(), countSoFar.get()),
                        triggeredLevelLessThan(subject, i),
                        nothingReported());
            }
        }
    }

    @ParameterizedTest
    @EnumSource(value = LineType.class, names = { "STANDARD", "ALTERNATIVE" })
    public void brandLevelThresholdTest(LineType type) throws Throwable {
        when(pickEmConfig.alternativeTradingMarketStatsEnabled()).thenReturn(true);
        BrandProp mainBrandProp = altBrandProp(td.brandMain, liverpoolVsMancity, ooEpl.liverpoolSalah,
                "goals", 1.5, true);
        BrandProp controlBrandProp = altBrandProp(td.brand2, liverpoolVsMancity, ooEpl.liverpoolSalah,
                "goals", 1.5, true);
        saveAllInTx(
                testThresholds(),
                mainBrandProp,
                controlBrandProp);
        testRank(ThresholdRank.BRAND, type, mainBrandProp, controlBrandProp, step -> {
            entry(step, 1)
                    .pick(mainBrandProp, OVER)
                    .submit();
            entry(step, 1, td.brand2)
                    .pick(controlBrandProp, OVER)
                    .submit();
        });
        assertThat(stats).hasSize(4);

        // Discounted
        DiscountedLine mainDiscountedLine = absoluteDiscountedLineFor(mainBrandProp, whateverSegment, 0.5);
        BrandProp mainDiscountedBp = discountedPropFor(mainBrandProp, mainDiscountedLine, 0.5);

        DiscountedLine controlDiscountedLine = absoluteDiscountedLineFor(controlBrandProp, whateverSegment, 0.5);
        BrandProp controlDiscountedBp = discountedPropFor(controlBrandProp, controlDiscountedLine, 0.5);

        saveAllInTx(
                mainDiscountedLine,
                mainDiscountedBp,
                controlDiscountedLine,
                controlDiscountedBp);
        testRank(ThresholdRank.BRAND, LineType.DISCOUNTED, mainBrandProp, controlBrandProp, step -> {
            entry(step, 1)
                    .pick(mainDiscountedBp, OVER)
                    .submit();
            entry(step, 1, td.brand2)
                    .pick(controlDiscountedBp, OVER)
                    .submit();
        });
        assertThat(stats).hasSize(6);
    }

    private void sameBrandThresholdTest(ThresholdRank rank, LineType type, BrandProp mainBrandProp, BrandProp controlBrandProp) throws Throwable {
        when(pickEmConfig.alternativeTradingMarketStatsEnabled()).thenReturn(true);
        saveAllInTx(
                testThresholds(),
                mainBrandProp,
                controlBrandProp);
        testRank(rank, type, mainBrandProp, controlBrandProp, step -> enterProps(step, mainBrandProp, controlBrandProp));
        assertThat(stats).hasSize(4);

        // Discounted
        DiscountedLine mainDiscountedLine = absoluteDiscountedLineFor(mainBrandProp, whateverSegment, 0.5);
        BrandProp mainDiscountedBp = discountedPropFor(mainBrandProp, mainDiscountedLine, 0.5);

        DiscountedLine controlDiscountedLine = absoluteDiscountedLineFor(controlBrandProp, whateverSegment, 0.5);
        BrandProp controlDiscountedBp = discountedPropFor(controlBrandProp, controlDiscountedLine, 0.5);

        saveAllInTx(
                mainDiscountedLine,
                mainDiscountedBp,
                controlDiscountedLine,
                controlDiscountedBp);
        testRank(rank, LineType.DISCOUNTED, mainBrandProp, controlBrandProp, step -> enterProps(step, mainDiscountedBp, controlDiscountedBp));
        assertThat(stats).hasSize(6);
    }

    @ParameterizedTest
    @EnumSource(value = LineType.class, names = { "STANDARD", "ALTERNATIVE" })
    public void leagueLevelThresholdTest(LineType type) throws Throwable {
        // Same brand, different leagues
        BrandProp mainBrandProp = altBrandProp(td.brandMain, bullsVsCeltics, swishNba.bullsLaVine,
                "assist", 1.5, true);
        BrandProp controlBrandProp = altBrandProp(td.brandMain, liverpoolVsMancity, ooEpl.liverpoolSalah,
                "assist", 1.5, true);
        sameBrandThresholdTest(ThresholdRank.LEAGUE, type, mainBrandProp, controlBrandProp);
    }

    @ParameterizedTest
    @EnumSource(value = LineType.class, names = { "STANDARD", "ALTERNATIVE" })
    public void leagueMetricLevelThresholdTest(LineType type) throws Throwable {
        // Same brand and league, different metrics
        BrandProp mainBrandProp = altBrandProp(td.brandMain, bullsVsCeltics, swishNba.bullsLaVine,
                "points", 1.5, true);
        BrandProp controlBrandProp = altBrandProp(td.brandMain, bullsVsCeltics, swishNba.bullsLaVine,
                "assist", 1.5, true);
        sameBrandThresholdTest(ThresholdRank.LEAGUE_METRIC, type, mainBrandProp, controlBrandProp);
    }

    @ParameterizedTest
    @EnumSource(value = LineType.class, names = { "STANDARD", "ALTERNATIVE" })
    public void matchLevelThresholdTest(LineType type) throws Throwable {
        // Same brand, league and metrics, different match
        BrandProp mainBrandProp = altBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersDavis,
                "assists", 1.5, true);
        BrandProp controlBrandProp = altBrandProp(td.brandMain, bullsVsCeltics, swishNba.lakersDavis,
                "assists", 1.5, true);
        sameBrandThresholdTest(ThresholdRank.MATCH, type, mainBrandProp, controlBrandProp);
    }

    @ParameterizedTest
    @EnumSource(value = LineType.class, names = { "STANDARD", "ALTERNATIVE" })
    public void matchMetricLevelThresholdTest(LineType type) throws Throwable {
        // Same brand, league and metrics, different match
        BrandProp mainBrandProp = altBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersDavis,
                "points", 1.5, true);
        BrandProp controlBrandProp = altBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersDavis,
                "assists", 1.5, true);
        sameBrandThresholdTest(ThresholdRank.MATCH_METRIC, type, mainBrandProp, controlBrandProp);
    }

    @ParameterizedTest
    @EnumSource(value = LineType.class, names = { "STANDARD", "ALTERNATIVE" })
    public void matchMetricPlayerLevelThresholdTest(LineType type) throws Throwable {
        // Same brand, league and metrics, different match
        BrandProp mainBrandProp = altBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersLebron,
                "points", 1.5, true);
        BrandProp controlBrandProp = altBrandProp(td.brandMain, lakersVsKnicks, swishNba.lakersDavis,
                "points", 1.5, true);
        sameBrandThresholdTest(ThresholdRank.MATCH_METRIC_PLAYER, type, mainBrandProp, controlBrandProp);
    }

    private void enterProps(int step, BrandProp... brandProps) {
        for (BrandProp bp : brandProps) {
            entry(step, 1)
                    .pick(bp, OVER)
                    .submit();
        }
    }

    private enum ThresholdRank {
        BRAND(6),
        LEAGUE(5),
        LEAGUE_METRIC(4),
        MATCH(3),
        MATCH_METRIC(2),
        MATCH_METRIC_PLAYER(1);

        public final int weight;

        ThresholdRank(int weight) {
            this.weight = weight;
        }
    }

    private int testThreshold(ThresholdRank rank, TradingThresholds.Subject subject, int level, LineType type) {
        int threshold = rank.weight * (subject == WAGER ? 100 : 1000) +
                (10 * (level - 1));
        if (type == LineType.ALTERNATIVE) {
            threshold /= 2;
        } else if (type == LineType.DISCOUNTED) {
            threshold += 50;
        }
        return threshold;
    }

    private TradingAlertConfig.TradingAlertConfigBuilder tradingConfigForRank(ThresholdRank rank, LineType type) {
        return TradingAlertConfig.builder()
                .brand(td.brandMain)
                .lineType(type)
                .wagerThreshold1(bd(testThreshold(rank, WAGER, 1, type)))
                .wagerThreshold2(bd(testThreshold(rank, WAGER, 2, type)))
                .wagerThreshold3(bd(testThreshold(rank, WAGER, 3, type)))
                .exposureThreshold1(bd(testThreshold(rank, EXPOSURE, 1, type)))
                .exposureThreshold2(bd(testThreshold(rank, EXPOSURE, 2, type)))
                .exposureThreshold3(bd(testThreshold(rank, EXPOSURE, 3, type)));
    }

    private List<TradingAlertConfig> testThresholds(LineType type) {
        return asList(
                tradingConfigForRank(ThresholdRank.BRAND, type).build(),
                tradingConfigForRank(ThresholdRank.LEAGUE, type)
                        .league(td.nba)
                        .build(),
                tradingConfigForRank(ThresholdRank.LEAGUE_METRIC, type)
                        .league(td.nba)
                        .metric("points")
                        .build(),
                tradingConfigForRank(ThresholdRank.MATCH, type)
                        .match(lakersVsKnicks)
                        .build(),
                tradingConfigForRank(ThresholdRank.MATCH_METRIC, type)
                        .match(lakersVsKnicks)
                        .metric("points")
                        .build(),
                tradingConfigForRank(ThresholdRank.MATCH_METRIC_PLAYER, type)
                        .match(lakersVsKnicks)
                        .metric("points")
                        .player(swishNba.lakersLebron)
                        .build());
    }

    public List<TradingAlertConfig> testThresholds() {
        return ListUtils.union(testThresholds(LineType.STANDARD), testThresholds(LineType.DISCOUNTED));
    }

    private ThrowingConsumer<TradingMarketStats> stats(int wager, int exposure, int count) {
        return s -> {
            assertThat(s.getWager().intValueExact()).isEqualTo(wager);
            assertThat(s.getExposure().intValueExact()).isEqualTo(exposure);
            assertThat(s.getPickCount()).isEqualTo(count);
        };
    }

    private ThrowingConsumer<TradingMarketStats> triggeredWagers(boolean wager1, boolean wager2, boolean wager3) {
        return s -> {
            assertThat(s.getWagerThreshold1Triggered()).isEqualTo(wager1);
            assertThat(s.getWagerThreshold2Triggered()).isEqualTo(wager2);
            assertThat(s.getWagerThreshold3Triggered()).isEqualTo(wager3);
        };
    }

    private ThrowingConsumer<TradingMarketStats> noExposuresTriggered() {
        return s -> {
            assertThat(s.getExposureThreshold1Triggered()).isFalse();
            assertThat(s.getExposureThreshold2Triggered()).isFalse();
            assertThat(s.getExposureThreshold3Triggered()).isFalse();
        };
    }

    private ThrowingConsumer<TradingMarketStats> triggeredExposures(boolean exposure1, boolean exposure2, boolean exposure3) {
        return s -> {
            assertThat(s.getExposureThreshold1Triggered()).isEqualTo(exposure1);
            assertThat(s.getExposureThreshold2Triggered()).isEqualTo(exposure2);
            assertThat(s.getExposureThreshold3Triggered()).isEqualTo(exposure3);
        };
    }

    private ThrowingConsumer<TradingMarketStats> triggeredLevel(TradingThresholds.Subject subject, int level) {
        return s -> assertThat(s.triggeredLevel(subject)).isEqualTo(level);
    }

    private ThrowingConsumer<TradingMarketStats> triggeredLevelLessThan(TradingThresholds.Subject subject, int level) {
        return s -> assertThat(s.triggeredLevel(subject)).isLessThan(level);
    }

    static private ThrowingConsumer<TradingMarketStats> nothingTriggered() {
        return s -> {
            assertThat(s.getWagerThreshold1Triggered()).isFalse();
            assertThat(s.getWagerThreshold2Triggered()).isFalse();
            assertThat(s.getWagerThreshold3Triggered()).isFalse();
            assertThat(s.getExposureThreshold1Triggered()).isFalse();
            assertThat(s.getExposureThreshold2Triggered()).isFalse();
            assertThat(s.getExposureThreshold3Triggered()).isFalse();
        };
    }

    static private ThrowingConsumer<TradingMarketStats> nothingReported() {
        return s -> {
            assertThat(s.getWagerThreshold1Reported()).isFalse();
            assertThat(s.getWagerThreshold2Reported()).isFalse();
            assertThat(s.getWagerThreshold3Reported()).isFalse();
            assertThat(s.getExposureThreshold1Reported()).isFalse();
            assertThat(s.getExposureThreshold2Reported()).isFalse();
            assertThat(s.getExposureThreshold3Reported()).isFalse();
        };
    }

    private AbstractObjectAssert<ObjectAssert<TradingMarketStats>, TradingMarketStats>
            assertStats(BrandProp bp, OverUnder overUnder, LineType type, String description) {
        return assertThat(stats).filteredOn(s -> s.getBrand().equals(bp.getBrand()) &&
                s.getMatch().equals(bp.getMatch()) && s.getPlayer().equals(bp.getPlayer()) &&
                s.getMetric().equals(bp.getMetric()) && s.getOverUnder().equals(overUnder) &&
                s.getType() == type).singleElement().as(description);
    }

    private AbstractObjectAssert<ObjectAssert<TradingMarketStats>, TradingMarketStats>
            assertStandardStat(Match match, Player player, String metric, OverUnder overUnder) throws Throwable {
        return assertStandardStat(td.brandMain, match, player, metric, overUnder);
    }
    private AbstractObjectAssert<ObjectAssert<TradingMarketStats>, TradingMarketStats>
            assertStandardStat(Brand brand, Match match, Player player, String metric, OverUnder overUnder) throws Throwable {
        return assertThat(stats).filteredOn(s -> s.getBrand().equals(brand) &&
                s.getMatch().equals(match) && s.getPlayer().equals(player) && s.getMetric().equals(metric) &&
                s.getOverUnder() == overUnder && s.getType() == LineType.STANDARD)
                .singleElement();
    }

    private AbstractObjectAssert<ObjectAssert<TradingMarketStats>, TradingMarketStats>
            assertDiscountedStat(Match match, Player player, String metric, OverUnder overUnder) throws Throwable {
        return assertDiscountedStat(td.brandMain, match, player, metric, overUnder);
    }
    private AbstractObjectAssert<ObjectAssert<TradingMarketStats>, TradingMarketStats>
            assertDiscountedStat(Brand brand, Match match, Player player, String metric, OverUnder overUnder) throws Throwable {
        return assertThat(stats).filteredOn(s -> s.getBrand().equals(brand) &&
                s.getMatch().equals(match) && s.getPlayer().equals(player) && s.getMetric().equals(metric) &&
                s.getOverUnder() == overUnder && s.getType() == LineType.DISCOUNTED)
                .singleElement();
    }

    private AbstractObjectAssert<ObjectAssert<TradingMarketStats>, TradingMarketStats>
            assertAltStat(Match match, Player player, String metric, OverUnder overUnder, double line) throws Throwable {
        return assertAltStat(td.brandMain, match, player, metric, overUnder, line);
    }
    private AbstractObjectAssert<ObjectAssert<TradingMarketStats>, TradingMarketStats>
            assertAltStat(Brand brand, Match match, Player player, String metric, OverUnder overUnder, double line) throws Throwable {
        return assertThat(stats).filteredOn(s -> s.getBrand().equals(brand) &&
                s.getMatch().equals(match) && s.getPlayer().equals(player) && s.getMetric().equals(metric) &&
                s.getOverUnder() == overUnder && s.getType() == LineType.ALTERNATIVE &&
                s.getLine() != null && s.getLine().doubleValue() == line)
                .singleElement();
    }

    private TestEntry entry(int amount, int multiplier) {
        return new TestEntry(amount, multiplier, td.brandMain);
    }

    private TestEntry entry(int amount, int multiplier, Brand brand) {
        return new TestEntry(amount, multiplier, brand);
    }

    private class TestEntry {
        private final Entry entry;

        private TestEntry(int amount, int multiplier, Brand brand) {
            this.entry = new Entry();
            entry.setBrand(brand);
            entry.setEffectiveMaxMultiplier(BigDecimal.valueOf(multiplier));
            entry.setEffectiveAmount(BigDecimal.valueOf(amount));
        }

        public TestEntry pick(BrandProp brandProp, OverUnder overUnder) {
            Pick pick = new Pick();
            pick.setEntry(entry);
            pick.setBrand(entry.getBrand());
            pick.setMatch(brandProp.getMatch());
            pick.setPlayer(brandProp.getPlayer());
            pick.setMetric(brandProp.getMetric());
            pick.setProjection(brandProp.getLine());
            pick.setLineType(brandProp.getType() == LineType.DISCOUNTED ? LineType.DISCOUNTED : LineType.STANDARD);
            pick.setBrandProp(brandProp);
            pick.setOption(overUnder);
            entry.getPicks().add(pick);
            return this;
        }

        public void submit() {
            submitTimes(1);
        }

        public void submitTimes(int times) {
            try (var tx = ebean.beginTransaction()) {
                for (int i = 0; i < times; i++) {
                    tradingAlertsService.updatePickemStats(entry, tx);
                }
                stats = new QTradingMarketStats(ebean).usingTransaction(tx).findList();
                tx.commit();
            }
        }
    }

}
