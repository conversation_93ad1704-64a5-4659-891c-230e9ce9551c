package notification.service;

import api.v1.ApiFactory;
import api.v1.ApplicationException;
import com.turbospaces.rpc.QueuePostTemplate;
import io.ebean.Transaction;
import lombok.extern.slf4j.Slf4j;
import notification.NotificationJpaManager;
import notification.NotificationServerProperties;
import notification.api.v1.GetAccountNotificationsResponse;
import notification.api.v1.NotificationCategoryInfo;
import notification.api.v1.QuestlineInstanceNotification;
import notification.api.v1.QuestlineInstanceNotificationCategory;
import notification.dto.Mappers;
import notification.model.AccountNotification;
import notification.model.AccountQuestlineInstance;
import notification.model.NotificationCategory;
import notification.model.NotificationCategoryCodeSpec;
import notification.model.NotificationStatus;
import notification.model.QuestlineInstanceAccountNotification;
import notification.model.QuestlineStatusSpec;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class QuestlineNotificationService extends AbstractNotificationService<AccountQuestlineInstance> {
    public QuestlineNotificationService(NotificationJpaManager ebean, NotificationServerProperties props,
                                        QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        super(ebean, props, postTemplate, apiFactory);
    }

    @Override
    public void create(AccountQuestlineInstance entity, Transaction tx) throws ApplicationException {
        var existingAccountNotification = notificationRepo().questlineInstanceNotificationByAccountInstance(entity, tx);

        AccountNotification accountNotification;
        if (existingAccountNotification.isPresent()) {
            log.debug("Found existing AccountNotification {}", existingAccountNotification.get().getId());
            accountNotification = existingAccountNotification.get().getAccountNotification();
            accountNotification.setStatus(mapStatus(entity.getStatus()));
            save(accountNotification, tx);
            log.debug("Updated status of existing AccountNotification to {}", accountNotification.getStatus());
        } else {
            QuestlineInstanceAccountNotification notification = new QuestlineInstanceAccountNotification();
            notification.setAccountQuestlineInstance(entity);
            accountNotification = createAccountNotification(entity.getAccount(), null, tx);
            accountNotification.setStatus(mapStatus(entity.getStatus()));
            notification.setAccountNotification(accountNotification);
            save(notification, tx);
            log.debug("Saved notification: {}", notification);
        }

        tx.flush();
        notify(accountNotification, tx);
    }

    @Override
    public void buildResponseForNotificationCategory(GetAccountNotificationsResponse.Builder response,
                                                     List<AccountNotification> accountNotifications,
                                                     NotificationCategory category, Transaction tx, Date currentDate) {
        log.debug("Build response for questline notification category - {}", accountNotifications.size());
        NotificationCategoryInfo notificationCategoryInfo = Mappers.toNotificationCategoryInfoBuilder(category).build();
        QuestlineInstanceNotificationCategory.Builder builder = QuestlineInstanceNotificationCategory.newBuilder();
        builder.setCategoryInfo(notificationCategoryInfo);
        buildQuestlineInstanceNotificationCategory(builder, accountNotifications, tx, currentDate);
        response.getCategoriesBuilder().setQuestlineNotificationCategory(builder.build());
    }

    private void buildQuestlineInstanceNotificationCategory(
            QuestlineInstanceNotificationCategory.Builder builder,
            List<AccountNotification> accountNotifications,
            Transaction tx, Date currentDate) {

        if (CollectionUtils.isEmpty(accountNotifications)) {
            return;
        }

        List<QuestlineInstanceAccountNotification> notifications = notificationRepo()
                .questlineInstanceNotificationsByAccountNotificationList(accountNotifications, tx);

        for (QuestlineInstanceAccountNotification notification : notifications) {
            var instance = notification.getAccountQuestlineInstance();
            var status = instance.getStatus();

            log.debug("Including questline instance notification for status: {}, code: {}",
                    status, instance.getCode());

            builder.addQuestlineNotifications(
                    QuestlineInstanceNotification.newBuilder()
                            .setQuestlineNotificationInfo(Mappers.toQuestlineInstance(instance).build())
                            .setAccountNotification(Mappers.toAccountNotification(notification.getAccountNotification(), currentDate))
                            .build()
            );
        }
    }

    @Override
    public void claim(AccountQuestlineInstance entity, Transaction tx) {
        throw new UnsupportedOperationException("Claim is not supported for notification category");
    }

    @Override
    public NotificationCategoryCodeSpec notificationCategoryCode() {
        return NotificationCategoryCodeSpec.QUESTLINES;
    }

    private NotificationStatus mapStatus(QuestlineStatusSpec status) {
        if (status == null) {
            throw new IllegalArgumentException("Status must not be null");
        }
        return switch (status) {
            case EXPIRED, RETRACTED, REPLACED -> NotificationStatus.EXPIRED;
            case CLAIMED -> NotificationStatus.CLAIMED;
            case UNCLAIMED -> NotificationStatus.UNREAD;
            case COMPLETED -> NotificationStatus.READ;
            default -> NotificationStatus.UNREAD;
        };
    }
}