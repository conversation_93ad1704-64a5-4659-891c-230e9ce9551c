package notification.service;

import api.v1.ApiFactory;
import api.v1.ApplicationException;
import com.turbospaces.rpc.QueuePostTemplate;
import io.ebean.Transaction;
import lombok.extern.slf4j.Slf4j;
import notification.NotificationJpaManager;
import notification.NotificationServerProperties;
import notification.api.v1.GetAccountNotificationsResponse;
import notification.api.v1.MilestoneUpdateNotification;
import notification.api.v1.MilestoneUpdateNotificationCategory;
import notification.api.v1.NotificationCategoryInfo;
import notification.dto.Mappers;
import notification.model.AccountMilestoneUpdateInstance;
import notification.model.AccountNotification;
import notification.model.MilestoneStatusSpec;
import notification.model.MilestouneUpdateInstanceAccountNotification;
import notification.model.NotificationCategory;
import notification.model.NotificationCategoryCodeSpec;
import notification.model.NotificationStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MilestoneNotificationService extends AbstractNotificationService<AccountMilestoneUpdateInstance> {

    public MilestoneNotificationService(NotificationJpaManager ebean, NotificationServerProperties props, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        super(ebean, props, postTemplate, apiFactory);
    }

    @Override
    public void create(AccountMilestoneUpdateInstance entity, Transaction tx) throws ApplicationException {
        var existingAccountNotification =
                notificationRepo().milestouneUpdateInstanceNotificationByAccountInstance(entity, tx);

        AccountNotification accountNotification;
        if (existingAccountNotification.isPresent()) {
            log.debug("Found existing AccountNotification {}", existingAccountNotification.get().getId());
            accountNotification = existingAccountNotification.get().getAccountNotification();
            accountNotification.setStatus(mapStatus(entity.getStatus()));
            save(accountNotification);
            log.debug("Updated status of existing AccountNotification to {}", accountNotification.getStatus());
        } else {
            MilestouneUpdateInstanceAccountNotification notification = new MilestouneUpdateInstanceAccountNotification();
            notification.setAccountMilestoneUpdateInstance(entity);

            accountNotification = createAccountNotification(entity.getAccount(), null, tx);
            notification.setAccountNotification(accountNotification);
            save(notification, tx);

            log.debug("Saved notification: {}", notification);
        }
        notify(accountNotification, tx);
    }

    @Override
    public void buildResponseForNotificationCategory(GetAccountNotificationsResponse.Builder response,
                                                     List<AccountNotification> accountNotifications,
                                                     NotificationCategory category, Transaction tx, Date currentDate) {
        NotificationCategoryInfo notificationCategoryInfo = Mappers.toNotificationCategoryInfoBuilder(category).build();
        MilestoneUpdateNotificationCategory.Builder builder = MilestoneUpdateNotificationCategory.newBuilder();
        builder.setCategoryInfo(notificationCategoryInfo);
        buildMilestoneUpdateInstanceNotificationCategory(builder, accountNotifications, tx, currentDate);
        response.getCategoriesBuilder().setMilestoneNotificationCategory(builder.build());
    }

    private void buildMilestoneUpdateInstanceNotificationCategory(MilestoneUpdateNotificationCategory.Builder builder,
                                                                  List<AccountNotification> accountNotifications,
                                                                  Transaction tx, Date currentDate) {
        if (CollectionUtils.isEmpty(accountNotifications)) {
            return;
        }
        List<MilestouneUpdateInstanceAccountNotification> notifications = notificationRepo()
                .milestoneUpdateInstanceNotificationsByAccountNotificationList(accountNotifications, tx);
        notifications.forEach(notification ->
                builder.addMilestoneNotification(
                        MilestoneUpdateNotification.newBuilder()
                                .setMilestoneNotificationInfo(Mappers.toMilestoneUpdateInstance(notification.getAccountMilestoneUpdateInstance()))
                                .setAccountNotification(Mappers.toAccountNotification(notification.getAccountNotification(), currentDate))
                                .build())
        );
    }

    private NotificationStatus mapStatus(MilestoneStatusSpec status) {
        if (status == null) {
            throw new IllegalArgumentException("Status must not be null");
        }
        return switch (status) {
            case MILESTONE_UNCLAIMED -> NotificationStatus.UNREAD;
            case COMPLETED -> NotificationStatus.READ;
            default -> NotificationStatus.UNREAD;
        };
    }

    @Override
    public void claim(AccountMilestoneUpdateInstance entity, Transaction tx) {
        throw new UnsupportedOperationException("Claim is not supported for notification category");
    }

    @Override
    public NotificationCategoryCodeSpec notificationCategoryCode() {
        return NotificationCategoryCodeSpec.MILESTONES;
    }

}

