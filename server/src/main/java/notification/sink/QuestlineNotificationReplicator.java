package notification.sink;


import api.v1.AccountRoutingInfo;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.metrics.Metrics;
import common.MetricTags;
import io.ebean.Transaction;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import notification.NotificationJpaManager;
import notification.StatusNotSupportedException;
import notification.model.AccountQuestlineInstance;
import notification.model.QuestlineStatusSpec;
import notification.service.QuestlineNotificationService;
import org.springframework.stereotype.Service;
import quest.api.v1.QuestlineOverallStatusEnum;
import quest.api.v1.QuestlineUpdatedEvent;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
public class QuestlineNotificationReplicator implements SinkReplicator {
    private final NotificationJpaManager ebean;
    private final QuestlineNotificationService questlineNotificationService;

    public QuestlineNotificationReplicator(NotificationJpaManager ebean,
                                           QuestlineNotificationService questlineNotificationService) {
        this.ebean = Objects.requireNonNull(ebean);
        this.questlineNotificationService = Objects.requireNonNull(questlineNotificationService);
    }

    @Timed(value = Metrics.REPLICATOR, extraTags = {MetricTags.OPERATION, "questline_updated_instance"})
    public void apply(KafkaWorkUnit unit, QuestlineUpdatedEvent event) throws Throwable {
        log.debug("Received QuestlineUpdatedEvent data {}", event.getInfo());
        try (var tx = ebean.newTransaction()) {
            AccountQuestlineInstance questlineInstance = getOrCreateQuestlineInstance(event, tx);
            questlineNotificationService.create(questlineInstance, tx);
            tx.commit();
        } catch (StatusNotSupportedException e){
            log.warn("Questline status {} is not supported", event.getInfo().getStatus());
        }
    }

    private AccountQuestlineInstance getOrCreateQuestlineInstance(QuestlineUpdatedEvent event,
                                                                    Transaction tx) {
        Optional<AccountQuestlineInstance> q = findQuestlineInstanceByCode(UUID.fromString(event.getInfo().getCode()), tx);
        if(q.isPresent()){
            AccountQuestlineInstance a = q.get();
            update(event, a, tx);
            return a;
        }else {
           return createAccountQuestlineInstance(event, tx);
        }
    }

    private Optional<AccountQuestlineInstance> findQuestlineInstanceByCode(UUID code, Transaction tx) {
        return Optional.of(code).flatMap(c ->
                ebean.find(AccountQuestlineInstance.class)
                        .usingTransaction(tx)
                        .where()
                        .eq("code", c)
                        .findOneOrEmpty()
        );
    }

    private AccountQuestlineInstance createAccountQuestlineInstance(QuestlineUpdatedEvent event,
                                                                    Transaction tx)  {
        AccountRoutingInfo routing = event.getRouting();

        var brand = ebean.brandRepo().getOrCreate(routing.getBrand(), tx);
        var account = ebean.accountRepo().getOrCreateAccount(routing.getId(), routing.getHash(), brand, tx);

        AccountQuestlineInstance a = new AccountQuestlineInstance();
        a.setAccount(account);

        update(event, a, tx);

        return a;
    }

    private void update(QuestlineUpdatedEvent event, AccountQuestlineInstance a, Transaction tx){

        a.setCode(UUID.fromString(event.getInfo().getCode()));
        a.setStatus(mapQuestlineStatus(event.getInfo().getStatus()));

        a.setTitle(event.getInfo().getTitle());
        a.setDescription(event.getInfo().getDescription());
        a.setIcon(event.getInfo().getIcon());
        a.setExpireDate(new Date(event.getInfo().getExpirationTimestamp()));
        ebean.save(a, tx);
    }

    private QuestlineStatusSpec mapQuestlineStatus(QuestlineOverallStatusEnum status)  {
        return switch (status) {
            case QuestlineOverallStatusEnum.QUESTLINE_IN_PROGRESS ->QuestlineStatusSpec.IN_PROGRESS;
            case QuestlineOverallStatusEnum.QUESTLINE_COMPLETED -> QuestlineStatusSpec.COMPLETED;
            case QuestlineOverallStatusEnum.QUESTLINE_UNCLAIMED -> QuestlineStatusSpec.UNCLAIMED;
            case QuestlineOverallStatusEnum.QUESTLINE_CLAIMED -> QuestlineStatusSpec.CLAIMED;
            case QuestlineOverallStatusEnum.QUESTLINE_EXPIRED -> QuestlineStatusSpec.EXPIRED;
            case QuestlineOverallStatusEnum.QUESTLINE_RETRACTED -> QuestlineStatusSpec.RETRACTED;
            case QuestlineOverallStatusEnum.QUESTLINE_REPLACED -> QuestlineStatusSpec.REPLACED;
            default -> throw new IllegalArgumentException("Unexpected value: " + status);
        };
    }
}
