package notification.dto;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import api.v1.AccountRoutingInfo;
import common.utils.ProtoUtils;
import notification.api.v1.AccountPreferencesInfo;
import notification.api.v1.FreeSpinInfo;
import notification.api.v1.ManualBonusInfo;
import notification.api.v1.MilestoneUpdateNotificationInfo;
import notification.api.v1.NotificationCategoryInfo;
import notification.api.v1.OfferChainInstanceNotificationInfo;
import notification.api.v1.ProductInfo;
import notification.api.v1.PromotionInfo;
import notification.api.v1.QuestlineInstanceNotificationInfo;
import notification.model.Account;
import notification.model.AccountFreeSpin;
import notification.model.AccountMilestoneUpdateInstance;
import notification.model.AccountNotification;
import notification.model.AccountOfferChainInstance;
import notification.model.AccountPreferences;
import notification.model.AccountQuestlineInstance;
import notification.model.BonusReward;
import notification.model.FreeSpinCampaign;
import notification.model.JackpotAccountFreeContribution;
import notification.model.MilestoneStatusSpec;
import notification.model.NotificationCategory;
import notification.model.Product;
import notification.model.Promotion;
import notification.model.QuestlineStatusSpec;
import quest.api.v1.MilestoneStatusEnum;
import quest.api.v1.QuestlineOverallStatusEnum;

public class Mappers {
    public static FreeSpinInfo toFreeSpinInfo(AccountFreeSpin fs) {
        FreeSpinCampaign freeSpinCampaign = fs.getFreeSpinCampaign();
        List<ProductInfo> products = freeSpinCampaign.getProducts().stream()
                .map(Mappers::toProductInfo)
                .toList();

        var builder = FreeSpinInfo.newBuilder()
                .setCurrency(freeSpinCampaign.getCurrency())
                .setRounds(freeSpinCampaign.getTotalSpins())
                .setRoundsPlayed(freeSpinCampaign.getTotalSpins() - fs.getLeftSpins())
                .addAllProducts(products);
        ProtoUtils.setNullableValue(fs.getFreeSpinId(), builder::setFreeSpinId);

        return builder.build();
    }

    public static ProductInfo toProductInfo(Product product) {
        ProductInfo.Builder b = ProductInfo.newBuilder();

        b.setCode(product.getCode());

        if (StringUtils.isNotEmpty(product.getName())) {
            b.setName(product.getName());
        }
        if (StringUtils.isNotEmpty(product.getTitle())) {
            b.setTitle(product.getTitle());
        }
        if (StringUtils.isNotEmpty(product.getRoute())) {
            b.setRoute(product.getRoute());
        }

        if (CollectionUtils.isNotEmpty(product.getTags())) {
            b.addAllTags(product.getTags());
        }

        return b.build();
    }

    public static notification.api.v1.JackpotAccountFreeContribution.Builder toJackpotAccountFreeContribution(
            JackpotAccountFreeContribution freeContribution, String currency) {
        return notification.api.v1.JackpotAccountFreeContribution.newBuilder()
                .setAmount(freeContribution.getAmount().toString())
                .setUsedAmount(freeContribution.getUsedAmount().toString());
    }

    public static ManualBonusInfo.Builder toManualAccountBonus(BonusReward reward) {
        ManualBonusInfo.Builder mb = ManualBonusInfo.newBuilder();

        mb.setCode(reward.getCode());
        if (reward.getGoldAmount() != null) {
            mb.setGoldAmount(reward.getGoldAmount().toString());
        }
        if (reward.getSweepstakeAmount() != null) {
            mb.setSweepstakeAmount(reward.getSweepstakeAmount().toString());
        }
        if (reward.getFiatAmount() != null) {
            mb.setFiatAmount(reward.getFiatAmount().toString());
        }

        return mb;
    }

    public static NotificationCategoryInfo.Builder toNotificationCategoryInfoBuilder(NotificationCategory category) {
        return NotificationCategoryInfo.newBuilder()
                .setCode(category.getCode().getValue())
                .setTitle(category.getTitle())
                .setIcon(category.getIcon());
    }

    public static notification.api.v1.AccountNotification toAccountNotification(AccountNotification accountNotification, Date currentDate) {
        return notification.api.v1.AccountNotification.newBuilder()
                .setId(accountNotification.getId())
                .setStatus(accountNotification.getLatestStatus().getValue())
                .setCreatedAt(accountNotification.getCreatedAt().getTime())
                .setExpireAt(accountNotification.getExpireAt() == null ? 0 : accountNotification.getExpireAt().getTime())
                .build();
    }

    public static PromotionInfo toPromotionInfo(Promotion promotion) {
        var builder = PromotionInfo.newBuilder()
                .setCode(promotion.getCode())
                .setUrl(promotion.getUrl().toString())
                .setTitle(promotion.getTitle())
                .setDescription(promotion.getDescription())
                .setImportant(promotion.isImportant())
                .setShimmer(promotion.isShimmer());
        if (promotion.getImage() != null) {
            builder.setImage(promotion.getImage());
        }
        if (promotion.getTag() != null) {
            builder.setTag(promotion.getTag());
        }
        if (promotion.getTag2() != null) {
            builder.setTag2(promotion.getTag2());
        }
        if (promotion.getStyle() != null) {
            builder.setStyle(promotion.getStyle());
        }
        return builder.build();
    }

    public static AccountPreferencesInfo.Builder toAccountPreferences(AccountPreferences preferences) {
        AccountPreferencesInfo.Builder result = AccountPreferencesInfo.newBuilder();
        result.setDoNotSendSms(preferences.isDoNotSendSms());
        result.setDoNotCall(preferences.isDoNotCall());
        result.setNoPhoneNumber(StringUtils.isEmpty(preferences.getAccount().getPhoneNumber()));
        return result;
    }

    public static QuestlineInstanceNotificationInfo.Builder toQuestlineInstance(AccountQuestlineInstance questlineInstance) {
        QuestlineInstanceNotificationInfo.Builder result = QuestlineInstanceNotificationInfo.newBuilder();

        result.setStatus(mapQuestlineStatus(questlineInstance.getStatus()));
        result.setCode(questlineInstance.getCode().toString());

        result.setTitle(questlineInstance.getTitle());
        result.setDescription(questlineInstance.getDescription());
        result.setIcon(questlineInstance.getIcon());
        result.setExpirationTimestamp(questlineInstance.getExpireDate().getTime());

        return result;
    }

    public static MilestoneUpdateNotificationInfo.Builder toMilestoneUpdateInstance(AccountMilestoneUpdateInstance m) {
        MilestoneUpdateNotificationInfo.Builder result = MilestoneUpdateNotificationInfo.newBuilder();

        result.setStatus(mapMilestoneStatus(m.getStatus()));
        result.setMilestoneCode(m.getCode().toString());
        result.setQuestlineCode(m.getQuestlineCode().toString());
        if (m.getQuestCode() != null) {
            result.setQuestCode(m.getQuestCode().toString());
        }

        return result;
    }

    private static QuestlineOverallStatusEnum mapQuestlineStatus(QuestlineStatusSpec status) {
        return switch (status) {
            case QuestlineStatusSpec.IN_PROGRESS -> QuestlineOverallStatusEnum.QUESTLINE_IN_PROGRESS;
            case QuestlineStatusSpec.COMPLETED -> QuestlineOverallStatusEnum.QUESTLINE_COMPLETED;
            case QuestlineStatusSpec.UNCLAIMED -> QuestlineOverallStatusEnum.QUESTLINE_UNCLAIMED;
            case QuestlineStatusSpec.CLAIMED -> QuestlineOverallStatusEnum.QUESTLINE_CLAIMED;
            case QuestlineStatusSpec.EXPIRED -> QuestlineOverallStatusEnum.QUESTLINE_EXPIRED;
            case QuestlineStatusSpec.RETRACTED -> QuestlineOverallStatusEnum.QUESTLINE_RETRACTED;
            case QuestlineStatusSpec.REPLACED -> QuestlineOverallStatusEnum.QUESTLINE_REPLACED;
        };
    }

    private static MilestoneStatusEnum mapMilestoneStatus(MilestoneStatusSpec instance) {
        return switch (instance) {
            case MilestoneStatusSpec.COMPLETED -> MilestoneStatusEnum.MILESTONE_COMPLETED;
            case MilestoneStatusSpec.MILESTONE_UNCLAIMED -> MilestoneStatusEnum.MILESTONE_UNCLAIMED;
        };
    }

    public static OfferChainInstanceNotificationInfo toOfferChainInstance(AccountOfferChainInstance offerChainInstance) {
        return OfferChainInstanceNotificationInfo.newBuilder()
                .setDisplayName(offerChainInstance.getDisplayName())
                .setDisplayTagline(offerChainInstance.getDisplayTagline())
                .setDisplayDescription(offerChainInstance.getDisplayDescription())
                .setIconInbox(offerChainInstance.getIconInbox())
                .setExpiresAt(offerChainInstance.getExpired().getTime())
                .setStatus(offerChainInstance.getStatus())
                .build();
    }

    public static AccountRoutingInfo.Builder toAccountRoutingInfo(Account account) {
        AccountRoutingInfo.Builder routing = AccountRoutingInfo.newBuilder();
        routing.setId(account.getId());
        routing.setHash(account.getHash());
        routing.setBrand(account.getBrand().getName());
        return routing;
    }
}