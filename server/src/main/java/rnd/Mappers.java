package rnd;

import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static rnd.BigDecimalUtil.readableString;
import static rnd.model.main.pickem.Pick.DISCOUNTED_OPTIONS;
import static rnd.model.main.pickem.Pick.OPTIONS;
import static rnd.services.PickemMarketCtx.isSuspendedByBrand;
import static rnd.services.Rulebooks.discountedLineRulebook;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.turbospaces.common.PlatformUtil;

import io.vavr.Tuple3;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import rnd.api.v1.common.TActiveUserTicketsCountEntry;
import rnd.api.v1.common.TCategory;
import rnd.api.v1.common.TDayInfo;
import rnd.api.v1.common.TLeague;
import rnd.api.v1.common.TMatchInfo;
import rnd.api.v1.common.TPlayerInfo;
import rnd.api.v1.common.TRewardBundle;
import rnd.api.v1.common.TRewardGroup;
import rnd.api.v1.common.TRewardGroupCount;
import rnd.api.v1.common.TRewardInfo;
import rnd.api.v1.common.TRewardTemplate;
import rnd.api.v1.common.TRewardTemplateCount;
import rnd.api.v1.common.TTeamInfo;
import rnd.api.v1.pickem.TFlexTierAmount;
import rnd.api.v1.pickem.TLineType;
import rnd.api.v1.pickem.TMetricAdminInfo;
import rnd.api.v1.pickem.TOption;
import rnd.api.v1.pickem.TPick;
import rnd.api.v1.pickem.TPickEmEntry;
import rnd.api.v1.pickem.TPickEmLine;
import rnd.api.v1.pickem.TPickEmOption;
import rnd.api.v1.pickem.TPlayerOption;
import rnd.api.v1.pickem.TPlayerOptions;
import rnd.api.v1.shuffle.TCorrectCondition;
import rnd.api.v1.shuffle.TDistribution;
import rnd.api.v1.shuffle.TExtendedLeg;
import rnd.api.v1.shuffle.TFlexOutcomeCoefficient;
import rnd.api.v1.shuffle.TLeg;
import rnd.api.v1.shuffle.TMaxWinConfig;
import rnd.api.v1.shuffle.TNoUpcomingGame;
import rnd.api.v1.shuffle.TPossibleLegOverview;
import rnd.api.v1.shuffle.TShuffleGameInfo;
import rnd.api.v1.shuffle.TSlugDetails;
import rnd.api.v1.shuffle.TSportLeagueInfo;
import rnd.api.v1.shuffle.TTicketEntry;
import rnd.api.v1.shuffle.TTicketEntryLimit;
import rnd.api.v1.shuffle.TTicketInfo;
import rnd.api.v1.shuffle.TUserTicket;
import rnd.api.v1.shuffle.TWinTable;
import rnd.enums.Currency;
import rnd.enums.RewardTriggerType;
import rnd.enums.RewardType;
import rnd.enums.VerticalType;
import rnd.model.LogoUrl;
import rnd.model.PropAdminInfo;
import rnd.model.PlayerCard;
import rnd.model.data.BrandProp;
import rnd.model.data.League;
import rnd.model.data.LeagueConfig;
import rnd.model.data.LineType;
import rnd.model.data.Logo;
import rnd.model.data.Match;
import rnd.model.data.Player;
import rnd.model.data.Sport;
import rnd.model.data.Team;
import rnd.model.main.Account;
import rnd.model.main.AccountReward;
import rnd.model.main.Brand;
import rnd.model.main.FavouritePlayers;
import rnd.model.main.GameCategory;
import rnd.model.main.OverUnder;
import rnd.model.main.PrizeType;
import rnd.model.main.Result;
import rnd.model.main.RewardBundleTemplate;
import rnd.model.main.RewardGroupCount;
import rnd.model.main.RewardTemplate;
import rnd.model.main.Stage;
import rnd.model.main.pickem.Entry;
import rnd.model.main.pickem.Multiplier;
import rnd.model.main.pickem.Pick;
import rnd.model.main.shuffle.FlexCoefficient;
import rnd.model.main.shuffle.FlexCoefficientGroup;
import rnd.model.main.shuffle.Leg;
import rnd.model.main.shuffle.LegTemplate;
import rnd.model.main.shuffle.MatchLeg;
import rnd.model.main.shuffle.OverUnderPrediction;
import rnd.model.main.shuffle.RandomMultiplier;
import rnd.model.main.shuffle.RandomMultiplierGroup;
import rnd.model.main.shuffle.ShuffleGameTemplate;
import rnd.model.main.shuffle.Ticket;
import rnd.model.main.shuffle.TicketBundle;
import rnd.services.MarketAdminInfo;
import rnd.testutils.UrlSlugUtils;

@UtilityClass
@Slf4j
public final class Mappers {

    private static final String TAG_MILLION_BLITZ_EM = "million blitz'em";
    private static final String TAG_UNLIMITED_BLITZ_EM = "unlimited";

    public static TLeague.Builder toLeague(Brand brand, League league) {
        return toLeague(brand, league, false);
    }

    public static TLeague.Builder toLeague(Brand brand, League league, boolean smallAltLogo) {
        TLeague.Builder b = TLeague.newBuilder()
                .setCode(league.getCode().toApiCode())
                .setSport(league.getSport().getCode().toApiCode());

        Optional<LeagueConfig> leagueConfig = league.getLeagueConfig(brand);
        if (leagueConfig.isPresent() && leagueConfig.get().getLogo() != null) {
            Logo logo = leagueConfig.get().getLogo();
            if (smallAltLogo) {
                logo.getSmallAltOrActualLogoUrl().ifPresent(b::setLogoUrl);
            } else {
                logo.getAltOrActualLogoUrl().ifPresent(b::setLogoUrl);
            }
        }
        return b;
    }

    public static TCategory.Builder toGameCategory(GameCategory gameCategory) {
        return TCategory.newBuilder().setCode(gameCategory.getType().getCode().toLowerCase());
    }

    public static TTeamInfo.Builder toTeamInfo(Brand brand, Team team) {
        TTeamInfo.Builder b = TTeamInfo.newBuilder();
        b.setId(team.getId());
        b.setCode(team.getCode());
        b.setAbbreviation(team.getAbbreviation());
        Optional.ofNullable(team.getNameByBrandOrDefault(brand)).ifPresent(b::setName);
        Optional.ofNullable(team.getImageByBrandOrDefault(brand)).flatMap(LogoUrl::getAltOrActualLogoUrl).ifPresent(b::setLogoUrl);
        Optional.ofNullable(team.getJerseyLogoUrlByBrandOrDefault(brand)).ifPresent(b::setJerseyLogoUrl);
        Optional.ofNullable(team.getJerseyNumberColorByBrandOrDefault(brand)).ifPresent(b::setJerseyNumberColor);

        return b;
    }

    public static TMatchInfo.Builder toMatchInfo(Brand brand, Match match) {
        TMatchInfo.Builder b = TMatchInfo.newBuilder();
        b.setCode(match.getCode());
        b.setLeague(toLeague(brand, match.getLeague()));
        if (match.getHome() != null) b.setHome(toTeamInfo(brand, match.getHome()));
        if (match.getAway() != null) b.setAway(toTeamInfo(brand, match.getAway()));
        if (match.getPlayer1() != null) b.setPlayer1(toGamePlayerInfo(brand, match.getPlayer1()));
        if (match.getPlayer2() != null) b.setPlayer2(toGamePlayerInfo(brand, match.getPlayer2()));
        if (match.getPlayAt() != null) {
            b.setEventStartsAt(match.getPlayAt().getEpochSecond());
        }
        if (match.getApiPlayAt() != null) {
            b.setApiPlayAt(match.getApiPlayAt());
        }
        return b;
    }

    public static TMatchInfo.Builder toMatchInfo(Brand brand, Team homeTeam, Team awayTeam) {
        TMatchInfo.Builder b = TMatchInfo.newBuilder();
        b.setHome(toTeamInfo(brand, homeTeam));
        b.setAway(toTeamInfo(brand, awayTeam));
        return b;
    }

    public static TPlayerInfo.Builder toGamePlayerInfo(Brand brand, Player player) {
        return toGamePlayerInfo(brand, player, null);
    }

    public static TPlayerInfo.Builder toGamePlayerInfo(Brand brand, Player player, Team team) {
        TPlayerInfo.Builder b = TPlayerInfo.newBuilder();
        b.setId(player.getId());
        b.setCode(player.getCode());
        if (player.getPosition() != null) {
            b.setPosition(player.getPosition());
        }
        Optional.ofNullable(player.getFirstNameByBrandOrDefault(brand))
                .ifPresent(b::setFirstName);
        Optional.ofNullable(player.getLastNameByBrandOrDefault(brand))
                .ifPresent(b::setLastName);
        if (team != null) {
            b.setTeamCode(team.getCode());
        }
        Optional.ofNullable(player.getLogoByBrandOrDefault(brand))
                .flatMap(LogoUrl::getAltOrActualLogoUrl)
                .ifPresent(b::setLogoUrl);

        Optional.ofNullable(player.getJerseyNumberByBrandOrDefault(brand))
                .ifPresent(b::setJerseyNumberOptional);

        return b;
    }

    public static TShuffleGameInfo.Builder toTShuffleGameInfo(
            Instant now,
            ShuffleGameTemplate template,
            Match match,
            Team team,
            Player player) {
        Brand brand = template.getGameCategory().getBrand();
        TShuffleGameInfo.Builder builder = toTShuffleGameInfo(brand, now, match, team, player, template.isFreeToPlay(), template.isMillionGame());
        GameCategory gameCategory = template.getGameCategory();
        builder.setSportCode(gameCategory.getLeague().getSport().getCode().toApiCode());
        builder.setLeague(toLeague(brand, gameCategory.getLeague()));

        builder.setCategoryCode(gameCategory.getType().getCode());
        builder.setMinLegsCount(template.getMinLegs());
        builder.setMaxLegsCount(template.getMaxLegs());
        builder.setGameTemplate(template.getCode());

        Optional.ofNullable(match).ifPresent(m -> builder.setGameDate(match.getApiPlayAt()));
        if (template.getRandomMultiplierGroup() != null) {
            builder.setMaxWinConfig(toMaxPrizeConfig(
                    template.getRandomMultiplierGroup().maxMultiplier().getMultiplier(),
                    template.getScEntryMax(),
                    template.getGcEntryMax()));
        }
        return builder;
    }

    public static TShuffleGameInfo.Builder toTShuffleGameInfo(
            Instant now,
            TicketBundle ticketBundle,
            Match match,
            Team team,
            Player player) {
        GameCategory gameCategory = ticketBundle.getGameCategory();
        TShuffleGameInfo.Builder builder = toTShuffleGameInfo(ticketBundle.getAccount().getBrand(), now, match, team, player,
                UrlSlugUtils.UNLIMITED_PLAY.equals(ticketBundle.getSlug()), UrlSlugUtils.MILLION_GAME.equals(ticketBundle.getSlug()));
        if (ticketBundle.getApiPlayAt() != null) {
            builder.setGameDate(ticketBundle.getApiPlayAt());
        } else if (match != null && match.getApiPlayAt() != null) {
            builder.setGameDate(match.getApiPlayAt());
        }
        builder.setUrlSlug(ticketBundle.getSlug());
        builder.setSportCode(gameCategory.getLeague().getSport().getCode().toApiCode());
        builder.setLeague(toLeague(gameCategory.getBrand(), gameCategory.getLeague()));

        builder.setCategoryCode(gameCategory.getType().getCode().toLowerCase());
        if (ticketBundle.getGameTemplateCode() != null) {
            builder.setGameTemplate(ticketBundle.getGameTemplateCode());
        }
        builder.setMaxLegsCount(ticketBundle.getMaxLegs());

        if (ticketBundle.getRandomMultiplierGroup() != null) {
            builder.setMaxWinConfig(toMaxPrizeConfig(
                    getBundleMaxMultiplier(ticketBundle),
                    ticketBundle.getScEntryMax(),
                    ticketBundle.getGcEntryMax()));
        }
        return builder;
    }

    private BigDecimal getBundleMaxMultiplier(TicketBundle ticketBundle) {
        return ticketBundle.getBundleBoostRewardMultiplier().multiply(ticketBundle.getRandomMultiplierGroup().maxMultiplier().getMultiplier());
    }

    public static TMaxWinConfig toMaxPrizeConfig(BigDecimal maxMultiplier, BigDecimal scEntryMax, BigDecimal gcEntryMax) {
        return TMaxWinConfig.newBuilder()
                .setMaxMultiplier(maxMultiplier.toString())
                .setMaxAbsoluteGc(maxMultiplier.multiply(gcEntryMax).toString())
                .setMaxAbsoluteSc(maxMultiplier.multiply(scEntryMax).toString())
                .build();
    }

    public static TWinTable.Builder toTWinTableForGame(
            RandomMultiplierGroup randomMultiplierGroup,
            FlexCoefficientGroup flexCoefficientGroup,
            int maxLegs,
            boolean includeFlex) {
        return toTWinTableForTicket(randomMultiplierGroup, null, flexCoefficientGroup, maxLegs, includeFlex, 0);
    }

    public static TWinTable.Builder toTWinTableForTicket(
            RandomMultiplierGroup randomMultiplierGroup,
            BigDecimal rewardMultiplierCoefficient,
            FlexCoefficientGroup flexCoefficientGroup,
            int maxLegs,
            boolean includeFlex,
            int voidLegsCount) {
        Objects.requireNonNull(randomMultiplierGroup);
        rewardMultiplierCoefficient = rewardMultiplierCoefficient == null ? BigDecimal.ONE : rewardMultiplierCoefficient;

        TWinTable.Builder builder = TWinTable.newBuilder();

        List<RandomMultiplier> multipliers = randomMultiplierGroup.multipliersSortedDesc();

        for (RandomMultiplier multiplier : multipliers) {
            builder.addFixedWin(TDistribution.newBuilder()
                    .setMultiplier(multiplier.getMultiplier().multiply(rewardMultiplierCoefficient).toString())
                    .setProbabilityNumerator(multiplier.getProbabilityNumerator())
                    .setProbabilityDenominator(multiplier.getProbabilityDenominator()));
        }
        if (includeFlex && flexCoefficientGroup != null) {
            List<FlexCoefficient> flexCoefficients = flexCoefficientGroup.flexCoefficientsSortedByTierAsc();
            BigDecimal commonCoefficient = flexCoefficientGroup.getCommonCoefficient();

            for (RandomMultiplier multiplier : multipliers) {
                builder.addFlexWin(TDistribution.newBuilder()
                        .setMultiplier(multiplier.getMultiplier().multiply(rewardMultiplierCoefficient).multiply(commonCoefficient)
                                .setScale(3, RoundingMode.HALF_UP).toString())
                        .setProbabilityNumerator(multiplier.getProbabilityNumerator())
                        .setProbabilityDenominator(multiplier.getProbabilityDenominator()));
            }

            // Adjusted for downgraded tickets if needed
            for (int i = 0; i < flexCoefficients.size(); i++) {
                builder.addFlexOutcomeCoefficients(TFlexOutcomeCoefficient.newBuilder()
                        .setNumberOfCorrect(maxLegs - i - Math.toIntExact(voidLegsCount))
                        .setCoefficient(flexCoefficients.get(i).getCoefficient().toString()));
            }
        }
        return builder;
    }

    public static TPossibleLegOverview.Builder toTPossibleLegOverview(LegTemplate legTemplate, List<String> rangeTypes) {
        TPossibleLegOverview.Builder builder = TPossibleLegOverview.newBuilder();
        builder.addAllRangeTypes(rangeTypes);
        builder.setObjective(legTemplate.getMetric());
        return builder;
    }

    public static List<TTicketEntryLimit> toTTicketEntryLimits(ShuffleGameTemplate template) {
        List<TTicketEntryLimit> ticketEntryLimits = new ArrayList<>();
        TTicketEntryLimit.Builder gcTicketEntryLimit = TTicketEntryLimit.newBuilder();
        gcTicketEntryLimit.setCurrency(Currency.GC.getCode());
        gcTicketEntryLimit.setEntryMin(template.getGcEntryMin().toString());
        gcTicketEntryLimit.setEntryMax(template.getGcEntryMax().toString());
        ticketEntryLimits.add(gcTicketEntryLimit.build());
        TTicketEntryLimit.Builder scTicketEntryLimit = TTicketEntryLimit.newBuilder();
        scTicketEntryLimit.setCurrency(Currency.SC.getCode());
        scTicketEntryLimit.setEntryMin(template.getScEntryMin().toString());
        scTicketEntryLimit.setEntryMax(template.getScEntryMax().toString());
        ticketEntryLimits.add(scTicketEntryLimit.build());
        return ticketEntryLimits;
    }

    public static TTicketEntry.Builder toTTicketEntry(String currency, BigDecimal amount) {
        return TTicketEntry.newBuilder()
                .setCurrency(currency)
                .setAmount(amount.toString());
    }

    public static List<String> toMultiplierList(TicketBundle ticketBundle) {
        List<String> multipliers = null;
        List<RandomMultiplier> randomMultipliers = ticketBundle.getRandomMultiplierGroup().multipliersSortedDesc();
        switch (ticketBundle.getPrizeType()) {
            case FLEX -> {
                BigDecimal commonCoefficient = ticketBundle.getFlexCoefficientGroup().getCommonCoefficient();
                multipliers = randomMultipliers.stream()
                        .map(t -> t.getMultiplier().multiply(ticketBundle.getBundleBoostRewardMultiplier()).multiply(commonCoefficient)
                                .setScale(3, RoundingMode.HALF_UP).toString())
                        .collect(Collectors.toList());
            }
            case FIXED -> {
                multipliers = randomMultipliers.stream()
                        .map(t -> t.getMultiplier().multiply(ticketBundle.getBundleBoostRewardMultiplier()).setScale(3, RoundingMode.HALF_UP).toString())
                        .collect(Collectors.toList());
            }
        }
        return multipliers;
    }

    public static TTicketInfo.Builder toTTicketInfo(Ticket ticket, List<TLeg> legInfo) {
        return TTicketInfo.newBuilder()
                .setCode(ticket.getCode())
                .setIndexInBundle(ticket.getIndexInBundle())
                .setDrawnMultiplier(ticket.getMultiplier().toString())
                .setSeen(ticket.isSeen())
                .addAllLegsInfo(legInfo);
    }

    public static TCorrectCondition.Builder toTCorrectCondition(OverUnderPrediction prediction) {
        String lowerBoundary;
        String upperBoundary;
        TCorrectCondition.Builder builder = TCorrectCondition.newBuilder()
                .setPredictionType(prediction.getType().name());
        switch (prediction.getType()) {
            case OVER -> {
                lowerBoundary = prediction.getMean().toString();
                builder.setLowerBoundary(lowerBoundary);
            }
            case UNDER -> {
                upperBoundary = prediction.getMean().toString();
                builder.setUpperBoundary(upperBoundary);
            }
        }
        return builder;
    }

    public static TUserTicket.Builder toTicketDetailsWithoutLegs(
            Instant now,
            Tuple3<Player, Team, Match> matchInfo,
            Ticket gameTicket) {

        TicketBundle bundle = gameTicket.getBundle();

        TUserTicket.Builder ticket = TUserTicket.newBuilder();

        ticket.setCode(gameTicket.getCode());
        ticket.setIndexInBundle(gameTicket.getIndexInBundle());
        if (gameTicket.getResult().equals(Result.VOID)) {
            // There is a requirement to show original multiplier for VOID tickets,
            // even downgraded ones, so we cannot use `getEffectiveMaxMultiplier`
            ticket.setDrawnMultiplier(gameTicket.getMultiplier().toString());
        } else {
            ticket.setDrawnMultiplier(gameTicket.getEffectiveMaxMultiplier().toString());
        }
        ticket.setSeen(gameTicket.isSeen());

        ticket.setSport(bundle.getGameCategory().getLeague().getSport().getCode().toApiCode());
        ticket.setLeague(bundle.getGameCategory().getLeague().getCode().toApiCode());
        ticket.setCategory(bundle.getGameCategory().getType().getCode());

        ticket.setStage(gameTicket.getStage().name());
        ticket.setResult(gameTicket.getResult().name());
        ticket.setSettled(gameTicket.isSettled());

        ticket.setLegsCorrect(gameTicket.countCorrectLegs());
        ticket.setVoidLegsCount((int) gameTicket.getVoidLegsCount());
        ticket.setIsDowngraded(gameTicket.isDowngraded());

        // deprecated
        ticket.setStatus(gameTicket.getStatus().getCode());
        // deprecated
        ticket.setWin(gameTicket.hasWon());
        // deprecated
        ticket.setStatus(gameTicket.getStatus().getCode());

        ticket.setWinAmount(gameTicket.getWinAmount() == null ? BigDecimal.ZERO.toString() : gameTicket.getWinAmount().toString());

        ticket.setCreatedAt(gameTicket.getCreatedAt().getEpochSecond());
        ticket.setMaxLegs(bundle.getMaxLegs());

        ticket.setWinTable(Mappers.toTWinTableForTicket(
                bundle.getRandomMultiplierGroup(),
                bundle.getRewardMultiplier(),
                bundle.getFlexCoefficientGroup(),
                bundle.getMaxLegs(),
                PrizeType.FLEX.equals(bundle.getPrizeType()),
                ticket.getVoidLegsCount()));
        ticket.setCountOfLegs(gameTicket.getLegs().size());
        ticket.setLegsCorrect(gameTicket.countCorrectLegs());

        ticket.setBundleCode(bundle.getCode().toString());
        ticket.setEntry(toTTicketEntry(bundle.getEntryCurrency(), bundle.getEffectiveEntryTicketAmount()));

        if (gameTicket.getAccountReward() != null) {
            ticket.setRewardInfo(toRewardInfo(gameTicket.getAccountReward().getRewardType(), gameTicket.getAccountReward().getMultiplier(),
                    gameTicket.getAccountReward().getEntryMax()));
        }
        TShuffleGameInfo.Builder tShuffleGameInfo = toTShuffleGameInfo(
                now,
                bundle,
                matchInfo._3,
                matchInfo._2,
                matchInfo._1);

        tShuffleGameInfo.setEventStartsAt(gameTicket.getPlayAt().getEpochSecond());
        tShuffleGameInfo.setIsLive(gameTicket.getStage().equals(Stage.IN_PROGRESS));
        if (bundle.getGameTemplateCode() != null) {
            tShuffleGameInfo.setGameTemplate(bundle.getGameTemplateCode());
        }

        ticket.setShuffleGameInfo(tShuffleGameInfo);

        ticket.setPrizeType(bundle.getPrizeType().getCode());
        ticket.setMaxWinConfig(toMaxPrizeConfig(
                getBundleMaxMultiplier(bundle),
                bundle.getScEntryMax(),
                bundle.getGcEntryMax()));
        return ticket;
    }

    public static TNoUpcomingGame toTNoUpcomingGame(Brand brand, Player player, Team team) {
        TSlugDetails.Builder slugDetailsBuilder = TSlugDetails.newBuilder();
        slugDetailsBuilder.setPlayerInfo(toGamePlayerInfo(brand, player, team));
        if (team != null) {
            slugDetailsBuilder.setTeamInfo(toTeamInfo(brand, team));
        }
        return TNoUpcomingGame.newBuilder()
                .setLeague(toLeague(brand, player.getLeague()).build())
                .setSlugDetails(slugDetailsBuilder)
                .build();
    }

    public static TNoUpcomingGame toTNoUpcomingGame(Brand brand, Team team) {
        return TNoUpcomingGame.newBuilder()
                .setLeague(toLeague(brand, team.getLeague()).build())
                .setSlugDetails(TSlugDetails.newBuilder()
                        .setTeamInfo(toTeamInfo(brand, team))
                        .build())
                .build();
    }

    public static TNoUpcomingGame toTNoUpcomingGame(Brand brand, Team homeTeam, Team awayTeam) {
        return TNoUpcomingGame.newBuilder()
                .setLeague(toLeague(brand, homeTeam.getLeague()).build())
                .setSlugDetails(TSlugDetails.newBuilder()
                        .setMatchInfo(toMatchInfo(brand, homeTeam, awayTeam))
                        .build())
                .build();
    }

    public static TNoUpcomingGame toTNoUpcomingGame(Brand brand, League league, String date) {
        return TNoUpcomingGame.newBuilder()
                .setLeague(Mappers.toLeague(brand, league).build())
                .setSlugDetails(TSlugDetails.newBuilder()
                        .setDayInfo(TDayInfo.newBuilder()
                                .setGameDate(date)
                                .build())
                        .build())
                .build();
    }

    private static TShuffleGameInfo.Builder toTShuffleGameInfo(Brand brand,
            Instant now,
            Match match,
            Team team,
            Player player,
            boolean isFreeToPlay,
            boolean isMillionGame) {
        TShuffleGameInfo.Builder builder = TShuffleGameInfo.newBuilder();

        if (match != null) {
            builder.setMatchInfo(Mappers.toMatchInfo(brand, match));

            // `isLive=true` should never be the case in theory because we do not offer games for started matches
            builder.setIsLive(match.getPlayAt().isBefore(now) && !match.isClosed());
            builder.setEventStartsAt(match.getPlayAt().getEpochSecond());
        }
        if (player != null) {
            builder.setPlayerInfo(Mappers.toGamePlayerInfo(brand, player, team));
        }
        if (team != null) {
            builder.setTeamInfo(Mappers.toTeamInfo(brand, team));
        }
        builder.setIsFreeToPlay(isFreeToPlay);
        builder.setIsMillionGame(isMillionGame);
        if (isMillionGame) {
            builder.addTags(TAG_MILLION_BLITZ_EM);
        }
        if (isFreeToPlay) {
            builder.addTags(TAG_UNLIMITED_BLITZ_EM);
        }
        return builder;
    }

    public static TLeg toLeg(
            Leg leg,
            Player player,
            Team team,
            Match match,
            int index) {
        Brand brand = leg.getBrand();
        TLeg.Builder tLeg = TLeg.newBuilder();
        tLeg.setIndex(index);
        tLeg.setCode(leg.getCode().toString());
        tLeg.setCategory(leg.getGameCategory().getType().getCode());
        tLeg.setObjective(leg.getMetric());

        if (leg instanceof MatchLeg matchLeg) {
            if (player != null) {
                tLeg.setPlayerInfo(Mappers.toGamePlayerInfo(brand, player, team));
            }
            if (team != null) {
                tLeg.setTeamInfo(Mappers.toTeamInfo(brand, team));
            }
            if (match != null) {
                tLeg.setMatchInfo(Mappers.toMatchInfo(brand, matchLeg.getMatch()));
            }
        }

        if (leg.getMetricValue() != null) {
            tLeg.setCurrentMetricValue(leg.getMetricValue().toString());
        } else {
            tLeg.setCurrentMetricValue(BigDecimal.ZERO.toString());
        }

        tLeg.setIsCorrect(leg.getIsCorrect());
        // deprecated
        tLeg.setStatus(leg.getStatus().getCode());

        tLeg.setStage(leg.getStage().name());
        tLeg.setResult(leg.getResult().name());

        tLeg.setCorrectCondition(Mappers.toTCorrectCondition(leg.getPrediction()));

        return tLeg.build();
    }

    public static TExtendedLeg toExtendedLegWithoutDetails(
            Leg leg,
            Player player,
            Team team,
            Match match,
            int index,
            boolean fullMatchInfo) {

        Brand brand = leg.getBrand();
        TLeg.Builder tLeg = TLeg.newBuilder();
        tLeg.setIndex(index);
        tLeg.setCode(leg.getCode().toString());
        tLeg.setCategory(leg.getGameCategory().getType().getCode());
        tLeg.setObjective(leg.getMetric());

        if (fullMatchInfo) {
            if (leg instanceof MatchLeg matchLeg) {
                if (player != null) {
                    tLeg.setPlayerInfo(Mappers.toGamePlayerInfo(brand, player, team));
                }
                if (team != null) {
                    tLeg.setTeamInfo(Mappers.toTeamInfo(brand, team));
                }
                if (match != null) {
                    tLeg.setMatchInfo(Mappers.toMatchInfo(brand, matchLeg.getMatch()));
                }
            }
        } else {
            switch (leg.getGameCategory().getType()) {
                case PLAYER -> tLeg.setPlayerInfo(toGamePlayerInfo(brand, player, team));
                case TEAM -> tLeg.setTeamInfo(toTeamInfo(brand, team));
                case MATCH -> tLeg.setMatchInfo(Mappers.toMatchInfo(brand, ((MatchLeg) leg).getMatch()));
                case DAY -> {
                }
            }
        }

        tLeg.setStatus(leg.getStatus().getCode());

        if (leg.getMetricValue() != null) {
            tLeg.setCurrentMetricValue(leg.getMetricValue().toString());
        } else {
            tLeg.setCurrentMetricValue(BigDecimal.ZERO.toString());
        }

        if (leg.getPrediction() != null) {
            tLeg.setCorrectCondition(Mappers.toTCorrectCondition(leg.getPrediction()));
        }

        TLeg tleg = tLeg.build();

        return TExtendedLeg.newBuilder()
                .setLeg(tleg)
                .setWeight(leg.getWeight())
                .setMinProjection(leg.getMinProjection().toString())
                .build();
    }

    public static TActiveUserTicketsCountEntry toActiveUserTicketsCountEntry(VerticalType type, int totalActive) {
        return TActiveUserTicketsCountEntry.newBuilder()
                .setVertical(type.getCode().toLowerCase())
                .setTotalActive(totalActive)
                .build();
    }

    public static TPickEmEntry.Builder toPickEmEntryWithPicks(Entry entry, FavouritePlayers favouritePlayers) {
        return toPickEmEntryWithoutPicks(entry)
                .addAllPicks(entry.picksSorted().stream().map(p -> Mappers.toPick(p, favouritePlayers)).toList());
    }

    public static TPickEmEntry.Builder toPickEmEntryWithoutPicks(Entry entry) {
        Long startDate = entry.getPicks().stream()
                .map(Pick::getMatch)
                .map(Match::getPlayAt)
                .filter(Objects::nonNull)
                .map(Instant::getEpochSecond)
                .min(Comparator.naturalOrder())
                .orElse(null);

        // TODO check if paid or effective entry should be returned
        TPickEmEntry.Builder entryBuilder = TPickEmEntry.newBuilder()
                .setEntryAmount(entry.getEffectiveAmount().doubleValue())
                .setEntryCurrency(entry.getCurrency().getCode())
                .setCode(entry.getCode())
                .setPicksTotal(entry.getPicks().size())
                .setPicksCorrect(entry.countCorrectPicks())
                .setPicksVoided(entry.countVoidedPicks())
                .setCreatedAt(entry.getCreatedAt().getEpochSecond())
                .setStage(entry.getStage().toString())
                .setResult(entry.getResult().toString())
                .setIsSettled(entry.isSettled())
                .setHasDiscounted(entry.isHasDiscounted())
                .setIsDowngraded(entry.isDowngraded())
                .setStatus(entry.getStatus().getCode())
                .setIsCompleted(entry.getIsCompleted())
                .setPrizeType(entry.getPrizeType().getCode())
                .setIsBoostedByPack(entry.getBoostedPack() != null);

        if (startDate != null) {
            entryBuilder.setStartDate(startDate);
        }

        if (entry.getBoostedPack() != null) {
            entryBuilder.setPackId(entry.getBoostedPack().getCode().toString());
        }

        if (entry.getResult() == Result.VOID) {
            // Just like for Shuffle, for VOIDed entries we return original multiplier
            entryBuilder.setMultiplier(readableString(entry.getMultiplier()));
        } else {
            entryBuilder.setMultiplier(readableString(entry.getEffectiveMaxMultiplier()));
        }

        if (entry.getWinAmount() != null) {
            entryBuilder.setWinAmount(entry.getWinAmount().doubleValue())
                    .setWinAmount2(readableString(entry.getWinAmount()));
        }
        if (entry.getPrizeType() == PrizeType.FLEX) {
            int activeLegsCount = entry.countActiveLegs();
            Multiplier multi = entry.getMultiplierGroup().getMultiplierForPickCount(entry.getBrand(), activeLegsCount).orElse(null);
            if (multi != null && isNotEmpty(multi.getFlexMultipliers())) {
                for (int i = 0; i < multi.getFlexMultipliers().size(); i++) {
                    entryBuilder.addFlexTiers(TFlexTierAmount.newBuilder()
                            .setNumberOfCorrect(activeLegsCount - i)
                            .setWinAmount(readableString(entry.adjustedWinForTableMultiplier(multi.getFlexMultipliers().get(i)))));
                }
                for (BigDecimal tableFlex : multi.getFlexMultipliers()) {
                    entry.adjustMultiplier(tableFlex);
                }
            }
        }

        AccountReward accountReward = entry.getAccountReward();
        if (accountReward != null) {
            entryBuilder.setRewardInfo(toRewardInfo(accountReward.getRewardType(), accountReward.getMultiplier(), accountReward.getEntryMax()));
        }
        return entryBuilder;
    }

    public static TPick toPick(Pick pick, FavouritePlayers favouritePlayers) {
        Brand brand = pick.getBrand();
        TPick.Builder builder = TPick.newBuilder()
                .setCode(pick.getCode().toString())
                .setMatchInfo(Mappers.toMatchInfo(brand, pick.getMatch()))
                .setPlayerInfo(Mappers
                        // pick.getPlayer() only for historical picks created before BrandProp
                        .toGamePlayerInfo(brand, pick.getPlayer(), pick.getBrandProp() != null ? pick.getBrandProp().getTeam() : pick.getPlayer().getTeam())
                        .setFavorite(Optional.ofNullable(favouritePlayers)
                                .map(fp -> fp.getPlayers().contains(pick.getPlayer()))
                                .orElse(false)))
                .setMetric(pick.getMetric())
                .setOption(pick.getOption().getCode())
                .setProjection(pick.getProjection().toString())
                .setLineType(pick.getLineType().name())
                .setActualValue(pick.getActualValueOrZero().toString())
                .setStage(pick.getStage().toString())
                .setResult(pick.getResult().toString())
                .setIsCorrect(pick.getIsCorrectValue())
                .setIsLive(pick.isLive());

        Optional.ofNullable(pick.getStandardLine()).ifPresent(sline -> builder.setStandardLine(sline.toString()));
        return builder.build();
    }

    public TRewardBundle toRewardBundle(String rewardBundle, List<RewardBundleTemplate> rewardBundleTemplates) {

        List<TRewardTemplateCount> rewardTemplates = new ArrayList<>();
        for (RewardBundleTemplate rewardBundleTemplate : rewardBundleTemplates) {
            TRewardTemplateCount rewardTemplate = TRewardTemplateCount.newBuilder()
                    .setRewardTemplate(toRewardTemplate(rewardBundleTemplate.getRewardTemplate()))
                    .setCount(rewardBundleTemplate.getRewardsCount())
                    .build();
            rewardTemplates.add(rewardTemplate);
        }
        return TRewardBundle.newBuilder()
                .setRewardBundle(rewardBundle)
                .addAllRewardTemplates(rewardTemplates)
                .build();
    }

    public TRewardTemplate toRewardTemplate(RewardTemplate rewardTemplate) {
        TRewardTemplate.Builder builder = TRewardTemplate.newBuilder()
                .setCode(rewardTemplate.getCode().toString())
                .setVertical(rewardTemplate.getVertical().getType().getCode())
                .setType(rewardTemplate.getType().name())
                .setEntryCurrency(rewardTemplate.getEntryCurrency())
                .setExpiryDays(rewardTemplate.getExpiryDays());
        if (rewardTemplate.getEntryAmount() != null) {
            builder.setEntryAmount(rewardTemplate.getEntryAmount().toString());
        }
        if (rewardTemplate.getEntryMax() != null) {
            builder.setEntryMax(rewardTemplate.getEntryMax().toString());
        }
        if (rewardTemplate.getMultiplier() != null) {
            builder.setMultiplier(rewardTemplate.getMultiplier().toString());
        }
        return builder.build();
    }

    public TRewardGroupCount toRewardGroupCount(RewardGroupCount rewardGroup) {
        TRewardGroup.Builder builder = TRewardGroup.newBuilder()
                .setVertical(rewardGroup.getVerticalType())
                .setType(rewardGroup.getRewardType().name())
                .setEntryCurrency(rewardGroup.getEntryCurrency());

        if (rewardGroup.getExpiresAt() != null) {
            builder.setExpiresAt(rewardGroup.getExpiresAt().getEpochSecond());
        }
        if (rewardGroup.getMultiplier() != null) {
            builder.setMultiplier(rewardGroup.getMultiplier().toString());
        }
        if (rewardGroup.getEntryMax() != null) {
            builder.setEntryMax(rewardGroup.getEntryMax().toString());
        }
        if (rewardGroup.getEntryAmount() != null) {
            builder.setEntryAmount(rewardGroup.getEntryAmount().toString());
        }

        return TRewardGroupCount.newBuilder()
                .setRewardGroup(builder.build())
                .setCount(rewardGroup.getRewardCount())
                .build();
    }

    public AccountReward accountRewardFromTemplate(RewardTemplate rt, Account account, LocalDate at, RewardTriggerType triggerEvent, String offerId) {
        LocalDate expiresDateAt = at.plusDays(rt.getExpiryDays());
        Instant expiresAt = expiresDateAt.atStartOfDay().atZone(Account.DEFAULT_TIME_ZONE).toInstant();

        AccountReward.AccountRewardBuilder builder = AccountReward.builder()
                .code(PlatformUtil.randomUUIDv7())
                .account(account)
                .status(AccountReward.Status.AVAILABLE)
                .vertical(rt.getVertical())
                .rewardType(rt.getType())
                .multiplier(rt.getMultiplier())
                .entryAmount(rt.getEntryAmount())
                .entryCurrency(rt.getEntryCurrency())
                .entryMax(rt.getEntryMax())
                .expiresAt(expiresAt)
                .triggerType(triggerEvent);
        if (StringUtils.isNotBlank(offerId)) {
            builder.remoteOfferId(offerId);
        }
        return builder.build();
    }

    public static TSportLeagueInfo toSportLeagueInfo(Sport sport, TLeague.Builder lb, int displayOrder) {
        var builder = TSportLeagueInfo.newBuilder()
                .setSport(sport.getCode().toApiCode())
                .setLeague(lb.build())
                .setDisplayOrder(displayOrder);
        return builder.build();
    }

    public static TRewardInfo toRewardInfo(RewardType rewardType, BigDecimal rewardMultiplier, BigDecimal rewardEntryMax) {
        TRewardInfo.Builder builder = TRewardInfo.newBuilder();
        builder.setType(rewardType.name());
        if (RewardType.ALL_BOOST.contains(rewardType)) {
            builder.setMultiplier(rewardMultiplier.toPlainString());
            builder.setMaxEntryAmount(rewardEntryMax.toPlainString());
        }
        return builder.build();
    }

    public static TMetricAdminInfo toPickEmMetricAdminInfo(PropAdminInfo metricAdminInfo) {
        TMetricAdminInfo.Builder builder = TMetricAdminInfo.newBuilder()
                .setIsActivePlayer(metricAdminInfo.getIsActivePlayer())
                .setIsActiveMetric(metricAdminInfo.getIsActiveMetric())
                .setMetric(metricAdminInfo.getMetric())
                .setIsActivePlayer(metricAdminInfo.getIsActivePlayer())
                .setIsActiveMetric(metricAdminInfo.getIsActiveMetric())
                .setPicksCount(metricAdminInfo.getPicksCount())
                .setLineType(toLineType(metricAdminInfo.getLineType()));
        builder.setOver(toOption(metricAdminInfo.getOver()));
        builder.setUnder(toOption(metricAdminInfo.getUnder()));
        if (metricAdminInfo.getSuspendReason() != null) {
            builder.setSuspendReason(metricAdminInfo.getSuspendReason().name());
        }
        if (metricAdminInfo.getWagerThreshold1() != null) {
            builder.setWagerThreshold1(metricAdminInfo.getWagerThreshold1().toPlainString());
        }
        if (metricAdminInfo.getWagerThreshold2() != null) {
            builder.setWagerThreshold2(metricAdminInfo.getWagerThreshold2().toPlainString());
        }
        if (metricAdminInfo.getWagerThreshold3() != null) {
            builder.setWagerThreshold3(metricAdminInfo.getWagerThreshold3().toPlainString());
        }
        if (metricAdminInfo.getExposureThreshold1() != null) {
            builder.setExposureThreshold1(metricAdminInfo.getExposureThreshold1().toPlainString());
        }
        if (metricAdminInfo.getExposureThreshold2() != null) {
            builder.setExposureThreshold2(metricAdminInfo.getExposureThreshold2().toPlainString());
        }
        if (metricAdminInfo.getExposureThreshold3() != null) {
            builder.setExposureThreshold3(metricAdminInfo.getExposureThreshold3().toPlainString());
        }
        return builder.build();
    }

    public static TLineType toLineType(LineType type) {
        return switch (type) {
            case STANDARD -> TLineType.STANDARD;
            case DISCOUNTED -> TLineType.DISCOUNTED;
            case ALTERNATIVE -> TLineType.ALTERNATIVE;
        };
    }

    public static LineType toLineType(TLineType type) {
        return switch (type) {
            case STANDARD -> LineType.STANDARD;
            case DISCOUNTED -> LineType.DISCOUNTED;
            case ALTERNATIVE -> LineType.ALTERNATIVE;
            default -> throw new IllegalArgumentException("Unknown prop type: " + type);
        };
    }

    private static TOption toOption(PropAdminInfo.Option over) {
        return TOption.newBuilder()
                .setPicksCount(over.getPicksCount())
                .setCurrentWager(over.getCurrentWager() == null ? BigDecimal.ZERO.toPlainString() : over.getCurrentWager().toPlainString())
                .setCurrentExposure(over.getCurrentExposure() == null ? BigDecimal.ZERO.toPlainString() : over.getCurrentExposure().toPlainString())
                .setAlertLevelTriggered(over.getAlertLevelTriggered() == null ? 0 : over.getAlertLevelTriggered())
                .build();
    }

    public static TPlayerOption toPlayerOptions(BrandProp brandProp, List<String> overUnderList, FavouritePlayers favouritePlayers) {
        return TPlayerOption.newBuilder()
                .setMatchInfo(Mappers.toMatchInfo(brandProp.getBrand(), brandProp.getMatch()))
                .setPlayerInfo(Mappers.toGamePlayerInfo(brandProp.getBrand(), brandProp.getPlayer(), brandProp.getTeam())
                        .setFavorite(Optional.ofNullable(favouritePlayers)
                                .map(fp -> fp.getPlayers().contains(brandProp.getPlayer()))
                                .orElse(false)))
                .setPickEmOption(Mappers.toPickEmOption(brandProp, overUnderList))
                .build();
    }

    public static TPlayerOption toPlayerOptions(PlayerCard.PlayerProp pp, boolean acceptAlternative, FavouritePlayers favouritePlayers) {
        return TPlayerOption.newBuilder()
                .setMatchInfo(Mappers.toMatchInfo(pp.getBrand(), pp.getMatch()))
                .setPlayerInfo(Mappers.toGamePlayerInfo(pp.getBrand(), pp.getPlayer(), pp.getTeam())
                        .setFavorite(Optional.ofNullable(favouritePlayers)
                                .map(fp -> fp.getPlayers().contains(pp.getPlayer()))
                                .orElse(false)))
                .setPickEmOption(Mappers.toPickEmOption(pp, null, acceptAlternative))
                .build();
    }

    public static TPickEmOption toPickEmOption(BrandProp brandProp, List<String> overUnderList) {
        TPickEmOption.Builder builder = TPickEmOption.newBuilder()
                .setType(toLineType(brandProp.getType()))
                .setMetric(brandProp.getMetric())
                .setProjection(brandProp.getLine().doubleValue())
                .addAllPicks(overUnderList);
        if (brandProp.getStandardLine() != null) {
            builder.setStandardLine(brandProp.getStandardLine().toString());
        }
        return builder.build();
    }

    public static TPickEmOption toPickEmOption(PlayerCard.PlayerProp pp, MarketAdminInfo adminInfo, boolean acceptAlternative) {
        TPickEmOption.Builder pickEmOptionBuilder = TPickEmOption.newBuilder()
                .setMetric(pp.getMetricName());
        PropAdminInfo metricAdminInfo = null;
        switch (pp) {
            case PlayerCard.MultilineProp mlp -> {
                pickEmOptionBuilder
                        .setProjection((mlp.getBalancedLine().orElse(BigDecimal.ZERO).doubleValue()))
                        .setType(toLineType(acceptAlternative ? LineType.ALTERNATIVE : LineType.STANDARD))
                        .addAllPicks(OPTIONS);
                if (adminInfo != null) {
                    metricAdminInfo = adminInfo.getPropAdminInfo(pp.getBrand(),
                            pp.getMatch(), pp.getPlayer(), pp.getMetricName(), LineType.STANDARD, null);
                    if (mlp.getBalanced().isPresent()) {
                        BrandProp balancedBp = mlp.getBalanced().get();
                        metricAdminInfo.setIsActiveMetric(!isSuspendedByBrand(pp.getMatch(), pp.getMetricName(),
                                pp.getPlayer().getId(), balancedBp.getLine()));
                        metricAdminInfo.setSuspendReason(balancedBp.getSuspendReason());
                    } else {
                        metricAdminInfo.setIsActiveMetric(false);
                        metricAdminInfo.setSuspendReason(BrandProp.SuspendReason.NO_BALANCED);
                    }
                }
                if (acceptAlternative && !mlp.brandProps.isEmpty()) {
                    pickEmOptionBuilder.addAllLines(mlp.brandProps.values().stream().map(l -> {
                        TPickEmLine.Builder lineBuilder = TPickEmLine.newBuilder()
                                .setProjection(readableString(l.getLine()))
                                .setBalanced(Boolean.TRUE.equals(l.getBalanced()));

                        BigDecimal overMulti = l.marginalMulti(OverUnder.OVER);
                        if (overMulti != null) {
                            lineBuilder.setOverMulti(readableString(overMulti));
                        }

                        BigDecimal underMulti = l.marginalMulti(OverUnder.UNDER);
                        if (underMulti != null) {
                            lineBuilder.setUnderMulti(readableString(underMulti));
                        }

                        if (adminInfo != null) {
                            PropAdminInfo lineAdminInfo = adminInfo.getPropAdminInfo(pp.getBrand(),
                                    pp.getMatch(), pp.getPlayer(), pp.getMetricName(), LineType.ALTERNATIVE,
                                    l.getLine().doubleValue());

                            lineAdminInfo.setIsActiveMetric(!isSuspendedByBrand(pp.getMatch(), pp.getMetricName(),
                                    pp.getPlayer().getId(), l.getLine()));
                            lineAdminInfo.setSuspendReason(l.getSuspendReason());
                            lineBuilder.setMetricAdminInfo(toPickEmMetricAdminInfo(lineAdminInfo));
                        }
                        return lineBuilder.build();
                    }).toList());
                }
            }
            case PlayerCard.DiscountedProp dp -> {
                BrandProp discountedBp = dp.brandProp;
                pickEmOptionBuilder
                        .setProjection(discountedBp.isSuspended() ? 0 : dp.brandProp.getLine().doubleValue())
                        .setType(toLineType(LineType.DISCOUNTED))
                        .addAllPicks(DISCOUNTED_OPTIONS);
                if (discountedBp.getStandardLine() != null) {
                    pickEmOptionBuilder.setStandardLine(discountedBp.getStandardLine().toPlainString());
                }
                if (discountedBp.getDiscountedLine() != null) {
                    pickEmOptionBuilder.setRulebook(
                            discountedLineRulebook(discountedBp.getDiscountedLine(), null));
                }
                if (adminInfo != null) {
                    metricAdminInfo = adminInfo.getPropAdminInfo(pp.getBrand(),
                            pp.getMatch(), pp.getPlayer(), pp.getMetricName(), LineType.DISCOUNTED, null);
                    metricAdminInfo.setIsActiveMetric(discountedBp.getDiscountedLine().isActive());
                    metricAdminInfo.setSuspendReason(discountedBp.getSuspendReason());
                }
            }
            default -> log.error("Unexpected prop type: {}", pp.getClass().getSimpleName());
        }
        if (metricAdminInfo != null) {
            pickEmOptionBuilder.setMetricAdminInfo(toPickEmMetricAdminInfo(metricAdminInfo));
        }
        return pickEmOptionBuilder.build();
    }

    public static TPlayerOptions toPickEmOptions(PlayerCard pc, MarketAdminInfo adminInfo,
            FavouritePlayers favouritePlayers, boolean acceptAlternative) {
        return TPlayerOptions.newBuilder()
                .setMatchInfo(toMatchInfo(pc.getBrand(), pc.getMatch()))
                .setPlayerInfo(toGamePlayerInfo(pc.getBrand(), pc.getPlayer(), pc.getTeam())
                        .setFavorite(Optional.ofNullable(favouritePlayers)
                                .map(fp -> fp.getPlayers().contains(pc.getPlayer()))
                                .orElse(false)))
                .setSportCode(pc.getMatch().getLeague().getSport().getCode().toApiCode())
                .setLeagueCode(pc.getMatch().getLeague().getCode().toApiCode())
                .setCategoryCode(GameCategory.Type.PLAYER.getCode())
                .addAllOptions(pc.getProps().stream()
                        .map(pp -> toPickEmOption(pp, adminInfo, acceptAlternative)).toList())
                .build();
    }
}
