package rnd.handlers.admin;

import static api.v1.Code.ERR_BAD_REQUEST;
import static org.apache.commons.lang3.StringUtils.trimToNull;

import java.math.BigDecimal;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.Reason;
import io.ebean.Transaction;
import jakarta.inject.Inject;
import rnd.Mappers;
import rnd.RandomizerJpaManager;
import rnd.RandomizerServerProperties;
import rnd.api.v1.admin.TCreateOrUpdateTradingAlertConfigRequest;
import rnd.api.v1.admin.TCreateOrUpdateTradingAlertConfigResponse;
import rnd.handlers.AbstractRequestHandler;
import rnd.model.alert.TradingAlertConfig;
import rnd.model.alert.TradingMarketStats;
import rnd.model.data.League;
import rnd.model.data.LineType;
import rnd.model.data.Match;
import rnd.model.data.Player;
import rnd.model.main.Brand;
import rnd.model.main.pickem.DiscountedLine;
import rnd.model.main.pickem.PickEmSuspended;

@Service
public class CreateOrUpdateTradingAlertConfigRequestHandler
        extends AbstractRequestHandler<TCreateOrUpdateTradingAlertConfigRequest, TCreateOrUpdateTradingAlertConfigResponse.Builder>
        implements ModifyHandler<TCreateOrUpdateTradingAlertConfigRequest, TCreateOrUpdateTradingAlertConfigResponse.Builder> {

    @Inject
    public CreateOrUpdateTradingAlertConfigRequestHandler(RandomizerServerProperties props, RandomizerJpaManager ebean) {
        super(props, ebean);
    }

    @Override
    public void apply(TransactionalRequest<TCreateOrUpdateTradingAlertConfigRequest, TCreateOrUpdateTradingAlertConfigResponse.Builder> transactionalRequest)
            throws Throwable {
        var request = transactionalRequest.request();
        var response = transactionalRequest.reply();
        var id = request.getId();

        validateRequest(request);

        TradingAlertConfig tradingAlertConfig;
        try (Transaction tx = ebean.newTransaction()) {
            var league = resolveLeague(request, tx);
            var match = resolveMatch(request, tx);
            var player = resolvePlayer(request, tx);
            var brand = resolveBrand(request, tx);
            LineType lineType = Mappers.toLineType(request.getLineType());

            String metric = request.getMetric();
            var configBuilder = TradingAlertConfig.builder()
                    .id(id)
                    .brand(brand)
                    .league(league)
                    .match(match)
                    .player(player)
                    .metric(trimToNull(metric))
                    .lineType(lineType);

            if (player != null && (match == null || StringUtils.isBlank(metric))) {
                throw EnhancedApplicationException.of("Match and metric are required for player", ERR_BAD_REQUEST, Reason.BAD_REQUEST);
            }

            if (request.hasWagerThreshold1()) {
                configBuilder.wagerThreshold1(BigDecimal.valueOf(request.getWagerThreshold1()));
            }

            if (request.hasWagerThreshold2()) {
                configBuilder.wagerThreshold2(BigDecimal.valueOf(request.getWagerThreshold2()));
            }

            if (request.hasWagerThreshold3()) {
                configBuilder.wagerThreshold3(BigDecimal.valueOf(request.getWagerThreshold3()));
            }

            if (request.hasExposureThreshold1()) {
                configBuilder.exposureThreshold1(BigDecimal.valueOf(request.getExposureThreshold1()));
            }

            if (request.hasExposureThreshold2()) {
                configBuilder.exposureThreshold2(BigDecimal.valueOf(request.getExposureThreshold2()));
            }

            if (request.hasExposureThreshold3()) {
                configBuilder.exposureThreshold3(BigDecimal.valueOf(request.getExposureThreshold3()));
            }

            // Override the isProtected flag to ensure changes are not ignored after restart
            configBuilder.isProtected(true);

            tradingAlertConfig = configBuilder.build();
            id = ebean.randomizerRepo().upsertTradingAlertsConfig(tradingAlertConfig, tx);

            // enable/disable the corresponding standard/discounted line
            if (request.hasLineActive()) {
                switch (lineType) {
                    case DISCOUNTED -> {
                        DiscountedLine discountedLine = ebean.randomizerRepo().getDiscountedLine(brand, match, player, metric, tx)
                                .orElseThrow(ApplicationException.orElseThrow("unable to find discounted line by brand %s, match %s, player %s, metric (%s)"
                                        .formatted(brand.getCode(), match.getCode(), player.getCode(), metric), Code.ERR_NOT_FOUND));
                        discountedLine.setActive(request.getLineActive());
                        ebean.save(discountedLine);
                    }
                    case STANDARD -> {
                        PickEmSuspended pickEmSuspended = ebean.randomizerRepo().getPickEmSuspended(brand, match, player, metric, tx).orElseGet(() -> {
                            PickEmSuspended p = new PickEmSuspended();
                            p.setBrand(brand);
                            p.setMatch(match);
                            p.setPlayer(player);
                            p.setMetric(metric);
                            return p;
                        });
                        pickEmSuspended.setSuspensionActive(!request.getLineActive());
                        ebean.save(pickEmSuspended);
                    }
                }
            }

            tx.commit();
        }

        if (request.getUnsuspendStatThreshold3()) {
            unsuspendStatThreshold3(tradingAlertConfig);
        }

        response.setOperation(request.getId() == 0 ? "create" : "update");
        response.setId(id);
    }

    private void unsuspendStatThreshold3(TradingAlertConfig tradingAlertConfig) throws Throwable {

        try (Transaction tx = ebean.newTransaction()) {
            List<TradingMarketStats> deactivatedMarkets = ebean.randomizerRepo().getTradingMarketStatsForDeActivatedPropsL3(tradingAlertConfig, tx);
            deactivatedMarkets.forEach(stat -> {
                stat.setWagerThreshold3Reported(false);
                stat.setWagerThreshold3Triggered(false);
                stat.setExposureThreshold3Reported(false);
                stat.setExposureThreshold3Triggered(false);
            });
            ebean.updateAll(deactivatedMarkets);
            tx.commit();
        }

    }

    private void validateRequest(TCreateOrUpdateTradingAlertConfigRequest request) throws ApplicationException {
        request.getLeagueCode();
        if (request.getLeagueCode().isEmpty()) {
            throw EnhancedApplicationException.of("League code cannot be null or empty", ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }

        validateThreshold(request.hasWagerThreshold1(), request.getWagerThreshold1(), "Wager threshold 1");
        validateThreshold(request.hasWagerThreshold2(), request.getWagerThreshold2(), "Wager threshold 2");
        validateThreshold(request.hasWagerThreshold3(), request.getWagerThreshold3(), "Wager threshold 3");

        validateThreshold(request.hasExposureThreshold1(), request.getExposureThreshold1(), "Exposure threshold 1");
        validateThreshold(request.hasExposureThreshold2(), request.getExposureThreshold2(), "Exposure threshold 2");
        validateThreshold(request.hasExposureThreshold3(), request.getExposureThreshold3(), "Exposure threshold 3");
    }

    private void validateThreshold(boolean hasValue, long threshold, String fieldName) throws ApplicationException {
        if (hasValue && threshold <= 0) {
            throw EnhancedApplicationException.of(fieldName + " must be greater than 0", ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }
    }

    private League resolveLeague(TCreateOrUpdateTradingAlertConfigRequest request, Transaction tx) throws ApplicationException {
        return ebean.dataRepo().leagueByCode(getValidLeagueCode(request.getLeagueCode()), tx)
                .orElseThrow(() -> EnhancedApplicationException.of("League not found: %s".formatted(request.getLeagueCode()), ERR_BAD_REQUEST));
    }

    private Match resolveMatch(TCreateOrUpdateTradingAlertConfigRequest request, Transaction tx) throws EnhancedApplicationException {
        return request.hasMatchCode() ? ebean.dataRepo().matchByCode(request.getMatchCode(), tx)
                .orElseThrow(() -> EnhancedApplicationException.of(
                        "No Match found by code '%s'".formatted(request.getMatchCode()),
                        ERR_BAD_REQUEST,
                        Reason.BAD_REQUEST))
                : null;
    }

    private Player resolvePlayer(TCreateOrUpdateTradingAlertConfigRequest request, Transaction tx) throws EnhancedApplicationException {
        return request.hasPlayerCode() ? ebean.dataRepo().playerByCode(request.getPlayerCode(), tx)
                .orElseThrow(() -> EnhancedApplicationException.of(
                        "No Player found by code '%s'".formatted(request.getPlayerCode()),
                        ERR_BAD_REQUEST,
                        Reason.BAD_REQUEST))
                : null;
    }

    private Brand resolveBrand(TCreateOrUpdateTradingAlertConfigRequest request, Transaction tx) throws ApplicationException {
        return ebean.brandRepo().requiredBrand(request.getBrandName(), tx);
    }

}
