package rnd.handlers.admin;

import static java.util.stream.Collectors.groupingBy;
import static rnd.enums.PackType.BOOSTED;

import java.math.BigDecimal;
import java.net.URL;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.Reason;
import io.ebean.Transaction;
import jakarta.inject.Inject;
import rnd.RandomizerJpaManager;
import rnd.RandomizerServerProperties;
import rnd.UncorrelatedCriteria;
import rnd.api.v1.admin.TCreateOrUpdateDefaultResponse;
import rnd.api.v1.admin.TCreateOrUpdatePickEmPackConfigRequest;
import rnd.enums.EntityOperation;
import rnd.enums.PackType;
import rnd.handlers.AbstractRequestHandler;
import rnd.handlers.admin.utils.RequestValidator;
import rnd.model.alert.TradingAlertConfig;
import rnd.model.alert.TradingMarketStats;
import rnd.model.data.BrandProp;
import rnd.model.data.DataProvider;
import rnd.model.data.League;
import rnd.model.data.Player;
import rnd.model.data.Prop;
import rnd.model.main.Brand;
import rnd.model.main.OverUnder;
import rnd.model.main.UserSegment;
import rnd.model.main.pickem.PickEmPackConfig;
import rnd.model.main.pickem.PickEmPackConfigBrandProps;
import rnd.services.PickEmConfig;

@Service
public class CreateOrUpdatePickEmPackConfigHandler
        extends AbstractRequestHandler<TCreateOrUpdatePickEmPackConfigRequest, TCreateOrUpdateDefaultResponse.Builder>
        implements ModifyHandler<TCreateOrUpdatePickEmPackConfigRequest, TCreateOrUpdateDefaultResponse.Builder> {
    private final PickEmConfig pickEmConfig;

    @Inject
    public CreateOrUpdatePickEmPackConfigHandler(RandomizerServerProperties props, RandomizerJpaManager ebean, PickEmConfig config) {
        super(props, ebean);
        this.pickEmConfig = config;
    }

    @Override
    public void apply(TransactionalRequest<TCreateOrUpdatePickEmPackConfigRequest, TCreateOrUpdateDefaultResponse.Builder> cmd) throws Throwable {
        TCreateOrUpdatePickEmPackConfigRequest request = cmd.request();
        TCreateOrUpdateDefaultResponse.Builder response = cmd.reply();
        PickEmPackConfig pickEmPackConfig;

        try (Transaction tx = ebean.newTransaction()) {
            Brand brand = ebean.brandRepo().requiredBrand(request.getBrandName(), tx);
            UUID code = request.hasCode() ? UUID.fromString(request.getCode()) : null;

            if (request.hasBoostMultiplier()) {
                var boostMultiplier = new BigDecimal(request.getBoostMultiplier());
                var boostMultiplierLimit = BigDecimal.valueOf(props.PICKEM_PACK_MAX_BOOST_MULTIPLIER.get());
                if (boostMultiplier.compareTo(boostMultiplierLimit) > 0) {
                    throw EnhancedApplicationException.of(
                            "Boost multiplier limit %s exceeded".formatted(boostMultiplierLimit.toPlainString()),
                            Code.ERR_BAD_REQUEST,
                            Reason.BAD_REQUEST);
                }
            }

            if (code != null) {
                pickEmPackConfig = ebean.adminRepo().requiredPickEmPackConfigByCode(code, tx);

                if (pickEmPackConfig.getType() != PackType.valueOf(request.getPackType())) {
                    throw EnhancedApplicationException.of("It is restricted to change pack type.", Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
                }
                updatePickEmPackConfigFields(request, pickEmPackConfig, brand, tx);

                ebean.update(pickEmPackConfig, tx);
                response.setOperation(EntityOperation.UPDATED.getCode());
            } else {
                pickEmPackConfig = new PickEmPackConfig();
                pickEmPackConfig.setBrand(brand);
                pickEmPackConfig.setCode(PlatformUtil.randomUUID());
                updatePickEmPackConfigFields(request, pickEmPackConfig, brand, tx);

                ebean.save(pickEmPackConfig, tx);
                response.setOperation(EntityOperation.CREATED.getCode());
            }

            if (BOOSTED.equals(pickEmPackConfig.getType())) {
                validateAndCreateTradingAlertConfig(request, pickEmPackConfig, brand, tx);
            }

            tx.commit();
        }
        response.setCode(pickEmPackConfig.getCode().toString());
    }

    private void updatePickEmPackConfigFields(
            TCreateOrUpdatePickEmPackConfigRequest request,
            PickEmPackConfig config,
            Brand brand,
            Transaction tx) throws Throwable {
        config.setName(request.getPackName());
        config.setActive(request.getIsActive());

        PackType packType = PackType.valueOf(request.getPackType());
        validatePackConfiguration(request, packType);
        config.setType(packType);

        config.setBoostMultiplier(request.hasBoostMultiplier() ? new BigDecimal(request.getBoostMultiplier()) : null);
        config.setMaxEntryAmount(request.hasMaxEntryAmount() ? new BigDecimal(request.getMaxEntryAmount()) : null);

        if (request.hasUserSegment()) {
            handleUserSegment(request, config, brand, tx, packType);
        }

        Map<Long, String> brandPropsOverUnder = request.getBrandPropsMap();
        validateBrandPropsSize(brandPropsOverUnder);

        List<BrandProp> brandProps = ebean.dataRepo().requiredBrandPropsByIDs(brandPropsOverUnder.keySet(), brand, tx);
        validateBrandProps(brandProps);

        List<PickEmPackConfigBrandProps> pickEmPackConfigBrandProps = createPickEmPackConfigBrandProps(request, config, brandProps);
        config.setBrandProps(pickEmPackConfigBrandProps);

        List<String> allowedLogoFormats = props.LOGO_ALLOWED_FORMATS.get();
        URL iconUrl = RequestValidator.validateLogoFormatAndGetURL(allowedLogoFormats, request.getIconUrl());
        config.setIconUrl(iconUrl);
    }

    private void handleUserSegment(
            TCreateOrUpdatePickEmPackConfigRequest request,
            PickEmPackConfig config,
            Brand brand,
            Transaction tx,
            PackType packType) throws ApplicationException {
        if (packType == PackType.STANDARD) {
            throw EnhancedApplicationException.of(
                    "Standard Pack must not have a user segment",
                    Code.ERR_BAD_REQUEST,
                    Reason.BAD_REQUEST);
        }
        UserSegment userSegment = ebean.randomizerRepo().requiredUserSegmentById(request.getUserSegment(), brand, tx);
        RequestValidator.validateOneTimeUsageSegment(userSegment);
        config.setUserSegment(userSegment);
    }

    private void validateBrandPropsSize(Map<Long, String> brandPropsOverUnder) throws EnhancedApplicationException {
        if (brandPropsOverUnder.size() < 2 || brandPropsOverUnder.size() > 12) {
            throw EnhancedApplicationException.of(
                    "Packs should contain at least 2 props or at most 12 props",
                    Code.ERR_BAD_REQUEST,
                    Reason.BAD_REQUEST);
        }
    }

    private void validateBrandProps(List<BrandProp> brandProps) throws EnhancedApplicationException {
        Map<Player, List<BrandProp>> propsPerPlayer = brandProps.stream()
                .collect(groupingBy(BrandProp::getPlayer, LinkedHashMap::new, Collectors.toList()));

        if (propsPerPlayer.size() < pickEmConfig.minDistinctPlayers()) {
            throw EnhancedApplicationException.of(
                    "Boosted pack contains %s distinct players, while at least %s required"
                            .formatted(propsPerPlayer.size(), pickEmConfig.minDistinctPlayers()),
                    Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }

        int packTeamsCount = brandProps.stream()
                .map(Prop::getTeam)
                .collect(Collectors.toSet())
                .size();

        long playersCountInNotTeamSports = propsPerPlayer.keySet()
                .stream().map(Player::getLeague)
                .map(League::getSport)
                .filter(sport -> !sport.isTeamSport())
                .count();

        if (packTeamsCount + playersCountInNotTeamSports < pickEmConfig.minDistinctTeams()) {
            throw EnhancedApplicationException.of(
                    "Boosted pack contains %s distinct teams, while at least %s required"
                            .formatted(packTeamsCount, pickEmConfig.minDistinctTeams()),
                    Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }

        if (pickEmConfig.maxOccurrencesOfSamePlayerPerEntryEnabled()) {
            validateMaxOccurrencesPerPlayer(propsPerPlayer);
        }

        validateUncorrelatedCriteria(brandProps);
    }

    private void validateMaxOccurrencesPerPlayer(Map<Player, List<BrandProp>> propsPerPlayer) throws EnhancedApplicationException {
        for (List<BrandProp> singlePlayerLegs : propsPerPlayer.values()) {
            if (singlePlayerLegs.size() > pickEmConfig.maxOccurrencesOfSamePlayerPerEntry()) {
                throw EnhancedApplicationException.of(
                        "Boosted pack contains multiple single player legs. Max allowed: %s"
                                .formatted(pickEmConfig.maxOccurrencesOfSamePlayerPerEntry()),
                        Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
            }
        }
    }

    private void validateUncorrelatedCriteria(List<BrandProp> brandProps) throws EnhancedApplicationException {
        Map<League, List<BrandProp>> ooProps = brandProps.stream()
                .filter(p -> (p.getMatch().getDataProvider().getCode() == DataProvider.Code.OPTIC_ODDS_V3)
                        || !p.getMatch().isOfTeamSport())
                .collect(groupingBy(p -> p.getMatch().getLeague(), Collectors.toList()));

        for (Map.Entry<League, List<BrandProp>> entry : ooProps.entrySet()) {
            League league = entry.getKey();
            UncorrelatedCriteria criteria = pickEmConfig.uncorrelatedCriteria(league.getCode());

            if (criteria == UncorrelatedCriteria.PER_MATCH) {
                validateMaxPicksPerGroup(
                        ooProps.get(league),
                        Prop::getMatch,
                        pickEmConfig.maxPicksPerMatch(league.getCode()),
                        "Pack must have no more than '%s' players in the same match",
                        Reason.RND_PICKEM_MULTIPLE_PLAYERS_FROM_SAME_MATCH);
            }

            if (criteria == UncorrelatedCriteria.PER_TEAM) {
                validateMaxPicksPerGroup(
                        ooProps.get(league),
                        Prop::getTeam,
                        pickEmConfig.maxPicksPerTeam(league.getCode()),
                        "Pack must have no more than '%s' players in the same team",
                        Reason.RND_PICKEM_MULTIPLE_PLAYERS_FROM_SAME_TEAM);
            }
        }
    }

    private List<PickEmPackConfigBrandProps> createPickEmPackConfigBrandProps(
            TCreateOrUpdatePickEmPackConfigRequest request,
            PickEmPackConfig config,
            List<BrandProp> brandProps) throws EnhancedApplicationException {
        List<PickEmPackConfigBrandProps> pickEmPackConfigBrandProps = new ArrayList<>();

        for (BrandProp brandProp : brandProps) {
            if (brandProp.getDiscountedLine() != null) {
                throw EnhancedApplicationException.of(
                        "Packs can not contain discounted props",
                        Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
            }

            PickEmPackConfigBrandProps pickEmPackConfigBrandProp = new PickEmPackConfigBrandProps();
            OverUnder overUnder = OverUnder.valueOf(request.getBrandPropsOrThrow(brandProp.getId()));
            pickEmPackConfigBrandProp.setOverUnder(overUnder);
            pickEmPackConfigBrandProp.setPickEmPackConfig(config);
            pickEmPackConfigBrandProp.setBrandProp(brandProp);

            pickEmPackConfigBrandProps.add(pickEmPackConfigBrandProp);
        }

        return pickEmPackConfigBrandProps;
    }

    private void validateAndCreateTradingAlertConfig(
            TCreateOrUpdatePickEmPackConfigRequest request,
            PickEmPackConfig packConfig,
            Brand brand,
            Transaction tx) throws Throwable {

        TradingAlertConfig.TradingAlertConfigBuilder configBuilder = ebean.randomizerRepo()
                .findPreBuildPackTradingAlertConfig(packConfig, tx)
                .map(TradingAlertConfig::toBuilder)
                .orElse(TradingAlertConfig.builder().brand(brand));

        if (!request.hasWagerThreshold1() || !request.hasWagerThreshold2() || !request.hasWagerThreshold3()) {
            throw EnhancedApplicationException.of("All wager thresholds must be provided for BOOSTED pack.",
                    Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }

        if (!request.hasExposureThreshold1() || !request.hasExposureThreshold2() || !request.hasExposureThreshold3()) {
            throw EnhancedApplicationException.of("All exposure thresholds must be provided for BOOSTED pack.",
                    Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }

        configBuilder.wagerThreshold1(new BigDecimal(request.getWagerThreshold1()));
        configBuilder.wagerThreshold2(new BigDecimal(request.getWagerThreshold2()));
        configBuilder.wagerThreshold3(new BigDecimal(request.getWagerThreshold3()));

        configBuilder.exposureThreshold1(new BigDecimal(request.getExposureThreshold1()));
        configBuilder.exposureThreshold2(new BigDecimal(request.getExposureThreshold2()));
        configBuilder.exposureThreshold3(new BigDecimal(request.getExposureThreshold3()));

        configBuilder.pack(packConfig);
        configBuilder.lineType(null);

        TradingAlertConfig tradingAlertConfig = configBuilder.build();
        ebean.randomizerRepo().upsertTradingAlertsConfig(tradingAlertConfig, tx);

        if (request.getUnsuspendStatThreshold3()) {
            unsuspendStatThreshold3(tradingAlertConfig, tx);
        }

    }

    private void unsuspendStatThreshold3(TradingAlertConfig tradingAlertConfig, Transaction tx) throws Throwable {

        List<TradingMarketStats> deactivatedMarkets = ebean.randomizerRepo().getTradingMarketStatsForDeActivatedPropsL3(tradingAlertConfig, tx);
        deactivatedMarkets.forEach(stat -> {
            stat.setWagerThreshold3Reported(false);
            stat.setWagerThreshold3Triggered(false);
            stat.setExposureThreshold3Reported(false);
            stat.setExposureThreshold3Triggered(false);

        });

        ebean.updateAll(deactivatedMarkets, tx);
    }

    private void validatePackConfiguration(TCreateOrUpdatePickEmPackConfigRequest request, PackType packType) throws EnhancedApplicationException {
        if (packType == BOOSTED && !request.hasBoostMultiplier()) {
            throw EnhancedApplicationException.of("Boosted Pack must have a valid boost multiplier",
                    Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }
        if (packType == BOOSTED && !request.hasMaxEntryAmount()) {
            throw EnhancedApplicationException.of("Boosted Pack must have a valid max entry amount",
                    Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }
        if (packType == PackType.STANDARD && request.hasBoostMultiplier()) {
            throw EnhancedApplicationException.of("Standard Pack must not have a boost multiplier",
                    Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }
        if (packType == PackType.STANDARD && request.hasMaxEntryAmount()) {
            throw EnhancedApplicationException.of("Standard Pack must not have a max entry amount",
                    Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }
        if (packType == BOOSTED && !request.hasUserSegment()) {
            throw EnhancedApplicationException.of("Boosted Pack must have a valid user segment",
                    Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }

    }

    private <T> void validateMaxPicksPerGroup(
            List<BrandProp> props,
            Function<BrandProp, T> groupBy,
            long maxAllowed,
            String errorMessageTemplate,
            Reason reason) throws EnhancedApplicationException {
        boolean exceeded = props.stream()
                .collect(Collectors.groupingBy(groupBy, Collectors.counting()))
                .values().stream()
                .anyMatch(count -> count > maxAllowed);

        if (exceeded) {
            throw EnhancedApplicationException.of(
                    errorMessageTemplate.formatted(maxAllowed),
                    Code.ERR_BAD_REQUEST,
                    reason);
        }
    }
}
