package rnd.handlers.admin;

import static rnd.utils.Validator.badRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import api.v1.ApplicationException;
import io.ebean.Transaction;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import rnd.RandomizerJpaManager;
import rnd.RandomizerServerProperties;
import rnd.api.v1.admin.TEmptyAdminResponse;
import rnd.api.v1.admin.TUpdatePickEmMetricsRequest;
import rnd.handlers.AbstractRequestHandler;
import rnd.model.data.League;
import rnd.model.main.Brand;
import rnd.model.main.pickem.PickEmMetric;

@Slf4j
@Service
public class UpdatePickEmMetricsRequestHandler
        extends AbstractRequestHandler<TUpdatePickEmMetricsRequest, TEmptyAdminResponse.Builder>
        implements ModifyHandler<TUpdatePickEmMetricsRequest, TEmptyAdminResponse.Builder> {

    @Inject
    public UpdatePickEmMetricsRequestHandler(RandomizerServerProperties props, RandomizerJpaManager ebean) {
        super(props, ebean);
    }

    @Override
    public void apply(TransactionalRequest<TUpdatePickEmMetricsRequest, TEmptyAdminResponse.Builder> cmd) throws Throwable {
        TUpdatePickEmMetricsRequest request = cmd.request();
        try (Transaction tx = ebean.newTransaction()) {
            updatePickEmMetrics(request, tx);
            tx.commit();
        }
    }

    private void updatePickEmMetrics(TUpdatePickEmMetricsRequest request, Transaction tx) throws ApplicationException {
        String brandName = request.getBrandName();
        Brand brand = ebean.brandRepo().requiredBrand(brandName, tx);

        String leagueCodeStr = request.getLeagueCode();
        if (StringUtils.isEmpty(leagueCodeStr)) {
            throw badRequest("League code is required");
        }

        String metricStr = request.getMetric();
        if (StringUtils.isEmpty(metricStr)) {
            throw badRequest("Metric is required");
        }
        League.Code leagueCode = getValidLeagueCode(leagueCodeStr);
        League league = ebean.dataRepo().requiredLeagueByCode(leagueCode, tx);

        PickEmMetric metric = ebean.randomizerRepo()
                .findPickEmMetricByBrandLeagueAndMetric(brand, league, metricStr, tx)
                .orElseThrow(() -> badRequest("PickEmMetric for brand %s, league %s, and metric %s not found"
                        .formatted(brandName, leagueCodeStr, metricStr)));

        metric.setActive(request.getIsActive());
        // Override the isProtected flag to ensure changes are not ignored after restart
        metric.setProtected(true);

        int displayOrder = request.getDisplayOrder();
        if (displayOrder < 0) {
            throw badRequest("Display order cannot be negative");
        }

        metric.setDisplayOrder(displayOrder);
        ebean.save(metric);
        log.info("Updated PickEm metric: {}, league: {}, active: {}, displayOrder: {}, isProtected: {}",
                metric.getMetric(), league.getCode(), metric.isActive(), metric.getDisplayOrder(),
                metric.isProtected());
    }
}
