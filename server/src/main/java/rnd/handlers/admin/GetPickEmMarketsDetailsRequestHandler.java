package rnd.handlers.admin;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ReadOnlyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.Reason;
import io.ebean.Transaction;
import jakarta.inject.Inject;
import rnd.Mappers;
import rnd.RandomizerJpaManager;
import rnd.RandomizerServerProperties;
import rnd.api.v1.admin.TGetPickEmMarketsDetailsRequest;
import rnd.api.v1.admin.TGetPickEmMarketsDetailsResponse;
import rnd.handlers.AbstractRequestHandler;
import rnd.handlers.OffsetLimit;
import rnd.model.PlayerCard;
import rnd.model.data.BrandProp;
import rnd.model.data.Prop;
import rnd.model.main.Brand;
import rnd.services.Market;

@Service
public class GetPickEmMarketsDetailsRequestHandler
        extends AbstractRequestHandler<TGetPickEmMarketsDetailsRequest, TGetPickEmMarketsDetailsResponse.Builder>
        implements ReadOnlyHandler<TGetPickEmMarketsDetailsRequest, TGetPickEmMarketsDetailsResponse.Builder> {
    private final Market market;

    @Inject
    public GetPickEmMarketsDetailsRequestHandler(RandomizerServerProperties props, RandomizerJpaManager ebean, Market market) {
        super(props, ebean);
        this.market = market;
    }

    @Override
    public void apply(TransactionalRequest<TGetPickEmMarketsDetailsRequest, TGetPickEmMarketsDetailsResponse.Builder> cmd) throws Throwable {
        TGetPickEmMarketsDetailsRequest request = cmd.request();
        TGetPickEmMarketsDetailsResponse.Builder response = cmd.reply();
        PlayerCard playerProp;
        Brand brand;
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            brand = ebean.brandRepo().requiredBrand(request.getBrand(), tx);
        }
        String playerCode = request.getPlayer();
        String matchCode = request.getMatch();

        playerProp = market.query(brand, null, List.of(matchCode), List.of(playerCode), null,
                true, true, true, new OffsetLimit(0, Long.MAX_VALUE))
                .stream()
                .findFirst().orElseThrow(() -> EnhancedApplicationException.of(
                        "No player found for player '%s' and match '%s'".formatted(playerCode, matchCode),
                        Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST));

        response.setMatchInfo(Mappers.toMatchInfo(brand, playerProp.getMatch()));
        response.setPlayerInfo(Mappers.toGamePlayerInfo(brand, playerProp.getPlayer(), playerProp.getTeam()));
        response.setIsPickEmDisabled(!playerProp.getPlayer().isActive());
        for (PlayerCard.PlayerProp prop : playerProp.getProps()) {
            if (prop instanceof PlayerCard.MultilineProp mp && mp.hasBalanced()) {
                mp.getBalanced().ifPresent(b -> response.putMetricsEnabledStatus(prop.getMetricName(), b.isActive()));
            }
        }
    }
}
