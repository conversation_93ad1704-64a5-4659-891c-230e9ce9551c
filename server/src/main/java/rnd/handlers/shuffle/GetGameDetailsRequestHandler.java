package rnd.handlers.shuffle;

import static java.util.stream.Collectors.toList;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ReadOnlyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import api.v1.ApplicationException;
import api.v1.Code;
import io.ebean.Transaction;
import jakarta.inject.Inject;
import rnd.Mappers;
import rnd.RandomizerJpaManager;
import rnd.RandomizerServerProperties;
import rnd.api.v1.shuffle.TGetGameDetailsRequest;
import rnd.api.v1.shuffle.TGetGameDetailsResponse;
import rnd.api.v1.shuffle.TNoUpcomingGame;
import rnd.api.v1.shuffle.TPossibleLegOverview;
import rnd.api.v1.shuffle.TShuffleGameInfo;
import rnd.enums.VerticalType;
import rnd.handlers.AbstractRequestHandler;
import rnd.model.data.DataProvider;
import rnd.model.data.DataProviderAttributes;
import rnd.model.data.League;
import rnd.model.data.Match;
import rnd.model.data.Player;
import rnd.model.data.Sport;
import rnd.model.data.Team;
import rnd.model.main.Brand;
import rnd.model.main.GameCategory;
import rnd.model.main.shuffle.Game;
import rnd.model.main.shuffle.GameUrlSlug;
import rnd.model.main.shuffle.LegTemplate;
import rnd.model.main.shuffle.ShuffleGameTemplate;
import rnd.services.DataProviderResolver;
import rnd.services.MatchRefreshableCache;
import rnd.testutils.UrlSlugUtils;
import rnd.ticket.generation.services.TicketGenerationService;

@Service
public class GetGameDetailsRequestHandler
        extends AbstractRequestHandler<TGetGameDetailsRequest, TGetGameDetailsResponse.Builder>
        implements ReadOnlyHandler<TGetGameDetailsRequest, TGetGameDetailsResponse.Builder> {

    private static final List<String> RANGE_TYPES = List.of("Over", "Under");
    private final TicketGenerationService ticketGenerationService;
    private final DataProviderResolver dataProviderResolver;
    private final MatchRefreshableCache matchRefreshableCache;

    @Inject
    public GetGameDetailsRequestHandler(
            RandomizerServerProperties props,
            RandomizerJpaManager ebean,
            TicketGenerationService ticketGenerationService,
            DataProviderResolver dataProviderResolver,
            MatchRefreshableCache matchRefreshableCache) {
        super(props, ebean);
        this.ticketGenerationService = ticketGenerationService;
        this.dataProviderResolver = dataProviderResolver;
        this.matchRefreshableCache = matchRefreshableCache;
    }

    @Override
    public void apply(TransactionalRequest<TGetGameDetailsRequest, TGetGameDetailsResponse.Builder> cmd) throws Throwable {
        TGetGameDetailsRequest req = cmd.request();
        TGetGameDetailsResponse.Builder resp = cmd.reply();
        Instant now = cmd.timestamp().toInstant();

        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            List<Match> dayMatches = new LinkedList<>();
            Brand brand = getBrand(req.getBrand(), tx);
            Sport sport = getSport(req.getSportCode(), tx);
            League league = getLeague(req.getLeagueCode(), tx);
            DataProvider dataProvider = dataProviderResolver.findDataProvider(DataProviderAttributes.builder()
                    .brandName(brand.getName())
                    .verticalType(VerticalType.SHUFFLE)
                    .sportCode(sport.getCode())
                    .leagueCode(league.getCode())
                    .build(), tx);

            Optional<GameUrlSlug> playerOrTeamSlugOpt = ebean.randomizerRepo().findGameUrlSlug(req.getUrlSlug(), league, dataProvider, tx);
            boolean isFreeToPlayGame = UrlSlugUtils.UNLIMITED_PLAY.equals(req.getUrlSlug());
            boolean isMillionGame = UrlSlugUtils.MILLION_GAME.equals(req.getUrlSlug());

            List<Match> matches = List.of();
            AtomicReference<Team> team = new AtomicReference<>();
            AtomicReference<Player> player = new AtomicReference<>();
            GameCategory.Type gameCategoryType = null;
            TNoUpcomingGame tNoUpcomingGame = null;
            String targetCode = null;

            if (playerOrTeamSlugOpt.isEmpty()) {
                boolean isGameDay = UrlSlugUtils.GAME_DAY_PLAY_SLUG.equals(req.getUrlSlug());

                if (isGameDay || isFreeToPlayGame || isMillionGame) {
                    gameCategoryType = GameCategory.Type.DAY;
                    tNoUpcomingGame = Mappers.toTNoUpcomingGame(brand, league, LocalDate.now(ZoneOffset.UTC).toString());
                } else {
                    Optional<Pair<String, String>> homeAwayTeams = UrlSlugUtils.returnHomeAwayTeams(req.getUrlSlug());
                    if (homeAwayTeams.isPresent()) {
                        String homeTeamName = homeAwayTeams.get().getLeft();
                        String awayTeamName = homeAwayTeams.get().getRight();
                        Optional<GameUrlSlug> homeTeamUrlSlug = ebean.randomizerRepo().findGameUrlSlug(homeTeamName, league, dataProvider, tx);
                        Optional<GameUrlSlug> awayTeamUrlSlug = ebean.randomizerRepo().findGameUrlSlug(awayTeamName, league, dataProvider, tx);

                        if (homeTeamUrlSlug.isEmpty() || awayTeamUrlSlug.isEmpty()) {
                            throw ApplicationException.of("Next match not found by slug: %s", Code.ERR_NOT_FOUND, req.getUrlSlug());
                        }
                        gameCategoryType = GameCategory.Type.MATCH;
                        tNoUpcomingGame = Mappers.toTNoUpcomingGame(brand, homeTeamUrlSlug.get().getTeam(), awayTeamUrlSlug.get().getTeam());
                        matches = nextMatchesByTeams(now, league, dataProvider, tx, homeTeamUrlSlug.get().getTeam(), awayTeamUrlSlug.get().getTeam());
                    }
                }
            } else if (playerOrTeamSlugOpt.get().getTeam() != null) {
                team.set(playerOrTeamSlugOpt.get().getTeam());
                gameCategoryType = GameCategory.Type.TEAM;
                tNoUpcomingGame = Mappers.toTNoUpcomingGame(brand, team.get());
                matches = nextMatchesByTeams(now, league, dataProvider, tx, team.get());
                targetCode = team.get().getCode();
            } else if (playerOrTeamSlugOpt.get().getPlayer() != null) {
                gameCategoryType = GameCategory.Type.PLAYER;
                player.set(playerOrTeamSlugOpt.get().getPlayer());
                tNoUpcomingGame = Mappers.toTNoUpcomingGame(brand, player.get(), player.get().getTeam());
                matches = nextMatchesByTeams(now, league, dataProvider, tx, player.get().getTeam());
                targetCode = player.get().getCode();
            }

            if (!GameCategory.Type.DAY.equals(gameCategoryType)) {
                if (matches.isEmpty() && tNoUpcomingGame != null) {
                    resp.setNoUpcomingGame(tNoUpcomingGame);
                    return;
                }

                if (matches.isEmpty()) {
                    throw ApplicationException.of("Next match not found by slug: %s", Code.ERR_NOT_FOUND, req.getUrlSlug());
                }
            }

            GameCategory gameCategory = getSportCategory(brand, league, gameCategoryType, tx);
            Optional<ShuffleGameTemplate> shuffleGameTemplate = ebean.gamesRepo().activeShuffleGameTemplate(brand, league, gameCategory.getType(),
                    isFreeToPlayGame, Optional.of(isMillionGame), tx);
            if (shuffleGameTemplate.isEmpty()) {
                throw ApplicationException.of("Game Template not found by brand: %s, and gameCategory: %s",
                        Code.ERR_NOT_FOUND, brand.getName(), gameCategory.getType().getCode());
            }

            ShuffleGameTemplate template = shuffleGameTemplate.get();
            List<LegTemplate> legTemplates = brandRepo().cachedLegTemplatesByLeagueAndBrand(brand, league, tx);

            int i = 0;
            Match match;
            do {
                match = matches.isEmpty() ? null : matches.get(i++);
                Game game = Game.builder()
                        .at(now)
                        .category(gameCategory.getType())
                        .league(league)
                        .match(match)
                        .targetCode(targetCode)
                        .dataProvider(dataProvider)
                        .build();

                boolean enoughQuestionsExist = switch (gameCategory.getType()) {
                    case PLAYER -> !ticketGenerationService.availablePlayerGamesForMatch(template, game, legTemplates, p -> p.equals(player.get())).isEmpty();
                    case TEAM -> !ticketGenerationService.availableTeamGamesForMatch(template, game, legTemplates, t -> t.equals(team.get())).isEmpty();
                    case MATCH -> ticketGenerationService.availableMatchGame(template, game, legTemplates).isPresent();
                    case DAY -> {
                        SortedMap<LocalDate, List<Match>> dayMatchesMap = matchRefreshableCache.getUpcomingMatches(now, league, dataProvider, tx)
                                .stream()
                                .collect(Collectors.groupingBy(m -> LocalDate.parse(m.getApiPlayAt(), Match.API_DATE_FMT), TreeMap::new, toList()));

                        Optional<LocalDate> gameDay = ticketGenerationService.nextAvailableDayGame(dayMatchesMap, template, game, legTemplates);
                        if (gameDay.isPresent()) {
                            dayMatches = dayMatchesMap.get(gameDay.get());
                            yield true;
                        }
                        yield false;
                    }
                };

                if (!enoughQuestionsExist) {
                    resp.setNoUpcomingGame(tNoUpcomingGame);
                    continue;
                }

                Collection<TPossibleLegOverview> possibleLegsOverview = switch (gameCategoryType) {
                    case PLAYER -> legTemplates.stream()
                            .filter(q -> q.getGameCategory().getType().equals(GameCategory.Type.PLAYER))
                            .map(q -> Mappers.toTPossibleLegOverview(q, RANGE_TYPES).build())
                            .collect(Collectors.toSet());
                    case TEAM -> legTemplates.stream()
                            .filter(q -> q.getGameCategory().getType().equals(GameCategory.Type.PLAYER)
                                    || q.getGameCategory().getType().equals(GameCategory.Type.TEAM))
                            .map(q -> Mappers.toTPossibleLegOverview(q, RANGE_TYPES).build())
                            .collect(Collectors.toSet());
                    case MATCH, DAY -> legTemplates.stream()
                            .filter(q -> q.getGameCategory().getType().equals(GameCategory.Type.PLAYER)
                                    || q.getGameCategory().getType().equals(GameCategory.Type.TEAM)
                                    || q.getGameCategory().getType().equals(GameCategory.Type.MATCH))
                            .map(q -> Mappers.toTPossibleLegOverview(q, RANGE_TYPES).build())
                            .collect(Collectors.toSet());
                };

                TShuffleGameInfo.Builder tShuffleGameInfo = Mappers.toTShuffleGameInfo(now, template, match, team.get(), player.get());
                tShuffleGameInfo.setUrlSlug(req.getUrlSlug());
                if (GameCategory.Type.DAY.equals(gameCategoryType)) {
                    tShuffleGameInfo.addAllDayMatchesInfo(dayMatches.stream().map(m -> Mappers.toMatchInfo(brand, m).build()).toList());
                    tShuffleGameInfo.setGameDate(dayMatches.get(0).getApiPlayAt());
                    tShuffleGameInfo.setEventStartsAt(dayMatches.get(0).getPlayAt().getEpochSecond());
                } else {
                    tShuffleGameInfo.setGameDate(match.getApiPlayAt());
                }
                resp.setMaxNumberOfTickets(template.getMaxNumberOfTickets());
                resp.addAllTicketEntryLimits(Mappers.toTTicketEntryLimits(template));
                resp.setWinTable(Mappers.toTWinTableForGame(
                        template.getRandomMultiplierGroup(),
                        template.getFlexCoefficientGroup(),
                        template.getMaxLegs(),
                        template.getFlexCoefficientGroup() != null));
                resp.addAllPossibleLegsOverview(possibleLegsOverview);
                resp.setShuffleGameInfo(tShuffleGameInfo);
                return; // exit loop on first non-empty match
            } while (match != null && i < matches.size());
        }
    }

    private List<Match> nextMatchesByTeams(Instant now, League league, DataProvider dataProvider, Transaction tx, Team... teams) {
        return matchRefreshableCache.getUpcomingMatches(now, league, dataProvider, tx).stream()
                .filter(m -> {
                    if (teams.length == 1) {
                        return m.getHome().equals(teams[0]) || m.getAway().equals(teams[0]);
                    } else if (teams.length == 2) {
                        return m.getHome().equals(teams[0]) && m.getAway().equals(teams[1]);
                    } else {
                        throw new IllegalStateException();
                    }
                }).toList();
    }

    private GameCategory getSportCategory(Brand brand, League league, GameCategory.Type gameCategoryType, Transaction tx) throws ApplicationException {
        Optional<GameCategory> categoryOpt = ebean.randomizerRepo().activeCategoryByBrandVerticalLeagueAndType(brand, VerticalType.SHUFFLE, league,
                gameCategoryType, tx);
        if (categoryOpt.isEmpty()) {
            throw ApplicationException.of("GameCategory not found: league - %s, gameCategory - %s", Code.ERR_NOT_FOUND,
                    league.getCode(), gameCategoryType.getCode());
        }
        return categoryOpt.get();
    }

}
