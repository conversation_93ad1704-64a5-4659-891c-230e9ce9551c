package rnd.handlers.pickem;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ReadOnlyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import io.ebean.Transaction;
import jakarta.inject.Inject;
import rnd.Mappers;
import rnd.RandomizerIdentityManager;
import rnd.RandomizerJpaManager;
import rnd.RandomizerServerProperties;
import rnd.api.v1.pickem.TGetPickEmPlayerStatsRequest;
import rnd.api.v1.pickem.TGetPickEmPlayerStatsResponse;
import rnd.api.v1.pickem.THistoricalEntry;
import rnd.api.v1.pickem.THistoricalMetric;
import rnd.handlers.AbstractAuthAwareRequestHandler;
import rnd.model.data.League;
import rnd.model.data.Match;
import rnd.model.data.Player;
import rnd.model.data.PlayerMatchStats;
import rnd.model.main.Account;
import rnd.model.main.Brand;
import rnd.model.main.FavouritePlayers;
import rnd.model.main.pickem.PickEmMetric;

/**
 * Resolves brand, match & player, then for each lobby‑configured metric
 * computes a season average (defaulting to zero) plus up to 5 games of history,
 * and builds the TGetPickEmPlayerStatsResponse.
 */
@Service
public class GetPickEmPlayerStatsHandler
        extends AbstractAuthAwareRequestHandler<TGetPickEmPlayerStatsRequest, TGetPickEmPlayerStatsResponse.Builder>
        implements ReadOnlyHandler<TGetPickEmPlayerStatsRequest, TGetPickEmPlayerStatsResponse.Builder> {

    @Inject
    protected GetPickEmPlayerStatsHandler(
            RandomizerServerProperties props,
            RandomizerJpaManager ebean,
            RandomizerIdentityManager sessionManager) {
        super(props, ebean, sessionManager);
    }

    @Override
    public void apply(TransactionalRequest<TGetPickEmPlayerStatsRequest, TGetPickEmPlayerStatsResponse.Builder> cmd)
            throws Throwable {

        var req = cmd.request();
        Account account = getOrCreateAccount(req.getIdentity(), cmd);
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            Brand brand = getBrand(req.getBrand(), tx);
            Player player = dataRepo().requiredPlayerByCode(req.getPlayer(), tx);
            League league = player.getLeague();
            Match match = null;
            if (req.hasMatch()) {
                match = dataRepo().requiredMatchByCode(req.getMatch(), tx);
            }
            FavouritePlayers favouritePlayers = Optional.ofNullable(account)
                    .map(a -> ebean.randomizerRepo().favouritePlayers(a.getId(), tx))
                    .orElse(null);

            // 1) load all metrics (to preserve displayOrder)
            List<PickEmMetric> allMetrics = dataRepo()
                    .activePickEmMetrics(brand, List.of(league), tx)
                    .stream()
                    .sorted(Comparator.comparingInt(PickEmMetric::getDisplayOrder))
                    .toList();

            // 2) find which metrics actually have a line for this match+player
            Set<String> liveMetrics;
            if (match != null) {
                liveMetrics = dataRepo().findActivePickEmMetricsFor(brand, match, player, tx);
            } else {
                // If match is empty, use all active metrics for the player's league
                liveMetrics = allMetrics.stream().map(PickEmMetric::getMetric).collect(java.util.stream.Collectors.toSet());
            }

            // 3) intersect & keep order
            List<PickEmMetric> metricsToShow = allMetrics.stream()
                    .filter(pm -> liveMetrics.contains(pm.getMetric()))
                    .toList();

            // 4) last 5 games, desc by playAt
            List<PlayerMatchStats> lastMatches = dataRepo().playerLastMatches(
                    player,
                    5,
                    Optional.empty(), // no season filter
                    tx);

            // 5) build response
            var resp = cmd.reply();
            resp.setLeague(Mappers.toLeague(brand, league).build());
            if (match != null) {
                resp.setMatchInfo(Mappers.toMatchInfo(brand, match).build());
            }
            resp.setPlayerInfo(
                    (match == null ? Mappers.toGamePlayerInfo(brand, player)
                            : Mappers.toGamePlayerInfo(brand, player, match.teamByPlayerFromBrandProps(player)))
                            .setFavorite(Optional.ofNullable(favouritePlayers)
                                    .map(fp -> fp.getPlayers().contains(player))
                                    .orElse(false))
                            .build());

            // 6) for each “live” metric, one‐pass history + average
            for (PickEmMetric pm : metricsToShow) {
                String metric = pm.getMetric();
                var mb = THistoricalMetric.newBuilder().setMetric(metric);

                BigDecimal sum = BigDecimal.ZERO;
                int count = 0;

                // NOTE: we want oldest -> newest, so iterate reverse
                for (int i = lastMatches.size() - 1; i >= 0; i--) {
                    PlayerMatchStats stats = lastMatches.get(i);
                    var playAt = stats.getMatch().getPlayAt();
                    if (playAt == null) continue;

                    var maybeVal = stats.getBigDecimal(metric);
                    if (maybeVal.isPresent()) {
                        BigDecimal v = maybeVal.get();

                        var eb = THistoricalEntry.newBuilder()
                                .setValue(v.stripTrailingZeros().toPlainString())
                                .setOpponentAbbr(stats.getMatch().getOpponentAbbr(player, brand))
                                .setEventStartedAt(playAt.getEpochSecond());

                        if (stats.getMatch().isOfTeamSport()) {
                            String venue = stats.getMatch().getHome().equals(player.getTeam())
                                    ? "home"
                                    : "away";
                            eb.setVenue(venue);
                        }

                        mb.addHistory(eb.build());
                        sum = sum.add(v);
                        count++;
                    }
                }

                String avg = count > 0
                        ? sum
                                .divide(BigDecimal.valueOf(count), 1, RoundingMode.HALF_UP)
                                .stripTrailingZeros()
                                .toPlainString()
                        : "0";

                mb.setAverageValue(avg);
                resp.addHistoricalMetrics(mb.build());
            }
        }
    }
}
