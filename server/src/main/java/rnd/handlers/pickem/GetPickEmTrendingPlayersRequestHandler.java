package rnd.handlers.pickem;

import static org.apache.commons.lang3.StringUtils.isBlank;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ReadOnlyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import io.ebean.Transaction;
import jakarta.inject.Inject;
import rnd.Mappers;
import rnd.RandomizerIdentityManager;
import rnd.RandomizerJpaManager;
import rnd.RandomizerServerProperties;
import rnd.api.v1.pickem.TGetPickEmTrendingPlayersRequest;
import rnd.api.v1.pickem.TGetPickEmTrendingPlayersResponse;
import rnd.api.v1.pickem.TTrendingPlayerPick;
import rnd.handlers.AbstractAuthAwareRequestHandler;
import rnd.handlers.OffsetLimit;
import rnd.handlers.OffsetLimitValidator;
import rnd.model.TrendingPlayerProp;
import rnd.model.main.Account;
import rnd.model.main.Brand;
import rnd.model.main.FavouritePlayers;
import rnd.services.Market;

@Service
public class GetPickEmTrendingPlayersRequestHandler
        extends AbstractAuthAwareRequestHandler<TGetPickEmTrendingPlayersRequest, TGetPickEmTrendingPlayersResponse.Builder>
        implements ReadOnlyHandler<TGetPickEmTrendingPlayersRequest, TGetPickEmTrendingPlayersResponse.Builder> {

    private final OffsetLimitValidator offsetLimitValidator;
    private final Market market;

    @Inject
    public GetPickEmTrendingPlayersRequestHandler(
            RandomizerServerProperties props,
            RandomizerJpaManager ebean,
            RandomizerIdentityManager sessionManager,
            Market market) {
        super(props, ebean, sessionManager);
        this.offsetLimitValidator = new OffsetLimitValidator(props, props.PAGINATION_MAX_LIMIT_GET_TRENDING_PLAYERS);
        this.market = market;
    }

    @Override
    public void apply(TransactionalRequest<TGetPickEmTrendingPlayersRequest, TGetPickEmTrendingPlayersResponse.Builder> cmd) throws Throwable {

        TGetPickEmTrendingPlayersRequest req = cmd.request();
        TGetPickEmTrendingPlayersResponse.Builder resp = cmd.reply();

        OffsetLimit offsetLimit = offsetLimitValidator.validateAndGet(req.getOffset(), req.getLimit(), false);
        String leagueFilter = StringUtils.trimToNull(req.getLeague());

        Account account = getOrCreateAccount(req.getIdentity(), cmd);
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            Brand brand = getBrand(req.getBrand(), tx);
            FavouritePlayers favouritePlayers = Optional.ofNullable(account)
                    .map(a -> ebean.randomizerRepo().favouritePlayers(a.getId(), tx))
                    .orElse(null);

            AtomicInteger rank = new AtomicInteger(offsetLimit.getOffsetAsInt() + 1);
            var trendingPlayers = market.requiredSnapshot(brand, tx)
                    .getTrendingPlayersSorted()
                    .stream()
                    .filter(tp -> (isBlank(leagueFilter) ||
                            tp.playerProp().getMatch().getLeague().getCode().toString().equalsIgnoreCase(leagueFilter)) &&
                            (req.getIncludeTbdMatches() || tp.playerProp().getMatch().getPlayAt() != null));

            if (req.hasUniquePlayers() && req.getUniquePlayers()) {
                trendingPlayers = trendingPlayers
                        .collect(Collectors.toMap(
                                tp -> tp.playerProp().getPlayer().getId(),
                                tp -> tp,
                                (existing, replacement) -> existing.pickCount() >= replacement.pickCount() ? existing : replacement))
                        .values()
                        .stream();
            }

            trendingPlayers
                    .skip(offsetLimit.getOffsetAsInt())
                    .limit(offsetLimit.getLimitAsInt())
                    .forEach(tp -> resp.addData(buildProtoPick(tp, rank.getAndIncrement(), favouritePlayers)));
        }
    }

    private TTrendingPlayerPick buildProtoPick(TrendingPlayerProp tp, int rank, FavouritePlayers favouritePlayers) {
        return TTrendingPlayerPick.newBuilder()
                .setRank(rank) // if alt lines needed for the trending players, change the acceptAlternative below
                .setPlayerOption(Mappers.toPlayerOptions(tp.playerProp(), false, favouritePlayers))
                .setPickCount(tp.pickCount())
                .build();
    }
}
