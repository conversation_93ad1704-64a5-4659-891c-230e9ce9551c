package rnd.handlers.pickem;

import java.util.Comparator;
import java.util.HashSet;
import java.util.Set;

import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ReadOnlyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import io.ebean.Transaction;
import jakarta.inject.Inject;
import rnd.Mappers;
import rnd.RandomizerIdentityManager;
import rnd.RandomizerJpaManager;
import rnd.RandomizerServerProperties;
import rnd.api.v1.pickem.TGetUserFavouritePlayersRequest;
import rnd.api.v1.pickem.TGetUserFavouritePlayersResponse;
import rnd.api.v1.pickem.TPlayerOptions;
import rnd.handlers.AbstractAuthAwareRequestHandler;
import rnd.handlers.OffsetLimit;
import rnd.handlers.OffsetLimitValidator;
import rnd.model.data.Player;
import rnd.model.main.Account;
import rnd.model.main.Brand;
import rnd.model.main.FavouritePlayers;
import rnd.services.LobbyQuery;
import rnd.services.Market;

@Service
public class GetUserFavouritePlayersRequestHandler
        extends AbstractAuthAwareRequestHandler<TGetUserFavouritePlayersRequest, TGetUserFavouritePlayersResponse.Builder>
        implements ReadOnlyHandler<TGetUserFavouritePlayersRequest, TGetUserFavouritePlayersResponse.Builder> {
    private final OffsetLimitValidator offsetLimitValidator;
    private final Market market;

    @Inject
    public GetUserFavouritePlayersRequestHandler(
            RandomizerServerProperties props,
            RandomizerJpaManager ebean,
            RandomizerIdentityManager sessionManager,
            Market market) {
        super(props, ebean, sessionManager);
        this.market = market;
        this.offsetLimitValidator = new OffsetLimitValidator(props, () -> 50L);
    }

    @Override
    public void apply(TransactionalRequest<TGetUserFavouritePlayersRequest, TGetUserFavouritePlayersResponse.Builder> cmd) throws Throwable {
        TGetUserFavouritePlayersRequest req = cmd.request();
        TGetUserFavouritePlayersResponse.Builder reply = cmd.reply();
        Account account = getOrCreateAccount(req.getIdentity(), cmd);
        Brand brand = account.getBrand();
        OffsetLimit offsetLimit = offsetLimitValidator.validateAndGet(req.getOffset(), req.getLimit(), false);

        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            FavouritePlayers favouritePlayers = ebean.randomizerRepo().favouritePlayers(account.getId(), tx);
            if (favouritePlayers == null) return;
            Set<Player> withMatches = new HashSet<>(Math.max(req.getLimit(), 12));
            market.requiredSnapshot(brand, tx).query(new LobbyQuery.Top()).stream()
                    .filter(p -> !p.getProps().isEmpty())
                    .filter(p -> favouritePlayers.getPlayers().contains(p.getPlayer()))
                    .sorted(Comparator.comparing(p -> p.getMatch().getPlayAt(), Comparator.nullsLast(Comparator.naturalOrder())))
                    .peek(p -> withMatches.add(p.getPlayer()))
                    .map(p -> market.processDiscounted(p, account.getId(), true, tx))
                    .skip(offsetLimit.getOffset())
                    .limit(offsetLimit.getLimit())
                    .forEach(p -> reply.addOptions(
                            Mappers.toPickEmOptions(p, null, favouritePlayers, req.getAcceptAlternative())));

            if (!reply.getOptionsList().isEmpty() && reply.getOptionsCount() == req.getLimit()) return; // the page is full
            Set<Player> woMatches = new HashSet<>(favouritePlayers.getPlayers());
            woMatches.removeAll(withMatches);
            if (woMatches.isEmpty()) return;

            woMatches.stream()
                    .sorted(Comparator.comparing(Player::getId))
                    .skip(!reply.getOptionsList().isEmpty() ? 0 : offsetLimit.getOffset() - withMatches.size())
                    .limit(offsetLimit.getLimit() - reply.getOptionsCount())
                    .forEach(p -> reply.addOptions(TPlayerOptions.newBuilder()
                            .setPlayerInfo(Mappers.toGamePlayerInfo(brand, p, p.getTeam()).setFavorite(true))
                            .build()));
        }
    }
}
