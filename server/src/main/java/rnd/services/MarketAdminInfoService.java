package rnd.services;

import java.util.List;
import java.util.Set;

import io.ebean.Transaction;
import rnd.RandomizerJpaManager;
import rnd.model.PlayerCard;
import rnd.model.data.League;
import rnd.model.main.Brand;
import rnd.model.main.MatchPlayerTeam;

public class MarketAdminInfoService {
    private final PickEmConfig pickEmConfig;
    private final RandomizerJpaManager ebean;

    public MarketAdminInfoService(RandomizerJpaManager ebean, PickEmConfig pickEmConfig) {
        this.pickEmConfig = pickEmConfig;
        this.ebean = ebean;
    }

    public MarketAdminInfo getAdminInfo(Brand brand, List<PlayerCard> playerCards) throws Throwable {
        Set<MatchPlayerTeam> matchesPlayers = PlayerCard.disctinctMatchesPlayers(playerCards);
        Set<League> allLeagues = MatchPlayerTeam.distinctLeagues(matchesPlayers);

        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            return new MarketAdminInfo(
                    ebean.randomizerRepo().getTradingMarketStats(matchesPlayers, brand, tx),
                    ebean.randomizerRepo().findLeaguesTradingAlertConfig(allLeagues, tx),
                    pickEmConfig.altTradingThresholdsCoefficient());
        }
    }
}
