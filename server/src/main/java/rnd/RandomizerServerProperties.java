package rnd;

import static rnd.enums.Currency.GC;
import static rnd.enums.Currency.SC;
import static rnd.enums.Currency.USD;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Collections;
import java.util.List;

import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.DynamicPropertyFactory;
import com.turbospaces.cfg.ScopedProperty;

import rnd.enums.Currency;
import rnd.enums.VerticalType;

public class RandomizerServerProperties extends RandomizerModelProperties {

    public RandomizerServerProperties(DynamicPropertyFactory pf) {
        super(pf);

        REQUEST_REPLY_TIMEOUT = pf.get("request-reply.timeout", int.class).orElse(60);

        READONLY_DEV_MODE_ENABLED = pf.get("readonly.dev.mode.enabled", boolean.class).orElse(false);

        JACKPOT_CONTRIBUTION_ENABLED = pf.get("jackpot.contribution.enabled", Boolean.class).orElse(true);
        JACKPOT_GC_CONTRIBUTION_MULTIPLIER = pf.get("jackpot.gc.contribution.multiplier", BigDecimal.class).orElse(new BigDecimal("0.01"));
        JACKPOT_SC_CONTRIBUTION_AMOUNT = pf.get("jackpot.sc.contribution.amount", BigDecimal.class).orElse(new BigDecimal("0.1"));

        BRANDS_AUTOCONFIGURATION_LIST = pf.listOfStrings("brands.autoconfiguration.list").orElse(List.of("sportsmillions"));
        BRANDS_AUTOCONFIGURATION_ENABLED = pf.get("brands.autoconfiguration.enabled", boolean.class).orElse(true);

        MATCH_CACHE_ENABLED = pf.get("match.cache.enabled", boolean.class).orElse(true);
        MATCH_CACHE_REFRESH_FREQ = pf.get("match.cache.refresh-freq", Duration.class).orElse(Duration.ofSeconds(30));

        LIFECYCLE_SHUFFLE_RUN_ON_START = pf.get("lifecycle.shuffle.run-on-start", Boolean.class).orElse(true);
        LIFECYCLE_SHUFFLE_ENABLED = pf.get("lifecycle.shuffle.enabled", boolean.class).orElse(true);
        LIFECYCLE_PICK_EM_RUN_ON_START = pf.get("lifecycle.pickem.run-on-start", Boolean.class).orElse(true);
        LIFECYCLE_PICK_EM_ENABLED = pf.get("lifecycle.pickem.enabled", boolean.class).orElse(true);
        LIFECYCLE_FREQ_SEC = pf.get("lifecycle.freq-interval", int.class).orElse(60);
        LIFECYCLE_SPORTS_EVENTS_BATCH_SIZE = pf.get("lifecycle.sports-events-batch-size", int.class).orElse(500); // sports events per batch
        LIFECYCLE_DESIRED_TRANSACTION_DURATION_MS = pf.get("lifecycle.desired-transaction-duration-ms", int.class).orElse(2000);
        LIFECYCLE_DELAY_BETWEEN_BATCHES_MS = pf.get("lifecycle.delay-between-batches-ms", int.class).orElse(0);
        LIFECYCLE_BATCH_SIZE = pf.get("lifecycle.batch-size", int.class).orElse(700); // legs per batch

        SETTLE_REQUESTS_RUN_ON_START = pf.get("settle-requests.run-on-start", Boolean.class).orElse(true);
        SETTLE_REQUESTS_ENABLED = pf.get("settle-requests.enabled", boolean.class).orElse(true);
        SETTLE_REQUESTS_FREQ_SEC = pf.get("settle-requests.freq-interval", int.class).orElse(15);
        SETTLE_REQUESTS_BATCH_SIZE = pf.get("settle-requests.batch-size", int.class).orElse(100);

        MILLION_GAMES_IOS_MIN_SUPPORTED_VERSION = pf.get("million-games.ios.min-supported-version", String.class).orElse("1.0.3");
        MILLION_GAMES_ANDROID_MIN_SUPPORTED_VERSION = pf.get("million-games.android.min-supported-version", String.class).orElse("1.1");

        BLOOMREACH_RUN_ON_START = pf.get("bloomreach.run-on-start", Boolean.class).orElse(true);
        BLOOMREACH_ENABLED = pf.get("bloomreach.enabled", boolean.class).orElse(true);
        BLOOMREACH_FREQ_SEC = pf.get("bloomreach.freq-interval", int.class).orElse(30);

        PICKEM_TRADING_ALERTS_JOB_ENABLED = pf.get("alerts.trading.pickem.enabled", Boolean.class).orElse(true);
        PICKEM_TRADING_ALERTS_JOB_RUN_ON_START = pf.get("alerts.trading.pickem.run-on-start", Boolean.class).orElse(false);
        PICKEM_TRADING_ALERTS_JOB_FREQ_SEC = pf.get("alerts.trading.pickem.freq-interval", int.class).orElse(60);
        PICKEM_TRADING_ALERTS_SAVE_STATS_ENABLED = pf.get("alerts.trading.pickem.save.stats.enabled", Boolean.class).orElse(true);
        PICKEM_TRADING_ALERTS_SEND_NOTIFICATIONS_ENABLED = pf.get("alerts.trading.pickem.send.notifications.enabled", Boolean.class).orElse(true);
        PICKEM_TRADING_ALERTS_MARKET_SUSPENSION_ENABLED = pf.get("alerts.trading.pickem.market.suspension.enabled", Boolean.class).orElse(true);

        SHUFFLE_LOCATION_POLICY_ENABLED = pf.get("shuffle.location-policy.enabled", Boolean.class).orElse(true);
        PICKEM_LOCATION_POLICY_ENABLED = pf.get("pickem.location-policy.enabled", Boolean.class).orElse(true);

        ACCOUNT_FILL_SIGNUP_INFO_ENABLED = pf.get("account.fill-signup-info.enabled", Boolean.class).orElse(true);
        ACCOUNT_FILL_FULL_NAME_ENABLED = pf.get("account.fill-full-name.enabled", Boolean.class).orElse(true);
        ACCOUNT_DOMAIN_WHITELIST = pf.getList("account.domain-whitelist", String.class).orElse(List.of());
        ACCOUNT_GET_ROUTING_INFO_BATCH_SIZE = pf.get("account.get-routing-info.batch-size", int.class).orElse(10);
        ACCOUNT_GET_POLICIES_BATCH_SIZE = pf.get("account.get-policies.batch-size", int.class).orElse(10);
        ACCOUNT_DEFAULT_CURRENCY_POLICIES_ENABLED = pf.get("account.default-currency-policies.enabled", boolean.class).orElse(false);

        DEFAULT_LOCATION_COUNTRY = pf.get("default.location.country", String.class).orElse("US");

        WEBSOCKET_QUEUE_SIZE = pf.get("websocket.queue-size", int.class).orElse(10_000);

        PAGINATION_MAX_LIMIT_VALIDATION_ENABLED = pf.get("pagination.max-limit.validation.enabled", boolean.class).orElse(true);

        PAGINATION_MAX_LIMIT_GET_SHUFFLE_GAMES = pf.get("pagination.max-limit.get-shuffle-games", long.class).orElse(100L);
        PAGINATION_MAX_LIMIT_GET_USER_SHUFFLE_TICKETS = pf.get("pagination.max-limit.get-user-shuffle-tickets", long.class).orElse(100L);
        PAGINATION_MAX_LIMIT_GET_PICKEM_MATCHES = pf.get("pagination.max-limit.get-pickem-matches", long.class).orElse(100L);
        PAGINATION_MAX_LIMIT_GET_PICKEM_OPTIONS = pf.get("pagination.max-limit.get-pickem-options", long.class).orElse(100L);
        PAGINATION_MAX_LIMIT_GET_USER_PICKEM_ENTRIES = pf.get("pagination.max-limit.get-user-pickem-entries", long.class).orElse(100L);
        PAGINATION_MAX_LIMIT_GET_USER_REWARDS = pf.get("pagination.max-limit.get-user-rewards", long.class).orElse(100L);
        PAGINATION_MAX_LIMIT_GET_PICKEM_PACKS = pf.get("pagination.max-limit.get-pickem-packs", long.class).orElse(30L);
        PAGINATION_MAX_LIMIT_GET_DL = pf.get("pagination.max-limit.get-dl", long.class).orElse(30L);
        PAGINATION_MAX_LIMIT_GET_TRENDING_PLAYERS = pf.get("pagination.max-limit.get-trending-players", long.class).orElse(100L);

        EXPIRE_USER_REWARDS_BATCH_SIZE = pf.get("expire.user-rewards.batch-size", int.class).orElse(500);
        EXPIRE_USER_REWARDS_RUN_ON_START = pf.get("expire.user-rewards.run-on-start", boolean.class).orElse(true);
        EXPIRE_USER_REWARDS_ENABLED = pf.get("expire.user-rewards.enabled", boolean.class).orElse(true);
        EXPIRE_USER_REWARDS_FREQ_SEC = pf.get("expire.user-rewards.freq-interval", int.class).orElse(3600);

        REWARDS_ISSUES_CURRENCIES_ALLOWED = pf.getList("rewards.issues-currencies.allowed", Currency.class).orElse(List.of(USD));

        TICKET_SORT_USE_INDEX = pf.get("ticket.sort.use.index", Boolean.class).orElse(true);
        GAMES_CACHE_TTL = pf.get("games.cache.ttl", int.class).orElse(10);

        LOGO_ALLOWED_FORMATS = pf.getList("logo.allowed-formats", String.class).orElse(List.of(".png", ".svg", ".jpeg", ".jpg", ".webp"));
        JERSEY_ALLOWED_FORMATS = pf.getList("jersey.allowed-formats", String.class).orElse(List.of(".png"));

        RANDOMIZER_ALERTS_SLACK_CHANNEL = pf.get("randomizer.alerts.slack.channel", String.class).orElse("");
        PICK_EM_PACK_ALERTS_SLACK_CHANNEL = pf.get("randomizer.alerts.packs.slack.channel", String.class).orElse("");

        ENTER_TICKET_BUNDLE_AUTO_ROUND_AMOUNT_ENABLED = pf.get("enter-ticket-bundle.auto-round-amount.enabled", Boolean.class).orElse(false);

        EVENTS_SINK_ENABLED = pf.get("events-sink.enabled", boolean.class).orElse(true);
        EVENTS_SINK_RETRY_ENABLED = pf.get("events-sink.retry.enabled", boolean.class).orElse(true);

        PICKEM_MAX_AMOUNT_OF_SAME_PLAYER_IN_ENTRY_ENABLED = pf.get("pickem.max-amount-of-same-player-in-entry.enabled", boolean.class).orElse(true);
        PICKEM_MAX_AMOUNT_OF_SAME_PLAYER_IN_ENTRY = pf.get("pickem.max-amount-of-same-player-in-entry", int.class).orElse(1);
        PICKEM_MIN_AMOUNT_OF_DIFFERENT_TEAMS_IN_ENTRY = pf.get("pickem.min-amount-of-different-teams-in-entry", int.class).orElse(2);
        MAX_PLAYERS_MODE = pf.getScoped("pickem.max-players-mode", String.class, "PER_TEAM");
        PICKEM_MAX_MULTIPLE_PLAYERS_FROM_SAME_TEAM = pf.getScoped("pickem.max-multiple-players-from-same-team", int.class, 1);
        PICKEM_MAX_MULTIPLE_PLAYERS_FROM_SAME_MATCH = pf.getScoped("pickem.max-multiple-players-from-same-match", int.class, 2);
        PICKEM_MIN_AMOUNT_OF_DIFFERENT_PLAYERS_IN_ENTRY = pf.get("pickem.min-amount-of-different-players-in-entry", int.class).orElse(2);
        PICKEM_CORRECTION_PERCENTAGE_APPLIED_BELOW = pf.get("pickem.correction-precentage-applied-below", int.class).orElse(98);
        PICKEM_MAX_LINE_PROB_PERCENTAGE = pf.get("pickem.max-line-precentage", int.class).orElse(53);
        PICKEM_PACKS_SWISH_BET_CHECK_FREQUENCY_MINS = pf.get("pickem.packs.swish-bet-check-frequency-mins", int.class).orElse(4);
        PICKEM_EXTRA_BET_ERRORS_RESULTING_UNACCEPTABLE_PARLAY = pf.listOfStrings("pickem.extra-bet-errors-resulting-unacceptable-parlay")
                .orElse(Collections.emptyList());
        PICKEM_DEFAULT_MAX_DAILY_LIMIT = pf.get("pickem.default-max-daily-limit", BigDecimal.class).orElse(null);
        PICKEM_DEFAULT_MAX_WEEKLY_LIMIT = pf.get("pickem.default-max-weekly-limit", BigDecimal.class).orElse(null);
        PICKEM_LIMITS_VALIDATION_ENABLED = pf.get("pickem.limit-validation.enabled", Boolean.class).orElse(true);
        PICKEM_OPTIONS_SORTING_BATCH_SIZE = pf.get("pickem.options-sorting.batch-size", int.class).orElse(2);
        PICKEM_OPTIONS_SORTING_ENABLED = pf.get("pickem.options-sorting.enabled", boolean.class).orElse(true);
        PICKEM_LOBBY_LEAGUE_LIMIT = pf.get("pickem.lobby.league-limit", int.class).orElse(10);
        PICKEM_PACK_MAX_BOOST_MULTIPLIER = pf.get("pickem.pack-max-boost-multiplier", int.class).orElse(5);
        PICKEM_MAX_CONJUNCTED_MULTIPLIER = pf.get("pickem.max-conjuncted-multiplier", BigDecimal.class).orElse(new BigDecimal("1500"));
        PICKEM_SUBMIT_ENTRY_ENABLED = pf.get("pickem.submit-entry.enabled", Boolean.class).orElse(true);

        VERTICAL_DETAILS_CACH_EXPIRED_SEC = pf.get("vertical-details.cache.expired-sec", int.class).orElse(60);
        VERTICAL_DETAILS_CACHE_SIZE = pf.get("vertical-details.cache.size", int.class).orElse(10);
        RANDOMIZER_VERTICALS_ALLOWED = pf.getList("randomizer.verticals.allowed", VerticalType.class)
                .orElse(List.of(VerticalType.PICKEM));

        MARKET_MANAGER_RUN_ON_START = pf.get("market-manager.run-on-start", Boolean.class).orElse(true);
        MARKET_MANAGER_ENABLED = pf.get("market-manager.enabled", boolean.class).orElse(true);
        MARKET_MANAGER_FREQ_SEC = pf.get("market-manager.freq-interval", int.class).orElse(5);
        PICKEM_MARKET_ENABLED = pf.get("pickem.market.enabled", boolean.class).orElse(true);
        MARKET_PROP_EXPIRATION_SEC = pf.get("market.prop-expiration-sec", int.class).orElse(1);
        MARKET_GENERAL_EXPIRATION_SEC = pf.get("market.general-expiration-sec", int.class).orElse(30);
        MARKET_CACHE_BT_ENABLED = pf.get("market.cache.bt-enabled", boolean.class).orElse(true);
        MARKET_CACHE_MAX_ACCEPTABLE_REFRESH_TIME = pf.get("market.cache.max-acceptable-refresh-time", Duration.class).orElse(Duration.ofSeconds(5));
        PICKEM_ALT_TRADING_THRESHOLDS_COEFFICIENT = pf.get("pickem.alt-trading-thresholds-coefficient", BigDecimal.class)
                .orElse(new BigDecimal("0.5"));
        PICKEM_PROB_AWARE = pf.get("pickem.prob-aware", boolean.class).orElse(false);
        OPTIC_ODDS_MARKETS_OUTDATED_AFTER = pf.get("markets.outdated-after.optic_odds_v3", Duration.class).orElse(Duration.ofMinutes(3));
        SWISH_MARKETS_OUTDATED_AFTER = pf.get("markets.outdated-after.swish_analytics", Duration.class).orElse(Duration.ofMinutes(3));
        SEGMENT_SIZE_EVERY_NTH_ACCOUNT = pf.get("segment-size.every-nth-account", int.class).orElse(5);
        MATCH_START_TIME_TBD_ALLOWED = pf.getScoped("match.start-time-tbd-allowed", Boolean.class, false);

        ALTERNATIVE_TRADING_MARKET_STATS_ENABLED = pf.get("alternative.trading_market_stats.enabled", Boolean.class).orElse(false);
        ALTERNATIVE_TRADING_THRESHOLDS_COEFFICIENT = pf.get("alternative.trading.thresholds.coefficient", BigDecimal.class).orElse(new BigDecimal("0.5"));

        MATCH_WATCHER_RUN_ON_START = pf.get("match-watcher.run-on-start", Boolean.class).orElse(true);
        MATCH_WATCHER_ENABLED = pf.get("match-watcher.enabled", boolean.class).orElse(true);
        MATCH_WATCHER_FREQ_SEC = pf.get("match-watcher.freq-interval", int.class).orElse(15);
        MATCH_WATCHER_BATCH_SIZE = pf.get("match-watcher.batch-size", int.class).orElse(100);
    }

    public final Property<Integer> REQUEST_REPLY_TIMEOUT;

    public final Property<Boolean> READONLY_DEV_MODE_ENABLED;

    public final Property<Boolean> JACKPOT_CONTRIBUTION_ENABLED;
    public final Property<BigDecimal> JACKPOT_GC_CONTRIBUTION_MULTIPLIER;
    public final Property<BigDecimal> JACKPOT_SC_CONTRIBUTION_AMOUNT;

    public final Property<List<String>> BRANDS_AUTOCONFIGURATION_LIST;
    public final Property<Boolean> BRANDS_AUTOCONFIGURATION_ENABLED;
    public final Property<Boolean> MATCH_CACHE_ENABLED;
    public final Property<Duration> MATCH_CACHE_REFRESH_FREQ;

    public final Property<Boolean> LIFECYCLE_SHUFFLE_RUN_ON_START;
    public final Property<Boolean> LIFECYCLE_SHUFFLE_ENABLED;
    public final Property<Boolean> LIFECYCLE_PICK_EM_RUN_ON_START;
    public final Property<Boolean> LIFECYCLE_PICK_EM_ENABLED;
    public final Property<Integer> LIFECYCLE_FREQ_SEC;
    public final Property<Integer> LIFECYCLE_SPORTS_EVENTS_BATCH_SIZE;
    public final Property<Integer> LIFECYCLE_DELAY_BETWEEN_BATCHES_MS;
    public final Property<Integer> LIFECYCLE_DESIRED_TRANSACTION_DURATION_MS;
    public final Property<Integer> LIFECYCLE_BATCH_SIZE;

    public final Property<Boolean> SETTLE_REQUESTS_RUN_ON_START;
    public final Property<Boolean> SETTLE_REQUESTS_ENABLED;
    public final Property<Integer> SETTLE_REQUESTS_FREQ_SEC;
    public final Property<Integer> SETTLE_REQUESTS_BATCH_SIZE;

    public final Property<String> MILLION_GAMES_IOS_MIN_SUPPORTED_VERSION;
    public final Property<String> MILLION_GAMES_ANDROID_MIN_SUPPORTED_VERSION;

    public final Property<Boolean> BLOOMREACH_RUN_ON_START;
    public final Property<Boolean> BLOOMREACH_ENABLED;
    public final Property<Integer> BLOOMREACH_FREQ_SEC;

    public final Property<Boolean> SHUFFLE_LOCATION_POLICY_ENABLED;
    public final Property<Boolean> PICKEM_LOCATION_POLICY_ENABLED;

    public final Property<Boolean> ACCOUNT_FILL_SIGNUP_INFO_ENABLED;
    public final Property<Boolean> ACCOUNT_FILL_FULL_NAME_ENABLED;
    public final Property<List<String>> ACCOUNT_DOMAIN_WHITELIST;
    public final Property<Integer> ACCOUNT_GET_ROUTING_INFO_BATCH_SIZE;
    public final Property<Integer> ACCOUNT_GET_POLICIES_BATCH_SIZE;
    public final Property<Boolean> ACCOUNT_DEFAULT_CURRENCY_POLICIES_ENABLED;

    public final Property<String> DEFAULT_LOCATION_COUNTRY;

    public final Property<Integer> WEBSOCKET_QUEUE_SIZE;

    public final Property<Boolean> PAGINATION_MAX_LIMIT_VALIDATION_ENABLED;

    public final Property<Boolean> PICKEM_TRADING_ALERTS_JOB_ENABLED;
    public final Property<Boolean> PICKEM_TRADING_ALERTS_JOB_RUN_ON_START;
    public final Property<Integer> PICKEM_TRADING_ALERTS_JOB_FREQ_SEC;
    public final Property<Boolean> PICKEM_TRADING_ALERTS_SAVE_STATS_ENABLED;
    public final Property<Boolean> PICKEM_TRADING_ALERTS_SEND_NOTIFICATIONS_ENABLED;
    public final Property<Boolean> PICKEM_TRADING_ALERTS_MARKET_SUSPENSION_ENABLED;
    public final Property<Integer> PICKEM_OPTIONS_SORTING_BATCH_SIZE;
    public final Property<Boolean> PICKEM_OPTIONS_SORTING_ENABLED;
    public final Property<Integer> PICKEM_LOBBY_LEAGUE_LIMIT;

    public final Property<Long> PAGINATION_MAX_LIMIT_GET_SHUFFLE_GAMES;
    public final Property<Long> PAGINATION_MAX_LIMIT_GET_USER_SHUFFLE_TICKETS;
    public final Property<Long> PAGINATION_MAX_LIMIT_GET_PICKEM_MATCHES;
    public final Property<Long> PAGINATION_MAX_LIMIT_GET_PICKEM_OPTIONS;
    public final Property<Long> PAGINATION_MAX_LIMIT_GET_USER_PICKEM_ENTRIES;
    public final Property<Long> PAGINATION_MAX_LIMIT_GET_USER_REWARDS;
    public final Property<Long> PAGINATION_MAX_LIMIT_GET_PICKEM_PACKS;
    public final Property<Long> PAGINATION_MAX_LIMIT_GET_DL;
    public final Property<Long> PAGINATION_MAX_LIMIT_GET_TRENDING_PLAYERS;

    public final Property<Integer> EXPIRE_USER_REWARDS_BATCH_SIZE;
    public final Property<Boolean> EXPIRE_USER_REWARDS_RUN_ON_START;
    public final Property<Boolean> EXPIRE_USER_REWARDS_ENABLED;
    public final Property<Integer> EXPIRE_USER_REWARDS_FREQ_SEC;

    public final Property<List<Currency>> REWARDS_ISSUES_CURRENCIES_ALLOWED;

    public final Property<Boolean> TICKET_SORT_USE_INDEX;
    public final Property<Integer> GAMES_CACHE_TTL;

    public final Property<List<String>> LOGO_ALLOWED_FORMATS;
    public final Property<List<String>> JERSEY_ALLOWED_FORMATS;

    public final Property<String> RANDOMIZER_ALERTS_SLACK_CHANNEL;
    public final Property<String> PICK_EM_PACK_ALERTS_SLACK_CHANNEL;

    public final Property<Boolean> ENTER_TICKET_BUNDLE_AUTO_ROUND_AMOUNT_ENABLED;

    public final Property<Boolean> EVENTS_SINK_ENABLED;
    public final Property<Boolean> EVENTS_SINK_RETRY_ENABLED;

    public final Property<Boolean> PICKEM_MAX_AMOUNT_OF_SAME_PLAYER_IN_ENTRY_ENABLED;
    public final Property<Integer> PICKEM_MAX_AMOUNT_OF_SAME_PLAYER_IN_ENTRY;
    public final Property<Integer> PICKEM_MIN_AMOUNT_OF_DIFFERENT_TEAMS_IN_ENTRY;
    public final ScopedProperty<String> MAX_PLAYERS_MODE;
    public final ScopedProperty<Integer> PICKEM_MAX_MULTIPLE_PLAYERS_FROM_SAME_TEAM;
    public final ScopedProperty<Integer> PICKEM_MAX_MULTIPLE_PLAYERS_FROM_SAME_MATCH;
    public final Property<Integer> PICKEM_MIN_AMOUNT_OF_DIFFERENT_PLAYERS_IN_ENTRY;
    public final Property<Integer> PICKEM_CORRECTION_PERCENTAGE_APPLIED_BELOW;
    public final Property<Integer> PICKEM_MAX_LINE_PROB_PERCENTAGE;
    public final Property<Integer> PICKEM_PACKS_SWISH_BET_CHECK_FREQUENCY_MINS;
    // If new error resulting in the unacceptable parlay appears, add here on prod, but on dev add to the static
    // ones in SwishBetApi and extend the rnd.services.swish.BetServiceTest.unacceptableParlays
    public final Property<List<String>> PICKEM_EXTRA_BET_ERRORS_RESULTING_UNACCEPTABLE_PARLAY;
    public final Property<BigDecimal> PICKEM_DEFAULT_MAX_DAILY_LIMIT;
    public final Property<BigDecimal> PICKEM_DEFAULT_MAX_WEEKLY_LIMIT;
    public final Property<Boolean> PICKEM_LIMITS_VALIDATION_ENABLED;
    public final Property<Integer> PICKEM_PACK_MAX_BOOST_MULTIPLIER;
    public final Property<BigDecimal> PICKEM_MAX_CONJUNCTED_MULTIPLIER;
    public final Property<Boolean> PICKEM_SUBMIT_ENTRY_ENABLED;

    public final Property<Integer> VERTICAL_DETAILS_CACH_EXPIRED_SEC;
    public final Property<Integer> VERTICAL_DETAILS_CACHE_SIZE;
    public final Property<List<VerticalType>> RANDOMIZER_VERTICALS_ALLOWED;

    public final Property<Boolean> MARKET_MANAGER_RUN_ON_START;
    public final Property<Boolean> MARKET_MANAGER_ENABLED;
    public final Property<Integer> MARKET_MANAGER_FREQ_SEC;
    @Deprecated // feature flag. Remove when MarketManager will start to send notifications
    public final Property<Boolean> PICKEM_MARKET_ENABLED;
    @Deprecated // feature flag. Hardcode when alternative lines will launch.
    public final Property<Boolean> PICKEM_PROB_AWARE;
    public final Property<Integer> MARKET_PROP_EXPIRATION_SEC;
    public final Property<Integer> MARKET_GENERAL_EXPIRATION_SEC;
    public final Property<Boolean> MARKET_CACHE_BT_ENABLED;
    public final Property<Duration> MARKET_CACHE_MAX_ACCEPTABLE_REFRESH_TIME;
    // To get trading thresholds (wagers, exposures) for alternative TradingMarketStats we multiply standard ones with
    // this coefficient
    public final Property<BigDecimal> PICKEM_ALT_TRADING_THRESHOLDS_COEFFICIENT;

    public final Property<Duration> OPTIC_ODDS_MARKETS_OUTDATED_AFTER;
    public final Property<Duration> SWISH_MARKETS_OUTDATED_AFTER;
    public final Property<Integer> SEGMENT_SIZE_EVERY_NTH_ACCOUNT;
    public final ScopedProperty<Boolean> MATCH_START_TIME_TBD_ALLOWED;

    public final Property<Boolean> ALTERNATIVE_TRADING_MARKET_STATS_ENABLED;
    public final Property<BigDecimal> ALTERNATIVE_TRADING_THRESHOLDS_COEFFICIENT;

    public final Property<Boolean> MATCH_WATCHER_RUN_ON_START;
    public final Property<Boolean> MATCH_WATCHER_ENABLED;
    public final Property<Integer> MATCH_WATCHER_FREQ_SEC;
    public final Property<Integer> MATCH_WATCHER_BATCH_SIZE;
}
