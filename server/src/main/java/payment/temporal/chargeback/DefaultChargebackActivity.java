package payment.temporal.chargeback;

import common.utils.ProtoUtils;
import io.ebean.Transaction;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import org.apache.commons.lang3.StringUtils;
import payment.PaymentJpaManager;
import payment.api.v1.ChargebackEvent;
import payment.api.v1.internal.temporal.BuildChargebackEventIn;
import payment.api.v1.internal.temporal.BuildChargebackEventOut;
import payment.api.v1.internal.temporal.LabelTransactionIn;
import payment.api.v1.internal.temporal.LabelTransactionOut;
import payment.api.v1.internal.temporal.SaveChargebackInfoIn;
import payment.api.v1.internal.temporal.SaveChargebackInfoOut;
import payment.card.CardPaymentMethod;
import payment.model.AccountPaymentAggregation;
import payment.model.AccountPaymentMethod;
import payment.model.ChargebackHistory;
import payment.model.ChargebackStatusSpec;
import payment.model.EthocaAlertTypeSpec;
import payment.model.PaymentOrder;
import payment.services.PaymentFraudService;
import payment.temporal.AbstractPaymentActivity;
import payment.type.PaymentMethodTypeSpec;
import uam.api.v1.SetChargebackInfoRequest;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.Optional;

public class DefaultChargebackActivity extends AbstractPaymentActivity implements ChargebackActivity {

    public static final String CHARGEBACK_STATUS_FORMAT = "chargeback_%s";

    @Inject
    private PaymentJpaManager ebean;
    @Inject
    private PaymentFraudService fraudService;

    @Override
    public SaveChargebackInfoOut saveChargebackInfo(SaveChargebackInfoIn in) throws Throwable {
        var req = in.getRequest();
        PaymentOrder order;
        boolean noChbBefore;
        try (var tx = ebean.newTransaction()) {
            order = ebean.paymentRepo().requiredSuccessOrderByOrderSn(req.getOrderSn(), tx);
            noChbBefore = order.getChargebackStatus() == null;
            var curStatus = ChargebackStatusSpec.fromString(req.getStatus());
            if (order.getOriginalChargebackAt() == null && curStatus.isNotAlert()) {
                var date = new Date(ProtoUtils.getNullableValue(req.getDate()));
                saveChargebackAggregation(order, date, tx);
                order.setOriginalChargebackAt(LocalDate.ofInstant(Instant.ofEpochMilli(req.getDate()), ZoneOffset.UTC));
            }
            order.setChargebackAt(LocalDate.ofInstant(Instant.ofEpochMilli(req.getDate()), ZoneOffset.UTC));
            order.setChargebackStatus(curStatus);
            if (ProtoUtils.getNullableValue(req.getEthocaId()) != null) {
                order.setEthocaId(req.getEthocaId());
            }
            if (ProtoUtils.getNullableValue(req.getEthocaAlertType()) != null) {
                order.setEthocaAlertType(EthocaAlertTypeSpec.fromString(req.getEthocaAlertType()));
            }
            ebean.save(order, tx);
            saveChargebackHistory(order, req, tx);
            tx.commit();
        }
        var out = SaveChargebackInfoOut.newBuilder()
                .setBrand(order.getAccount().getBrand().getName())
                .setRoutingKey(order.getAccount().getHash())
                .setNoChbBefore(noChbBefore);
        order.fraudRequestId().ifPresent(out::setFraudRequestId);
        return out.build();
    }

    private void saveChargebackHistory(PaymentOrder order, SetChargebackInfoRequest req, Transaction tx) {
        if (isChargebackStatusNotSaved(req, order, tx)) {
            var chargebackHistory = new ChargebackHistory();
            chargebackHistory.setStatus(ChargebackStatusSpec.fromString(req.getStatus()));
            chargebackHistory.setDate(new Date(ProtoUtils.getNullableValue(req.getDate())));
            chargebackHistory.setReason(ProtoUtils.getNullableValue(req.getReason()));
            chargebackHistory.setReasonCode(ProtoUtils.getNullableValue(req.getReasonCode()));
            chargebackHistory.setAgentName(ProtoUtils.getNullableValue(req.getAgentName()));
            chargebackHistory.setExternalId(ProtoUtils.getNullableValue(req.getExternalId()));
            chargebackHistory.setProviderStatus(ProtoUtils.getNullableValue(req.getProviderStatus()));
            chargebackHistory.setOrder(order);
            ebean.save(chargebackHistory, tx);
        }
    }

    private boolean isChargebackStatusNotSaved(SetChargebackInfoRequest req, PaymentOrder order, Transaction tx) {
        var providerStatus = ProtoUtils.getNullableValue(req.getProviderStatus());
        var status = ChargebackStatusSpec.fromString(req.getStatus());
        return ebean.paymentRepo().chargebackHistory(order, tx).stream()
                .findFirst()
                .filter(chargeback -> chargeback.getStatus().equals(status) &&
                        StringUtils.equals(chargeback.getProviderStatus(), providerStatus))
                .isEmpty();
    }

    private void saveChargebackAggregation(PaymentOrder order, Date date, Transaction tx) {
        var chargeBackDate = LocalDate.ofInstant(date.toInstant(), ZoneOffset.UTC);
        // saving aggr
        var agg = ebean.paymentAggregationRepo().accountAggregation(order, chargeBackDate, tx)
                .orElseGet(() -> new AccountPaymentAggregation(order, chargeBackDate));
        agg.addOrderChargebackAggregation(order);
        ebean.paymentAggregationRepo().save(agg, tx);
        // saving total
        var totalAgg = ebean.paymentAggregationRepo().accountAggregation(order, null, tx)
                .orElseGet(() -> new AccountPaymentAggregation(order, null));
        totalAgg.addOrderChargebackAggregation(order);
        ebean.paymentAggregationRepo().save(totalAgg, tx);
    }

    @Override
    public BuildChargebackEventOut buildChargebackEvent(BuildChargebackEventIn in) throws Throwable {
        PaymentOrder order;
        String brand;
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            order = ebean.paymentRepo().requiredSuccessOrderByOrderSn(in.getOrderSn(), tx);
            brand = order.getAccount().getBrand().getName();
        }
        var cardPaymentMethod = order.getPaymentMethod().cardPaymentMethod();
        var chargebackEventBuilder = ChargebackEvent.newBuilder()
                .setEmail(order.getAccount().getEmail())
                .setAgentName(in.getAgentName())
                .setBrand(brand)
                .setStatus(order.getChargebackStatus().getValue())
                .setOrderSn(order.getOrderSn())
                .setAccountId(order.getAccount().getId())
                .setHash(order.getAccount().getHash())
                .setIsOfferPurchase(order.isOfferPurchase())
                .setNoChbBefore(in.getNoChbBefore())
                .setOrderTransactionId(order.getTransactionId().toString())
                .setIsAlert(order.getChargebackStatus().isAlert())
                .setAmount(order.getAmount().toString())
                .setChargebackAt(order.getChargebackAt().atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli())
                .setOrderCreatedAt(order.getCreatedAt().getTime())
                .setCardBrand(cardPaymentMethod.map(CardPaymentMethod::getBrand).orElse(StringUtils.EMPTY));
        if (order.isOfferPurchase()) {
            chargebackEventBuilder.setOfferId(order.getOffer().getId());
            order.getOffer().externalRewardCode().ifPresent(chargebackEventBuilder::setExternalRewardCode);
        }
        Optional.ofNullable(order.getPaymentMethod())
                .filter(pm -> PaymentMethodTypeSpec.SPREEDLY_GATEWAY.getCode().equals(pm.getType()))
                .map(AccountPaymentMethod::getFingerprint)
                .ifPresent(chargebackEventBuilder::setCardFingerprint);
        return BuildChargebackEventOut.newBuilder().setEvent(chargebackEventBuilder.build()).build();
    }

    @Override
    public LabelTransactionOut sendFraudLabelRequest(LabelTransactionIn labelTransactionInput) throws Exception {
        var skipLabel = labelTransactionInput.getSkipLabel();

        if (skipLabel || !labelTransactionInput.hasFraudRequestId()) {
            return LabelTransactionOut.getDefaultInstance();
        }

        var fraudRequestId = labelTransactionInput.getFraudRequestId();
        var curStatus = ChargebackStatusSpec.fromString(labelTransactionInput.getStatus());
        var label = CHARGEBACK_STATUS_FORMAT.formatted(curStatus.getValue());
        var hash = labelTransactionInput.getHash();
        fraudService.labelRequest(labelTransactionInput.getBrand(), fraudRequestId, label, AsciiString.of(hash)).get().unpackAndVerifyOk();

        return LabelTransactionOut.getDefaultInstance();
    }

}
