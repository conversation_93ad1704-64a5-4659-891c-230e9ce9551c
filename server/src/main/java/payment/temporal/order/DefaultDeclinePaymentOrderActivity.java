package payment.temporal.order;

import fraud.api.FraudServiceApi;
import fraud.api.v1.GetConfirmedKYCInfoRequest;
import org.apache.commons.lang3.StringUtils;

import com.turbospaces.rpc.QueuePostTemplate;

import common.utils.ProtoUtils;
import io.ebean.Transaction;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import payment.PaymentJpaManager;
import payment.PaymentProviders;
import payment.api.v1.PurchaseDeclineNotification;
import payment.api.v1.internal.GetAccountInternalInfoInput;
import payment.api.v1.internal.GetAccountInternalInfoOutput;
import payment.api.v1.internal.temporal.CompleteOrderIn;
import payment.api.v1.internal.temporal.CompleteOrderOut;
import payment.api.v1.internal.temporal.DeclinePaymentOrderIn;
import payment.api.v1.internal.temporal.DeclinePaymentOrderOut;
import payment.api.v1.internal.temporal.LabelFraudRequestIn;
import payment.api.v1.internal.temporal.LabelFraudRequestOut;
import payment.api.v1.internal.temporal.OnPaymentOrderDeclineIn;
import payment.api.v1.internal.temporal.OnPaymentOrderDeclineOut;
import payment.dto.FraudInfo;
import payment.dto.mappers.Mappers;
import payment.dto.mappers.ReplicationMappers;
import payment.model.PaymentOrder;
import payment.services.PaymentFraudService;
import payment.services.ctx.DefaultAccountInfoContext;
import payment.services.ctx.DefaultDeclinePaymentOrderContext;
import payment.services.order.PaymentOrderService;
import payment.services.paymentmethod.PaymentMethodService;
import payment.temporal.AbstractPaymentActivity;
import payment.temporal.dto.CompleteOrderJsonIn;
import uam.api.UamServiceApi;
import uam.api.v1.GetAccountEngagementInfoRequest;
import uam.api.v1.GetAccountPersonalInfoRequest;
import uam.api.v1.OfferDeclineNotification;

public class DefaultDeclinePaymentOrderActivity extends AbstractPaymentActivity implements DeclinePaymentOrderActivity {

    @Inject
    private PaymentJpaManager ebean;
    @Inject
    private UamServiceApi uamServiceApi;
    @Inject
    private FraudServiceApi fraudServiceApi;
    @Inject
    private PaymentMethodService paymentMethodService;
    @Inject
    private PaymentFraudService fraudService;
    @Inject
    private PaymentOrderService orderService;
    @Inject
    private PaymentProviders paymentProviders;
    @Inject
    private QueuePostTemplate<?> postTemplate;

    @Override
    public GetAccountInternalInfoOutput getAccountInternalInfo(GetAccountInternalInfoInput input) throws Throwable {
        var pi = uamServiceApi.getAccountPersonalInfo(GetAccountPersonalInfoRequest.newBuilder().setIdentity(input.getIdentity()).build(),
                AsciiString.cached(input.getRoutingKey()));
        var eni = uamServiceApi.getAccountEngagementInfo(GetAccountEngagementInfoRequest.newBuilder().setIdentity(input.getIdentity()).build(),
                AsciiString.cached(input.getRoutingKey()));
        var ki = fraudServiceApi.getConfirmedKYCInfo(GetConfirmedKYCInfoRequest.newBuilder().setIdentity(input.getIdentity()).build(),
                AsciiString.cached(input.getRoutingKey()));

        var piOk = pi.get().unpackAndVerifyOk();
        var eniOk = eni.get().unpackAndVerifyOk();
        var kiOk = ki.get().unpackAndVerifyOk();
        return GetAccountInternalInfoOutput.newBuilder()
                .setPersonalInfo(piOk)
                .setEngagementInfo(eniOk)
                .setKyc(kiOk.getKyc())
                .setKycInfo(kiOk.getKycInfo())
                .build();
    }

    @Override
    public DeclinePaymentOrderOut decline(DeclinePaymentOrderIn in) throws Throwable {
        PaymentOrder order;
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            order = ebean.paymentRepo().requiredOrder(in.getOrderId(), tx);
        }
        var accCtx = new DefaultAccountInfoContext(order.getAccount(),
                Mappers.toAccountPersonalInfo(in.getPersonalInfo().getPersonalInfo(), in.getPersonalInfo().getAccountPreferences()),
                Mappers.toAccountEngagementInfo(in.getEngagementInfo(), in.getKyc()),
                in.getPersonalInfo().getRoutingInfo(),
                Mappers.getKycInfo(in.getKycInfo()),
                in.getEngagementInfo().getInfo().getPlayable());
        DefaultDeclinePaymentOrderContext declineCtx = new DefaultDeclinePaymentOrderContext(order, ebean, accCtx);
        declineCtx.setDeclineReason(in.getErrMsg());
        if (in.hasError()) {
            declineCtx.setError(in.getError().getErrorCode(), in.getError());
        }
        ProtoUtils.getOptionalValue(in.getError().getStatus()).ifPresent(declineCtx::setStatus);
        try (Transaction tx = ebean.newTransaction()) {
            if (in.getRemoveSavedCards()) {
                order.paymentMethod().ifPresent(m -> paymentMethodService.discardPaymentMethods(m.getType(), tx, order.getAccount()));
            }
            if (StringUtils.isNotEmpty(in.getCode())) {
                declineCtx.setCode(in.getCode());
            }
            var toUpdate = declineCtx.getPaymentMethodContext().getUpdates().apply(ebean, order.getAccount(), tx);
            if (toUpdate != null) {
                ebean.save(toUpdate, tx);
                declineCtx.getPaymentMethodContext().setMethod(toUpdate);
                declineCtx.setPaymentMethod(toUpdate.getId());
            }

            declineCtx.getPaymentOrderContext().getUpdates().apply(ebean, tx);
            tx.commit();
        }
        if (paymentProviders.get(declineCtx.providerName()) != null) {
            paymentProviders.get(declineCtx.providerName()).onDecline(declineCtx);
        }
        try (Transaction tx = ebean.newTransaction()) {
            var toUpdate = declineCtx.getPaymentMethodContext().getUpdates().apply(ebean, declineCtx.getPaymentOrderContext().getOrder().getAccount(), tx);
            if (toUpdate != null) {
                ebean.save(toUpdate, tx);
                declineCtx.getPaymentMethodContext().setMethod(toUpdate);
                declineCtx.setPaymentMethod(toUpdate.getId());
            }
            declineCtx.getPaymentOrderContext().getUpdates().apply(ebean, tx);
            tx.commit();
        }
        return DeclinePaymentOrderOut.newBuilder()
                .setDeclineReason(declineCtx.getDeclineReason().orElse("")).build();
    }

    @Override
    public OnPaymentOrderDeclineOut onDecline(OnPaymentOrderDeclineIn in) throws Throwable {
        var order = getPaymentOrder(in.getOrderId());
        var explicitErrMsg = ProtoUtils.getNullableValue(in.getDeclineReason());
        var notification = orderService.getDeclineNtf(order, in.getPaymentRouting(), null, explicitErrMsg, order.getTransactionId().toString(),
                in.getError());
        var event = ReplicationMappers.toPurchaseDeclineEvent(order);
        var out = OnPaymentOrderDeclineOut.newBuilder()
                .setDeclineEvent(event);
        if (notification instanceof OfferDeclineNotification) {
            return out.setOfferDeclineNotification((OfferDeclineNotification) notification).build();
        }
        return out.setPurchaseDeclineNotification((PurchaseDeclineNotification) notification).build();
    }

    @Override
    public LabelFraudRequestOut labelTransaction(LabelFraudRequestIn in) throws Throwable {
        FraudInfo fraudInfo = new FraudInfo(in.getFraudRequestId());
        fraudInfo.setLabel(PaymentOrder.FAILED + ": " + in.getReason());
        fraudService.labelTransaction(fraudInfo, in.getBrand(), AsciiString.cached(in.getHash()));
        return LabelFraudRequestOut.newBuilder().setFraudRequestId(in.getFraudRequestId()).setReason(in.getReason()).build();
    }

    @Override
    public CompleteOrderOut completeOrder(CompleteOrderIn in, CompleteOrderJsonIn jsonIn) throws Throwable {
        PaymentOrder order = getPaymentOrder(in.getOrderId());
        orderService.onComplete(order, in.getPaymentRoutingInfo(), jsonIn.getEngagementInfo());
        return CompleteOrderOut.newBuilder().build();
    }

    private PaymentOrder getPaymentOrder(long id) throws Throwable {
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            return ebean.paymentRepo().requiredOrder(id, tx);
        }
    }
}
