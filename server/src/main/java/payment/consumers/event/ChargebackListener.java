package payment.consumers.event;

import static org.apache.commons.lang3.BooleanUtils.isFalse;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

import java.math.BigDecimal;
import java.util.EnumSet;
import java.util.List;

import fraud.api.v1.DisputeDecisionEvent;
import org.springframework.stereotype.Service;

import io.ebean.Transaction;
import io.netty.util.AsciiString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import payment.Feature;
import payment.PaymentJpaManager;
import payment.PaymentServerProperties;
import payment.api.PaymentServiceApi;
import payment.api.v1.ChargebackEvent;
import payment.dto.mappers.Mappers;
import payment.model.ChargebackStatusSpec;
import payment.model.PaymentOrder;
import payment.model.RedeemStatusSpec;
import payment.model.WithdrawMoneyRequest;
import payment.repo.PaymentRepo;
import payment.services.chargeback.PaymentChargebackService;
import uam.api.v1.CancelRedeemMoneyRequest;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;
import uam.api.v1.SetAccountPaymentSettingsRequest;
import uam.api.v1.UnlockRedeemMoneyRequest;
import uam.api.v1.internal.AccountBalanceChangeEvent;
import uam.api.v1.internal.BalanceType;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChargebackListener {
    private final PaymentJpaManager ebean;
    private final PaymentServerProperties props;
    private final PaymentServiceApi paymentServiceApi;
    private final PaymentChargebackService chargebackService;

    public void onChargebackEvent(ChargebackEvent event) throws Throwable {
        log.debug("Start processing: {}", event);
        PaymentOrder order;
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            order = ebean.paymentRepo().requiredSuccessOrderByOrderSn(event.getOrderSn(), tx);
        }
        if (order.getChargebackStatus().isAlert()) {
            log.debug("Chargeback status is Alert. Skip processing...");
            return;
        }
        if (isTrue(Feature.DEPOSIT_CHARGEBACKS.isEnabled(props, order.getAccount())) && order.isDeposit()) {
            log.debug("Starting chargeback collection process for: accountId={}, orderId={}, orderSn={}",
                    order.getAccount().getId(), order.getId(), order.getOrderSn());
            IdentityByAccountId byAccountId = IdentityByAccountId.newBuilder()
                    .setAccountId(order.getAccount().getId())
                    .build();
            Identity identity = Identity.newBuilder().setByAccountId(byAccountId).build();

            for (WithdrawMoneyRequest withdraw : withdrawalsToCancel(order)) {
                if (withdraw.getStatus() == RedeemStatusSpec.LOCKED) {
                    UnlockRedeemMoneyRequest urmr = UnlockRedeemMoneyRequest.newBuilder()
                            .setId(withdraw.getId())
                            .setIdentity(identity)
                            .setUnlockedByAgent("system")
                            .build();
                    paymentServiceApi.unlockRedeemMoney(urmr, AsciiString.cached(order.getAccount().getHash())).get().verifyOk();
                }

                CancelRedeemMoneyRequest crmr = CancelRedeemMoneyRequest.newBuilder()
                        .setId(withdraw.getId())
                        .setIdentity(identity)
                        .setDoNotSendEmail(true)
                        .setComments("Cancelled due to chargeback received")
                        .build();
                paymentServiceApi.cancelRedeemMoney(crmr, AsciiString.cached(order.getAccount().getHash())).get().verifyOk();
            }
            handleDepositChargebackEvent(order);
        }
        force3ds(order);
    }

    public void onAccountBalanceChangeEvent(AccountBalanceChangeEvent event) throws Throwable {
        log.debug("Start processing: {}", event);
        if (event.getBalanceType() != BalanceType.OWED_BALANCE) {
            log.debug("Balance type is not OWED_BALANCE. Skip processing...");
            return;
        }
        if (event.getAmount().isBlank()) {
            log.debug("Amount is blank. Skip processing...");
            return;
        }
        var collectedAmount = new BigDecimal(event.getAmount());
        if (collectedAmount.compareTo(BigDecimal.ZERO) >= 0) {
            log.debug("Amount should be negative. Skip processing...");
            return;
        }
        chargebackService.handleOwedBalanceChange(event.getAccount().getId(), collectedAmount.negate(), event.getTransactionId());
    }

    public void onDisputeDecisionEvent(DisputeDecisionEvent event) throws Throwable {
        log.debug("Start processing: {}", event);
        var setChargebackInfoRequest = Mappers.toSetChargebackInfoRequest(event);
        var routingKey = AsciiString.cached(event.getRoutingKey());
        paymentServiceApi.setChargebackInfo(setChargebackInfoRequest, routingKey).get().verifyOk();
    }

    private List<WithdrawMoneyRequest> withdrawalsToCancel(PaymentOrder order) throws Throwable {
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            PaymentRepo.FindWithdrawsParams params = PaymentRepo.FindWithdrawsParams.builder()
                    .account(order.getAccount())
                    .statuses(EnumSet.of(RedeemStatusSpec.LOCKED, RedeemStatusSpec.PRE_AUTHORIZED))
                    .isPreConfirmed(Boolean.FALSE)
                    .currency(order.getCurrency())
                    .build();

            return ebean.paymentRepo().withdraws(params, tx);
        }
    }

    private void handleDepositChargebackEvent(PaymentOrder order) throws Throwable {
        var status = order.getChargebackStatus();
        if (isNotActualChargeback(status)) {
            return;
        }
        chargebackService.makeReadyForProcessing(order, status);
    }

    private static boolean isNotActualChargeback(ChargebackStatusSpec statusSpec) {
        return switch (statusSpec) {
            case CHARGEBACK, WON, LOST -> false;
            default -> true;
        };
    }

    private void force3ds(PaymentOrder order) throws Exception {
        if (isNotActualChargeback(order.getChargebackStatus())) {
            return;
        }
        if (isFalse(props.CHARGEBACK_FORCE_3DS.get(order.getAccount().getBrand().getName()))) {
            return;
        }
        var accountId = order.getAccount().getId();
        SetAccountPaymentSettingsRequest request = SetAccountPaymentSettingsRequest.newBuilder()
                .setIdentity(Identity.newBuilder()
                        .setByAccountId(IdentityByAccountId.newBuilder()
                                .setAccountId(accountId)
                                .build())
                        .build())
                .setSecure3DAction(uam.api.v1.Secure3dAction.FORCE)
                .build();

        paymentServiceApi.setAccountPaymentSettings(request, order.getAccount().routingKey()).get().verifyOk();
    }
}
