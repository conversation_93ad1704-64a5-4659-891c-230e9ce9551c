package payment.consumers;

import com.google.protobuf.Any;

import fraud.api.v1.ConfirmKYCInfoEvent;
import fraud.api.v1.DisputeDecisionEvent;
import fraud.api.v1.FraudCheckEvent;
import jakarta.inject.Inject;
import payment.api.v1.ChargebackEvent;
import payment.api.v1.PaymentOrderCompletedEvent;
import payment.api.v1.PaymentOrderCreatedEvent;
import payment.api.v1.PurchaseDeclineEvent;
import payment.api.v1.RedeemConfirmEvent;
import payment.api.v1.RedeemPreConfirmEvent;
import payment.consumers.event.ChargebackListener;
import payment.consumers.event.FraudListener;
import payment.consumers.event.KYCInfoConfirmedListener;
import payment.consumers.event.PaymentOrderListener;
import payment.consumers.event.PurchaseDeclineListener;
import payment.consumers.event.RedeemConfirmListener;
import uam.api.v1.internal.AccountBalanceChangeEvent;
import uam.api.v1.internal.KYCInfoConfirmedEvent;
import uam.api.v1.internal.OfferRefundEvent;

public class PaymentSinkEventProcessor {
    private final PurchaseDeclineListener purchaseDeclineListener;
    private final PaymentOrderListener paymentOrderCompletedListener;
    private final RedeemConfirmListener redeemConfirmListener;
    private final KYCInfoConfirmedListener kycInfoConfirmedListener;
    private final FraudListener fraudListener;
    private final ChargebackListener chargebackListener;

    @Inject
    public PaymentSinkEventProcessor(
            PurchaseDeclineListener purchaseDeclineListener,
            PaymentOrderListener paymentOrderCompletedListener,
            RedeemConfirmListener redeemConfirmListener,
            KYCInfoConfirmedListener kycInfoConfirmedListener,
            FraudListener fraudListener,
            ChargebackListener chargebackListener) {
        this.purchaseDeclineListener = purchaseDeclineListener;
        this.paymentOrderCompletedListener = paymentOrderCompletedListener;
        this.redeemConfirmListener = redeemConfirmListener;
        this.kycInfoConfirmedListener = kycInfoConfirmedListener;
        this.fraudListener = fraudListener;
        this.chargebackListener = chargebackListener;
    }

    public void accept(Any event) throws Throwable {
        if (event.is(PurchaseDeclineEvent.class)) {
            purchaseDeclineListener.onPurchaseDecline(event.unpack(PurchaseDeclineEvent.class));
        } else if (event.is(PaymentOrderCreatedEvent.class)) {
            paymentOrderCompletedListener.onPaymentOrderCreated(event.unpack(PaymentOrderCreatedEvent.class));
        } else if (event.is(PaymentOrderCompletedEvent.class)) {
            paymentOrderCompletedListener.onPaymentOrderCompleted(event.unpack(PaymentOrderCompletedEvent.class));
        } else if (event.is(OfferRefundEvent.class)) {
            paymentOrderCompletedListener.onPaymentOrderRefund(event.unpack(OfferRefundEvent.class));
        } else if (event.is(RedeemConfirmEvent.class)) {
            redeemConfirmListener.onRedeemConfirm(event.unpack(RedeemConfirmEvent.class));
        } else if (event.is(RedeemPreConfirmEvent.class)) {
            redeemConfirmListener.onRedeemPreConfirm(event.unpack(RedeemPreConfirmEvent.class));
        } else if (event.is(KYCInfoConfirmedEvent.class)) {
            kycInfoConfirmedListener.onKYCConfirmed(event.unpack(KYCInfoConfirmedEvent.class));
        } else if (event.is(ConfirmKYCInfoEvent.class)) {
            kycInfoConfirmedListener.onKYCConfirmed(event.unpack(ConfirmKYCInfoEvent.class));
        } else if (event.is(FraudCheckEvent.class)) {
            fraudListener.onFraudCheck(event.unpack(FraudCheckEvent.class));
        } else if (event.is(ChargebackEvent.class)) {
            chargebackListener.onChargebackEvent(event.unpack(ChargebackEvent.class));
        } else if (event.is(AccountBalanceChangeEvent.class)) {
            chargebackListener.onAccountBalanceChangeEvent(event.unpack(AccountBalanceChangeEvent.class));
        } else if (event.is(DisputeDecisionEvent.class)) {
            chargebackListener.onDisputeDecisionEvent(event.unpack(DisputeDecisionEvent.class));
        }
    }
}
