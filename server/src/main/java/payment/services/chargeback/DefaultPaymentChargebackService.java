package payment.services.chargeback;

import static payment.PaymentConfigResolver.getMostRelevant;
import static payment.dto.mappers.ReplicationMappers.toPaymentAccountInfo;
import static payment.model.ChargebackCollectionStatusSpec.NOT_COLLECTED;
import static payment.model.ChargebackCollectionStatusSpec.WAITING_FOR_COLLECTION;
import static payment.model.ChargebackStatusSpec.BAD_DEBT;
import static payment.model.ChargebackStatusSpec.CHARGEBACK;
import static payment.model.ChargebackStatusSpec.LOST;
import static payment.model.ChargebackStatusSpec.WON;
import static payment.model.ChargebackTransactionTypeSpec.CHB_CREDIT;
import static payment.model.ChargebackTransactionTypeSpec.CHB_DEBIT;
import static payment.model.ChargebackTransactionTypeSpec.OWED_DEBIT;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

import org.springframework.stereotype.Service;

import com.turbospaces.rpc.QueuePostTemplate;

import api.v1.ApplicationException;
import api.v1.CommonMappers;
import io.ebean.Transaction;
import io.netty.util.AsciiString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import payment.PaymentEbeanJpaManager;
import payment.PaymentServerProperties;
import payment.api.v1.PaymentMethodBlacklistEvent;
import payment.card.BillingDetails;
import payment.card.CardPaymentMethod;
import payment.dto.mappers.Mappers;
import payment.model.AccountPaymentMethod;
import payment.model.Chargeback;
import payment.model.ChargebackConfig;
import payment.model.ChargebackHistory;
import payment.model.ChargebackStatusSpec;
import payment.model.ChargebackTransactionTypeSpec;
import payment.model.PaymentOrder;
import payment.model.util.EmailTemplate;
import payment.services.email.PaymentEmailService;
import payment.type.PaymentMethodTypeSpec;
import uam.api.UamServiceApi;
import uam.api.v1.LockAccountRequest;
import uam.api.v1.RewardAccountManuallyRequest;
import uam.api.v1.RewardMode;
import uam.api.v1.WalletDeductRequest;
import uam.model.LockReason;
import uam.model.TransactionTypeSpec;
import uam.model.WalletSessionTypeSpec;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultPaymentChargebackService implements PaymentChargebackService {

    static final String CHARGEBACK_DEDUCT = "chargeback_deduct";
    static final String CHARGEBACK_CREDIT = "chargeback_credit";

    private final PaymentEbeanJpaManager ebean;
    private final PaymentServerProperties props;
    private final PaymentEmailService emailService;
    private final UamServiceApi uamServiceApi;
    private final QueuePostTemplate<?> postTemplate;

    @Override
    public void makeReadyForProcessing(PaymentOrder order, ChargebackStatusSpec status) throws Throwable {
        List<ChargebackHistory> chargebackHistories;
        try (var tx = ebean.newReadOnlyTransaction()) {
            chargebackHistories = ebean.chargebackRepo().allChargebackHistoryByOrder(order, tx);
        }
        var readyForProcessing = chargebackHistories.stream()
                .filter(h -> Objects.isNull(h.getChargeback()) && Objects.isNull(h.getCollectionStatus()))
                .filter(h -> h.getStatus() == status)
                .findFirst();
        if (readyForProcessing.isPresent()) {
            var history = readyForProcessing.get();
            history.setCollectionStatus(WAITING_FOR_COLLECTION);
            try (var tx = ebean.newTransaction()) {
                ebean.save(history, tx);
                tx.commit();
            }
            log.debug("Chargeback is waiting for collection: transactionId={}", order.getTransactionId().toString());
        }
    }

    @Override
    public void handleChargeback(PaymentOrder order) throws Throwable {
        boolean isCollected;
        List<ChargebackConfig> chargebackConfigs;
        try (var tx = ebean.newTransaction()) {
            isCollected = isCollected(order, tx);
            if (isCollected) {
                var chargeback = ebean.chargebackRepo().requiredChargeback(order, tx);
                // ~ update history to avoid job reprocessing
                ebean.chargebackRepo().waitingForCollection(order, CHARGEBACK, tx).ifPresentOrElse(h -> {
                    h.setChargeback(chargeback);
                    h.setCollectionStatus(NOT_COLLECTED);
                    h.setCollectedAmount(BigDecimal.ZERO);
                    h.setTotalCollectedAmount(chargeback.getCollectedAmount());
                    h.setOwedAmount(chargeback.owedAmount());
                    h.setTransactionId(chargeback.transactionId());
                    ebean.save(h, tx);
                }, () -> log.error("Unable to find chargeback history for order: {}", order.getId()));
            }
            chargebackConfigs = ebean.chargebackRepo().chargebackConfig(order, tx);
            tx.commit();
        }
        if (isCollected) {
            log.debug("Chargeback already collected: transactionId={}", order.getTransactionId());
            return;
        }
        BigDecimal fee = resolveFee(chargebackConfigs);
        tryDeductAndCollect(order, CHARGEBACK, fee);
    }

    @Override
    public void handleLost(PaymentOrder order) throws Throwable {
        boolean isCollected;
        List<ChargebackConfig> chargebackConfigs;
        try (var tx = ebean.newTransaction()) {
            isCollected = isCollected(order, tx);
            if (isCollected) {
                ebean.chargebackRepo().chargeback(order, tx).ifPresent(chargeback -> {
                    chargeback.setStatus(LOST);
                    ebean.save(chargeback, tx);
                    saveChargebackHistory(chargeback, BigDecimal.ZERO, null, order.getTransactionId().toString(), tx);
                });
            }
            chargebackConfigs = ebean.chargebackRepo().chargebackConfig(order, tx);
            tx.commit();
        }
        if (isCollected) {
            log.debug("Chargeback already collected: transactionId={}", order.getTransactionId());
            return;
        }
        BigDecimal fee = resolveFee(chargebackConfigs);
        tryDeductAndCollect(order, LOST, fee);
    }

    @Override
    public void handleWon(PaymentOrder order) throws Throwable {
        var skipReward = false;
        try (var tx = ebean.newTransaction()) {
            if (!isCollected(order, tx)) {
                var chargeback = Chargeback.completeUncollected(order);
                ebean.save(chargeback, tx);
                saveChargebackHistory(chargeback, BigDecimal.ZERO, ChargebackTransactionTypeSpec.CHB_CREDIT, chargeback.transactionId(), tx);
                skipReward = true;
            }
            tx.commit();
        }
        if (skipReward) {
            log.debug("Skip chargeback reward since account was not deducted: transactionId: {}", order.getTransactionId());
            return;
        }
        rewardAccountManually(order);
    }

    @Override
    public void handleOwedBalanceChange(long accountId, BigDecimal amount, String transactionId) throws Throwable {
        try (var tx = ebean.newTransaction()) {
            updateCollectedAmount(accountId, amount, transactionId, tx);
            tx.commit();
        }
    }

    @Override
    public void handleBadDept(String transactionId) throws Throwable {
        try (var tx = ebean.newTransaction()) {
            var order = ebean.paymentRepo().requiredOrderByTransactionId(UUID.fromString(transactionId), tx);
            var chargeback = updateChargebackStatus(order, ChargebackStatusSpec.BAD_DEBT, tx);
            saveChargebackHistory(chargeback, BigDecimal.ZERO, null, chargeback.transactionId(), tx);
            tx.commit();
            onChargebackStatusChange(chargeback);
        }
    }

    private void onChargebackStatusChange(Chargeback chargeback) throws Throwable {
        boolean isBadDebt = chargeback.getStatus() == ChargebackStatusSpec.BAD_DEBT;
        boolean isNotCollected = chargeback.getCollectionStatus().isNotCollected();
        boolean toLockByPartial = props.CHARGEBACK_LOCK_PARTIAL_ENABLED.get() && chargeback.getCollectionStatus().isPartial();
        boolean toLockAccount = isBadDebt || isNotCollected || toLockByPartial;

        if (toLockAccount) {
            lockAccount(chargeback.getOrder(), isBadDebt);
        }

        sendPaymentMethodBlacklistEvent(chargeback);
    }

    private boolean isCollected(PaymentOrder order, Transaction tx) {
        return ebean.chargebackRepo().allChargebackHistoryByOrder(order, tx)
                .stream()
                .anyMatch(h -> Objects.nonNull(h.getChargeback()));
    }

    private static BigDecimal resolveFee(List<ChargebackConfig> chargebackConfigs) {
        if (chargebackConfigs.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return getMostRelevant(chargebackConfigs, ChargebackConfig::getBrand, ChargebackConfig::getIntegrationType).getFee();
    }

    private void saveChargebackHistory(Chargeback chargeback, BigDecimal collectedAmount,
                                       ChargebackTransactionTypeSpec transactionType, String transactionId, Transaction tx) {
        var historyList = ebean.chargebackRepo().chargebackHistory(chargeback.getOrder(), chargeback.getStatus(), tx)
                .stream()
                .filter(h -> Objects.isNull(h.getTransactionId()))
                .toList();
        if (historyList.size() > 1) {
            throw new IllegalStateException("Broken chargeback history: " + historyList);
        }
        var history = historyList.stream().findFirst().orElseGet(() -> {
            var ch = new ChargebackHistory();
            ch.setStatus(chargeback.getStatus());
            ch.setOrder(chargeback.getOrder());
            ch.setDate(new Date());
            return ch;
        });
        if (history.getChargeback() == null) {
            history.setChargeback(chargeback);
            history.setCollectionStatus(chargeback.getCollectionStatus());
            history.setCollectedAmount(collectedAmount);
            history.setOwedAmount(chargeback.owedAmount());
            history.setTotalCollectedAmount(chargeback.getCollectedAmount());
            history.setTransactionType(transactionType);
            history.setTransactionId(transactionId);
        }
        ebean.save(history, tx);
    }

    private void tryDeductAndCollect(PaymentOrder order, ChargebackStatusSpec status, BigDecimal fee) throws Throwable {
        var totalAmount = order.getAmount().add(fee);
        var account = order.getAccount();
        var request = WalletDeductRequest.newBuilder()
                .setIdentity(Mappers.toAccountIdentity(order))
                .setType(WalletSessionTypeSpec.CHARGEBACK.name())
                .setSessionId(order.getTransactionId().toString())
                .setSource(CHARGEBACK_DEDUCT)
                .setTransactionReference(order.getTransactionId().toString())
                .setTransactionType(TransactionTypeSpec.CHB_DEBIT.name())
                .setAmount(totalAmount.toPlainString())
                .setCurrency(order.getCurrency())
                .setAt(CommonMappers.toDate(order.getOriginalChargebackAt()))
                .setUseOwed(true);
        var deductReply = uamServiceApi.walletDeduct(request.build(), account.routingKey()).get().unpack();
        if (deductReply.getApplied()) {
            var owedAmount = deductReply.getOwedAmount().isBlank() ? BigDecimal.ZERO : new BigDecimal(deductReply.getOwedAmount());
            var collectedAmount = totalAmount.subtract(owedAmount);
            Chargeback chargeback;
            try (var tx = ebean.newTransaction()) {
                chargeback = new Chargeback(order, fee, status);
                chargeback.addCollectedAmount(collectedAmount);
                ebean.save(chargeback, tx);
                saveChargebackHistory(chargeback, collectedAmount, CHB_DEBIT, order.getTransactionId().toString(), tx);
                tx.commit();
            }
            log.debug("Successfully collected chargeback: {}", chargeback);
            sendChargebackCollectedEmailAsync(chargeback);
            onChargebackStatusChange(chargeback);
        }
    }

    private void rewardAccountManually(PaymentOrder order) throws Throwable {
        var request = RewardAccountManuallyRequest.newBuilder()
                .setIdentity(Mappers.toAccountIdentity(order))
                .setMode(RewardMode.IMMEDIATE)
                .setSessionId(order.getTransactionId().toString())
                .setSource(CHARGEBACK_CREDIT)
                .setTransactionType(TransactionTypeSpec.CHB_CREDIT.name())
                .setWsType(WalletSessionTypeSpec.CHARGEBACK.code())
                .setFiatAmount(order.getAmount().toPlainString())
                .setSkipEmail(true)
                .setToForce(true); // ~ reward locked account
        boolean applied = uamServiceApi.rewardAccountManually(request.build(), order.getAccount().routingKey())
                .get()
                .unpackAndVerifyOk()
                .getApplied();
        if (applied) {
            try (var tx = ebean.newTransaction()) {
                var chargeback = updateChargebackStatus(order, WON, tx);
                saveChargebackHistory(chargeback, BigDecimal.ZERO, CHB_CREDIT, chargeback.transactionId(), tx);
                tx.commit();
                onChargebackStatusChange(chargeback);
            }
        }
    }

    private void sendChargebackCollectedEmailAsync(Chargeback chargeback) {
        if (chargeback.isCompleted()) {
            emailService.sendEmailAsync(chargeback.getAccount().routingKey(), chargeback, EmailTemplate.CHARGEBACK_COLLECTED);
        }
    }

    private void lockAccount(PaymentOrder order, boolean delete) throws ExecutionException, InterruptedException {
        var request = LockAccountRequest.newBuilder()
                .setIdentity(Mappers.toAccountIdentity(order))
                .setDelete(delete)
                .setReason(LockReason.CHARGEBACK_BAD_DEBT.getReason());
        uamServiceApi.lockAccount(request.build(), order.getAccount().routingKey()).get();
    }

    private void updateCollectedAmount(long accountId, BigDecimal amount, String transactionId, Transaction tx) throws Exception {
        var availableAmount = amount;
        var account = ebean.accountRepo().requiredAccount(accountId, tx);
        var chargebacks = ebean.chargebackRepo().activeChargebacks(account, tx);
        for (var chargeback : chargebacks) {
            if (availableAmount.compareTo(BigDecimal.ZERO) > 0) {
                var uncollectedAmount = chargeback.owedAmount();
                var amountToCollect = uncollectedAmount.min(availableAmount);
                chargeback.addCollectedAmount(amountToCollect);
                ebean.save(chargeback, tx);
                saveChargebackHistory(chargeback, amountToCollect, OWED_DEBIT, transactionId, tx);
                sendChargebackCollectedEmailAsync(chargeback);

                availableAmount = availableAmount.subtract(amountToCollect);
            }
        }
    }

    private Chargeback updateChargebackStatus(PaymentOrder order, ChargebackStatusSpec chargebackStatusSpec, Transaction tx) throws ApplicationException {
        var chargeback = ebean.chargebackRepo().requiredChargeback(order, tx);
        chargeback.setStatus(chargebackStatusSpec);
        ebean.save(chargeback, tx);
        return chargeback;
    }

    public void sendPaymentMethodBlacklistEvent(Chargeback chargeback) throws Throwable {
        if (chargeback.getStatus() == BAD_DEBT) {
            try (Transaction tx = ebean.newTransaction()) {
                UUID id = UUID.fromString(chargeback.transactionId());
                var order = ebean.paymentRepo().requiredOrderByTransactionId(id, tx);

                var pmBlacklistedEvent = PaymentMethodBlacklistEvent.newBuilder();
                pmBlacklistedEvent.setPaymentAccountInfo(toPaymentAccountInfo(chargeback.getAccount()));
                pmBlacklistedEvent.setOrderSn(order.getOrderSn());
                order.paymentMethod().map(AccountPaymentMethod::getFingerprint).filter(_ -> isSpreedlyCard(order))
                        .ifPresent(pmBlacklistedEvent::setFingerprint);
                order.paymentMethod().flatMap(AccountPaymentMethod::cardPaymentMethod)
                        .flatMap(CardPaymentMethod::billingAddress)
                        .map(BillingDetails::getAddress1)
                        .ifPresent(pmBlacklistedEvent::setBillingDetails);
                Optional.ofNullable(order.getRemoteIp()).ifPresent(pmBlacklistedEvent::setRemoteIp);
                Optional.ofNullable(order.getFraudResponseId()).ifPresent(pmBlacklistedEvent::setFraudResponseId);

                postTemplate.sendEvent(
                        pmBlacklistedEvent.build(),
                        AsciiString.cached(order.getAccount().getHash()));
            } catch (ApplicationException e) {
                log.error("Unable to find transactionalId: {}", chargeback.transactionId());
            }
        }
    }

    private static boolean isSpreedlyCard(PaymentOrder order) {
        PaymentMethodTypeSpec type = PaymentMethodTypeSpec.fromCode(order.getPaymentMethod().getType());
        return type.isSpreedly() && PaymentMethodTypeSpec.CARD_METHOD_TYPES.contains(type.code());
    }
}
