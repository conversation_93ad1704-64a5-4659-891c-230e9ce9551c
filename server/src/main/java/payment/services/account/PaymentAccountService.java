package payment.services.account;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.turbospaces.rpc.ApiResponse;

import io.ebean.Transaction;
import io.netty.util.AsciiString;
import payment.context.account.AccountInfoContext;
import payment.model.OfferTemplate;
import payment.model.immutable.ImmutableAccount;
import payment.services.account.dto.AccountPaymentInfoWrapper;
import payment.services.account.dto.PurchaseLimit;
import payment.type.OfferPlatformSpec;
import uam.api.v1.GetAccountPersonalInfoResponse;
import uam.api.v1.GetAccountRoutingInfoResponse;
import uam.api.v1.Identity;

public interface PaymentAccountService {
    OfferTemplate getOfferIfAllowed(String offerCode, OfferPlatformSpec platform, Date timestamp, LocalDate at, ImmutableAccount account, Identity identity, AccountInfoContext ctx, boolean allowNative) throws Throwable;
    Optional<PurchaseLimit> getActualPurchaseLimit(Transaction tx, LocalDate date, ImmutableAccount account);
    List<PurchaseLimit> getActualPurchaseLimits(Transaction tx, LocalDate date, ImmutableAccount account);
    List<PurchaseLimit> getInternalPurchaseLimits(Transaction tx, LocalDate date, ImmutableAccount account);
    List<PurchaseLimit> getMemberRequestPurchaseLimits(Transaction tx, LocalDate date, ImmutableAccount account);
    String getSkrillEmail(ImmutableAccount account) throws Throwable;
    Optional<BigDecimal> getAvailableWeeklyWageredGold(LocalDate at, ImmutableAccount account, Transaction tx) throws Throwable;
    Optional<Date> getFirstPurchaseDate(ImmutableAccount account) throws Throwable;
    String getCountry(ImmutableAccount account) throws Throwable;
    ApiResponse<GetAccountPersonalInfoResponse> getAccountPersonalInfo(ImmutableAccount account) throws Throwable;
    ApiResponse<GetAccountPersonalInfoResponse> getAccountPersonalInfo(Identity identity, ImmutableAccount account);
    AccountInfoContext getAccountInfoContext(Identity identity, ImmutableAccount account) throws Throwable;
    AccountInfoContext getAccountInfoContext(Identity identity, ImmutableAccount account, AccountPaymentInfoWrapper accountPaymentInfoWrapper) throws Throwable;
    AccountPaymentInfoWrapper getAccountPaymentInfo(Identity identity, AsciiString key) throws Throwable;
    ApiResponse<GetAccountRoutingInfoResponse> getAccountRouting(ImmutableAccount account) throws Throwable;
    Map<Long, Long> getFixedOfferPurchaseCounts(ImmutableAccount account, String country, Collection<OfferTemplate> offer, boolean includePending, Transaction tx);
    //todo: move to account offer service & fix di
    default Long getFixedOfferPurchaseCount(ImmutableAccount account, String country, OfferTemplate offer, boolean includePending, Transaction tx) {
        return getFixedOfferPurchaseCounts(account, country, Collections.singletonList(offer), includePending, tx).getOrDefault(offer.getId(), 0L);
    }
}
