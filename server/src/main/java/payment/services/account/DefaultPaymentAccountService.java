package payment.services.account;

import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toSet;
import static payment.repo.OfferTemplateRepo.FindOfferParams.activeOffers;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.turbospaces.common.PlatformUtil;
import com.google.common.util.concurrent.Futures;
import fraud.api.FraudServiceApi;
import fraud.api.v1.GetConfirmedKYCInfoRequest;
import fraud.api.v1.GetConfirmedKYCInfoResponse;
import fraud.api.v1.KYCInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Service;

import com.turbospaces.cfg.ScopedProperty;
import com.turbospaces.ebean.JpaManager;
import com.turbospaces.rpc.ApiResponse;

import api.v1.ApplicationException;
import api.v1.Code;
import common.utils.ProtoUtils;
import io.ebean.FetchGroup;
import io.ebean.Transaction;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import payment.PaymentJpaManager;
import payment.PaymentServerProperties;
import payment.context.account.AccountCategory;
import payment.context.account.AccountInfoContext;
import payment.dto.mappers.Mappers;
import payment.model.AccountMetaInfo;
import payment.model.AccountOfferMetaInfo;
import payment.model.AccountPurchaseLimit;
import payment.model.OfferTemplate;
import payment.model.PaymentOrder;
import payment.model.PurchaseLimitReasonSpec;
import payment.model.WeeklyPersonalizedOfferAggregationHistory;
import payment.model.immutable.ImmutableAccount;
import payment.model.query.QOfferTemplate;
import payment.model.query.QPaymentOrder;
import payment.repo.PaymentRepo;
import payment.services.account.dto.AccountPaymentInfoWrapper;
import payment.services.account.dto.PurchaseLimit;
import payment.services.ctx.DefaultAccountInfoContext;
import payment.services.offer.OfferTemplateUtil;
import payment.services.offer.OffersFilterService;
import payment.type.OfferPlatformSpec;
import payment.type.OfferTypeSpec;
import uam.api.UamServiceApi;
import uam.api.v1.AccountPaymentInfo;
import uam.api.v1.AccountRoutingById;
import uam.api.v1.GetAccountEngagementInfoRequest;
import uam.api.v1.GetAccountPaymentInfoRequest;
import uam.api.v1.GetAccountPaymentInfoResponse;
import uam.api.v1.GetAccountPersonalInfoRequest;
import uam.api.v1.GetAccountPersonalInfoResponse;
import uam.api.v1.GetAccountRoutingInfoRequest;
import uam.api.v1.GetAccountRoutingInfoResponse;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;

@Slf4j
@Service
public class DefaultPaymentAccountService implements PaymentAccountService {
    private static final String ERR_OFFER_UNAVAILABLE = "This offer is either already purchased by you or it is unavailable. Please purchase a different package.";
    private static final FetchGroup<PaymentOrder> FIXED_OFFER_FETCH = FetchGroup.of(PaymentOrder.class)
            .select(QPaymentOrder.alias().createdAt.toString())
            .fetch(QPaymentOrder.alias().offer.toString(), FetchGroup.of(OfferTemplate.class).select(QOfferTemplate.alias().id.toString()).build())
            .build();
    @Delegate(excludes = JpaManager.class)
    private final PaymentJpaManager ebean;
    private final UamServiceApi uamServiceApi;
    private final FraudServiceApi fraudServiceApi;
    private final PaymentServerProperties props;

    @Inject
    public DefaultPaymentAccountService(PaymentJpaManager ebean, UamServiceApi uamServiceApi, FraudServiceApi fraudServiceApi, PaymentServerProperties props) {
        this.ebean = ebean;
        this.uamServiceApi = uamServiceApi;
        this.fraudServiceApi = fraudServiceApi;
        this.props = props;
    }

    @Override
    public OfferTemplate getOfferIfAllowed(
            String offerCode,
            OfferPlatformSpec platform,
            Date timestamp,
            LocalDate at,
            ImmutableAccount account,
            Identity identity,
            AccountInfoContext ctx,
            boolean allowNative) throws Throwable {
        List<OfferTemplate> brandOffers;
        int purchasesCount;
        List<OfferTemplate> appliedOffers;
        List<AccountOfferMetaInfo> offerMetaInfos;
        OfferTemplate offer;
        BigInteger offerPurchaseCount = BigInteger.ZERO;
        boolean eligibleIfOfferUpgrade = true;
        boolean eligibleToShow;
        var onlyApproved = props.OFFER_TEMPLATE_APPROVAL_ENABLED.get(account.getBrand().getName());
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            var params = activeOffers(account.getBrand(), platform, onlyApproved, allowNative);
            brandOffers = offerTemplateRepo().offerTemplates(params, tx)
                    .stream()
                    .filter(o -> o.isEligibleForCountry(ctx.country()))
                    .collect(Collectors.toList());
            purchasesCount = account.paymentMetaInfo().map(AccountMetaInfo::successfulOfferPurchaseCount).orElse(0);
            appliedOffers = paymentRepo().accountMetaInfo(account.getId(), tx)
                    .map(m -> OffersFilterService.getAppliedOffers(m.getOffers(), brandOffers, timestamp, account))
                    .orElse(List.of());
            offerMetaInfos = paymentRepo().getAccountOffersMetaInfo(account.getId(), tx);
            offer = brandOffers.stream().filter(o -> o.getCode().equals(offerCode)).findFirst()
                    .orElseThrow(() -> ApplicationException.of(ERR_OFFER_UNAVAILABLE, Code.ERR_NOT_FOUND, offerCode));

            if (offer.capacityPerOffer().isPresent()) {
                offerPurchaseCount = paymentRepo().getOfferPurchaseCount(offer.getId(), tx).map(AccountOfferMetaInfo::getTotalPurchaseCount)
                        .orElse(BigInteger.ZERO);
            }
            eligibleToShow = isEligibleToShow(offer, brandOffers, account, appliedOffers, purchasesCount, timestamp, at, offerMetaInfos,
                    offerPurchaseCount, ctx, tx);
        }

        boolean offerIsUpgrade = brandOffers.stream().anyMatch(v -> v.getUpgradeId() != null && v.getUpgradeId().equals(offer.getId()));
        if (offerIsUpgrade) {
            eligibleIfOfferUpgrade = checkUpgradeEligibility(brandOffers, offer.getId(), account, appliedOffers, purchasesCount, timestamp, at,
                    offerMetaInfos, ctx);
        }

        if (!eligibleToShow || !eligibleIfOfferUpgrade) {
            throw ApplicationException.of(ERR_OFFER_UNAVAILABLE, Code.ERR_NOT_FOUND, offerCode);
        }
        return offer;
    }

    @Override
    public Optional<PurchaseLimit> getActualPurchaseLimit(Transaction tx, LocalDate date, ImmutableAccount account) {
        return actualPurchaseLimitsAlreadyUsedStream(tx, date, account)
                // find limit with minimum available amount
                .min(Comparator.comparing(pair -> pair.getLeft().getThreshold().subtract(pair.getRight())))
                .map(pair -> applyPurchaseLimit(date, pair.getLeft(), pair.getRight()));
    }

    @Override
    public List<PurchaseLimit> getActualPurchaseLimits(Transaction tx, LocalDate date, ImmutableAccount account) {
        return actualPurchaseLimitsAlreadyUsedStream(tx, date, account)
                .collect(Collectors.groupingBy(p -> p.getLeft().getPeriod(),
                        Collectors.minBy(Comparator.comparing(pair -> pair.getLeft().getThreshold().subtract(pair.getRight())))))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(pair -> applyPurchaseLimit(date, pair.getLeft(), pair.getRight()))
                .toList();
    }

    @Override
    public List<PurchaseLimit> getMemberRequestPurchaseLimits(Transaction tx, LocalDate date, ImmutableAccount account) {
        return actualPurchaseLimitsAlreadyUsedStream(tx, date, account)
                .filter(lim -> lim.getLeft().getReason() == PurchaseLimitReasonSpec.MEMBER_REQUEST)
                .map(pair -> applyPurchaseLimit(date, pair.getLeft(), pair.getRight()))
                .toList();
    }

    @Override
    public List<PurchaseLimit> getInternalPurchaseLimits(Transaction tx, LocalDate date, ImmutableAccount account) {
        return actualPurchaseLimitsAlreadyUsedStream(tx, date, account)
                .filter(lim -> lim.getLeft().getReason() != PurchaseLimitReasonSpec.MEMBER_REQUEST)
                .collect(Collectors.groupingBy(p -> p.getLeft().getPeriod(),
                        Collectors.minBy(Comparator.comparing(pair -> pair.getLeft().getThreshold().subtract(pair.getRight())))))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(pair -> applyPurchaseLimit(date, pair.getLeft(), pair.getRight()))
                .toList();
    }

    private Stream<ImmutablePair<AccountPurchaseLimit, BigDecimal>> actualPurchaseLimitsAlreadyUsedStream(Transaction tx, LocalDate date,
                                                                                                          ImmutableAccount account) {
        return account.getPurchaseLimits().stream()
                .filter(AccountPurchaseLimit::isActive)
                // mapping limit, to total sum of orders for given limit period
                .map(limit -> new ImmutablePair<>(limit, paymentAggregationRepo().sumOfPurchaseDepositBaseAmount(
                        limit.getPeriod().calculateFrom(date),
                        limit.getPeriod().calculateTo(date),
                        account,
                        tx)))
                .filter(pair -> Optional.ofNullable(pair.getKey().getLimitEnd())
                        .map(d -> d.plusDays(1))
                        .map(date::isBefore)
                        .orElse(true));
    }

    @Override
    public String getSkrillEmail(ImmutableAccount account) throws Throwable {
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            return paymentRepo().accountMetaInfo(account.getId(), tx).map(AccountMetaInfo::skrillEmail).orElse(StringUtils.EMPTY);
        }
    }

    @Override
    public Optional<BigDecimal> getAvailableWeeklyWageredGold(LocalDate at, ImmutableAccount account, Transaction tx) {
        if (hasOfferPurchasesFrom(account, at, tx)) {
            return Optional.empty();
        }
        LocalDate from = at.with(DayOfWeek.MONDAY).minusWeeks(1);
        LocalDate to = at.with(DayOfWeek.SUNDAY).minusWeeks(1);
        return paymentRepo().accountWeeklyPersonalizedOfferAggr(account, from, to, tx)
                .map(WeeklyPersonalizedOfferAggregationHistory::getTotalWeeklyWageredGold)
                .or(() -> Optional.of(BigDecimal.ZERO));
    }

    private boolean hasOfferPurchasesFrom(ImmutableAccount account, LocalDate at, Transaction tx) {
        var offersFrom = at.with(DayOfWeek.MONDAY);
        var params = PaymentRepo.FindPaymentOrdersParams.builder()
                .account(account)
                .from(offersFrom)
                .offerType(OfferTypeSpec.PERSONALIZED)
                .build();

        return paymentRepo().hasSuccessPaymentOrders(params, tx);
    }

    @Override
    public Optional<Date> getFirstPurchaseDate(ImmutableAccount account) throws Throwable {
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            return paymentRepo().accountMetaInfo(account.getId(), tx).flatMap(AccountMetaInfo::firstPurchaseOrDeposit).map(PaymentOrder::getCreatedAt);
        }
    }

    @Override
    public String getCountry(ImmutableAccount account) throws Exception {
        Identity identity = Identity.newBuilder().setByAccountId(IdentityByAccountId.newBuilder().setAccountId(account.getId()).build())
                .build();

        return ProtoUtils.getOptionalValue(uamServiceApi
                .getAccountPersonalInfo(GetAccountPersonalInfoRequest.newBuilder().setIdentity(identity).build(),
                        account.routingKey())
                .get().unpack()
                .getPersonalInfo().getCountry()).orElse(account.getCountry());
    }

    @Override
    public ApiResponse<GetAccountPersonalInfoResponse> getAccountPersonalInfo(ImmutableAccount account) throws Exception {
        Identity identity = Identity.newBuilder().setByAccountId(IdentityByAccountId.newBuilder().setAccountId(account.getId()).build())
                .build();
        return getAccountPersonalInfo(identity, account);

    }

    @Override
    public ApiResponse<GetAccountPersonalInfoResponse> getAccountPersonalInfo(Identity identity, ImmutableAccount account) {
        return uamServiceApi.getAccountPersonalInfo(GetAccountPersonalInfoRequest.newBuilder().setIdentity(identity).build(),
                account.routingKey());
    }

    @Override
    public AccountInfoContext getAccountInfoContext(Identity identity, ImmutableAccount account) throws Exception {

        var pi = uamServiceApi.getAccountPersonalInfo(GetAccountPersonalInfoRequest.newBuilder().setIdentity(identity).build(),
                account.routingKey());
        var eni = uamServiceApi.getAccountEngagementInfo(GetAccountEngagementInfoRequest.newBuilder().setIdentity(identity).build(), account.routingKey());
        var ki = fraudServiceApi.getConfirmedKYCInfo(GetConfirmedKYCInfoRequest.newBuilder().setIdentity(identity).build(), account.routingKey());

        var piOk = pi.get().unpackAndVerifyOk();
        var eniOk = eni.get().unpackAndVerifyOk();
        var kiOk = ki.get().unpackAndVerifyOk();
        return new DefaultAccountInfoContext(account,
                Mappers.toAccountPersonalInfo(piOk.getPersonalInfo(), piOk.getAccountPreferences()),
                Mappers.toAccountEngagementInfo(eniOk, kiOk.getKyc()),
                piOk.getRoutingInfo(),
                Mappers.getKycInfo(kiOk.getKycInfo()),
                eniOk.getInfo().getPlayable());
    }

    @Override
    public AccountInfoContext getAccountInfoContext(Identity identity, ImmutableAccount account, AccountPaymentInfoWrapper accountPaymentInfoWrapper) throws Throwable {
        var pi = uamServiceApi.getAccountPersonalInfo(GetAccountPersonalInfoRequest.newBuilder().setIdentity(identity).build(),
                account.routingKey());
        var eni = uamServiceApi.getAccountEngagementInfo(GetAccountEngagementInfoRequest.newBuilder().setIdentity(identity).build(), account.routingKey());

        var piOk = pi.get().unpackAndVerifyOk();
        var eniOk = eni.get().unpackAndVerifyOk();
        return new DefaultAccountInfoContext(account,
                Mappers.toAccountPersonalInfo(piOk.getPersonalInfo(), piOk.getAccountPreferences()),
                Mappers.toAccountEngagementInfo(eniOk, accountPaymentInfoWrapper.isKycRequired()),
                piOk.getRoutingInfo(),
                accountPaymentInfoWrapper.getKycInfo(),
                eniOk.getInfo().getPlayable());
    }

    @Override
    public AccountPaymentInfoWrapper getAccountPaymentInfo(Identity identity, AsciiString key) throws Exception {
        var req = GetAccountPaymentInfoRequest.newBuilder().setIdentity(identity);
        var uamFuture = uamServiceApi.getAccountPaymentInfo(req.build(), key).thenVerifyOk();
        var fraudReq = GetConfirmedKYCInfoRequest.newBuilder().setIdentity(identity);
        var fraudFuture = fraudServiceApi.getConfirmedKYCInfo(fraudReq.build(), key).thenVerifyOk();
        Futures.allAsList(uamFuture, fraudFuture).get();
        AccountPaymentInfo info = uamFuture.get().unpack().getInfo();
        var kycInfoResp = fraudFuture.get().unpack();
        return new AccountPaymentInfoWrapper(info, Mappers.getKycInfo(kycInfoResp.getKycInfo()), kycInfoResp.getKyc());
    }

    @Override
    public ApiResponse<GetAccountRoutingInfoResponse> getAccountRouting(ImmutableAccount account) throws Exception {
        var req = GetAccountRoutingInfoRequest.newBuilder()
                .setBrandName(account.getBrand().getName())
                .setByAccountId(AccountRoutingById.newBuilder().setId(account.getId().toString()));
        return uamServiceApi.getAccountRouting(req.build(), account.routingKey());
    }

    @Override
    public Map<Long, Long> getFixedOfferPurchaseCounts(ImmutableAccount account, String country, Collection<OfferTemplate> offers, boolean includePending, Transaction tx) {
        var fixedOfferPurchaseCounts = new HashMap<Long, Long>();
        try {
            Map<OfferTypeSpec, List<OfferTemplate>> fixedOffersByType = offers.stream()
                    .filter(offer -> offer.getType().isFixed())
                    .collect(groupingBy(OfferTemplate::getType));
            for (var offerEntry : fixedOffersByType.entrySet()) {
                var fixedOffers = offerEntry.getValue();
                if (fixedOffers.isEmpty()) {
                    continue;
                }
                var offerType = offerEntry.getKey();
                var today = now().toLocalDate();
                LocalDate from;
                Instant start;
                Instant end;
                var brand = account.getBrand().getName();
                switch (offerType) {
                    case FIXED_DAILY -> {
                        from = today.minusDays(1);
                        var startCfg = resolveCfg(props.FIXED_DAILY_OFFER_START_TIME, country, brand);
                        var currentTime = now().toLocalTime();
                        start = currentTime.isBefore(startCfg)
                                ? from.atTime(startCfg).toInstant(ZoneOffset.UTC)
                                : today.atTime(startCfg).toInstant(ZoneOffset.UTC);
                        end = start.plus(1, ChronoUnit.DAYS);
                    }
                    case FIXED_WEEKLY -> {
                        var startCfg = resolveCfg(props.FIXED_WEEKLY_OFFER_START_TIME, country, brand);
                        from = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                        start = now().toInstant(ZoneOffset.UTC).isBefore(from.atTime(startCfg).toInstant(ZoneOffset.UTC)) ? from.minusWeeks(1).atTime(startCfg).toInstant(ZoneOffset.UTC) : from.atTime(startCfg).toInstant(ZoneOffset.UTC);
                        end = start.plus(7, ChronoUnit.DAYS);
                    }
                    default -> throw new IllegalStateException("Unexpected value: " + offerType);
                }
                var offerCodes = fixedOffers.stream().map(OfferTemplate::getCode).collect(toSet());
                var purchaseCountByOfferId = ebean.paymentRepo().ordersByOffers(account, offerCodes, includePending, from, FIXED_OFFER_FETCH, tx)
                        .stream()
                        .filter(o -> {
                            var createdAt = o.getCreatedAt().toInstant();
                            return createdAt.isAfter(start) && createdAt.isBefore(end);
                        })
                        .collect(Collectors.groupingBy(o -> o.getOffer().getId(), counting()));
                log.debug("Found {} offer purchases with codes [{}] in range [{} - {}]: {}", offerType, offerCodes, start, end, purchaseCountByOfferId);
                fixedOfferPurchaseCounts.putAll(purchaseCountByOfferId);
            }
        } catch (Exception e) {
            log.error("Failed to get fixed offer purchase counts", e);
        }
        return fixedOfferPurchaseCounts;
    }

    public LocalDateTime now() {
        return LocalDateTime.now(ZoneOffset.UTC);
    }

    private static PurchaseLimit applyPurchaseLimit(LocalDate date, AccountPurchaseLimit lim, BigDecimal total) {
        return new PurchaseLimit(
                lim.getThreshold().subtract(total).compareTo(BigDecimal.ZERO) < 0
                        ? BigDecimal.valueOf(0.0)
                        : lim.getThreshold().subtract(total),
                lim.getThreshold(),
                lim.getPeriod(),
                lim.getPeriod().calculateTo(date).plusDays(1));
    }

    private boolean isEligibleToShow(OfferTemplate offer,
                                     List<OfferTemplate> brandOffers,
                                     ImmutableAccount account,
                                     List<OfferTemplate> appliedOffers,
                                     int purchasesCount,
                                     Date timestamp,
                                     LocalDate at,
                                     List<AccountOfferMetaInfo> offerMetaInfos,
                                     BigInteger offerPurchaseCount, AccountInfoContext ctx, Transaction tx) throws Exception {
        int xpLevel;
        if (CollectionUtils.isNotEmpty(offer.getXpLevels())) {
            xpLevel = ctx.engagementInfo().getXpLevel();
        } else {
            xpLevel = 0;
        }

        Map<String, String> initialTrafficSourceMap = OfferTemplateUtil.parseInitialTrafficSource(ctx.engagementInfo().getInitialTrafficSource());


        List<AccountCategory> accountCategories = ctx.engagementInfo().getAccountCategories();
        if (!offer.getType().isPersonalized()) {
            var fixedOfferPurchaseCount = getFixedOfferPurchaseCount(account, ctx.country(), offer, true, tx);
            boolean isValid = OffersFilterService.isValidPurchaseOffer(offer, account, accountCategories, appliedOffers,
                    purchasesCount, timestamp, xpLevel, offerMetaInfos, offerPurchaseCount, fixedOfferPurchaseCount);
            if (isValid && offer.getType().isReward()) {
                return ebean.offerRewardRepo().isOfferRewardActive(account, offer, tx);
            }

            if (isValid && offer.isWelcomeOffer()) {

                return OffersFilterService.findBestWelcomeOffer(initialTrafficSourceMap, List.of(offer))
                        .map(OfferTemplate::getId)
                        .map(id -> id.equals(offer.getId()))
                        .orElse(false);
            }
            return isValid;
        }

        Optional<BigDecimal> weeklyWageredGold = getAvailableWeeklyWageredGold(at, account, tx);
        return OffersFilterService.isValidPersonalizedOffer(brandOffers, weeklyWageredGold.orElse(null), offer, account, accountCategories, appliedOffers,
                purchasesCount, timestamp, xpLevel, offerMetaInfos, offerPurchaseCount);
    }

    private boolean checkUpgradeEligibility(List<OfferTemplate> brandOffers,
                                            Long upgradeOfferId,
                                            ImmutableAccount account,
                                            List<OfferTemplate> appliedOffers,
                                            int purchasesCount,
                                            Date timestamp,
                                            LocalDate at,
                                            List<AccountOfferMetaInfo> offerMetaInfos, AccountInfoContext ctx) throws Throwable {
        boolean eligible = false;
        BigInteger offerPurchaseCount = BigInteger.ZERO;
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            List<OfferTemplate> offersThatCouldBeUpgraded = brandOffers.stream()
                    .filter(v -> v.getUpgradeId() != null && v.getUpgradeId().equals(upgradeOfferId)).toList();
            for (OfferTemplate offerTemplate : offersThatCouldBeUpgraded) {
                if (offerTemplate.capacityPerOffer().isPresent()) {
                    offerPurchaseCount = paymentRepo().getOfferPurchaseCount(offerTemplate.getId(), tx).map(AccountOfferMetaInfo::getTotalPurchaseCount)
                            .orElse(BigInteger.ZERO);
                }
                boolean eligibleToShow = isEligibleToShow(offerTemplate, brandOffers, account, appliedOffers, purchasesCount, timestamp, at,
                        offerMetaInfos,
                        offerPurchaseCount, ctx, tx);
                if (eligibleToShow) {
                    return true;
                }
            }
        }
        return eligible;
    }

    private static LocalTime resolveCfg(ScopedProperty<LocalTime> cfg, String country, String brand) {
        var defaultCfg = cfg.get();
        var brandCountryCfg = cfg.get(brand + "_" + country);
        if (!Objects.equals(defaultCfg, brandCountryCfg)) {
            return brandCountryCfg;
        }
        var countryCfg = cfg.get(country);
        if (!Objects.equals(defaultCfg, countryCfg)) {
            return countryCfg;
        }
        return cfg.get(brand);
    }

}
