package payment.services.order;

import static payment.services.util.PaymentOrderErrors.ERR_PURCHASE_IN_PROGRESS;

import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Objects;
import java.util.Optional;

import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;

import api.v1.ApplicationException;
import io.ebean.FetchGroup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import payment.PaymentJpaManager;
import payment.PaymentOrderAllowanceService;
import payment.PaymentServerProperties;
import payment.context.order.CreatePaymentOrderContext;
import payment.model.PaymentOrder;
import payment.model.query.QPaymentOrder;
import payment.model.query.QProvider;
import uam.api.v1.PaymentProvider;

@Slf4j
@RequiredArgsConstructor
public class DefaultPaymentOrderAllowanceService implements PaymentOrderAllowanceService {

    private final PaymentServerProperties props;
    private final PaymentJpaManager ebean;

    @Override
    public void assertPendingOrders(CreatePaymentOrderContext ctx) throws Throwable {
        if (ctx.offerCode().isEmpty() || ctx.isInAppPurchase()) {
            return;
        }
        if (isBonusAbuseAllowed(ctx)){
            log.debug("Bonus abuse is allowed");
            return;
        }
        var accountId = ctx.accountId();
        var offerCode = ctx.offerCode().get();
        var provider = ctx.provider();
        var providerCode = provider.name().toLowerCase();
        var now = Instant.now().atOffset(ZoneOffset.UTC);
        var today = now.toLocalDate();
        var yesterday = today.minusDays(1);
        Optional<PaymentOrder> lastPendingOrder;
        try (var tx = ebean.newReadOnlyTransaction()) {
            var account = ebean.accountRepo().requiredAccount(accountId, tx);
            lastPendingOrder = ebean.paymentRepo().latestPendingOrderByOffer(account, offerCode, yesterday, today, fetchGroup(), tx);
        }
        if (lastPendingOrder.isEmpty()) {
            log.debug("No pending orders found for account:[{}], provider:[{}], range:[{}-{}]", accountId, providerCode, yesterday, today);
            return;
        }
        if (!isWithinProviderSession(accountId, lastPendingOrder.get(), ctx, now)) {
            return;
        }
        ctx.setError(ERR_PURCHASE_IN_PROGRESS.getCode(), ERR_PURCHASE_IN_PROGRESS.getApiError());
        throw ApplicationException.of(ERR_PURCHASE_IN_PROGRESS.getApiError(), ERR_PURCHASE_IN_PROGRESS.getCode());
    }

    private boolean isWithinProviderSession(long accountId, PaymentOrder order, CreatePaymentOrderContext ctx, OffsetDateTime now) {
        var sessionTimeout = resolveSessionTimeout(ctx);
        var from = now.minus(sessionTimeout);
        boolean withinProviderSession = order.getCreatedAt().toInstant().isAfter(from.toInstant());
        if (!withinProviderSession) {
            var providerCode = order.getProvider().getCode();
            var integrationType = order.getProvider().getIntegrationType().code();
            log.debug("Last pending order is not within session window: account: {}, provider: {}-{} orderId: {}, createdAt: {}, window: {}-{}",
                    accountId, providerCode, integrationType, order.getId(), order.getCreatedAt(), from.toLocalTime(), now.toLocalTime());
        }
        return withinProviderSession;
    }

    boolean isBonusAbuseAllowed(CreatePaymentOrderContext ctx) {
        if (ctx.provider() == PaymentProvider.SKRILL && Boolean.FALSE.equals(props.BONUS_ABUSE_ALLOWED.get(ctx.brand()))) {
            return false;
        }
        return props.GLOBAL_BONUS_ABUSE_ALLOWED.get(ctx.brand());
    }

    private static FetchGroup<PaymentOrder> fetchGroup() {
        return FetchGroup.of(PaymentOrder.class).select(Joiner.on(',')
                .join(ImmutableList.builder()
                        .add(QPaymentOrder.alias().pk.toString())
                        .add(QPaymentOrder.alias().createdAt.toString())
                        .add(QProvider.alias().code.toString())
                        .build()))
                .build();
    }

    private Duration resolveSessionTimeout(CreatePaymentOrderContext ctx) {
        var providerCode = ctx.providerName();
        var integrationType = ctx.integrationType();
        var defaultProviderCfg = props.PROVIDER_SESSION_TIMEOUT.get();
        var providerCfg = props.PROVIDER_SESSION_TIMEOUT.get(providerCode);
        var duration = !Objects.equals(defaultProviderCfg.getSeconds(), providerCfg.getSeconds())
                ? providerCfg
                : props.PROVIDER_SESSION_TIMEOUT.get(integrationType);
        log.debug("Current session timeout for {}-{} is {} minutes", providerCode, integrationType, duration.toMinutes());
        return duration;
    }

}
