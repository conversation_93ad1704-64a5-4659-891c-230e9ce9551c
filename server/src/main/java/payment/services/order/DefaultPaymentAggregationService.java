package payment.services.order;

import common.utils.ProtoUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import io.ebean.Transaction;
import jakarta.inject.Inject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import payment.PaymentJpaManager;
import payment.api.v1.PaymentOrderCompletedEvent;
import payment.api.v1.RedeemConfirmEvent;
import payment.api.v1.RedeemPreConfirmEvent;
import payment.model.PaymentAggregatedMetaInfo;
import payment.model.PaymentOrder;
import payment.model.PaymentOrderMetaInfo;
import payment.model.WithdrawMoneyRequestMetaInfo;

@Slf4j
@Service
public class DefaultPaymentAggregationService implements PaymentAggregationService {
    private final PaymentJpaManager ebean;

    @Inject
    public DefaultPaymentAggregationService(PaymentJpaManager ebean) {
        this.ebean = ebean;
    }

    @Override
    public void updateDailyAggregation(PaymentOrderCompletedEvent event) throws Throwable {
        if (!event.getSuccess()) {
            return;
        }
        try (var tx = ebean.newTransaction()) {
            ebean.paymentRepo().orderMetaInfo(event.getPaymentOrderId(), tx)
                    .filter(PaymentOrderMetaInfo::isSuccessful)
                    .filter(orderMetaInfo -> BooleanUtils.isFalse(orderMetaInfo.isAggregated()))
                    .ifPresent(orderMetaInfo -> {
                        var order = orderMetaInfo.getOrder();
                        var aggregation = getPurchaseAggregation(tx, order, event.getPaymentRouting().getCountry());
                        aggregation.applyPurchase(order.getAmount());
                        orderMetaInfo.setAggregated(true);
                        ebean.save(aggregation, tx);
                        ebean.save(orderMetaInfo, tx);
                    });
            tx.commit();
        }
    }

    @Override
    public void updateDailyAggregation(RedeemConfirmEvent event) throws Throwable {
        var redeemId = event.getRedeem().getId();
        var country = event.getPaymentRouting().getCountry();
        try (var tx = ebean.newTransaction()) {
            ebean.paymentRepo().withdrawMoneyRequestMetaInfo(redeemId, tx)
                    .filter(WithdrawMoneyRequestMetaInfo::isConfirmed)
                    .filter(metaInfo -> BooleanUtils.isFalse(metaInfo.isAggregated()))
                    .ifPresent(metaInfo -> {
                        var redeem = metaInfo.getWithdraw();
                        var at = redeem.getAt();
                        var provider = redeem.getProvider();
                        var currency = event.getRedeem().getCurrency();
                        var aggregation = ebean.paymentRepo()
                                .aggregatedMetaInfo(country, provider, currency, at, tx)
                                .orElse(new PaymentAggregatedMetaInfo(provider, country, currency, at));
                        aggregation.applyWithdraw(redeem.getAmount());
                        metaInfo.setAggregated(true);
                        ebean.save(aggregation, tx);
                        ebean.save(metaInfo, tx);
                    });
            tx.commit();
        }
    }

    @Override
    public void updatePreconfirmedDailyAggregation(RedeemPreConfirmEvent event) throws Throwable {
        var redeemId = event.getRedeem().getId();
        var country = event.getPaymentRouting().getCountry();
        try (var tx = ebean.newTransaction()) {
            ebean.paymentRepo().withdrawRequest(redeemId, tx).ifPresent(redeem -> {
                var at = redeem.getAt();
                Long providerId = ProtoUtils.getNullableValue(event.getRedeem().getProviderId());
                if (providerId != null) {
                    var currency = redeem.getLocalCurrency();
                    var aggregation = ebean.paymentRepo()
                            .aggregatedMetaInfo(country, providerId.intValue(), currency, at, tx)
                            .orElse(new PaymentAggregatedMetaInfo(ebean.providerRepo().findPaymentProviderById(providerId.intValue(), tx), country, currency, at));
                    aggregation.applyPreconfirmedWithdraw(redeem.getAmount());
                    ebean.save(aggregation, tx);
                    ebean.paymentRepo().withdrawMoneyRequestMetaInfo(redeem.getId(), tx)
                            .ifPresentOrElse(_ ->
                                    log.warn("WithdrawMoneyRequestMetaInfo was saved before preconfirm [" + redeem.getId() + "]"), () -> {
                                var metaInfo = new WithdrawMoneyRequestMetaInfo();
                                metaInfo.setWithdraw(redeem);
                                ebean.save(metaInfo);
                            });
                    tx.commit();
                } else {
                    log.warn("Provider ID is null for redeem pre-confirmation event: {}", event);
                }
            });
        }
    }

    @SneakyThrows
    private PaymentAggregatedMetaInfo getPurchaseAggregation(Transaction tx, PaymentOrder order, String country) {
        return ebean.paymentRepo()
                .aggregatedMetaInfo(order, country, tx)
                .orElse(new PaymentAggregatedMetaInfo(order.getProvider(), country, order.getCurrency(), order.getAt()));
    }
}
