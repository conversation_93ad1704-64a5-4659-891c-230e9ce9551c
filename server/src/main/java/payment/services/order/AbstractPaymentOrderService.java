package payment.services.order;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import com.turbospaces.common.PlatformUtil;
import com.turbospaces.rpc.QueuePostTemplate;

import api.v1.AccountRoutingInfo;
import api.v1.ApiFactory;
import api.v1.CommonMappers;
import api.v1.PlatformSpec;
import common.utils.IdentityUtil;
import common.utils.ProtoUtils;
import identity.GeoLocationManager;
import io.ebean.Transaction;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import payment.PaymentJpaManager;
import payment.PaymentOrderSeq;
import payment.PaymentServerProperties;
import payment.api.PaymentServiceApi;
import payment.api.v1.PaymentOrderCompletedEvent;
import payment.api.v1.PaymentOrderCreatedEvent;
import payment.api.v1.PurchaseSuccessEvent;
import payment.api.v1.UpdateInboxNotificationOnOrderCompleteRequest;
import payment.api.v1.UpdateInboxNotificationOnOrderRefreshRequest;
import payment.context.account.AccountEngagementInfo;
import payment.dto.mappers.Mappers;
import payment.dto.mappers.ReplicationMappers;
import payment.model.AccountCardAggregation;
import payment.model.AccountMetaInfo;
import payment.model.AccountPaymentAggregation;
import payment.model.AccountPaymentMethod;
import payment.model.PaymentOrder;
import payment.model.PaymentOrderMetaInfo;
import payment.model.QuickPurchaseTypeSpec;
import payment.model.ScaAuthentication;
import payment.services.InboxNotificationService;
import payment.services.PromotionNotificationStatusService;
import payment.services.account.PaymentAccountService;
import payment.services.ctx.CreatePaymentOrderContext;
import payment.type.PaymentMethodTypeSpec;
import uam.api.UamServiceApi;
import uam.api.v1.AccountPaymentInfo;
import uam.api.v1.AccountPersonalInfo;
import uam.api.v1.CreatePaymentOrderRequest;
import uam.api.v1.SetRefundInfoRequest;
import uam.api.v1.UpdateAccountsPaymentMethodMetaInfoRequest;
import uam.api.v1.WalletDepositRequest;
import uam.api.v1.WalletDepositResponse;
import uam.api.v1.WalletTransactionInfo;
import uam.api.v1.internal.AccountPaymentRoutingInfo;
import uam.api.v1.internal.OfferRefundEvent;
import uam.model.WalletSessionTypeSpec;

@Slf4j
public abstract class AbstractPaymentOrderService implements PaymentOrderService {
    protected final QueuePostTemplate<?> postTemplate;
    protected final UamServiceApi uamServiceApi;
    protected final PaymentServiceApi paymentServiceApi;
    protected final PaymentJpaManager ebean;
    protected final ApiFactory apiFactory;
    protected final PaymentAccountService accountService;
    protected final GeoLocationManager geoLocationManager;
    protected final CurrencyRateService currencyRateService;
    protected PaymentServerProperties props;
    protected final InboxNotificationService inboxNotificationService;
    protected final PromotionNotificationStatusService promotionNotificationStatusService;

    @Inject
    public AbstractPaymentOrderService(
            PaymentServerProperties props,
            QueuePostTemplate<?> postTemplate,
            UamServiceApi uamServiceApi,
            PaymentServiceApi paymentServiceApi,
            PaymentJpaManager ebean,
            ApiFactory apiFactory,
            PaymentAccountService accountService,
            GeoLocationManager geoLocationManager,
            CurrencyRateService currencyRateService,
            InboxNotificationService inboxNotificationService,
            PromotionNotificationStatusService promotionNotificationStatusService) {
        this.props = Objects.requireNonNull(props);
        this.postTemplate = Objects.requireNonNull(postTemplate);
        this.uamServiceApi = Objects.requireNonNull(uamServiceApi);
        this.paymentServiceApi = Objects.requireNonNull(paymentServiceApi);
        this.ebean = Objects.requireNonNull(ebean);
        this.apiFactory = Objects.requireNonNull(apiFactory);
        this.accountService = Objects.requireNonNull(accountService);
        this.geoLocationManager = Objects.requireNonNull(geoLocationManager);
        this.currencyRateService = Objects.requireNonNull(currencyRateService);
        this.inboxNotificationService = Objects.requireNonNull(inboxNotificationService);
        this.promotionNotificationStatusService = Objects.requireNonNull(promotionNotificationStatusService);
    }

    //
    // ~ refund
    //

    @Override
    public void onPaymentOrderRefund(OfferRefundEvent refund) throws Exception {
        var routingKey = AsciiString.of(refund.getPaymentAccountInfo().getRoutingKey());
        paymentServiceApi.setRefundInfo(SetRefundInfoRequest.newBuilder().setEvent(refund).build(), routingKey)
                .thenVerifyOk().get();
    }

    //
    // ~ create
    //

    @Override
    public PaymentOrder createOrder(CreatePaymentOrderContext context) throws Throwable {
        var req = context.request();
        var metadata = context.request().getMetadata();
        // todo create and set within context
        var order = new PaymentOrder();
        order.setAccount(context.account());
        order.setAt(context.at());
        if (req.getAt() != 0) {
            order.setRequestedAt(new Date(req.getAt()));
        }
        ProtoUtils.getOptionalValue(req.getSourceId()).ifPresent(order::setSourceId);
        order.setUserAgent(req.getAgent().getUserAgent());
        order.setReferer(req.getReferrer());
        order.setPlatform(ProtoUtils.getOptionalValue(metadata.getPlatform()).map(PlatformSpec::fromString).orElse(IdentityUtil.platform(req.getIdentity())));
        order.setTransactionId(UUID.fromString(req.getTransactionId()));
        order.setCode(req.getTransactionId()); // fake code - will be updated later
        order.setOrderSn(StringUtils.lowerCase(PlatformUtil.randomAlphanumeric(props.ORDER_SN_LENGTH.get())));
        ProtoUtils.applyIfNotEmpty(metadata.getAppName(), order::setAppName);
        ProtoUtils.applyIfNotEmpty(metadata.getAppVersion(), order::setAppVersion);
        enhanceWithIpInfo(req, order);
        if (StringUtils.isNotBlank(req.getQuickPurchaseSupportedType())) {
            order.setQuickPurchaseSupportedType(QuickPurchaseTypeSpec.fromString(req.getQuickPurchaseSupportedType()));
        }

        return order;
    }

    @Override
    public void onCreate(PaymentOrder order) throws Throwable {
        var event = Mappers.toPaymentOrderCreatedEvent(order).build();
        try {
            onPaymentOrderCreated(event);
        } catch (Exception ex) {
            log.error("Error while creating payment order meta info", ex);
        }
        postTemplate.sendEvent(event, order.getAccount().routingKey());
    }

    @Override
    public void onPaymentOrderCreated(PaymentOrderCreatedEvent event) throws Throwable {
        PaymentOrder order;
        try (var tx = ebean.newTransaction()) {
            order = ebean.paymentRepo().requiredOrder(event.getPaymentOrderId(), tx);
            var meta = order.getAccount().paymentMetaInfo().orElse(new AccountMetaInfo(order.getAccount()));
            meta.setFirstOrder(order);
            ebean.save(meta, tx);
            order.getAccount().setPaymentMetaInfo(meta);
            tx.commit();
        }
    }

    @Override
    public void onRefresh(PaymentOrder order, AccountPaymentRoutingInfo routing) throws Throwable {
        processInboxNotificationsOnRefresh(order, routing);
    }

    //
    // ~ complete
    //

    @Override
    public void onComplete(PaymentOrder order, AccountPaymentRoutingInfo routingInfo, AccountEngagementInfo engagementInfo) throws Throwable {
        if (order.getInternalStatus().isPending() || order.getInternalStatus().isCreated()) {
            return;
        }
        AccountPaymentRoutingInfo invaitedRoutingInfo = null;
        if (order.getAccount().invitedBy().isPresent()) {
            AccountRoutingInfo invaitedAccountRoutingInfo = accountService.getAccountRouting(order.getAccount().getInvitedBy()).get().unpackAndVerifyOk().getRouting();
            invaitedRoutingInfo = Mappers.toAccountPaymentRoutingInfo(invaitedAccountRoutingInfo, invaitedAccountRoutingInfo.getCountry()).build();
        }
        var event = Mappers
                .toPaymentOrderCompletedEvent(order.getAccount(), order, routingInfo, invaitedRoutingInfo)
                .build();
        try {
            onPaymentOrderCompleted(event);
        } catch (Exception ex) {
            log.error("Error while completing payment order", ex);
        }
        String scaAuthenticateTxId = Optional.of(order).map(PaymentOrder::getScaAuthentication).map(ScaAuthentication::getToken).orElse(null);
        if (scaAuthenticateTxId != null && order.getInternalStatus().isSuccess()) {
            try {
                updateThreeDsData(scaAuthenticateTxId);
            } catch (Exception ex) {
                log.error("Error while updating sca data", ex);
            }
        }
        processInboxNotificationsOnComplete(order, event.getPaymentRouting());
        postTemplate.sendEvent(event, order.getAccount().routingKey());
    }

    @Override
    public void onPaymentOrderCompleted(PaymentOrderCompletedEvent event) throws Throwable {
        PaymentOrder order;
        PaymentOrderMetaInfo orderMetaInfo;
        try (var tx = ebean.newReadOnlyTransaction()) {
            order = ebean.paymentRepo().requiredOrder(event.getPaymentOrderId(), tx);
            orderMetaInfo = ebean.paymentRepo().orderMetaInfo(event.getPaymentOrderId(), tx).orElse(new PaymentOrderMetaInfo());
        }
        boolean successfulPurchase = event.getSuccess();
        AccountMetaInfo meta = order.getAccount().paymentMetaInfo().orElse(new AccountMetaInfo(order.getAccount()));

        if (successfulPurchase && BooleanUtils.isFalse(orderMetaInfo.isSuccessful())) {
            onSuccessPurchase(order, orderMetaInfo, meta, event.getPaymentRouting());
            sendPurchaseSuccessEvent(order, meta);
            promotionNotificationStatusService.updateNotificationState(order.getAccount(), event.getPaymentRouting());

        } else if (BooleanUtils.isFalse(successfulPurchase) && BooleanUtils.isFalse(orderMetaInfo.isFailed())) {
            onFailedPurchase(order, orderMetaInfo);
            savePaymentMetaInfoForFailedOnProviders(meta, order);
        } else {
            log.info("Duplicated event for order {}", order.getId());
        }
    }

    protected void processInboxNotificationsOnComplete(PaymentOrder order, AccountPaymentRoutingInfo accountRoutingInfo) throws Throwable {
        UpdateInboxNotificationOnOrderCompleteRequest request = UpdateInboxNotificationOnOrderCompleteRequest.newBuilder()
                .setOrderId(order.getId())
                .setPaymentRouting(accountRoutingInfo)
                .build();
        paymentServiceApi.updateInboxNotificationOnOrderComplete(request, order.getAccount().routingKey());
    }
    protected void processInboxNotificationsOnRefresh(PaymentOrder order, AccountPaymentRoutingInfo accountRoutingInfo) throws Throwable {
        UpdateInboxNotificationOnOrderRefreshRequest request = UpdateInboxNotificationOnOrderRefreshRequest.newBuilder()
                .setOrderId(order.getId())
                .setPaymentRouting(accountRoutingInfo)
                .build();
        paymentServiceApi.updateInboxNotificationOnOrderRefresh(request, order.getAccount().routingKey());
    }

    protected abstract void onSuccessPurchase(PaymentOrder order, PaymentOrderMetaInfo orderMetaInfo,
            AccountMetaInfo meta, AccountPaymentRoutingInfo paymentRoutingInfo) throws Throwable;

    protected void setAmounts(PaymentOrder order, AccountPaymentInfo paymentInfo, BigDecimal baseAmount) throws Throwable {
        AccountPersonalInfo personalInfo = paymentInfo.getPersonalInfo();
        order.setAmount(currencyRateService.priceByRate(order.getAccount().getBrand().getName(), personalInfo.getCountry(), personalInfo.getCurrency(), baseAmount));
        order.setBaseAmount(baseAmount);
        order.setCurrency(currencyRateService.resolveCurrency(paymentInfo));
    }

    protected void savePurchaseAggregation(PaymentOrder order, Transaction tx) {
        LocalDate orderDate = order.getAt();
        var agg = ebean.paymentAggregationRepo().accountAggregation(order, orderDate, tx)
                .orElseGet(() -> new AccountPaymentAggregation(order, orderDate));
        agg.addOrderPurchaseAggregation(order);
        ebean.paymentAggregationRepo().save(agg, tx);
        // saving total
        var totalAgg = ebean.paymentAggregationRepo().accountAggregation(order, null, tx)
                .orElseGet(() -> new AccountPaymentAggregation(order, null));
        totalAgg.addOrderPurchaseAggregation(order);
        ebean.paymentAggregationRepo().save(totalAgg, tx);
    }

    protected void saveCardAggregation(PaymentOrder order, Transaction tx) {
        var orderDate = order.getAt();

        var executionDate = props.ACCOUNT_CARD_AGGREGATION_EXECUTION_DATE.get();
        if (orderDate.isBefore(executionDate)) {
            log.debug("Execution skipped. The method can only be executed after: %s".formatted(executionDate));
            return;
        }

        boolean isValidPaymentMethod = Optional.ofNullable(order.getPaymentMethod())
                .filter(paymentMethod -> StringUtils.isNotBlank(paymentMethod.getFingerprint()))
                .map(AccountPaymentMethod::getCardPaymentMethod)
                .filter(cardPaymentMethod -> StringUtils.isNoneBlank(cardPaymentMethod.getBin(), cardPaymentMethod.getLastFour()))
                .isPresent();

        if (!isValidPaymentMethod) {
            log.warn("Found order with not valid payment method for account card aggregation purchase. Order id: {}", order.getId());
            return;
        }

        // saving aggregation
        var agg = ebean.cardAggregationRepo().cardAggregation(order, order.getAt(), tx)
                .orElseGet(() -> new AccountCardAggregation(order, orderDate));
        agg.addCardAggregation(order);
        ebean.cardAggregationRepo().save(agg, tx);

        // saving total aggregation
        var totalAgg = ebean.cardAggregationRepo().cardAggregation(order, null, tx)
                .orElseGet(() -> new AccountCardAggregation(order, null));
        totalAgg.addCardAggregation(order);
        ebean.cardAggregationRepo().save(totalAgg, tx);
    }

    private void enhanceWithIpInfo(CreatePaymentOrderRequest req, PaymentOrder order) throws Exception {
        if (req.getIdentity().hasByToken()) {
            String remoteIp = req.getIdentity().getByToken().getRemoteIp();
            order.setRemoteIp(remoteIp);
            geoLocationManager.city(remoteIp).ifPresent(resp -> {
                order.setRemoteIpCountry(resp.getCountry().getIsoCode());
                if (CollectionUtils.isNotEmpty(resp.getSubdivisions())) {
                    order.setRemoteIpState(resp.getSubdivisions().get(0).getIsoCode());
                }
            });
        } else {
            order.setRemoteIp("127.0.0.1");
        }
    }

    private void updateThreeDsData(String scaAuthenticateTxId) throws Throwable {
        try (var tx = ebean.newTransaction()) {
            var sca = ebean.paymentRepo().requiredSCAAuthenticationByToken(scaAuthenticateTxId, tx);
            if (sca.isUsed()) {
                log.error("SCA authenticate tx [{}] status is used on order completion", scaAuthenticateTxId);
            }
            sca.setUsed(true);
            ebean.paymentRepo().save(sca, tx);
            tx.commit();
        }
    }

    private void savePaymentMetaInfoForFailedOnProviders(AccountMetaInfo meta, PaymentOrder order) throws Throwable {
        try (var tx = ebean.newTransaction()) {
            if (order.getError() != null && order.getError().isFailedOnProvider()) {
                meta.setLastFailedOnProviderPaymentOrder(order);
                ebean.save(meta, tx);
                tx.commit();
            }
        }
    }

    protected void addAccountPaymentTags(PaymentOrder order) {
        AccountMetaInfo meta = order.getAccount().getPaymentMetaInfo();
        String provider = order.getProviderName();

        if (Objects.isNull(meta.getProvidersPurchase())) {
            meta.setProvidersPurchase(List.of(provider));
        } else {
            AccountMetaInfo.addIfAbsent(provider, meta.getProvidersPurchase());
        }

        order.paymentMethod().ifPresent(pm -> {
            if (pm.getType().equals(PaymentMethodTypeSpec.SKRILL.getCode())) {
                if (StringUtils.isNotEmpty(pm.getFingerprint())) {
                    String skrillEmail = pm.getFingerprint();

                    if (Objects.isNull(meta.getSkrillEmails())) {
                        meta.setSkrillEmails(List.of(skrillEmail));
                    } else {
                        AccountMetaInfo.addIfAbsent(skrillEmail, meta.getSkrillEmails());
                    }
                }
            } else {
                var cardDetails = pm.getCardPaymentMethod();
                if (Objects.nonNull(cardDetails)) {
                    String holderName = cardDetails.getHolderName();
                    String cardLastFour = cardDetails.getLastFour();
                    String cardBin = cardDetails.getBin();

                    if (StringUtils.isNotEmpty(holderName)) {
                        if (Objects.isNull(meta.getCardHolderNames())) {
                            meta.setCardHolderNames(List.of(holderName));
                        } else {
                            AccountMetaInfo.addIfAbsent(holderName, meta.getCardHolderNames());
                        }
                    }

                    if (StringUtils.isNotEmpty(cardLastFour)) {
                        if (Objects.isNull(meta.getCardLastFours())) {
                            meta.setCardLastFours(List.of(cardLastFour));
                        } else {
                            AccountMetaInfo.addIfAbsent(cardLastFour, meta.getCardLastFours());
                        }
                    }
                    if (StringUtils.isNotEmpty(cardBin)) {
                        if (Objects.isNull(meta.getCardBins())) {
                            meta.setCardBins(List.of(cardBin));
                        } else {
                            AccountMetaInfo.addIfAbsent(cardBin, meta.getCardBins());
                        }
                    }
                }
            }
        });
    }

    private void sendPurchaseSuccessEvent(PaymentOrder order, AccountMetaInfo meta) {
        PurchaseSuccessEvent purchaseEvent = ReplicationMappers.toPurchaseSuccessEvent(order, meta).build();
        postTemplate.sendEvent(purchaseEvent, AsciiString.cached(order.getAccount().getHash()));
    }

    private void onFailedPurchase(PaymentOrder order, PaymentOrderMetaInfo orderMetaInfo) throws Throwable {
        try (var tx = ebean.newTransaction()) {
            orderMetaInfo.complete(order);
            ebean.save(orderMetaInfo, tx);
            tx.commit();
        }
    }

    protected WalletDepositResponse depositWallet(PaymentOrder order) throws Exception {
        List<WalletTransactionInfo> trxs = new ArrayList<>();
        if (order.isDeposit()) {
            trxs.add(buildWalletTransaction(order, order.getAmount(), order.getCurrency()));
        }
        if (order.getGcAmount() != null && order.getGcAmount().compareTo(BigDecimal.ZERO) > 0) {
            trxs.add(buildWalletTransaction(order, order.getGcAmount(), order.getAccount().getBrand().getGoldCurrency()));
        }
        if (order.isSweepstake() && order.getScAmount() != null && order.getScAmount().compareTo(BigDecimal.ZERO) > 0) {
            trxs.add(buildWalletTransaction(order, order.getScAmount(), order.getAccount().getBrand().getSweepstakeCurrency()));
        }

        var req = WalletDepositRequest.newBuilder();
        req.addAllTransactions(trxs);
        req.setIdentity(Mappers.toAccountIdentity(order));
        req.setSessionId(order.getTransactionId().toString());
        req.setSource(order.getProvider().getCode());
        req.setAt(CommonMappers.toDate(order.getAt()));
        var routingKey = AsciiString.cached(order.getAccount().getHash());
        return uamServiceApi.createWalletDepositSession(req.build(), routingKey).get().unpackAndVerifyOk();
    }

    private static WalletTransactionInfo buildWalletTransaction(PaymentOrder order, BigDecimal amount, String currency) {
        var transaction = WalletTransactionInfo.newBuilder();
        transaction.setCurrency(currency);
        transaction.setAmount(amount.toString());
        transaction.setReference(order.getTransactionId().toString());
        transaction.setType(WalletSessionTypeSpec.PAYMENT.code());
        return transaction.build();
    }
}
