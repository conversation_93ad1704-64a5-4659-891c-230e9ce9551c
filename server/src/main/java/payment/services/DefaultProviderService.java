package payment.services;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.UriBasedServiceInfo;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.turbospaces.http.HttpProto;
import com.turbospaces.ups.UPSs;

import fraud.api.FraudServiceApi;
import io.ebean.Transaction;
import io.netty.handler.codec.http.QueryStringDecoder;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import payment.AccountCurrencyTypeInfo;
import payment.Feature;
import payment.PaymentJpaManager;
import payment.PaymentServerProperties;
import payment.ach.receiver.StandardAchReceiver;
import payment.api.v1.AeroPay;
import payment.api.v1.AndroidInApp;
import payment.api.v1.AppleInApp;
import payment.api.v1.Crypto;
import payment.api.v1.Fiserv;
import payment.api.v1.FiservApplePay;
import payment.api.v1.FiservGooglePay;
import payment.api.v1.Nuvei;
import payment.api.v1.Payper;
import payment.api.v1.PurchaseProviders;
import payment.api.v1.Skrill;
import payment.api.v1.Spreedly;
import payment.api.v1.SpreedlyApplePay;
import payment.api.v1.SpreedlyGooglePay;
import payment.api.v1.Trustly;
import payment.api.v1.WithdrawProviders;
import payment.dto.mappers.Mappers;
import payment.model.AccountProviderBlackList;
import payment.model.BrandSettings;
import payment.model.Provider;
import payment.model.ProviderIntegrationTypeSpec;
import payment.model.ProviderTypeSpec;
import payment.model.RoutingTypeSpec;
import payment.model.immutable.ImmutableAccount;
import payment.model.immutable.ImmutableBrand;
import payment.repo.ProviderRepo;
import payment.type.PaymentMethodTypeSpec;
import payment.type.PaymentMode;
import payment.type.PurchaseProviderSpec;
import payment.type.RedeemProviderSpec;
import uam.api.v1.PaymentProvider;

@Slf4j
@RequiredArgsConstructor
public class DefaultProviderService implements ProviderService {

    private static final String MERCHANT = "merchant";
    private static final String MERCHANT_ID = "merchantId";
    private static final String MERCHANT_NAME = "merchantName";
    private static final String GATEWAY = "gateway";

    private final PaymentJpaManager ebean;
    protected final FraudServiceApi fraudServiceApi;
    protected final PaymentServerProperties props;
    private final DynamicCloud cloud;
    private final List<StandardAchReceiver> standardAchReceivers;
    private final AccountCurrencyTypeService accountCurrencyTypeService;

    @Override
    public Optional<Provider> getWithdrawProviderByCode(ImmutableBrand brand, String code, Transaction tx) {
        var params = ProviderRepo.FindProviderParams.create()
                .withTypes(ProviderTypeSpec.WITHDRAW)
                .withCodes(code)
                .withBrands(brand.getName());
        return ebean.providerRepo().provider(params, tx);
    }

    @Override
    public List<Provider> getAvailableSpreedlyProviders(PaymentProvider gateway, String brand, String country, Transaction tx) {
        PaymentMode paymentMode = PurchaseProviderSpec.fromServerApi(gateway).getPaymentMode();
        List<PurchaseProviderSpec> purchaseProviderSpecs = PurchaseProviderSpec.forProviderMode(paymentMode);
        List<PurchaseProviderSpec> gatewayProviders = PurchaseProviderSpec.getGatewayProviders();
        Set<String> providerCodes = purchaseProviderSpecs.stream()
                .filter(p -> !gatewayProviders.contains(p) && p.getMethodType().isSpreedly())
                .map(spec -> spec.name().toLowerCase())
                .collect(Collectors.toSet());
        var params = ProviderRepo.FindProviderParams.create()
                .withBrands(brand)
                .withCountry(country)
                .withCodes(providerCodes)
                .withActive(Boolean.TRUE);
        List<Provider> availableProviders = ebean.providerRepo().providers(params, tx).toList();
        log.debug("available providers {}", availableProviders.stream().map(Provider::getCode).toList());
        return availableProviders;
    }

    @Override
    public Optional<Provider> getAvailablePurchaseProvider(ImmutableAccount account, String country, PaymentProvider provider) throws Throwable {
        AccountCurrencyTypeInfo currencyTypeInfo = accountCurrencyTypeService.resolveCurrencyUserInfo(account);

        PurchaseProviderSpec pps = PurchaseProviderSpec.fromServerApi(provider);
        if (!currencyTypeInfo.supports(pps)) {
            return Optional.empty();
        }

        //copied from CreatePaymentOrderRequestHandler resolveProvider
        var params = ProviderRepo.FindProviderParams.create()
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withCountry(country)
                .withBrands(account.getBrand().getName())
                .withCodes(provider.name().toLowerCase());
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            return ebean.providerRepo().provider(params, tx);

        }
        //todo to refactor/replace with following with all checks
        /*try (var tx = ebean.newReadOnlyTransaction()) {
            //discuss do we need score check?
            return getAvailablePurchaseProvidersForUser(account, account.getBrand().getName(), signupScore(account), country, Collections.singletonList(provider), tx).stream().findAny();
        }*/
    }

    @Override
    public Optional<Provider> getAvailableWithdrawalProvider(ImmutableAccount account, String country, RedeemProviderSpec spec) throws Throwable {
        AccountCurrencyTypeInfo currencyTypeInfo = accountCurrencyTypeService.resolveCurrencyUserInfo(account);
        if (!currencyTypeInfo.supports(spec)) {
            return Optional.empty();
        }
        try (var tx = ebean.newReadOnlyTransaction()) {
            //copied from RedeemMoneyRequestHandler resolveProvider
            var params = ProviderRepo.FindProviderParams.create()
                    .withTypes(ProviderTypeSpec.WITHDRAW)
                    .withCountry(country)
                    .withBrands(account.getBrand().getName())
                    .withCodes(spec.getMethodType().code());
            return ebean.providerRepo().provider(params, tx);
        }

        //todo to refactor/replace with following with all checks
        //return getAvailableWithdrawProvidersForUser(account.getBrand().getName(), account, signupScore(account), country, Collections.singletonList(spec.getMethodType().code()), tx).stream().findAny();
    }

    @Override
    public List<Provider> getAvailableTestSpreedlyProviders(String brand, String country) {
        Set<String> testProviderCodes = PurchaseProviderSpec.TEST_GATEWAY_PROVIDERS.stream()
                .map(spec -> spec.name().toLowerCase())
                .collect(Collectors.toSet());
        var params = ProviderRepo.FindProviderParams.create()
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withBrands(brand)
                .withCountry(country)
                .withCodes(testProviderCodes);
        List<Provider> availableProviders = ebean.providerRepo().providers(params).toList();
        log.debug("available providers {}", availableProviders.stream().map(Provider::getCode).toList());
        return availableProviders;
    }

    @Override
    public List<Provider> getActiveRedeemProviders(String brand, String country, Set<String> codes) {
        log.debug("Search providers by: brand=[{}], country=[{}], codes={}", brand, country, codes);
        var params = ProviderRepo.FindProviderParams.create()
                .withTypes(ProviderTypeSpec.WITHDRAW)
                .withBrands(brand)
                .withCountry(country)
                .withCodes(codes)
                .withActive(Boolean.TRUE);
        return ebean.providerRepo().providers(params)
                .filter(p -> p.getIntegrationType() != ProviderIntegrationTypeSpec.NSC) // NSC is manual provider used for big payouts, it should not be used for redeem routing
                .toList();
    }

    @Override
    public WithdrawProviders getAvailableWithdrawProviders(String brandName, ImmutableAccount account, int signUpFraudScore, String country) throws Throwable {
        WithdrawProviders.Builder reply = WithdrawProviders.newBuilder();
        BrandSettings brandSettings;
        List<Provider> providerList;
        try (var tx = ebean.newReadOnlyTransaction()) {
            var brand = ebean.brandRepo().requiredBrandByName(brandName, tx);
            brandSettings = ebean.brandRepo().getBrandSettings(brand, tx);
            providerList = getAvailableWithdrawProvidersForUser(brandName, account, signUpFraudScore, country, Lists.newArrayList(), tx);
        }
        hideStandardAchReceivers(providerList, standardAchReceivers);
        for (Provider p : providerList) {
            RedeemProviderSpec pps = RedeemProviderSpec.valueOf(p.getCode().toUpperCase());
            getRedeemSecret(brandName, country, pps).ifPresent(ups -> Mappers.addWithdrawProvider(reply, pps, brandSettings, ups));
        }
        return reply.build();
    }

    @Override
    public PurchaseProviders getAvailablePurchaseProviders(String brandName, ImmutableAccount account, int signUpFraudScore, String country) throws Throwable {
        List<Provider> providerList;
        try (var tx = ebean.newReadOnlyTransaction()) {
            providerList = getAvailablePurchaseProvidersForUser(account, brandName, signUpFraudScore, country, Collections.emptyList(), tx);
        }
        PurchaseProviders.Builder providers = PurchaseProviders.newBuilder();
        providerList.forEach(p -> {
                PurchaseProviderSpec pps = PurchaseProviderSpec.fromServerApi(p.getCode());
                switch (pps) {
                    case SPREEDLY -> getSecret(brandName, country, pps).ifPresent(ups -> {
                        if (routingChainExists(brandName, RoutingTypeSpec.SPREEDLY, account, country)) {
                            Spreedly.Builder spreedly = Spreedly.newBuilder();
                            spreedly.setId(ups.getUserName());
                            providers.setSpreedly(spreedly);
                            providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                        }
                    });
                    case FISERV -> getSecret(brandName, country, pps).ifPresent(ups -> {
                        Fiserv.Builder fiserv = Fiserv.newBuilder();
                        fiserv.setId(ups.getUserName());
                        providers.setFiserv(fiserv);
                        providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                    });
                    case FISERV_GOOGLE_PAY -> getSecret(brandName, country, pps).ifPresent(ups -> {
                        FiservGooglePay.Builder fiservGooglePayApp = getFiservGooglePayApp(ups);
                        providers.setFiservGooglePay(fiservGooglePayApp);
                        providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                    });
                    case SPREEDLY_GOOGLE_PAY -> getSecret(brandName, country, pps).ifPresent(ups -> {
                        if (routingChainExists(brandName, RoutingTypeSpec.SPREEDLY_GOOGLE_PAY, account, country)) {
                            SpreedlyGooglePay.Builder spreedlyGooglePayApp = getSpreedlyGooglePayApp(ups);
                            providers.setSpreedlyGooglePay(spreedlyGooglePayApp);
                            providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                        }
                    });
                    case FISERV_APPLE_PAY -> getSecret(brandName, country, pps).ifPresent(ups -> {
                        FiservApplePay.Builder fiservApplePayApp = getFiservApplePayApp(ups);
                        providers.setFiservApplePay(fiservApplePayApp);
                        providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                    });
                    case SPREEDLY_APPLE_PAY -> getSecret(brandName, country, pps).ifPresent(ups -> {
                        if (routingChainExists(brandName, RoutingTypeSpec.SPREEDLY_APPLE_PAY, account, country)) {
                            SpreedlyApplePay.Builder spreedlyApplePay = getSpreedlyApplePayApp(ups);
                            providers.setSpreedlyApplePay(spreedlyApplePay);
                            providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                        }
                    });
                    case CRYPTO -> {
                        if (Feature.CRYPTO.isEnabled(props, brandName, account.getId(), account.isAdmin())) {
                            if (Arrays.stream(PurchaseProviderSpec.values())
                                    .filter(spec -> spec.getMethodType() == PaymentMethodTypeSpec.CRYPTO)
                                    .filter(spec -> StringUtils.isNotEmpty(spec.getSecret()))
                                    .map(v -> getSecret(brandName, country, v))
                                    .findFirst()
                                    .isPresent()) {
                                providers.setCrypto(Crypto.newBuilder().build());
                                providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                            }
                        }
                    }
                    case SKRILL -> getSecret(brandName, country, pps).ifPresent(ups -> {
                        Skrill.Builder skapp = Skrill.newBuilder();
                        providers.setSkrill(skapp.build());
                        providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                    });
                    case TRUSTLY -> getSecret(brandName, country, pps).ifPresent(ups -> {
                        if (Feature.TRUSTLY.isEnabled(props, brandName, account.getId(), account.isAdmin())) {
                            ImmutableBrand brand = getBrand(brandName);
                            Trustly.Builder trustlyApp = getTrustlyApp(ups);
                            trustlyApp.setNotificationUrl(
                                ebean.brandRepo().getBrandSettings(brand, null).getPaymentWebhookUrl() + HttpProto.V1 + "/hook/trustly/" + brand.getName());
                            providers.setTrustly(trustlyApp.build());
                            providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                        }
                    });
                    case NUVEI_MAZOOMA_ACH -> getSecret(brandName, country, pps).ifPresent(ups -> {
                        Nuvei.Builder nvapp = Nuvei.newBuilder();
                        nvapp.setId(ups.getUserName());
                        providers.setNuvei(nvapp);
                        providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                    });
                    case APPLE_IN_APP -> getSecret(brandName, country, pps).ifPresent(_ -> {
                        AppleInApp.Builder aia = AppleInApp.newBuilder();
                        providers.setAppleInApp(aia);
                        providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                    });
                    case ANDROID_IN_APP -> getSecret(brandName, country, pps).ifPresent(_ -> {
                        AndroidInApp.Builder ania = AndroidInApp.newBuilder();
                        providers.setAndroidInApp(ania);
                        providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                    });
                    case PAYPER -> getSecret(brandName, country, pps).ifPresent(_ -> {
                        providers.setPayper(Payper.newBuilder());
                        providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                    });
                    case AEROPAY -> getSecret(brandName, country, pps).ifPresent(_ -> {
                        if (Feature.AEROPAY.isEnabled(props, brandName, account.getId(), account.isAdmin())) {
                            providers.setAeroPay(AeroPay.newBuilder());
                            providers.addEligibleProviders(pps.getPaymentProviderServerApi());
                        }
                    });
                }
            });
        return providers.build();
    }

    private List<Provider> getAvailablePurchaseProvidersForUser(ImmutableAccount account, String brandName, int signUpFraudScore, String country, List<PaymentProvider> limit, Transaction tx) {
        var providerCountryEnabled = providerCountryEnabled();
        var params = ProviderRepo.FindProviderParams.create()
                .withBrands(brandName)
                .withTypes(ProviderTypeSpec.PURCHASE);
        if (!limit.isEmpty()) {
            params.withCodes(limit.stream().map(p -> p.name().toLowerCase()).collect(Collectors.toSet()));
        }
        AccountProviderBlackList providerBlackListByAccount = ebean.paymentRepo().getAccountProviderBlackList(account.getId(), tx);
        return ebean.providerRepo().providers(params, tx)
                .filter(p -> p.isEligiblePaymentOrWithdraw(account, signUpFraudScore, props.PROVIDERS_LIST_DEPENDS_ON_PREVIOUS_FAILED_ON_PROVIDER.get()))
                .filter(p -> !providerCountryEnabled.get() || CollectionUtils.isEmpty(p.getCountries()) || p.getCountries().contains(country))
                .filter(p -> providerBlackListByAccount == null || providerBlackListByAccount.getPaymentMethods().stream()
                        .noneMatch(pm -> Objects.equals(pm, PurchaseProviderSpec.fromServerApi(p.getCode()).getMethodType())))
                .toList();
    }

    private Optional<UriBasedServiceInfo> getSecret(String brand, String country, PurchaseProviderSpec providerSpec) {
        Optional<UriBasedServiceInfo> secret = UPSs.findScopedServiceInfoByName(cloud, providerSpec.getSecret(), brand, country.toLowerCase());
        if (secret.isEmpty()) {
            log.warn("Secrets not found: brand =[{}], country=[{}], provider={}", brand, country, providerSpec);
        }
        return secret;
    }

    private static FiservGooglePay.Builder getFiservGooglePayApp(UriBasedServiceInfo ups) {
        QueryStringDecoder decoder = new QueryStringDecoder(ups.getUri());
        Map<String, List<String>> opts = decoder.parameters();
        String merchant = Iterables.getOnlyElement(opts.get(MERCHANT));
        String gateway = Iterables.getOnlyElement(opts.get(GATEWAY));
        String merchantId = Iterables.getOnlyElement(opts.get(MERCHANT_ID));
        String merchantName = Iterables.getOnlyElement(opts.get(MERCHANT_NAME));

        FiservGooglePay.Builder fiservGooglePay = FiservGooglePay.newBuilder();
        fiservGooglePay.setMerchant(merchant);
        fiservGooglePay.setGateway(gateway);
        fiservGooglePay.setMerchantId(merchantId);
        fiservGooglePay.setMerchantName(merchantName);
        return fiservGooglePay;
    }

    private static SpreedlyGooglePay.Builder getSpreedlyGooglePayApp(UriBasedServiceInfo ups) {
        QueryStringDecoder decoder = new QueryStringDecoder(ups.getUri());
        Map<String, List<String>> opts = decoder.parameters();
        String merchant = Iterables.getOnlyElement(opts.get(MERCHANT));
        String gateway = Iterables.getOnlyElement(opts.get(GATEWAY));
        String merchantId = Iterables.getOnlyElement(opts.get(MERCHANT_ID));
        String merchantName = Iterables.getOnlyElement(opts.get(MERCHANT_NAME));

        SpreedlyGooglePay.Builder spreedlyGooglePay = SpreedlyGooglePay.newBuilder();
        spreedlyGooglePay.setMerchant(merchant);
        spreedlyGooglePay.setGateway(gateway);
        spreedlyGooglePay.setMerchantId(merchantId);
        spreedlyGooglePay.setMerchantName(merchantName);
        return spreedlyGooglePay;
    }

    @SneakyThrows
    private ImmutableBrand getBrand(String brandName) {
        try (var tx = ebean.newReadOnlyTransaction()) {
            return ebean.brandRepo().requiredBrandByName(brandName, tx);
        }
    }
    private boolean routingChainExists(String brandName, RoutingTypeSpec type, ImmutableAccount account, String country) {
        return account.isAdmin() || hasAvailableSpreedlyProviders(PaymentProvider.valueOf(type.name()), brandName, country, null);
    }
    private static Trustly.Builder getTrustlyApp(UriBasedServiceInfo ups) {
        var decoder = new QueryStringDecoder(ups.getUri());
        Map<String, List<String>> opts = decoder.parameters();
        String merchantId = Iterables.getOnlyElement(opts.get(MERCHANT_ID));

        var trustly = Trustly.newBuilder();
        trustly.setId(ups.getUserName());
        trustly.setMerchantId(merchantId);
        return trustly;
    }

    private static FiservApplePay.Builder getFiservApplePayApp(UriBasedServiceInfo ups) {
        QueryStringDecoder decoder = new QueryStringDecoder(ups.getUri());
        Map<String, List<String>> opts = decoder.parameters();
        String merchantId = Iterables.getOnlyElement(opts.get(MERCHANT_ID));

        FiservApplePay.Builder fiservApplePay = FiservApplePay.newBuilder();
        fiservApplePay.setMerchantId(merchantId);
        return fiservApplePay;
    }

    private static SpreedlyApplePay.Builder getSpreedlyApplePayApp(UriBasedServiceInfo ups) {
        QueryStringDecoder decoder = new QueryStringDecoder(ups.getUri());
        Map<String, List<String>> opts = decoder.parameters();
        String merchantId = Iterables.getOnlyElement(opts.get(MERCHANT_ID));

        SpreedlyApplePay.Builder spreedlyApplePay = SpreedlyApplePay.newBuilder();
        spreedlyApplePay.setMerchantId(merchantId);
        return spreedlyApplePay;
    }

    private boolean isFeatureEnabled(ProviderIntegrationTypeSpec integrationType, String brandName, ImmutableAccount account) {
        return switch (integrationType) {
            case TRUSTLY -> Feature.TRUSTLY.isEnabled(props, brandName, account.getId(), account.isAdmin());
            case STANDARD_ACH -> Feature.STANDARD_ACH.isEnabled(props, brandName, account.getId(), account.isAdmin());
            case CRYPTO -> Feature.CRYPTO.isEnabled(props, brandName, account.getId(), account.isAdmin());
            case AEROPAY -> Feature.AEROPAY.isEnabled(props, brandName, account.getId(), account.isAdmin());
            default -> true;
        };
    }

    private boolean hasSecret(String brand, String country, Provider p) {
        RedeemProviderSpec spec = RedeemProviderSpec.valueOf(p.getCode().toUpperCase());
        return getRedeemSecret(brand, country, spec).isPresent();
    }

    private Optional<UriBasedServiceInfo> getRedeemSecret(String brand, String country, RedeemProviderSpec providerSpec) {
        if (providerSpec == RedeemProviderSpec.STANDARD_ACH) {
            return RedeemProviderSpec.getAchProviders().stream()
                    .map(v -> getRedeemSecret(brand, country, v))
                    .flatMap(Optional::stream)
                    .findFirst();
        }
        if (providerSpec == RedeemProviderSpec.CRYPTO) {
            return Arrays.stream(RedeemProviderSpec.values())
                    .filter(RedeemProviderSpec::isCrypto)
                    .filter(s -> StringUtils.isNotEmpty(s.getSecret()))
                    .map(v -> getRedeemSecret(brand, country, v))
                    .flatMap(Optional::stream)
                    .findFirst();
        }
        Optional<UriBasedServiceInfo> secret = UPSs.findScopedServiceInfoByName(cloud, providerSpec.getSecret(), brand, country.toLowerCase());
        if (secret.isEmpty()) {
            log.warn("Secrets not found: brand =[{}], country=[{}], provider={}", brand, country, providerSpec);
        }
        return secret;
    }

    private Supplier<Boolean> providerCountryEnabled() {
        return props.PROVIDER_COUNTRY_ENABLED;
    }

    private List<Provider> getAvailableWithdrawProvidersForUser(String brandName, ImmutableAccount account, int signUpFraudScore, String country, List<String> codes, Transaction tx) throws Exception {

        AccountProviderBlackList providerBlackListByAccount = ebean.paymentRepo().getAccountProviderBlackList(account.getId(), tx);
        var params = ProviderRepo.FindProviderParams.create()
                .withBrands(brandName)
                .withTypes(ProviderTypeSpec.WITHDRAW);
        if (!codes.isEmpty()) {
            params.withCodes(Sets.newHashSet(codes));
        }
        return ebean.providerRepo().providers(params)
                .filter(p -> p.isEligiblePaymentOrWithdraw(account, signUpFraudScore, false))
                .filter(p -> !providerCountryEnabled().get() || CollectionUtils.isEmpty(p.getCountries()) || p.getCountries().contains(country))
                .filter(p -> providerBlackListByAccount == null || providerBlackListByAccount.getWithdrawMethods()
                        .stream()
                        .noneMatch(wm -> Objects.equals(wm, RedeemProviderSpec.valueOf(p.getCode().toUpperCase()).getMethodType())))
                .filter(p -> hasSecret(brandName, country, p))
                .filter(p -> isFeatureEnabled(p.getIntegrationType(), brandName, account))
                .collect(Collectors.toCollection(ArrayList::new));
    }

    public static void hideStandardAchReceivers(List<Provider> providers, List<StandardAchReceiver> standardAchReceivers) {
        if (providers.stream().anyMatch(p -> p.getIntegrationType() == ProviderIntegrationTypeSpec.STANDARD_ACH)) {
            var receivers = standardAchReceivers.stream()
                    .map(r -> r.getRedeemProvider().getIntegrationType())
                    .collect(Collectors.toSet());
            providers.removeIf(p -> receivers.contains(p.getIntegrationType()));
        }
    }
}
