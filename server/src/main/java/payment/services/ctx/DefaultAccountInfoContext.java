package payment.services.ctx;

import java.time.LocalDate;
import java.util.Optional;

import api.v1.AccountRoutingInfo;
import payment.context.account.AccountEngagementInfo;
import payment.context.account.AccountInfoContext;
import payment.context.account.AccountPersonalInfo;
import payment.context.account.KycInfo;
import payment.dto.mappers.Mappers;
import payment.model.immutable.ImmutableAccount;
import uam.api.v1.internal.AccountPaymentRoutingInfo;

public class DefaultAccountInfoContext extends DefaultGenericAccountInfoContext implements AccountInfoContext {
    private final KycInfo softKycInfo;
    private final LocalDate softKycDob;
    private final AccountEngagementInfo engagementInfo;
    private final AccountPaymentRoutingInfo paymentRouting;
    private final KycInfo kycInfo;

    public DefaultAccountInfoContext(ImmutableAccount account,
            AccountPersonalInfo personalInfo,
            AccountEngagementInfo accountEngagementInfo,
            AccountRoutingInfo accountRoutingInfo,
            KycInfo hardKycInfo,
            boolean isPlayable) {
        super(account, personalInfo, isPlayable);
        this.softKycInfo = accountEngagementInfo.getSoftKycInfo();
        this.softKycDob = personalInfo.birthDay().orElse(null);
        this.engagementInfo = accountEngagementInfo;
        this.paymentRouting = Mappers.toAccountPaymentRoutingInfo(accountRoutingInfo, country).build();
        kycInfo = hardKycInfo;
    }

    @Override
    public Optional<String> softKycFirstName() {
        return Optional.ofNullable(softKycInfo).map(KycInfo::firstName);
    }

    @Override
    public Optional<String> softKycLastName() {
        return Optional.ofNullable(softKycInfo).map(KycInfo::lastName);
    }

    @Override
    public Optional<String> softKycPostal() {
        return Optional.ofNullable(softKycInfo).map(KycInfo::zip);
    }

    @Override
    public Optional<String> softKycAddress1() {
        return Optional.ofNullable(softKycInfo).map(KycInfo::address);
    }

    @Override
    public Optional<LocalDate> softKycDob() {
        return Optional.ofNullable(softKycDob);
    }

    @Override
    public AccountEngagementInfo engagementInfo() {
        return engagementInfo;
    }

    @Override
    public AccountPaymentRoutingInfo paymentRouting() {
        return paymentRouting;
    }

    @Override
    public KycInfo kycInfo() {
        return kycInfo;
    }
}
