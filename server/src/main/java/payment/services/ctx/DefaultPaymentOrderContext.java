package payment.services.ctx;

import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static payment.model.PaymentOrder.resolveInternalStatus;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import api.v1.ApplicationException;
import api.v1.Code;
import io.ebean.Transaction;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import payment.PaymentJpaManager;
import payment.PaymentOrderSeq;
import payment.card.CardTransactionDetails;
import payment.context.order.GenericPaymentContext;
import payment.context.order.PaymentMethodContext;
import payment.context.order.PaymentOrderContext;
import payment.crypto.BlockchainTransactionData;
import payment.crypto.CryptoCurrency;
import payment.crypto.CryptoTxDetails;
import payment.crypto.FeeData;
import payment.crypto.PaymentRefundData;
import payment.crypto.PaymentThresholdData;
import payment.crypto.RateData;
import payment.model.AccountPaymentMethod;
import payment.model.CryptoPurchaseDetail;
import payment.model.ErrorMapping;
import payment.model.ExternalReward;
import payment.model.OfferTemplate;
import payment.model.OrderStatusSpec;
import payment.model.PaymentOrder;
import payment.model.PaymentOrderError;
import payment.model.Provider;
import payment.model.ProviderTypeSpec;
import payment.model.RoutingErrorTypeSpec;
import payment.model.ScaAuthentication;
import payment.repo.ProviderRepo;
import uam.api.v1.PaymentProvider;

@Slf4j
@Getter
public class DefaultPaymentOrderContext implements PaymentOrderContext {
    public static final String DEFAULT_ERROR_MSG = "Payment has failed, please try again.";

    protected PaymentOrder order;
    private final PaymentOrderUpdates updates;
    protected final DefaultPaymentMethodContext paymentMethodContext;
    private final PaymentJpaManager ebean;
    private String confirmTempToken;

    protected DefaultPaymentOrderContext(PaymentOrder order, PaymentJpaManager ebean) {
        this.order = Objects.requireNonNull(order);
        this.updates = new PaymentOrderUpdates(order);
        this.paymentMethodContext = new DefaultPaymentMethodContext(order.getPaymentMethod(), order.getAccount().getId(), ebean);
        this.ebean = Objects.requireNonNull(ebean);
    }

    public void markError(PaymentOrderError err) {
        updates.error = err;
        updates.error.getErrorDetails()
                .filter(StringUtils::isNotEmpty)
                .ifPresent(code -> paymentMethodContext.addMethodError(currentProvider(), code));
    }

    @Override
    public void setCryptoPurchaseDetails(CryptoTxDetails details) {
        CryptoPurchaseDetail cryptoPurchaseDetail = updates.cryptoPurchaseDetail != null
                ? updates.cryptoPurchaseDetail
                : order.cryptoPaymentDetail().orElse(new CryptoPurchaseDetail());
        updates.cryptoPurchaseDetail = cryptoPurchaseDetail;
        if (details.getNetwork() != null) {
            cryptoPurchaseDetail.setSourceNetwork(details.getNetwork());
        }

        cryptoPurchaseDetail.setSourceCurrency(CryptoCurrency.valueOf(details.getSourceCurrency()));
        cryptoPurchaseDetail.setStatus(details.getStatus());

        cryptoPurchaseDetail.setBlockChains(details.getBlockchainTransactions()
                .stream()
                .map(tx -> new BlockchainTransactionData(tx.getTxHash(), tx.getSourceAddress(), tx.getDestinationAddress()))
                .toList());

        Optional.ofNullable(details.getThreshold()).ifPresent(th -> cryptoPurchaseDetail.setThresholdData(new PaymentThresholdData(th.getOverpayment(), th.getUnderpayment())));
        Optional.ofNullable(details.getRefundData()).ifPresent(r -> cryptoPurchaseDetail.setRefundData(new PaymentRefundData(
                r.getAmount(),
                r.getNetAmount(),
                r.getNetworkFee(),
                r.getNetworkFeeCurrency(),
                r.getTxHash()
        )));

        cryptoPurchaseDetail.setFees(details.getFees()
                .stream()
                .map(fee -> new FeeData(fee.getAmount(), fee.getCurrency()))
                .toList());
        details.getPaidRate().ifPresent(cryptoPurchaseDetail::setPaidRate);
        cryptoPurchaseDetail.setRates(details.getRates()
                .stream()
                .map(r -> new RateData(r.getRate(), r.getTicker()))
                .toList());

        cryptoPurchaseDetail.setPaidAmount(details.getPaidAmount());
        cryptoPurchaseDetail.setPaidAmountInBaseCurrency(details.getPaidAmountInBaseCurrency() != null ? details.getPaidAmountInBaseCurrency().setScale(2, RoundingMode.DOWN) : null);
        cryptoPurchaseDetail.setPaidAmountInLocalCurrency(details.getPaidAmountInLocalCurrency() != null ? details.getPaidAmountInLocalCurrency().setScale(2, RoundingMode.DOWN) : null);
        cryptoPurchaseDetail.setKytAlertLevel(details.getKytAlertLevel());
        cryptoPurchaseDetail.setKytStatus(details.getKytStatus());
        cryptoPurchaseDetail.setSourceAmount(details.getSourceAmount());
        cryptoPurchaseDetail.setRefundStatus(details.getRefundStatus());
    }

    @Override
    public final void setCardTransactionDetails(payment.CardTransactionDetails details) {
        updates.cardTransactionDetails = details;
    }

    @Override
    public void setAppliedRoutingRules(Set<String> val) {
        updates.appliedRoutingRules = val;
    }

    @Override
    public void setSecure3d(boolean val) {
        updates.secure3d = val;
    }

    @Override
    public void setScaAuthenticationId(Long val) {
        updates.scaAuthenticationId = val;
    }

    @Override
    public void setFraud(boolean val) {
        updates.fraud = val;
    }

    @Override
    public void setFraudScore(int val) {
        updates.fraudScore = val;
    }

    @Override
    public void setFraudRequestId(String val) {
        updates.fraudRequestId = val;
    }

    @Override
    public void setFraudResponseId(long val) {
        updates.fraudResponseId = val;
    }

    @Override
    public final void setSuccess(boolean val) {
        updates.success = val;
        updates.internalStatus = resolveInternalStatus(val);
    }

    // todo will be removed
    @SneakyThrows
    public void setProvider(PaymentProvider provider, Transaction tx) {
        var params = ProviderRepo.FindProviderParams.create()
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withBrands(order.getAccount().getBrand().getName())
                .withCodes(provider.name().toLowerCase());
        var providerMaybe = ebean.providerRepo().provider(params, tx);
        if (providerMaybe.isPresent()) {
            updates.provider = providerMaybe.get();
        } else {
            throw ApplicationException.of("Purchase payment provider " + provider + " was not found", Code.ERR_NOT_FOUND);
        }
    }

    @Override
    @SneakyThrows
    public final void setProvider(PaymentProvider provider) {
        var params = ProviderRepo.FindProviderParams.create()
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withBrands(order.getAccount().getBrand().getName())
                .withCodes(provider.name().toLowerCase());
        try (var transaction = ebean.newReadOnlyTransaction()) {
            var providerMaybe = ebean.providerRepo().provider(params, transaction);
            if (providerMaybe.isPresent()) {
                updates.provider = providerMaybe.get();
            } else {
                throw ApplicationException.of("Purchase payment provider " + provider + " was not found", Code.ERR_NOT_FOUND);
            }
        }
    }

    @Override
    public final Optional<Integer> fraudScore() {
        return Optional.ofNullable(updates.fraudScore != null ? updates.fraudScore : order.getFraudScore());
    }

    @Override
    public String fraudRequestId() {
        return updatesFirst(updates.fraudRequestId, order.getFraudRequestId());
    }

    @Override
    public final void setError(Code code, String exceptionClass, String message, String apiError) {
        PaymentOrderError error = PaymentOrderError.builder()
                .errorCode(code.name().toLowerCase())
                .exceptionClass(exceptionClass)
                .message(message)
                .apiError(apiError)
                .build();
        markError(error);
    }

    @Override
    public final void setError(Code code, Integer httpCode, String responseCode, String message, String apiError) {
        try (Transaction trx = ebean.newReadOnlyTransaction()) {
            Optional<ErrorMapping> errorMapping = ebean.paymentRepo().getErrorMappingByErrorCodeAndType(responseCode, RoutingErrorTypeSpec.PROVIDER_CUSTOM, trx);
            String errorCode = errorMapping.map(ErrorMapping::getMappedErrorCode).orElse(code.name().toLowerCase());

            PaymentOrderError error = PaymentOrderError.builder()
                    .errorCode(errorCode)
                    .httpCode(Optional.ofNullable(httpCode).map(Integer::shortValue).orElse(null))
                    .responseCode(responseCode)
                    .message(message)
                    .apiError(apiError)
                    .build();
            markError(error);
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    @Override
    public final void setError(String code, uam.api.v1.PaymentOrderError paymentOrderError) {
        PaymentOrderError error = new PaymentOrderError(
                paymentOrderError, code);
        String errorMappingCode = StringUtils.firstNonEmpty(error.getResponseCode(), error.getCardNetworkError());
        if (errorMappingCode != null) {
            try (Transaction trx = ebean.newReadOnlyTransaction()) {
                Optional<ErrorMapping> errorMapping = ebean.paymentRepo().getErrorMappingByErrorCodeAndType(errorMappingCode,
                        RoutingErrorTypeSpec.PROVIDER_CUSTOM, trx);
                String errorCode = errorMapping.map(ErrorMapping::getMappedErrorCode).orElse(error.getErrorCode());
                error.setErrorCode(errorCode);
            } catch (Throwable err) {
                ExceptionUtils.wrapAndThrow(err);
            }
        }
        markError(error);
    }

    @Override
    public final void setRoutingErrorByFraudRule(Code code, List<String> routingFraudRules, String errorCode) {
        try (Transaction trx = ebean.newReadOnlyTransaction()) {
            Optional<ErrorMapping> errorMapping = ebean.paymentRepo().getErrorMappingByErrorCodeAndType(errorCode, RoutingErrorTypeSpec.SEON_RULE, trx);
            String message = errorMapping.map(ErrorMapping::getMessage).orElse(DEFAULT_ERROR_MSG);
            String apiError = errorMapping.map(ErrorMapping::getDescription).orElse(DEFAULT_ERROR_MSG);

            markError(PaymentOrderError.builder()
                    .errorCode(code.name().toLowerCase())
                    .routingFraudRules(routingFraudRules)
                    .message(message)
                    .apiError(apiError)
                    .build());
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    @Override
    public final void setProviderResponseCode(Code code, String exceptionClass, String responseCode, String message) {
        try (Transaction trx = ebean.newReadOnlyTransaction()) {
            Optional<ErrorMapping> errorMapping = ebean.paymentRepo().getErrorMappingByErrorCodeAndType(responseCode, RoutingErrorTypeSpec.PROVIDER_CUSTOM, trx);
            String apiError = errorMapping.map(ErrorMapping::getDescription).orElse(DEFAULT_ERROR_MSG);
            String resultErrorCode = errorMapping.map(ErrorMapping::getMappedErrorCode).orElse(code.name().toLowerCase());

            if (message != null) {
                message = message.substring(0, Math.min(message.length(), 255));
            }

            markError(PaymentOrderError.builder()
                    .errorCode(resultErrorCode)
                    .exceptionClass(exceptionClass)
                    .responseCode(responseCode)
                    .message(message)
                    .apiError(apiError)
                    .build());
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    @Override
    public final void setProviderResponseCodeWithMessage(Code code, String exceptionClass, String responseCode, String message, String apiError) {
        try (Transaction trx = ebean.newReadOnlyTransaction()) {
            Optional<ErrorMapping> errorMapping = ebean.paymentRepo().getErrorMappingByErrorCodeAndType(responseCode, RoutingErrorTypeSpec.PROVIDER_CUSTOM, trx);
            String errorCode = errorMapping.map(ErrorMapping::getMappedErrorCode).orElse(code.name().toLowerCase());

            markError(PaymentOrderError.builder()
                    .errorCode(errorCode)
                    .exceptionClass(exceptionClass)
                    .responseCode(responseCode)
                    .message(message)
                    .apiError(apiError)
                    .build());
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    @Override
    public final void setHttpError(Code code, String exceptionClass, Integer httpCode, String message, String apiError) {
        markError(PaymentOrderError.builder()
                .errorCode(code.name().toLowerCase())
                .exceptionClass(exceptionClass)
                .httpCode(Optional.ofNullable(httpCode).map(Integer::shortValue).orElse(null))
                .message(message)
                .apiError(apiError)
                .build());
    }

    @Override
    public final void setHttpErrorByCode(Code code, String exceptionClass, Integer httpCode, String message) {
        try (Transaction trx = ebean.newReadOnlyTransaction()) {
            Optional<ErrorMapping> errorMapping = ebean.paymentRepo().getErrorMappingByErrorCodeAndType(String.valueOf(httpCode),
                    RoutingErrorTypeSpec.HTTP_CODE, trx);
            String apiError = errorMapping.map(ErrorMapping::getDescription).orElse(DEFAULT_ERROR_MSG);

            markError(PaymentOrderError.builder()
                    .errorCode(code.name().toLowerCase())
                    .exceptionClass(exceptionClass)
                    .httpCode(Optional.ofNullable(httpCode).map(Integer::shortValue).orElse(null))
                    .message(message)
                    .apiError(apiError)
                    .build());
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    @Override
    public final void setCardNetworkError(Code code, String exceptionClass, String cardNetworkError, String providerResponseCode, String message) {
        try (Transaction trx = ebean.newReadOnlyTransaction()) {
            Optional<ErrorMapping> errorMapping = ebean.paymentRepo().getErrorMappingByErrorCodeAndType(cardNetworkError, RoutingErrorTypeSpec.CARD_NETWORK, trx);

            String apiError = errorMapping.map(ErrorMapping::getDescription).orElse(DEFAULT_ERROR_MSG);
            if (message != null) {
                message = message.substring(0, Math.min(message.length(), 255));
            }

            String errorMappingCode = StringUtils.firstNonEmpty(providerResponseCode, cardNetworkError);
            errorMapping = ebean.paymentRepo().getErrorMappingByErrorCodeAndType(errorMappingCode, RoutingErrorTypeSpec.PROVIDER_CUSTOM, trx);
            String errorCode = errorMapping.map(ErrorMapping::getMappedErrorCode).orElse(code.name().toLowerCase());

            markError(PaymentOrderError.builder()
                    .errorCode(errorCode)
                    .exceptionClass(exceptionClass)
                    .cardNetworkError(cardNetworkError)
                    .responseCode(providerResponseCode)
                    .message(message)
                    .apiError(apiError)
                    .build());
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    @Override
    public final void setTempToken(String val) {
        updates.tempToken = val;
    }

    @Override
    public final void setNotCompletedReason() {
        updates.reason = GenericPaymentContext.NOT_COMPLETED;
    }

    @Override
    public final void setRedirectUrl(String val) {
        updates.redirectUrl = val;
    }

    @Override
    public final void setConfirmTempToken(String tempToken, boolean persist) {
        if (persist) {
            updates.confirmTempToken = tempToken;
        } else {
            confirmTempToken = tempToken;
        }
    }

    @Override
    public final void setReason(String val) {
        updates.reason = val;
    }

    @Override
    public final void setStatus(String val) {
        updates.status = val;
    }

    @Override
    public final void setInternalStatus(OrderStatusSpec val) {
        updates.internalStatus = val;
    }

    // ~ Read
    @Override
    public final String status() {
        return updatesFirst(updates.status, order.getStatus());
    }

    @Override
    public final OrderStatusSpec internalStatus() {
        return updatesFirst(updates.internalStatus, order.getInternalStatus());
    }

    @Override
    public final Optional<Object> apiError() {
        return paymentOrderError().map(PaymentOrderError::getApiError);
    }

    @Override
    public String errorCode() {
        return paymentOrderError().map(PaymentOrderError::getErrorCode).orElse(null);
    }

    @Override
    public Optional<String> errorDetails() {
        return paymentOrderError().flatMap(PaymentOrderError::getErrorDetails);
    }

    @Override
    public final Optional<String> offerCode() {
        return order.offer().map(OfferTemplate::getCode);
    }

    @Override
    public final Optional<BigDecimal> offerAmount() {
        return order.offer().map(OfferTemplate::getPrice);
    }

    @Override
    public final String orderSn() {
        return order.getOrderSn();
    }

    @Override
    public String integrationType() {
        return currentProvider().getIntegrationType().code();
    }

    @Override
    public final String sourceId() {
        return updatesFirst(updates.sourceId, order.getSourceId());
    }

    @Override
    public boolean isSecure3d() {
        return BooleanUtils.isTrue(updatesFirst(updates.secure3d, order.getSecure3d()));
    }

    @Override
    public String redirectUrl() {
        return updatesFirst(updates.redirectUrl, order.getRedirectUrl());
    }

    @Override
    public boolean isSuccess() {
        return updatesFirst(updates.internalStatus, order.getInternalStatus()).isSuccess();
    }

    @Override
    public void setPaymentMethod(Long val) {
        updates.paymentMethodId = val;
    }

    @Override
    public void setAmount(BigDecimal val) {
        updates.amount = val;
    }

    @Override
    public boolean hasError() {
        return paymentOrderError().isPresent();
    }

    @Override
    public final String confirmToken() {
        if (StringUtils.isNotEmpty(confirmTempToken)) {
            return confirmTempToken;
        }
        return updatesFirst(updates.confirmTempToken, order.getConfirmTempToken());
    }

    @Override
    public final PaymentProvider provider() {
        return currentProvider().toPaymentProvider();
    }

    @Override
    public final PaymentMethodContext getPmCtx() {
        return paymentMethodContext;
    }

    @Override
    public final String currency() {
        return order.getCurrency();
    }

    @Override
    public final String description() {
        return order.getDescription();
    }

    @Override
    public final String tempToken() {
        return updatesFirst(updates.tempToken, order.getTempToken());
    }

    @Override
    public final void setSecure3dReason(String val) {
        updates.secure3dReason = val;
    }

    @Override
    public final void setSourceId(String sourceId) {
        updates.sourceId = sourceId;
    }

    @Override
    public final void setRetryConditionTriggered(boolean used) {
        updates.retryConditionTriggered = used;
    }

    @Override
    public final Optional<String> getCardNetworkError() {
        return paymentOrderError().map(PaymentOrderError::getCardNetworkError);
    }

    @Override
    public final Optional<String> getProviderCustomError() {
        return paymentOrderError().map(PaymentOrderError::getResponseCode);
    }

    @Override
    public final Optional<Short> getHttpErrorCode() {
        return paymentOrderError().map(PaymentOrderError::getHttpCode);
    }

    @Override
    public final List<String> getFraudRuleRouting() {
        Optional<PaymentOrderError> opt = paymentOrderError();
        if (opt.isPresent()) {
            PaymentOrderError error = opt.get();
            if (CollectionUtils.isNotEmpty(error.getRoutingFraudRules())) {
                return error.getRoutingFraudRules();
            }
        }
        return List.of();
    }

    @Override
    public final String orderCode() {
        return updatesFirst(updates.code, order.getCode());
    }

    @Override
    public final void setCode(String val) {
        updates.code = val;
    }

    @Override
    public final UUID transactionId() {
        return order.getTransactionId();
    }

    @Override
    public final BigDecimal amount() {
        return updatesFirst(updates.amount, order.getAmount());
    }

    @Override
    public final BigDecimal baseAmount() {
        return order.getBaseAmount();
    }

    public final void setOrder(PaymentOrder order) {
        this.order = order;
        this.updates.order = order;
    }

    private Optional<PaymentOrderError> paymentOrderError() {
        return Optional.ofNullable(updates.error).or(() -> order.error());
    }

    private Provider currentProvider() {
        return updatesFirst(updates.provider, order.getProvider());
    }

    private <T> T updatesFirst(T upd, T defaultVal) {
        return upd != null ? upd : defaultVal;
    }

    public class PaymentOrderUpdates {
        private PaymentOrder order;

        private Long paymentMethodId;
        private BigDecimal amount;
        private Long scaAuthenticationId;
        private Boolean secure3d;
        private CryptoPurchaseDetail cryptoPurchaseDetail;
        private String sourceId;
        private OrderStatusSpec internalStatus;
        private String status;
        private String redirectUrl;
        private Boolean success;
        private Set<String> appliedRoutingRules;
        private String code;
        private Provider provider;
        private PaymentOrderError error;
        private payment.CardTransactionDetails cardTransactionDetails;
        private String tempToken;
        private String confirmTempToken;
        private String secure3dReason;
        private String reason;
        private Boolean retryConditionTriggered;
        private Boolean fraud;
        private Integer fraudScore;
        private String fraudRequestId;
        private Long fraudResponseId;
        private List<ExternalReward> externalRewards;

        public PaymentOrderUpdates(PaymentOrder order) {
            this.order = order;
        }

        public PaymentOrder apply(PaymentJpaManager ebean, Transaction tx) throws ApplicationException {
            var currentOrder = order.getId() != null ? ebean.paymentRepo().requiredOrder(order.getId(), tx) : order;
            updateScaAuthenticationId(currentOrder, ebean);
            updateSecure3d(currentOrder);
            updateCryptoPurchaseDetail(currentOrder);
            updateSourceId(currentOrder);
            updateInternalStatus(currentOrder);
            updateStatus(currentOrder);
            updateRedirectUrl(currentOrder);
            updateSuccess(currentOrder);
            updateAppliedRoutingRules(currentOrder);
            updateCardTransactionDetails(currentOrder);
            updateTempToken(currentOrder);
            updateConfirmTempToken(currentOrder);
            updateSecure3dReason(currentOrder);
            updateReason(currentOrder);
            updateRetryConditionTriggered(currentOrder);
            updateFraud(currentOrder);
            updateFraudScore(currentOrder);
            updateFraudRequestId(currentOrder);
            updateFraudResponseId(currentOrder);
            updateCode(currentOrder);
            updateProvider(currentOrder);
            updateError(currentOrder);
            updateAmount(currentOrder);
            updatePaymentMethod(currentOrder);

            if (currentOrder.getId() == null) {
                currentOrder.setId((long) ebean.idGenerator(PaymentOrderSeq.class).nextId(tx));
            }

            ebean.save(currentOrder, tx);

            if (CollectionUtils.isNotEmpty(currentOrder.getUserAppliedRewards())) {
                ebean.saveAll(currentOrder.getUserAppliedRewards(), tx);
            }

            DefaultPaymentOrderContext.this.order = currentOrder;

            return currentOrder;
        }

        private void updatePaymentMethod(PaymentOrder currentOrder) {
            if (paymentMethodId != null) {
                currentOrder.setPaymentMethod(ebean.find(AccountPaymentMethod.class, paymentMethodId));
            }
        }

        private void updateScaAuthenticationId(PaymentOrder order, PaymentJpaManager ebean) {
            if (scaAuthenticationId != null) {
                order.setScaAuthentication(ebean.find(ScaAuthentication.class, scaAuthenticationId));
            }
        }

        private void updateSecure3d(PaymentOrder order) {
            if (secure3d != null) {
                order.setSecure3d(secure3d);
            }
        }

        private void updateCryptoPurchaseDetail(PaymentOrder order) {
            if (cryptoPurchaseDetail != null) {
                order.setCryptoPurchaseDetail(cryptoPurchaseDetail);
            }
        }

        private void updateSourceId(PaymentOrder order) {
            if (isNotBlank(sourceId)) {
                order.setSourceId(sourceId);
            }
        }

        private void updateInternalStatus(PaymentOrder order) {
            if (internalStatus != null) {
                order.setInternalStatus(internalStatus);
            }
        }

        private void updateStatus(PaymentOrder order) {
            if (isNotBlank(status)) {
                order.setStatus(status);
            }
        }

        private void updateRedirectUrl(PaymentOrder order) {
            if (isNotBlank(redirectUrl)) {
                order.setRedirectUrl(redirectUrl);
            }
        }

        private void updateSuccess(PaymentOrder order) {
            if (success != null) {
                order.setSuccess(success);
            }
        }

        private void updateAppliedRoutingRules(PaymentOrder order) {
            if (appliedRoutingRules != null) {
                order.setAppliedRoutingRules(appliedRoutingRules);
            }
        }

        private void updateCardTransactionDetails(PaymentOrder order) {
            if (cardTransactionDetails != null) {
                var ctd = order.cardTransactionDetails().orElse(new CardTransactionDetails());

                if (StringUtils.isNotEmpty(cardTransactionDetails.getMerchantAdviceCode())) {
                    ctd.setMerchantAdviceCode(cardTransactionDetails.getMerchantAdviceCode());
                }
                if (StringUtils.isNotEmpty(cardTransactionDetails.getExternalTransactionId())) {
                    ctd.setExternalTransactionId(cardTransactionDetails.getExternalTransactionId());
                }
                if (StringUtils.isNotEmpty(cardTransactionDetails.getSchemeTransactionId())) {
                    ctd.setSchemeTransactionId(cardTransactionDetails.getSchemeTransactionId());
                }
                if (StringUtils.isNotEmpty(cardTransactionDetails.getAuthCode())) {
                    ctd.setAuthCode(cardTransactionDetails.getAuthCode());
                }
                order.setCardTransactionDetails(ctd);
            }
        }

        private void updateTempToken(PaymentOrder order) {
            if (isNotBlank(tempToken)) {
                order.setTempToken(tempToken);
            }
        }

        private void updateConfirmTempToken(PaymentOrder order) {
            if (isNotBlank(confirmTempToken)) {
                order.setConfirmTempToken(confirmTempToken);
            }
        }

        private void updateSecure3dReason(PaymentOrder order) {
            if (isNotBlank(secure3dReason)) {
                order.setSecure3dReason(secure3dReason);
            }
        }

        private void updateReason(PaymentOrder order) {
            if (isNotBlank(reason)) {
                order.setReason(reason);
            }
        }

        private void updateRetryConditionTriggered(PaymentOrder order) {
            if (retryConditionTriggered != null) {
                order.setRetryConditionTriggered(retryConditionTriggered);
            }
        }

        private void updateFraud(PaymentOrder order) {
            if (fraud != null) {
                order.setFraud(fraud);
            }
        }

        private void updateFraudScore(PaymentOrder order) {
            if (fraudScore != null) {
                order.setFraudScore(fraudScore);
            }
        }

        private void updateFraudRequestId(PaymentOrder order) {
            if (isNotBlank(fraudRequestId)) {
                order.setFraudRequestId(fraudRequestId);
            }
        }

        private void updateFraudResponseId(PaymentOrder order) {
            if (fraudResponseId != null) {
                order.setFraudResponseId(fraudResponseId);
            }
        }

        private void updateCode(PaymentOrder order) {
            if (isNotBlank(code)) {
                order.setCode(code);
            }
        }

        private void updateProvider(PaymentOrder order) {
            if (provider != null) {
                order.setProvider(provider);
            }
        }

        private void updateError(PaymentOrder order) {
            if (error != null) {
                order.setError(error);
            }
        }

        private void updateAmount(PaymentOrder order) {
            if (amount != null) {
                order.setAmount(amount);
            }
        }

    }

}
