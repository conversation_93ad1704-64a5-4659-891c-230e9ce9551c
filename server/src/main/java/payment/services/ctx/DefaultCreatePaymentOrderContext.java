package payment.services.ctx;

import java.net.URL;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import lombok.SneakyThrows;
import payment.CardBinDetails;
import payment.PaymentJpaManager;
import payment.context.account.AccountInfoContext;
import payment.context.account.KycInfo;
import payment.context.order.CreatePaymentOrderContext;
import payment.crypto.CryptoCurrency;
import payment.crypto.CryptoNetwork;
import payment.model.PaymentOrder;
import payment.services.account.dto.AccountPaymentInfoWrapper;
import payment.type.PurchaseProviderSpec;
import uam.api.v1.AccountPaymentInfo;
import uam.api.v1.PaymentProvider;

public class DefaultCreatePaymentOrderContext extends DefaultPaymentContext implements CreatePaymentOrderContext {
    private final AccountInfoContext accCtx;
    public UUID transactionId;
    public boolean sweepstake;
    public String remoteIp;
    public String token;
    public URL successURL;
    public URL cancelURL;
    public URL termURL;
    public URL termURLBase;
    public boolean cvvEntered;
    public String referer;
    public Set<String> appliedRoutingRules = new HashSet<>();
    public Map<String, String> providerToCustomerId;
    public long at;

    private final AccountPaymentInfo accountInfo;
    private final KycInfo kycInfo;
    private String paymentData;
    public Pair<CryptoCurrency, CryptoNetwork> cryptoData;

    public String clientToken;

    public String publicKeyBase64;
    public long nonce;
    public String validationUrl;
    public String completeMerchantValidation;
    public PaymentProvider provider;
    private boolean triggerSecure3dCheck;
    private boolean allowHighFraudScore;
    private CardBinDetails cardBinDetails;

    public DefaultCreatePaymentOrderContext(
            PaymentOrder order,
            AccountPaymentInfoWrapper accountInfoWrapper,
            List<payment.model.AccountPaymentInfo> paymentInfos,
            AccountInfoContext accCtx,
            PaymentJpaManager ebean) {
        super(order, ebean, accCtx);
        this.accountInfo = accountInfoWrapper.getAccountPaymentInfo();
        this.kycInfo = accountInfoWrapper.getKycInfo();
        this.providerToCustomerId = paymentInfos.stream().collect(Collectors.toMap(payment.model.AccountPaymentInfo::getProvider, payment.model.AccountPaymentInfo::getCustomerId));
        this.transactionId = order.getTransactionId();
        this.accCtx = accCtx;
    }

    @Override
    @SneakyThrows
    public URL homePage() {
        return new URL(accountInfo.getBrandInfo().getHomePage());
    }

    @Override
    public String supplier() {
        return accountInfo.getBrandInfo().getTitle();
    }

    @Override
    public Set<String> getAppliedRoutingRules() {
        return appliedRoutingRules;
    }

    @Override
    public Date orderRequestedAt() {
        return new Date(at);
    }

    @Override
    public URL successURL() {
        return successURL;
    }

    @Override
    public URL cancelURL() {
        return cancelURL;
    }

    @Override
    public URL termURL() {
        return termURL;
    }

    @Override
    public URL termURLBase() {
        return termURLBase;
    }

    @Override
    public boolean isAutoGenEmail() {
        return accountInfo.getAutoGenEmail();
    }

    @Override
    public String remoteIp() {
        return remoteIp;
    }


    @Override
    public String token() {
        return token;
    }

    @Override
    public void setProviderCustomerId(String provider, String customerId) {
        providerToCustomerId.put(provider.toLowerCase(Locale.ROOT), customerId);
    }

    @Override
    public void setTriggerSecure3dCheck(boolean val) {
        triggerSecure3dCheck = val;
    }

    @Override
    public void setAllowHighFraudScore(boolean val) {
        this.allowHighFraudScore = val;
    }

    @Override
    public void setPaymentData(String val) {
        this.paymentData = val;
    }

    @Override
    public String paymentData() {
        return paymentData;
    }

    @Override
    public Optional<Pair<CryptoCurrency, CryptoNetwork>> getCryptoData() {
        return Optional.ofNullable(cryptoData);
    }

    @Override
    public boolean cvvEntered() {
        return cvvEntered;
    }

    @Override
    public String referer() {
        return referer;
    }

    @Override
    public Optional<String> providerCustomerId(PaymentProvider provider) {
        for (PaymentProvider mid : PurchaseProviderSpec.fromServerApi(provider).getProvidersWithCustomerReuse()) {
            String customerId = providerToCustomerId.get(mid.name().toLowerCase());
            if (customerId != null) {
                return Optional.of(customerId);
            }

        }
        return Optional.empty();
    }

    @Override
    public Optional<String> mainProviderCustomerId(PaymentProvider provider) {
        String customerId = providerToCustomerId.get(PurchaseProviderSpec.fromServerApi(provider).name().toLowerCase());
        return Optional.ofNullable(customerId);
    }

    @Override
    public boolean isTriggerSecure3dCheck() {
        return triggerSecure3dCheck;
    }

    @Override
    public boolean isAllowHighFraudScore() {
        return allowHighFraudScore;
    }

    @Override
    public String clientToken() {
        return clientToken;
    }

    @Override
    public String publicKeyBase64() {
        return publicKeyBase64;
    }

    @Override
    public void setClientToken(String clientToken) {
        this.clientToken = clientToken;
    }

    @Override
    public void setPublicKeyBase64(String publicKeyBase64) {
        this.publicKeyBase64 = publicKeyBase64;
    }

    @Override
    public void setNonce(long nonce) {
        this.nonce = nonce;
    }

    @Override
    public String validationUrl() {
        return validationUrl;
    }

    @Override
    public String completeMerchantValidation() {
        return completeMerchantValidation;
    }

    @Override
    public boolean isInAppPurchase() {
        return getPaymentOrderContext().getOrder().getProvider().isInApp();
    }

    @Override
    public void setCompleteMerchantValidation(String val) {
        this.completeMerchantValidation = val;
    }

    @Override
    public AccountInfoContext getFullAccContext() {
        return accCtx;
    }

    @Override
    public PaymentProvider originalProvider() {
        return provider;
    }


    @Override
    public String resolveFirstName() {
        if (StringUtils.isNotEmpty(kycInfo.firstName())) {
            return kycInfo.firstName();
        }
        if (StringUtils.isNotEmpty(accountInfo.getSoftKycInfo().getFirstName())) {
            return accountInfo.getSoftKycInfo().getFirstName();
        }
        return accountInfo.getFirstName();
    }

    @Override
    public String resolveLastName() {
        if (StringUtils.isNotEmpty(kycInfo.lastName())) {
            return kycInfo.lastName();
        }
        if (StringUtils.isNotEmpty(accountInfo.getSoftKycInfo().getLastName())) {
            return accountInfo.getSoftKycInfo().getLastName();
        }
        return accountInfo.getLastName();
    }

    @Override
    public String resolveCountry() {
        if (StringUtils.isNotEmpty(kycInfo.country())) {
            return kycInfo.country();
        }
        if (StringUtils.isNotEmpty(accountInfo.getSoftKycInfo().getCountry())) {
            return accountInfo.getSoftKycInfo().getCountry();
        }
        return accountInfo.getCountry();
    }

    @Override
    public String resolveState() {
        if (StringUtils.isNotEmpty(kycInfo.state())) {
            return kycInfo.state();
        }
        if (StringUtils.isNotEmpty(accountInfo.getSoftKycInfo().getState())) {
            return accountInfo.getSoftKycInfo().getState();
        }
        return accountInfo.getState();
    }

    @Override
    public String resolveAddress1() {
        if (StringUtils.isNotEmpty(kycInfo.address())) {
            return kycInfo.address();
        }
        if (StringUtils.isNotEmpty(accountInfo.getSoftKycInfo().getAddress())) {
            return accountInfo.getSoftKycInfo().getAddress();
        }
        return null;
    }

    @Override
    public String resolveAddress2() {
        if (StringUtils.isNotEmpty(accountInfo.getSoftKycInfo().getAddress2())) {
            return accountInfo.getSoftKycInfo().getAddress2();
        }
        return null;
    }

    @Override
    public Optional<CardBinDetails> cardBinDetails() {
        return Optional.ofNullable(cardBinDetails);
    }

    public void setCardBinDetails(CardBinDetails cardBinDetails) {
        this.cardBinDetails = cardBinDetails;
    }
}
