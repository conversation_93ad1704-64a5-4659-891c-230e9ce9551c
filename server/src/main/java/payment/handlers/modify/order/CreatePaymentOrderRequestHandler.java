package payment.handlers.modify.order;

import static api.v1.Code.ERR_CARDHOLDER_NAME_MISMATCH;
import static api.v1.CodeSpec.ERR_PAYMENT_INPUT_CVV;
import static payment.services.util.PaymentOrderErrors.ERR_PURCHASE_LIMIT;
import static payment.services.util.PaymentOrderErrors.KYC_ERROR;

import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;
import com.turbospaces.http.UrlUtils;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.rpc.QueuePostTemplate;

import api.v1.AccountStatusSpec;
import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.CommonMappers;
import common.MetricTags;
import common.utils.IdentityUtil;
import fraud.api.FraudServiceApi;
import fraud.api.v1.PaymentFraudCheckResponse;
import io.ebean.DuplicateKeyException;
import io.ebean.Transaction;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import payment.CardBinDetails;
import payment.CardDetails;
import payment.CoreUtils;
import payment.Countries;
import payment.Feature;
import payment.PaymentIdentityManager;
import payment.PaymentJpaManager;
import payment.PaymentOrderAllowanceService;
import payment.PaymentProviders;
import payment.PaymentServerProperties;
import payment.PaymentService;
import payment.api.PaymentServiceApi;
import payment.api.v1.SaveCardPaymentMethodMetaInfoRequest;
import payment.card.CardPaymentMethodMetaInfo;
import payment.cloudflaregatewayhook.CloudflareGatewayHookService;
import payment.context.account.AccountInfoContext;
import payment.context.order.PaymentOrderContext;
import payment.context.sca.StartScaInputData;
import payment.crypto.CryptoCurrency;
import payment.crypto.CryptoNetwork;
import payment.dto.FraudInfo;
import payment.dto.mappers.Mappers;
import payment.dto.mappers.OfferMapper;
import payment.exceptions.PaymentOrderErrorException;
import payment.model.AccountPaymentMethod;
import payment.model.AccountProviderBlackList;
import payment.model.BlackList;
import payment.model.OrderStatusSpec;
import payment.model.PaymentOrder;
import payment.model.PaymentOrderError;
import payment.model.Provider;
import payment.model.ProviderTypeSpec;
import payment.model.ScaAuthentication;
import payment.model.ThirdPartyCardVerificationStatus;
import payment.model.TransactionLimit;
import payment.model.immutable.ImmutableAccount;
import payment.model.util.EmailTemplate;
import payment.repo.ProviderRepo;
import payment.services.PaymentFraudService;
import payment.services.ProviderService;
import payment.services.TransactionLimitService;
import payment.services.account.PaymentAccountService;
import payment.services.account.dto.AccountPaymentInfoWrapper;
import payment.services.account.dto.PurchaseLimit;
import payment.services.ctx.CreatePaymentOrderContext;
import payment.services.ctx.DefaultCreatePaymentOrderContext;
import payment.services.ctx.DefaultImmutableThreeDsContext;
import payment.services.ctx.DefaultPaymentMethodContext;
import payment.services.ctx.DefaultPaymentOrderContext;
import payment.services.ctx.MinimalImmutableThreeDsContext;
import payment.services.email.PaymentEmailService;
import payment.services.order.PaymentOrderService;
import payment.services.paymentmethod.BlackListService;
import payment.services.sca.ScaAuthService;
import payment.services.util.PaymentOrderErrors;
import payment.spreedly.SpreedlyStartScaInputData;
import payment.type.PaymentMode;
import payment.type.PurchaseProviderSpec;
import payment.type.ScaAuthState;
import payment.type.TransactionLimitTypeSpec;
import uam.api.UamServiceApi;
import uam.api.v1.AccountPaymentInfo;
import uam.api.v1.BrandInfo;
import uam.api.v1.CardPaymentMethod;
import uam.api.v1.CreatePaymentOrderRequest;
import uam.api.v1.CreatePaymentOrderResponse;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;
import uam.api.v1.PaymentProvider;
import uam.api.v1.ScaAuthenticateResponse;
import uam.api.v1.ScaAuthenticateState;
import uam.api.v1.SetAccountFraudInfoRequest;

@Service
public class CreatePaymentOrderRequestHandler
        extends AbstractPaymentRequestHandler<CreatePaymentOrderRequest, CreatePaymentOrderResponse.Builder>
        implements ModifyHandler<CreatePaymentOrderRequest, CreatePaymentOrderResponse.Builder> {
    public static final String KYC_REQUIRED_LABEL = "failed: kyc required";
    public static final String ERR_3DS_FAILED_LABEL = "The transaction failed due to 3DS verification";

    private final PaymentProviders paymentProviders;
    private final PaymentFraudService fraudService;
    private final TransactionLimitService transactionLimitService;
    private final PaymentOrderAllowanceService allowanceService;
    private final PaymentServiceApi paymentServiceApi;
    private final ScaAuthService scaAuthService;
    private final CloudflareGatewayHookService cloudflareGatewayHookService;
    private final BlackListService blackListService;

    @Inject
    public CreatePaymentOrderRequestHandler(
            PaymentServerProperties props,
            PaymentJpaManager ebean,
            QueuePostTemplate<?> postTemplate,
            PaymentProviders paymentProviders,
            PaymentIdentityManager sessionManager,
            PaymentFraudService fraudService,
            UamServiceApi uamServiceApi,
            FraudServiceApi fraudServiceApi,
            PaymentAccountService accountService,
            PaymentOrderService orderService,
            TransactionLimitService transactionLimitService,
            PaymentOrderAllowanceService allowanceService,
            PaymentServiceApi paymentServiceApi,
            CloudflareGatewayHookService cloudflareGatewayHookService,
            ScaAuthService scaCheckService,
            ProviderService providerService,
            PaymentEmailService emailService,
            BlackListService blackListService) {
        super(props, ebean, sessionManager, postTemplate, uamServiceApi, fraudServiceApi, accountService, providerService, emailService, orderService);
        this.paymentProviders = Objects.requireNonNull(paymentProviders);
        this.fraudService = fraudService;
        this.transactionLimitService = Objects.requireNonNull(transactionLimitService);
        this.allowanceService = Objects.requireNonNull(allowanceService);
        this.paymentServiceApi = paymentServiceApi;
        this.cloudflareGatewayHookService = cloudflareGatewayHookService;
        this.scaAuthService = scaCheckService;
        this.blackListService = blackListService;
    }

    @Override
    public boolean isImmediateAcknowledge() {
        return true;
    }

    @Override
    public void apply(TransactionalRequest<CreatePaymentOrderRequest, CreatePaymentOrderResponse.Builder> cmd) throws Throwable {
        CreatePaymentOrderRequest req = cmd.request();
        CreatePaymentOrderResponse.Builder reply = cmd.reply();
        cmd.addMDCTag(MdcTags.MDC_TRANSACTION_ID, req.getTransactionId());

        UUID transactionId = UUID.fromString(req.getTransactionId());
        Identity identity = req.getIdentity();
        ImmutableAccount account;
        List<payment.model.AccountPaymentInfo> paymentInfos;
        Optional<AccountPaymentMethod> accountPaymentMethod;
        try (var tx = ebean.newReadOnlyTransaction()) {
            // ~ check idempotency before any processing
            checkForIdempotency(transactionId, tx);
            account = requiredAccount(identity, cmd, tx);
            paymentInfos = paymentRepo().accountPaymentInfos(account, tx);
            accountPaymentMethod = previousPaymentMethod(req.getToken(), account, tx);
        }

        // ~ crm calls
        var accountInfoWrapper = accountService.getAccountPaymentInfo(identity, cmd.routingKey());
        var accountInfo = accountInfoWrapper.getAccountPaymentInfo();
        var accCtx = accountService.getAccountInfoContext(identity, account, accountInfoWrapper);

        PaymentOrder order = orderService.createOrder(CreatePaymentOrderContext.builder()
                .request(req)
                .account(account)
                .accountInfo(accountInfo)
                .accountInfoContext(accCtx)
                .at(cmd.at())
                .timestamp(cmd.timestamp())
                .isMobileApp(req.getIsMobileApp())
                .build());

        // ~ resolve method
        accountPaymentMethod.ifPresent(order::setPaymentMethod);
        cmd.addMetricTag(MetricTags.IS_TOKENISATION, String.valueOf(accountPaymentMethod.isEmpty()));

        // ~ resolve provider
        var providerName = req.getProvider().name().toLowerCase();
        cloudflareGatewayHookService.saveInKVUniqueId(providerName, transactionId.toString(), cmd.routingKey().toString());
        BrandInfo brandInfo = accountInfo.getBrandInfo();
        var brandName = brandInfo.getName();
        var country = accCtx.country();
        order.setProvider(resolveProvider(account, req.getProvider(), country));

        DefaultCreatePaymentOrderContext ctx = initPaymentOrderContext(req, accountInfoWrapper, paymentInfos, order, accCtx);
        ctx.cmd = cmd;
        var orderCtx = ctx.getPaymentOrderContext();

        PaymentService service = getPaymentProvider(req.getProvider().name());

        // for now applicable only to direct fiserv when no method exists, because of the way data is tokenized
        if (req.hasBillingAddress() && ctx.getPmCtx().getCardDetails().isEmpty()) {
            ctx.getPmCtx().setCardDetails(CardDetails.Impl.builder()
                    .country(CoreUtils.nullIfEmpty(Countries.adoptCountry(req.getBillingAddress().getCountry())))
                    .city(CoreUtils.nullIfEmpty(req.getBillingAddress().getCity()))
                    .region(CoreUtils.nullIfEmpty(req.getBillingAddress().getStateOrProvince()))
                    .postalCode(CoreUtils.nullIfEmpty(req.getBillingAddress().getPostalCode()))
                    .address1(CoreUtils.nullIfEmpty(req.getBillingAddress().getStreet()))
                    .address2(CoreUtils.nullIfEmpty(req.getBillingAddress().getHouseNumberOrName()))
                    .build());
        }
        assertPaymentMethodForVirtualWalletPaymentType(order);
        ctx.token = DefaultPaymentMethodContext.resolvePaymentMethodToken(order.paymentMethod().orElse(null)).orElse(req.getToken());

        try {
            service.resolveToken(ctx);
            throwIfOrderError(orderCtx);

            resolve3dsData(cmd.request(), ctx);
            service.beforeCreate(ctx);
            throwIfOrderError(orderCtx);

            allowanceService.assertPendingOrders(ctx);

            cmd.addMetricTag(MetricTags.PROVIDER, orderCtx.providerName());
            cmd.addMetricTag(MetricTags.PLATFORM, IdentityUtil.platform(req.getIdentity()).code());
            cmd.addMetricTag(MetricTags.MOBILE_APP, String.valueOf(req.getIsMobileApp()));
            cmd.addMetricTag(MetricTags.BRAND_NAME, brandName);

            order = saveGracefully(ctx);

            orderService.onCreate(order);
            doAssertions(cmd, account, accountInfo, ctx);
            initWebHookUrl(req, ctx);
        } catch (Exception e) {
            handleException(ctx, e);
            order = saveGracefully(ctx);
        } finally {
            handleOrderError(ctx, reply, cmd);
        }

        // ~ Fraud, card check
        final FraudInfo fraudInfo = new FraudInfo();
        fraudInfo.setLabel(PaymentOrder.INCOMPLETE);

        try {
            resolveFraudInfo(req, ctx, account, fraudInfo);

            checkForFraudBeforeThreeDs(reply, fraudInfo, orderCtx);
            boolean threeDsTriggered = threeDsRequiredAfterFraudCheck(req, ctx, account);
            checkForFraudAfterThreeDs(fraudInfo, ctx.isAllowHighFraudScore(), orderCtx);

            checkForCardDecline(ctx, account, accountInfo, order, cmd, fraudInfo);

            // as we resolve 3ds ctx after fraud check we should re-resolve provider if it doesn't support 3ds
            if (threeDsTriggered && StringUtils.isNotEmpty(ctx.sourceId()) && !ctx.threeDsCtx().get().supportsProvider(ctx.provider())) {
                logger.debug("Payment provider [{}] of 3ds order [{}] needs to be changed to 3ds supported", ctx.provider(), order.getId());
                ctx.setApiErrorCode(Code.ERR_PAYMENT_ROUTING);
                throw ApplicationException.of("3DS order recreate is required.", Code.ERR_3DS_PAYMENT_ROUTING);
            }

            beginScaAuthenticateIfRequired(ctx, req, order.getScaAuthentication(), account, fraudInfo);

            service.create(ctx);
            preserveAndNotify(ctx, cmd);

            cmd.addMetricTag(MetricTags.PROVIDER, ctx.providerName());
            if (ctx.hasError()) {
                fraudInfo.setLabel(getFraudLabel(ctx));
            }
            throwIfOrderError(orderCtx);
        } catch (Exception e) {
            // ~ drop MinimalImmutableThreeDsContext because we add reply with 3ds data in case of error
            ctx.setThreeDsCtx(null);
            handleException(ctx, e);
        } finally {
            if (ctx.hasError()) {
                service.onCreateFail(ctx);
            }
            // only for Fiserv
            cloudflareGatewayHookService.saveInKVCode(providerName, ctx.orderCode(), cmd.routingKey().toString());

            try (Transaction tx = ebean.newTransaction()) {
                String billingDescriptor = order.getProvider().getBillingDescriptor();
                addReply(order, req, reply, ctx, billingDescriptor);
                saveCustomerPaymentMappingIfNeeded(ctx, account, paymentInfos, tx);
                ctx.setAppliedRoutingRules(ctx.getAppliedRoutingRules());
                order = saveOrderAndMethodOnOperationComplete(ctx.getPaymentOrderContext(), ctx.getPaymentMethodContext(), tx);
                tx.commit();
            } catch (DuplicateKeyException err) {
                logger.warn(err.getMessage(), err);
                throw ApplicationException.rethrowing(err, Code.ERR_DUPLICATE);
            }

            boolean successfulPurchase = ctx.isSuccess();

            if (successfulPurchase) {
                fraudInfo.setLabel(PaymentOrder.SUCCESS);
            }

            fraudService.labelTransaction(fraudInfo, brandName, AsciiString.cached(accountInfo.getRouting().getHash()));
            orderService.onComplete(order, accCtx.paymentRouting(), accCtx.engagementInfo());

            if (successfulPurchase) {
                emailService.sendEmailAsync(cmd.routingKey(), identity, order, EmailTemplate.PURCHASE_CONFIRM, EmailTemplate.FIAT_DEPOSIT_CONFIRM,
                        EmailTemplate.CRYPTO_PURCHASE_CONFIRM);
            }

            handleOrderError(ctx, reply, cmd);
        }
    }

    private void handleException(DefaultCreatePaymentOrderContext ctx, Exception e) throws Throwable {
        logger.warn("Failure when creating payment order", e);
        e = wrapAsApplicationExceptionAtNetworkIssuePayment(e);
        if (!ctx.isSkipStatusUpdate()) {
            ctx.setInternalStatus(OrderStatusSpec.FAILED);
        }
        if (e instanceof ApplicationException ae) {
            ctx.getPaymentOrderContext().markError(PaymentOrderError.builder()
                    .message(ae.getMessage())
                    .apiError(ae.getMessage())
                    .exceptionClass(Optional.ofNullable(ExceptionUtils.getRootCause(ae)).orElse(ae).getClass().getSimpleName())
                    .errorCode(ae.getCode().toString().toLowerCase())
                    .build());
        } else {
            if (e instanceof PaymentOrderErrorException ignore) {
                // error already marked in context
                return;
            }
            // Unexpected exception
            var root = ExceptionUtils.getRootCause(e);
            ctx.getPaymentOrderContext().markError(PaymentOrderError.builder()
                    .message(e.getMessage())
                    .apiError(PaymentService.DEFAULT_ERROR_MSG)
                    .exceptionClass(root == null ? e.getClass().getSimpleName() : root.getClass().getSimpleName())
                    .errorCode(Code.ERR_SYSTEM.name().toLowerCase())
                    .build());
            saveGracefully(ctx);
            throw e;
        }
    }

    private void beginScaAuthenticateIfRequired(DefaultCreatePaymentOrderContext orderCtx,
                                                CreatePaymentOrderRequest req,
                                                ScaAuthentication sca,
                                                ImmutableAccount account,
                                                FraudInfo fraudInfo) throws Throwable {
        if (!orderCtx.isSecure3d()) {
            return;
        }

        if (sca == null) {
            sca = scaAuthService.startNewScaAuthentication(resolveRequiredScaInputDataOrThrow(orderCtx, req), orderCtx, account.getHash());
        }
        orderCtx.setScaAuthenticationId(sca.getId());
        if (StringUtils.isNotEmpty(sca.getError())) {
            handleScaError(orderCtx, sca, fraudInfo);
        }
        if (BooleanUtils.isTrue(Feature.ECI_CHECK.isEnabled(props, account)) && orderCtx.fraudScore().isPresent() && sca.getEcommerceIndicator() != null) {
            assertAllowedEci(orderCtx, sca);
        }
        String brand = account.getBrand().getName();
        orderCtx.setThreeDsCtx(new DefaultImmutableThreeDsContext(sca, orderCtx, scaAuthService.resolveScaProvidersMapping(brand).get(sca.getProviderTypeSpec()),
                orderCtx.amount(), orderCtx.provider(), account.getHash()));
    }

    private void handleScaError(DefaultCreatePaymentOrderContext orderCtx, ScaAuthentication sca, FraudInfo fraudInfo) {
        var errorCode = sca.getState() == ScaAuthState.FAILED ? Code.ERR_3DS_FAILED : Code.ERR_PAYMENT;
        orderCtx.getPaymentOrderContext().markError(PaymentOrderError.builder()
                .apiError(sca.getError())
                .errorCode(errorCode.toString().toLowerCase())
                .build());
        if (Code.ERR_3DS_FAILED.equals(errorCode) && isEciEligibleFor3dsFailedLabel(orderCtx.brand(), sca)) {
            fraudInfo.setLabel(PaymentOrder.FAILED + ": " + ERR_3DS_FAILED_LABEL);
        }
        throw new PaymentOrderErrorException();
    }

    private PaymentOrder saveGracefully(DefaultCreatePaymentOrderContext ctx) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            var order = saveOrderAndMethod(ctx.getPaymentOrderContext(), ctx.getPaymentMethodContext(), tx);
            tx.commit();
            return order;
        }
    }

    private static StartScaInputData resolveRequiredScaInputDataOrThrow(
            payment.context.order.CreatePaymentOrderContext orderCtx,
            CreatePaymentOrderRequest req) throws ApplicationException {
        if (req.hasSpreedly()) {
            return new SpreedlyStartScaInputData(orderCtx.provider(), req.getSpreedly().getBrowserInfo(), req.getToken(), orderCtx.currency(), orderCtx.amount());
        }
        throw ApplicationException.of("3DS input data is required.", Code.ERR_PAYMENT_3DS_REQUIRED);
    }

    private static void assertPaymentMethodForVirtualWalletPaymentType(PaymentOrder order) throws ApplicationException {
        var spec = PurchaseProviderSpec.fromServerApi(order.getProviderName());
        if (spec.getMethodType().isSpreedlyVirtualCard() && order.paymentMethod().isEmpty()) {
            throw ApplicationException.of("No payment method found for virtual payment type", Code.ERR_PAYMENT);
        }
    }

    private void doAssertions(TransactionalRequest<CreatePaymentOrderRequest, CreatePaymentOrderResponse.Builder> cmd, ImmutableAccount account,
                              AccountPaymentInfo accountInfo, DefaultCreatePaymentOrderContext ctx) throws Throwable {
        Optional<TransactionLimit> violatedTransactionLimit;
        Set<CardPaymentMethodMetaInfo> blockedCards;
        List<BlackList> blackList;
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            blockedCards = cardPaymentMethodMetaInfoRepo().uniqueByBinLast4GetByAccountIdBlocked(account.getId(), tx);
            checkProvider(cmd, ctx, tx, account);
            violatedTransactionLimit = assertPurchaseLimits(cmd, account, ctx.getPaymentOrderContext().getOrder(), accountInfo, tx);
            blackList = ebean.cardBinRepo().cardBinBlackList(account.getBrand(), ctx.country(), tx);
        }
        if (violatedTransactionLimit.isPresent()) {
            handleTransactionLimitViolation(violatedTransactionLimit.get(), cmd);
        }
        assertNoSessionRestrictions(accountInfo);
        assertNoPurchaseRestriction(accountInfo);
        assertPlayable(accountInfo.getPlayable());
        assertNoVerificationIsNeeded(cmd, accountInfo);
        blackListService.assertAllowed(ctx, blackList, blockedCards);
    }

    private void checkProvider(TransactionalRequest<CreatePaymentOrderRequest, CreatePaymentOrderResponse.Builder> cmd, DefaultCreatePaymentOrderContext ctx, Transaction tx,
                               ImmutableAccount account) throws Exception {
        String providerName = cmd.request().getProvider().name().toLowerCase();
        var params = ProviderRepo.FindProviderParams.create()
                .withTypes(ProviderTypeSpec.PURCHASE)
                .withBrands(ctx.brand())
                .withCodes(providerName);
        Provider provider = ebean.providerRepo().provider(params, tx)
                .orElseThrow(() -> ApplicationException.of("Unknown provider %s", Code.ERR_PAYMENT, providerName));

        if (provider.isInactiveForPurchase(account)) {
            throw ApplicationException.of("provider %s is inactive", Code.ERR_PAYMENT, cmd.request().getProvider().name().toLowerCase());
        }

        if (!account.isAdmin() && !provider.isEligibleForFirstPaymentOrWithdraw(account)) {
            throw ApplicationException.of("provider %s is not available for first purchase", Code.ERR_PAYMENT, cmd.request().getProvider().name().toLowerCase());
        }

        assertNotBlockedMethod(ctx, tx, account);
    }

    private void assertNotBlockedMethod(DefaultCreatePaymentOrderContext ctx, Transaction tx, ImmutableAccount account) throws ApplicationException {
        PurchaseProviderSpec purchaseProviderSpec = PurchaseProviderSpec.fromServerApi(ctx.provider);
        AccountProviderBlackList accountProviderBlackList = ebean.paymentRepo().getAccountProviderBlackList(account.getId(), tx);

        if (Optional.ofNullable(accountProviderBlackList)
                .map(AccountProviderBlackList::getPaymentMethods)
                .map(m -> m.contains(purchaseProviderSpec.getMethodType()))
                .orElse(false)) {
            throw ApplicationException.of("We're sorry, but there seems to be an issue with your payment method. Please contact us for more information.",
                    Code.ERR_PAYMENT_METHOD_BLOCKED);
        }
    }

    private void checkForCardDecline(DefaultCreatePaymentOrderContext ctx, ImmutableAccount account,
                                     AccountPaymentInfo accountInfo, PaymentOrder order,
                                     TransactionalRequest<CreatePaymentOrderRequest, CreatePaymentOrderResponse.Builder> cmd,
                                     FraudInfo fraudInfo) throws Throwable {
        if (order.paymentMethod().isEmpty()) {
            return;
        }
        var paymentMethod = order.paymentMethod().get();
        if (isNeedToCheckCardholderName(ctx, account.isAdmin())) {
            var metaInfoOpt = getCardPaymentMethodMetaInfo(ctx.accountId(), paymentMethod);
            if (metaInfoOpt.isEmpty() || cardIsNotVerified(metaInfoOpt.get())) {
                checkCardholderName(accountInfo, order, cmd.routingKey(), fraudInfo, ctx.getPaymentOrderContext());
            }
        }
    }

    private static String getFraudLabel(PaymentOrderContext ctx) {
        if (ERR_PAYMENT_INPUT_CVV.getValue().equals(ctx.errorCode())) {
            return PaymentOrder.FAILED + ": " + ERR_PAYMENT_INPUT_CVV.getValue();
        }
        return PaymentOrder.FAILED + ": " + ctx.errorDetails().orElse(ctx.apiError().map(Object::toString).orElse(""));
    }

    private boolean isNeedToCheckCardholderName(DefaultCreatePaymentOrderContext ctx, boolean isAdmin) {
        return Feature.BLOCK_3RD_PARTY_CARDS.isEnabled(props, ctx.brand(), ctx.accountId(), isAdmin)
                && props.THIRD_PARTY_CARD_PURCHASE_CHECK_ENABLED_FOR_BRAND.get().contains(ctx.brand())
                && PaymentMode.CARD.equals(PurchaseProviderSpec.valueOf(ctx.provider.name()).getPaymentMode())
                && BooleanUtils.isFalse(ctx.getPmCtx().prevMethodExists());
    }

    private static boolean cardIsNotVerified(CardPaymentMethodMetaInfo metaInfo) {
        return metaInfo.getThirdPartyCardVerificationStatus() != ThirdPartyCardVerificationStatus.VERIFIED;
    }

    private Optional<CardPaymentMethodMetaInfo> getCardPaymentMethodMetaInfo(Long accountId, AccountPaymentMethod paymentMethod) throws Throwable {
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            var cardBin = paymentMethod.cardPaymentMethod().map(payment.card.CardPaymentMethod::getBin);
            var lastFour = paymentMethod.cardPaymentMethod().map(payment.card.CardPaymentMethod::getLastFour);
            if (cardBin.isPresent() && lastFour.isPresent()) {
                return cardPaymentMethodMetaInfoRepo().cardPaymentMethodMetaInfo(accountId, cardBin.get(), lastFour.get(), tx);
            }
            return Optional.empty();
        }
    }

    private Optional<TransactionLimit> assertPurchaseLimits(TransactionalRequest<CreatePaymentOrderRequest, CreatePaymentOrderResponse.Builder> cmd,
                                                            ImmutableAccount account, PaymentOrder order,
                                                            AccountPaymentInfo accountInfo, Transaction tx) throws ApplicationException {
        var purchaseLimitOpt = accountService.getActualPurchaseLimit(tx, cmd.at(), account);
        if (purchaseLimitOpt.isPresent()) {
            verifyAndAddPurchaseLimits(order, purchaseLimitOpt.get(), cmd);
        }
        return transactionLimitService.checkDepositLimits(order, accountInfo, tx);
    }

    private void checkCardholderName(AccountPaymentInfo info, PaymentOrder order, AsciiString routing, FraudInfo fraudInfo,
                                     DefaultPaymentOrderContext ctx) throws PaymentOrderErrorException {
        boolean isNotMatched = fraudService.isCardholderNameMismatched(info, order);
        saveThirdPartyVerificationStatusFailed(order, isNotMatched, routing);
        if (isNotMatched && props.THIRD_PARTY_CARD_PURCHASE_CHECK_DECLINE_ENABLED.get()) {
            fraudInfo.setLabel(PaymentOrder.FAILED + ": " + ERR_CARDHOLDER_NAME_MISMATCH.name().toLowerCase());
            ctx.markError(PaymentOrderErrors.ERR_CARDHOLDER_NAME_MISMATCH.newError());
            throw new PaymentOrderErrorException();
        }
    }

    private void saveThirdPartyVerificationStatusFailed(PaymentOrder order, boolean isNotMatched, AsciiString routing) {
        var cardBinOpt = order.paymentMethod().flatMap(AccountPaymentMethod::cardPaymentMethod).map(payment.card.CardPaymentMethod::getBin);
        var cardLastOpt = order.paymentMethod().flatMap(AccountPaymentMethod::cardPaymentMethod).map(payment.card.CardPaymentMethod::getLastFour);
        if (cardBinOpt.isPresent() && cardLastOpt.isPresent()) {
            var cardBin = cardBinOpt.get();
            var lastFour = cardLastOpt.get();
            var status = convertThirdPartyCardVerificationStatus(isNotMatched);
            var req = SaveCardPaymentMethodMetaInfoRequest.newBuilder()
                    .setIdentity(Identity.newBuilder()
                            .setByAccountId(IdentityByAccountId.newBuilder().setAccountId(order.getAccount().getId()))
                            .build())
                    .setBin(cardBin)
                    .setLastFour(lastFour)
                    .setThirdPartyCardVerificationStatus(status)
                    .build();
            paymentServiceApi.saveCardPaymentMethodMetaInfo(req, routing);
        }
    }

    private static payment.api.v1.ThirdPartyCardVerificationStatus convertThirdPartyCardVerificationStatus(boolean isCardholderNameNotMatched) {
        if (isCardholderNameNotMatched) {
            return payment.api.v1.ThirdPartyCardVerificationStatus.VERIFICATION_FAILED;
        }
        return payment.api.v1.ThirdPartyCardVerificationStatus.VERIFIED;
    }

    private static void addReply(PaymentOrder order, CreatePaymentOrderRequest req,
                                 CreatePaymentOrderResponse.Builder reply,
                                 DefaultCreatePaymentOrderContext ctx,
                                 String billingDescriptor) {
        reply.setCode(ctx.orderCode());
        if (ctx.isSecure3d() && StringUtils.isNotEmpty(ctx.sourceId())) {
            reply.setSourceId(ctx.sourceId());
        }
        reply.setTransactionId(ctx.transactionId().toString());
        reply.setSecure3D(ctx.isSecure3d());
        reply.setTermUrl(ctx.termURL().toExternalForm());
        reply.setSupplier(ctx.supplier());
        reply.setCvvEntered(req.getCvvEntered());
        reply.setPaymentToken(req.getToken());
        reply.setQuickPurchaseSupportedType(req.getQuickPurchaseSupportedType());

        order.offer().ifPresent(offer -> {
            reply.setOffer(OfferMapper.toOffer(offer, ctx.currency(), ctx.amount()));
            reply.setOfferCode(offer.getCode());
            reply.setOfferPrice(ctx.baseAmount().toString());
        });

        reply.setProvider(ctx.providerName());
        reply.setCurrency(ctx.currency());
        reply.setAmount(ctx.amount().toString());
        reply.setIsFirstDeposit(true);
        reply.setOriginalProvider(req.getProvider());
        Optional.ofNullable(order.getAccount().getPaymentMetaInfo()).ifPresent(mi -> {
            reply.setIsFirstDeposit(mi.hasNoPurchases());
            mi.firstSuccessfulPurchaseOrDeposit().ifPresent(fpo -> reply.setFirstDeposit(fpo.getCreatedAt().getTime()));
        });
        ctx.getPmCtx().getCardDetails().ifPresent(ba -> {
            Optional.ofNullable(ba.getPostalCode()).ifPresent(reply::setZip);
            Optional.ofNullable(ba.getCity()).ifPresent(reply::setCity);
        });

        if (StringUtils.isNotEmpty(ctx.redirectUrl())) {
            reply.setRedirectUrl(ctx.redirectUrl());
        }
        if (StringUtils.isNotEmpty(ctx.tempToken())) {
            reply.setTempToken(ctx.tempToken());
        }
        if (StringUtils.isNotEmpty(ctx.paymentData())) {
            reply.setPaymentData(ctx.paymentData());
        }
        if (StringUtils.isNotEmpty(ctx.status())) {
            reply.setStatus(ctx.status());
        }
        if (StringUtils.isNoneEmpty(ctx.clientToken()) && StringUtils.isNoneEmpty(ctx.publicKeyBase64())) {
            reply.setClientToken(ctx.clientToken());
            reply.setPublicKeyBase64(ctx.publicKeyBase64());
        }
        if (StringUtils.isNotEmpty(ctx.completeMerchantValidation())) {
            reply.setCompleteMerchantValidation(ctx.completeMerchantValidation());
        }
        if (StringUtils.isNotEmpty(billingDescriptor)) {
            reply.setBillingDescriptor(billingDescriptor);
        }
        ctx.threeDsCtx().ifPresent(secureCtx -> reply.setScaAuthenticateResponse(ScaAuthenticateResponse.newBuilder()
                .setState(ScaAuthenticateState.valueOf(secureCtx.getState().name().toUpperCase()))
                .setAuthenticateTxId(secureCtx.getAuthenticateTxId())
                .build()));
        if (StringUtils.isNotEmpty(ctx.description())) {
            reply.setDescription(ctx.description());
        }
        reply.setMethod(Mappers.toPurchaseMethod(order));
        reply.setInternalStatus(Mappers.toOrderStatusSpec(ctx.internalStatus()));
    }

    private static void initWebHookUrl(CreatePaymentOrderRequest req, DefaultCreatePaymentOrderContext ctx) throws Exception {
        if (StringUtils.isNotEmpty(req.getReferrer())) {
            Map<String, String> successUriParams = new HashMap<>();
            successUriParams.put("payment_status", "success");
            successUriParams.put("transaction_id", ctx.transactionId().toString());
            successUriParams.put("originalProvider", ctx.providerName());
            successUriParams.put("currency", ctx.currency());
            successUriParams.put("amount", ctx.amount().toString());

            ctx.offerCode().ifPresent(offer -> successUriParams.put("offer", offer));

            successUriParams.put("description", ctx.description());
            ctx.successURL = UrlUtils.addParams(req.getReferrer(), successUriParams);

            Map<String, String> cancelledUriParams = new HashMap<>();
            cancelledUriParams.put("payment_status", "cancelled");
            cancelledUriParams.put("transaction_id", ctx.transactionId().toString());
            cancelledUriParams.put("originalProvider", ctx.providerName());
            ctx.cancelURL = UrlUtils.addParams(req.getReferrer(), cancelledUriParams);
        }
    }

    private DefaultCreatePaymentOrderContext initPaymentOrderContext(
            CreatePaymentOrderRequest req,
            AccountPaymentInfoWrapper accountPaymentInfoWrapper,
            List<payment.model.AccountPaymentInfo> paymentInfos,
            PaymentOrder order,
            AccountInfoContext accountInfoContext) throws MalformedURLException, URISyntaxException {
        BrandInfo brandInfo = accountPaymentInfoWrapper.getAccountPaymentInfo().getBrandInfo();
        Identity identity = req.getIdentity();
        DefaultCreatePaymentOrderContext ctx = new DefaultCreatePaymentOrderContext(order, accountPaymentInfoWrapper, paymentInfos, accountInfoContext, this.ebean);
        ctx.sweepstake = accountPaymentInfoWrapper.getAccountPaymentInfo().getSweepstake();
        ctx.termURLBase = brandRepo().getBrandSettings(order.getAccount().getBrand(), null).getPaymentWebhookUrl();
        ctx.successURL = new URL(brandInfo.getHomePage());
        ctx.cancelURL = new URL(brandInfo.getHomePage());
        ctx.remoteIp = identity.hasByToken() ? identity.getByToken().getRemoteIp() : "127.0.0.1";
        ctx.cvvEntered = req.getCvvEntered();
        ctx.referer = req.getReferrer();
        ctx.at = req.getAt();
        ctx.validationUrl = req.getValidationUrl();
        if (req.hasCryptoData()) {
            ctx.cryptoData = Pair.of(CryptoCurrency.valueOf(req.getCryptoData().getCurrency().name()), CryptoNetwork.valueOf(req.getCryptoData().getNetwork().name()));
        }

        ctx.provider = req.getProvider();
        ctx.termURL = new URIBuilder(ctx.termURLBase.toURI()).setPathSegments("v1", "hook", req.getProvider().name().toLowerCase(), order.getAccount().getBrand().getName()).build().toURL();
        return ctx;
    }

    private void checkForIdempotency(UUID transactionId, Transaction tx) throws Exception {
        Optional<PaymentOrder> existing = paymentRepo().orderByTransactionId(transactionId, tx);
        if (existing.isPresent()) {
            logger.warn("Found existing order with same transactionId {}", transactionId);
            throw ApplicationException.of("Transaction already processed - Please contact {link_support}", Code.ERR_DUPLICATE);
        }
    }

    private FraudInfo resolveFraudInfo(
            CreatePaymentOrderRequest req,
            DefaultCreatePaymentOrderContext ctx,
            ImmutableAccount account,
            FraudInfo fraudInfo) throws Throwable {
        PaymentFraudCheckResponse checkFraudResult = checkIsFraud(req, ctx, account);

        boolean toDecline = checkFraudResult.getIsFraud();
        fraudService.updateFraudInfo(checkFraudResult, fraudInfo);
        saveFraudInfo(toDecline, fraudInfo, ctx);
        addAppliedFraudRulesToPaymentMethod(ctx, checkFraudResult);
        return fraudInfo;
    }

    private void checkForFraudBeforeThreeDs(CreatePaymentOrderResponse.Builder reply, FraudInfo fraudInfo,
                                            DefaultPaymentOrderContext ctx) {
        if (KYC_REQUIRED_LABEL.equals(fraudInfo.getLabel())) {
            reply.setRequestKyc(true);
            ctx.markError(KYC_ERROR.newError(fraudInfo.getErrorCode(), fraudInfo.getDeclineReason()));
            throw new PaymentOrderErrorException();
        }
    }

    private void checkForFraudAfterThreeDs(FraudInfo fraudInfo, boolean allowHighFraudScore, DefaultPaymentOrderContext ctx) {
        boolean toDecline = fraudInfo.isFraud();
        if (toDecline) {
            String apiError = PaymentOrderErrors.FRAUD_DECLINED.getApiError();
            if (StringUtils.isNotEmpty(fraudInfo.getDeclineReason())) {
                apiError = fraudInfo.getDeclineReason();
            }
            if (fraudInfo.getDeclineCode() != Code.ERR_OK) {
                fraudInfo.setLabel("failed: " + fraudInfo.getDeclineCode().name().toLowerCase());
                ctx.markError(new PaymentOrderError(fraudInfo.getDeclineCode().name().toLowerCase(), fraudInfo.getDeclineReason(), apiError));
                throw new PaymentOrderErrorException();
            } else if (!allowHighFraudScore) {
                ctx.markError(PaymentOrderErrors.FRAUD_DECLINED.newError(apiError));
                throw new PaymentOrderErrorException();
            }
        }
    }

    private void addAppliedFraudRulesToPaymentMethod(DefaultCreatePaymentOrderContext ctx, PaymentFraudCheckResponse checkFraudResult) {
        var provider = ctx.getPaymentOrderContext().getOrder().getProvider();
        if (checkFraudResult.hasInfo() && checkFraudResult.getInfo().hasFraudResponse()) {
            checkFraudResult.getInfo().getFraudResponse().getAppliedRulesList()
                    .forEach(r -> ctx.getPaymentMethodContext().addAppliedFraudRule(provider, r.getId()));
        }
    }

    private PaymentFraudCheckResponse checkIsFraud(CreatePaymentOrderRequest req, DefaultCreatePaymentOrderContext ctx, ImmutableAccount account)
            throws Throwable {
        Optional<CardPaymentMethod> cardPaymentMethod = getCardPaymentMethod(ctx);
        var sreqb = paymentFraudCheckRequest(req.getIdentity(), req.getSession(), ctx, account);
        cardPaymentMethod.ifPresent(sreqb::setPaymentMethod);
        var response = fraudService.checkPaymentFraud(sreqb.build(), account.routingKey(), account.getBrand().getName());

        if (response.getInfo().getFraudResponse().hasCardBinDetails()) {
            var cardBinDetails = response.getInfo().getFraudResponse().getCardBinDetails();
            ctx.setCardBinDetails(new CardBinDetails(cardBinDetails.getBin(), cardBinDetails.getBank(), cardBinDetails.getType(), cardBinDetails.getLevel(),
                    cardBinDetails.getCountryCode()));
        }
        return response;
    }

    private static void throwIfOrderError(PaymentOrderContext ctx) throws PaymentOrderErrorException {
        if (ctx.hasError()) {
            throw new PaymentOrderErrorException();
        }
    }

    private void handleOrderError(
            DefaultCreatePaymentOrderContext ctx,
            CreatePaymentOrderResponse.Builder reply,
            TransactionalRequest<CreatePaymentOrderRequest, CreatePaymentOrderResponse.Builder> cmd) throws Throwable {
        var order = ctx.getPaymentOrderContext().getOrder();
        if (ctx.hasError()) {
            if (StringUtils.isNotEmpty(ctx.sourceId())) {
                reply.setSourceId(ctx.sourceId());
            }
            reply.setProvider(ctx.providerName());
            reply.setCurrency(ctx.currency());
            reply.setError(toProtoError(order, ctx.country()));
            reply.setInternalStatus(Mappers.toOrderStatusSpec(ctx.internalStatus()));
            if (Objects.nonNull(ctx.baseAmount())) {
                reply.setOfferPrice(ctx.baseAmount().toString());
            }
            order.offer().ifPresent(offer -> reply.setOffer(OfferMapper.toOffer(offer, ctx.currency(), ctx.amount())));
            ctx.errorDetails().ifPresent(exCode -> cmd.addMetricTag(MetricTags.EXTERNAL_ERROR_CODE, exCode));
            cmd.preserveNotifications();
            cmd.preserveReply();
            throw ApplicationException.of(ctx.apiError().get().toString(), ctx.getApiErrorCode() != null ? ctx.getApiErrorCode() : Code.valueOf(ctx.errorCode().toUpperCase()));
        }
    }

    private Optional<CardPaymentMethod> getCardPaymentMethod(DefaultCreatePaymentOrderContext ctx) {
        return ctx.getPmCtx().getCardDetails().map(cpm -> {
            var card = Mappers.toCardPaymentMethod(cpm, ctx.getPmCtx().getFingerprint().orElse(null));
            if (props.EIGHT_DIGITS_CARD_BIN_ENABLED.get() && StringUtils.isNotEmpty(cpm.getFirstEight())) {
                card.setCardNumber(cpm.getFirstEight());
            }
            if (StringUtils.isNotEmpty(ctx.transactionId().toString())) {
                card.setOrderId(ctx.transactionId().toString());
            }
            return card.build();
        });
    }

    protected Optional<AccountPaymentMethod> previousPaymentMethod(String token, ImmutableAccount account, Transaction tx) {
        return previousPaymentMethod(token, account, null, tx);
    }

    private PaymentService getPaymentProvider(String provider) throws ApplicationException {
        PaymentService service = paymentProviders.get(provider.toLowerCase().intern());
        if (Objects.isNull(service)) {
            String providerName = provider.toLowerCase();
            logger.error("Payment service for provider {} was not found.", providerName);
            throw ApplicationException.of("Payment service for provider " + providerName + " was not found", Code.ERR_NOT_FOUND);
        }
        return service;
    }

    private static void assertNoVerificationIsNeeded(
            TransactionalRequest<CreatePaymentOrderRequest, CreatePaymentOrderResponse.Builder> cmd,
            AccountPaymentInfo accountPurchaseInfo) throws ApplicationException {
        var reply = cmd.reply();
        //
        // ~ KYC check required
        //
        if (accountPurchaseInfo.getStatus().equals(AccountStatusSpec.REQUIRES_JUMIO_KYC.code())) {
            reply.setRequestKyc(true);
            cmd.preserveNotifications();
            cmd.preserveReply();
            throw ApplicationException.of("To protect your account please verify your identity", Code.ERR_KYC_REQUIRED);
        }
    }

    private void saveFraudInfo(boolean isFraud, FraudInfo fraudInfo, DefaultCreatePaymentOrderContext ctx) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            ctx.setFraudScore(fraudInfo.getFraudScore());
            ctx.setFraudRequestId(fraudInfo.getRequestID());
            fraudInfo.getFraudResponseId().ifPresent(ctx::setFraudResponseId);
            if (isFraud) {
                ctx.setSuccess(false);
            }
            ctx.setFraud(isFraud);
            saveOrderAndMethod(ctx.getPaymentOrderContext(), ctx.getPaymentMethodContext(), tx);

            tx.commit();
        }
    }

    private Provider resolveProvider(ImmutableAccount account, PaymentProvider provider, String country) throws Throwable {
        var providerMaybe = providerService.getAvailablePurchaseProvider(account, country, provider);
        if (providerMaybe.isEmpty()) {
            logger.error("Purchase payment provider {} was not found.", provider);
            throw ApplicationException.of("Purchase payment provider " + provider + " was not found", Code.ERR_NOT_FOUND);
        }
        return providerMaybe.get();
    }

    private static void verifyAndAddPurchaseLimits(
            PaymentOrder order,
            PurchaseLimit limits,
            TransactionalRequest<CreatePaymentOrderRequest, CreatePaymentOrderResponse.Builder> cmd)
            throws ApplicationException {
        BigDecimal amount = order.getAmount();
        BigDecimal available = limits.getAvailable();
        String limPeriod = limits.getLimPeriod().getLabel();
        BigDecimal threshold = limits.getLimit();
        cmd.reply().setLimitAmount(String.valueOf(threshold));
        cmd.reply().setLimitAvailable(String.valueOf(available));
        cmd.reply().setLimitPeriod(limPeriod);
        cmd.reply().setLimitEnd(CommonMappers.toDate(limits.getEnd()));

        if (available.subtract(amount).doubleValue() < 0) {
            cmd.preserveReply();
            throw ApplicationException.of("You exceeded your purchase limit: " + threshold + " by the period of " + limPeriod, Code.ERR_PAYMENT_PURCHASE_LIMIT);
        }
    }

    private void resolve3dsData(CreatePaymentOrderRequest req, DefaultCreatePaymentOrderContext ctx) throws Throwable {
        var triggerSecure3dCheck = false;
        if (scaAuthService.isSupported(ctx.originalProvider(), ctx)) {
            triggerSecure3dCheck = true;
            try (var tx = ebean.newReadOnlyTransaction()) {
                ScaAuthentication sca;
                if (StringUtils.isNotEmpty(req.getSourceId())) {
                    // ~ subsequent payment attempt
                    var poOpt = ebean.paymentRepo().first3DSResolvedBySourceId(req.getSourceId(), ctx.accountId(), tx);
                    if (poOpt.isPresent()) {
                        ctx.setAllowHighFraudScore(Boolean.TRUE.equals(poOpt.get().getFraud()));
                        sca = poOpt.get().getScaAuthentication();
                        triggerSecure3dCheck = false;
                        ctx.setSecure3d(poOpt.get().getSecure3d());
                        logger.debug(String.format("Resolved order with 3ds state [%s] from parent in chain [%s], sca present state [%s]", poOpt.get().getSecure3d(), poOpt.get().getTransactionId(), sca != null));

                        if (sca == null && ctx.isSecure3d()) {
                            // sca for 3ds order could be absent in err_3ds_routing error as example
                            var withScaMaybe = ebean.paymentRepo().firstBySourceIdWithSCA(req.getSourceId(), ctx.accountId(), tx);
                            if (withScaMaybe.isPresent()) {
                                sca = withScaMaybe.get().getScaAuthentication();
                                logger.debug(String.format("Resolved sca from order [%s]", withScaMaybe.get().getTransactionId()));
                            } else {
                                resolveRequiredScaInputDataOrThrow(ctx, req);
                            }
                        }
                        if (sca != null) {
                            ctx.setScaAuthenticationId(sca.getId());
                        }
                    }
                }
            }
            ctx.setTriggerSecure3dCheck(triggerSecure3dCheck);
            logger.debug(String.format("3ds check should be triggered: [%s]", triggerSecure3dCheck));
            if (ctx.isSecure3d()) {
                ctx.setThreeDsCtx(new MinimalImmutableThreeDsContext(ctx, scaAuthService.threeDsSupported(ctx.brand()), ctx.amount()));
            }
        }
    }

    private boolean threeDsRequiredAfterFraudCheck(CreatePaymentOrderRequest req, DefaultCreatePaymentOrderContext ctx, ImmutableAccount account)
            throws Throwable {
        if (ctx.isTriggerSecure3dCheck()) {
            if (scaAuthService.isSupported(ctx.originalProvider(), ctx)) {
                try (var tx = ebean.newReadOnlyTransaction()) {
                    var evalResult = scaAuthService.isRequired(ctx, account, tx);
                    boolean scaRequired = evalResult.isScaRequired();
                    ctx.setSecure3d(scaRequired);
                    logger.debug(String.format("Sca required result: [%s], allow high fraud score: [%s]", evalResult.toTextReason(), evalResult.isAllowHighFraudScore()));
                    if (scaRequired) {
                        ctx.setSecure3dReason(evalResult.toTextReason());
                        ctx.setThreeDsCtx(new MinimalImmutableThreeDsContext(ctx, scaAuthService.threeDsSupported(ctx.brand()), ctx.amount()));
                        ctx.setAllowHighFraudScore(evalResult.isAllowHighFraudScore());
                        resolveRequiredScaInputDataOrThrow(ctx, req);
                        return true;
                    }
                }
            }
            logger.debug("Sca check skipped due unsupported provider");
            ctx.setSecure3d(false);
        }
        return false;
    }

    private void handleTransactionLimitViolation(TransactionLimit limit,
                                                 TransactionalRequest<CreatePaymentOrderRequest, CreatePaymentOrderResponse.Builder> cmd) throws Exception {
        cmd.preserveReply();
        if (limit.getType() == TransactionLimitTypeSpec.TOTAL_DEPOSIT_BEFORE_KYC) {
            var req = SetAccountFraudInfoRequest.newBuilder()
                    .setIdentity(cmd.request().getIdentity())
                    .setStatus(AccountStatusSpec.REQUIRES_JUMIO_KYC.code())
                    .build();
            uamServiceApi.setAccountFraudInfo(req, cmd.routingKey()).get().verifyOk();
            cmd.reply().setRequestKyc(true);
            throw ApplicationException.of(KYC_ERROR.getApiError(), KYC_ERROR.getCode());
        }
        throw ApplicationException.of(ERR_PURCHASE_LIMIT.getApiError(), ERR_PURCHASE_LIMIT.getCode());
    }

    private void assertAllowedEci(DefaultCreatePaymentOrderContext orderCtx, ScaAuthentication sca) throws ApplicationException {
        if (orderCtx.fraudScore().get().compareTo(props.ECI_CHECK_FRAUD_SCORE_THRESHOLD.get(orderCtx.brand())) >= 0 &&
                props.ECI_CHECK_RESTRICTED_IDENTIFIERS.get(orderCtx
                        .brand()).contains(sca.getEcommerceIndicator())) {
            throw ApplicationException.of("Issuer doesn't support required 3ds", Code.ERR_3DS_NOT_SUPPORTED);
        }
    }
}
