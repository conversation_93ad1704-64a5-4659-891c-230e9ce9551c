package payment.handlers.modify.offer;

import static common.utils.ProtoUtils.getNullableValue;
import static payment.model.ApprovalStatusSpec.SENT_FOR_APPROVAL;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import api.v1.ApplicationException;
import api.v1.Code;
import common.utils.ProtoUtils;
import io.ebean.Transaction;
import jakarta.inject.Inject;
import payment.PaymentJpaManager;
import payment.PaymentServerProperties;
import payment.api.v1.OfferTemplateAdminInfo;
import payment.handlers.AbstractRequestHandler;
import payment.model.ApprovalStatusSpec;
import payment.model.OfferRuleSpec;
import payment.model.OfferTemplate;
import payment.model.immutable.ImmutableBrand;
import payment.type.OfferPlatformSpec;
import payment.type.OfferTypeSpec;
import payment.type.WelcomeOfferTypeSpec;
import uam.api.v1.ApprovalStatus;
import uam.api.v1.SaveOfferTemplateRequest;
import uam.api.v1.SaveOfferTemplateRequestOrBuilder;
import uam.api.v1.SaveOfferTemplateResponse;

@Service
public class SaveOfferTemplateRequestHandler
        extends AbstractRequestHandler<SaveOfferTemplateRequest, SaveOfferTemplateResponse.Builder>
        implements ModifyHandler<SaveOfferTemplateRequest, SaveOfferTemplateResponse.Builder> {

    @Inject
    public SaveOfferTemplateRequestHandler(PaymentServerProperties props, PaymentJpaManager ebean) {
        super(props, ebean);
    }

    @Override
    public void apply(TransactionalRequest<SaveOfferTemplateRequest, SaveOfferTemplateResponse.Builder> cmd) throws Throwable {
        SaveOfferTemplateRequestOrBuilder req = cmd.request();

        try (Transaction tx = ebean.newTransaction()) {
            var info = req.getOffer();
            var brand = brandRepo().requiredBrandByName(req.getBrandName(), tx);
            var approvalFlowEnabled = props.OFFER_TEMPLATE_APPROVAL_ENABLED.get(brand.getName());
            var selfApproved = req.getSelfApproved();

            var code = info.getCode();
            var offer = offerTemplateRepo().offerTemplateByCode(brand, code, tx).orElse(new OfferTemplate(brand, code));

            if (approvalFlowEnabled && !selfApproved && offer.getId() != null && offer.getApprovalStatus() == SENT_FOR_APPROVAL) {
                throw ApplicationException.of(
                        String.format("Offer with ID '%s' is in 'Sent for approval' status and cannot be edited.", offer.getId()),
                        Code.ERR_BAD_REQUEST
                );
            }

            var offerTemplateUpgrade = findUpgradeOffer(brand, info.getUpgradeCode(), tx);
            var offerTemplate = buildOfferTemplate(offer, info, offerTemplateUpgrade, approvalFlowEnabled, selfApproved, req.getStatusApproval(),
                    cmd.timestamp());

            if (props.OFFER_TEMPLATE_SC_RESTRICTION_ENABLED.get(brand.getName())) {
                var maxScRestriction = props.OFFER_TEMPLATE_SC_RESTRICTION_MAX.get(brand.getName()).get(offer.getType()).get(brand.getName());
                if (offerTemplate.getSweepstakeAmount().divide(offerTemplate.getPrice(), RoundingMode.UP).min(BigDecimal.ONE).intValue() * 100 > maxScRestriction) {
                    throw ApplicationException.of(
                            "Sweepcoins restriction exceeded for offer with ID '%d'".formatted(offer.getId()),
                            Code.ERR_BAD_REQUEST
                    );
                }
            }

            isUpgradeApplicable(offerTemplate, tx);

            ebean.save(offerTemplate, tx);

            tx.commit();
        }
    }

    private void isUpgradeApplicable(OfferTemplate offerTemplate, Transaction tx) throws ApplicationException {
        if (offerTemplate.getUpgradeId() == null) {
            return;
        }
        if (offerTemplate.getUpgrade().getUpgradeId() != null) {
            throw ApplicationException.of(String.format("Selected upgrade id %d already has upgrade offer. It is not allowed.", offerTemplate.getUpgradeId()),
                    Code.ERR_NOT_FOUND);
        }

        if (offerTemplate.getId() == null) {
            return;
        }
        boolean alreadyUpgrade = offerTemplateRepo().offerTemplateByUpgradeId(offerTemplate.getBrand(), offerTemplate.getId(), tx).isPresent();
        if (alreadyUpgrade) {
            throw ApplicationException.of(
                    String.format("Selected offer %s is already upgrade for another offer and can't have also upgrade.", offerTemplate.getId()),
                    Code.ERR_NOT_FOUND);
        }
    }

    private OfferTemplate buildOfferTemplate(OfferTemplate offer,
                                             OfferTemplateAdminInfo info,
                                             Optional<OfferTemplate> offerTemplateUpgrade,
                                             boolean approvalFlowEnabled,
                                             boolean selfApproved,
                                             ApprovalStatus approvalStatus,
                                             Date timestamp)
            throws Exception {
        offer.setInactive(info.getInactive());
        offer.setType(OfferTypeSpec.fromString(info.getType()));
        offer.setTitle(getNullableValue(info.getTitle()));
        offer.setPrice(getNullableValue(info.getPrice(), BigDecimal::valueOf));
        offer.setOldPrice(getNullableValue(info.getOldPrice(), BigDecimal::valueOf));
        offer.setStartAt(getNullableValue(info.getStartAt(), Date::new));
        offer.setEndAt(getNullableValue(info.getEndAt(), Date::new));
        offer.setGoldAmount(getNullableValue(info.getGoldAmount(), BigDecimal::valueOf));
        offer.setSweepstakeAmount(getNullableValue(info.getSweepstakeAmount(), BigDecimal::valueOf));
        offer.setGoldFirstOfferAmount(getNullableValue(info.getGoldFirstAmount(), BigDecimal::valueOf));
        offer.setSweepstakeFirstOfferAmount(getNullableValue(info.getSweepstakeFirstAmount(), BigDecimal::valueOf));
        offer.setFreeSpins(info.getFreeSpins());
        offer.setSegment(getNullableValue(info.getSegment()));
        offer.setSegmentTags(new ArrayList<>(info.getSegmentTagsList()));
        offer.setExcludeSegment(getNullableValue(info.getExcludeSegment()));
        offer.setExcludeSegmentTags(new ArrayList<>(info.getExcludeSegmentTagsList()));
        offer.setVipPoints(getNullableValue(info.getVipPoints(), BigDecimal::valueOf));
        offer.setVipLevels(new ArrayList<>(info.getVipLevelsList()));
        offer.setXpLevels(new ArrayList<>(info.getXpLevelsList().stream().map(Integer::parseInt).toList()));
        offer.setTags(new ArrayList<>(info.getTagsList()));
        offer.setPriority(ProtoUtils.getValue(info.getPriority(), val -> val, 0));
        offer.setRules(getRules(info));
        offer.setInboxNotification(info.getInboxNotification());
        offer.setSupportedPlatform(getAndValidatePlatform(offer, info));
        if (StringUtils.isNotEmpty(info.getBannerImageUrl())) {
            offer.setBannerImageUrl(new URL(info.getBannerImageUrl()));
        } else {
            offer.setBannerImageUrl(null);
        }
        if (StringUtils.isNotEmpty(info.getPopUpImageUrl())) {
            offer.setPopUpImageUrl(new URL(info.getPopUpImageUrl()));
        } else {
            offer.setPopUpImageUrl(null);
        }
        if (StringUtils.isNotEmpty(info.getSpecialOfferImageURL())) {
            offer.setSpecialOfferUrl(new URL(info.getSpecialOfferImageURL()));
        } else {
            offer.setSpecialOfferUrl(null);
        }
        offer.setShowStickybar(info.getShowStickybar());
        offer.setShowTimeLeft(info.getShowTimeLeft());
        setMinWeeklyWageredGoldCoins(offer, info);
        offer.setCapacityPerOffer(getCapacity(info.getCapacityPerOffer()));
        offer.setCapacityPerPlayer(getCapacity(info.getCapacityPerPlayer()));
        offer.setCountries(new HashSet<>(info.getCountriesList()));
        offerTemplateUpgrade.ifPresentOrElse(upgradeTemplate -> {
                    offer.setUpgradeId(upgradeTemplate.getId());
                    offer.setUpgrade(upgradeTemplate);
                },
                () -> offer.setUpgradeId(null));
        offer.setExternalRewardCode(getNullableValue(info.getExternalRewardCode()));
        if (StringUtils.isNotEmpty(info.getIconImageURL())) {
            offer.setIconImageUrl(new URL(info.getIconImageURL()));
        } else {
            offer.setIconImageUrl(null);
        }
        if (StringUtils.isNotEmpty(info.getBackgroundImageURL())) {
            offer.setBackgroundImageUrl(new URL(info.getBackgroundImageURL()));
        } else {
            offer.setBackgroundImageUrl(null);
        }
        if (StringUtils.isNotEmpty(info.getHomepageBannerImageURL())) {
            offer.setHomepageBannerImageUrl(new URL(info.getHomepageBannerImageURL()));
        } else {
            offer.setHomepageBannerImageUrl(null);
        }
        if (StringUtils.isNotEmpty(info.getBackgroundBorder())) {
            offer.setBackgroundBorder(info.getBackgroundBorder());
        } else {
            offer.setBackgroundBorder(null);
        }
        offer.setCreator(info.getCreator());

        if (approvalFlowEnabled) {
            if (selfApproved) {
                if (ApprovalStatus.DRAFT.equals(approvalStatus)) {
                    offer.setApprovalStatus(ApprovalStatusSpec.DRAFT);
                } else {
                    offer.setApprovalStatus(ApprovalStatusSpec.AUTO_APPROVED);
                }
                offer.setApprover(info.getCreator());
                offer.setApproverActionedAt(timestamp);
            } else {
                if (offer.getId() == null) {
                    if (ApprovalStatus.DRAFT.equals(approvalStatus)) {
                        offer.setApprovalStatus(ApprovalStatusSpec.DRAFT);
                    } else {
                        offer.setApprovalStatus(ApprovalStatusSpec.SENT_FOR_APPROVAL);
                    }
                }
            }
        } else {
            if (ApprovalStatus.DRAFT.equals(approvalStatus)) {
                offer.setApprovalStatus(ApprovalStatusSpec.DRAFT);
            } else {
            offer.setApprovalStatus(ApprovalStatusSpec.APPROVED);
            }
        }

        if (StringUtils.isNotEmpty(info.getWelcomeOfferType())) {
            offer.setWelcomeOfferType(WelcomeOfferTypeSpec.fromString((info.getWelcomeOfferType())));
        } else {
            offer.setWelcomeOfferType(WelcomeOfferTypeSpec.NONE);
        }

        if (StringUtils.isNotEmpty(info.getUtmSource())) {
            offer.setUtmSource(info.getUtmSource());
        } else {
            offer.setUtmSource(null);
        }

        if (StringUtils.isNotEmpty(info.getUtmMedium())) {
            offer.setUtmMedium(info.getUtmMedium());
        } else {
            offer.setUtmMedium(null);
        }

        if (StringUtils.isNotEmpty(info.getUtmCampaign())) {
            offer.setUtmCampaign(info.getUtmCampaign());
        } else {
            offer.setUtmCampaign(null);
        }

        if (StringUtils.isNotEmpty(info.getUtmContent())) {
            offer.setUtmContent(info.getUtmContent());
        } else {
            offer.setUtmContent(null);
        }

        if (StringUtils.isNotEmpty(info.getUtmTerm())) {
            offer.setUtmTerm(info.getUtmTerm());
        } else {
            offer.setUtmTerm(null);
        }

        if (StringUtils.isNotEmpty(info.getAffiliateCxd())) {
            offer.setAffiliateCxd(info.getAffiliateCxd());
        } else {
            offer.setAffiliateCxd(null);
        }

        if (StringUtils.isNotEmpty(info.getReferralCode())) {
            offer.setReferralCode(info.getReferralCode());
        } else {
            offer.setReferralCode(null);
        }

        return offer;
    }

    private static List<OfferRuleSpec> getRules(OfferTemplateAdminInfo info) {
        return info.getRulesList().stream().map(OfferRuleSpec::fromString).toList();
    }

    private static OfferPlatformSpec getAndValidatePlatform(OfferTemplate offer, OfferTemplateAdminInfo info) throws ApplicationException {
        if (Objects.nonNull(offer.getId()) && !offer.getSupportedPlatform().code().equals(info.getPlatform())) {
            throw ApplicationException.of("Invalid procedure : platform change for existing offer %s ", Code.ERR_BAD_REQUEST, offer);
        }
        return OfferPlatformSpec.fromString(info.getPlatform());
    }

    private static void setMinWeeklyWageredGoldCoins(OfferTemplate offer, OfferTemplateAdminInfo offerInfo) {
        long minWeeklyWageredGold = offerInfo.getMinWeeklyWageredGoldCoins();
        if (minWeeklyWageredGold == 0 && offer.getMinWeeklyWageredGoldCoins() != null && offer.getMinWeeklyWageredGoldCoins().compareTo(BigDecimal.ZERO) >= 0) {
            offer.setMinWeeklyWageredGoldCoins(BigDecimal.ZERO);
        } else if (minWeeklyWageredGold > 0) {
            offer.setMinWeeklyWageredGoldCoins(new BigDecimal(minWeeklyWageredGold));
        }
    }

    private static BigInteger getCapacity(long capacity) {
        if (capacity == 0) {
            return null;
        }
        return BigInteger.valueOf(capacity);
    }

    private Optional<OfferTemplate> findUpgradeOffer(ImmutableBrand brand, String upgradeCode, Transaction tx) throws ApplicationException {
        Optional<OfferTemplate> upgradeOffer = Optional.empty();
        if (getNullableValue(upgradeCode) != null) {
            upgradeOffer = offerTemplateRepo().offerTemplateByCode(brand, upgradeCode, tx);
            if (upgradeOffer.isEmpty()) {
                throw ApplicationException.of(String.format("Upgrade offer with such code %s doesn't exist", upgradeCode), Code.ERR_NOT_FOUND);
            }
        }
        return upgradeOffer;
    }
}
