package influencer.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static influencer.dto.GiveawayStatus.ACTIVE;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import influencer.repo.AccountRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.turbospaces.common.PlatformUtil;

import api.v1.ApplicationException;
import io.ebean.Transaction;
import influencer.dto.CreateGiveawayPayloadDto;
import influencer.dto.GiveawayDto;
import influencer.dto.GiveawayPrizeType;
import influencer.dto.GiveawayStatus;
import influencer.dto.InfluencerDto;
import influencer.model.InfluencerAccount;
import influencer.model.InfluencerBrand;
import influencer.model.Giveaway;
import influencer.model.GiveawayPrizePayload;
import influencer.model.GiveawaySubscriber;
import influencer.model.Influencer;
import influencer.repo.GiveawayRepo;
import influencer.repo.GiveawaySubscriberRepo;
import influencer.repo.BudgetSpendRepo;
import influencer.repo.InfluencerRepo;
import influencer.services.exception.GiveawayNotFoundOrInactiveException;
import influencer.services.exception.GiveawaySubscriberAlreadyExistsException;
import influencer.services.exception.MappingException;
import influencer.services.exception.NotAuthorizedToFinalizeGiveawayException;
import influencer.services.exception.NotPlayableAccountException;
import influencer.services.exception.OwnerCanNotOptInGiveawayException;
import influencer.services.exception.ValidationException;
import influencer.services.validation.GenericValidator;

@ExtendWith(MockitoExtension.class)
class DefaultGiveawayServiceTest {

    @Mock
    GiveawayRepo giveawayRepo;

    @Mock
    AccountRepo accountRepo;

    @Mock
    GiveawaySubscriberRepo giveawaySubscriberRepo;

    @Mock
    InfluencerRepo influencerRepo;

    @Mock
    BudgetSpendRepo influencerBudgetSpendRepo;

    @Mock
    GenericValidator<CreateGiveawayPayloadDto> createGiveawayPayloadValidator;

    DefaultGiveawayService giveawayService;

    @BeforeEach
    void setUp() {
        var giveawayMapper = new DefaultGiveawayMapper();
        giveawayService = new DefaultGiveawayService(
                giveawayRepo,
                giveawaySubscriberRepo,
                influencerBudgetSpendRepo,
                accountRepo,
                giveawayMapper);
    }

    @Test
    void findByCode_codeIsNull_throwsException() {
        assertThatThrownBy(() -> giveawayService.findByCode(null, null))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void findByCode_giveawayExists_returnsGiveaway() throws ValidationException, MappingException {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        var nowTimestamp = System.currentTimeMillis();
        var now = new Date(nowTimestamp);
        var account = new InfluencerAccount();
        account.setId(1L);
        var influencer = new Influencer()
                .setAccount(account)
                .setNickname("influencerNickname")
                .setActive(true);
        var giveaway = new Giveaway()
                .setCode(code)
                .setInfluencer(influencer)
                .setPrizeByType(Map.of(
                        GiveawayPrizeType.GC.name(), new GiveawayPrizePayload(BigDecimal.TEN),
                        GiveawayPrizeType.SC.name(), new GiveawayPrizePayload(BigDecimal.ONE)
                ))
                .setWinnersNumber(1)
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setCreatedAt(now)
                .setEndedAt(now);
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));

        assertThat(giveawayService.findByCode(code, tx)).get()
                .isEqualTo(
                        GiveawayDto.builder()
                                .code(code)
                                .gcPrize(BigDecimal.TEN)
                                .scPrize(BigDecimal.ONE)
                                .winnersNumber(giveaway.getWinnersNumber())
                                .status(GiveawayStatus.ACTIVE)
                                .createdAt(nowTimestamp)
                                .endedAt(nowTimestamp)
                                .subscribers(List.of())
                                .subscriberIds(Set.of())
                                .subscriberRemoteIds(Set.of())
                                .winnerIds(Set.of())
                                .winnerRemoteIds(Set.of())
                                .influencer(new InfluencerDto(
                                        account.getId(),
                                        influencer.getNickname(),
                                        influencer.isActive()))
                                .build());
    }

    @Test
    void findByCode_mappingIssue_throwsException() {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        var nowTimestamp = System.currentTimeMillis();
        var now = new Date(nowTimestamp);
        var account = new InfluencerAccount();
        account.setId(1L);
        var influencer = new Influencer()
                .setAccount(account)
                .setNickname("influencerNickname")
                .setActive(true);
        var giveaway = new Giveaway()
                .setCode(code)
                .setInfluencer(influencer)
                .setPrizeByType(Map.of(
                        GiveawayPrizeType.GC.name(), new GiveawayPrizePayload(BigDecimal.TEN),
                        GiveawayPrizeType.SC.name(), new GiveawayPrizePayload(BigDecimal.ONE)
                ))
                .setWinnersNumber(1)
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setEndedAt(now);
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));

        assertThatThrownBy(() -> giveawayService.findByCode(code, tx))
                .isInstanceOf(MappingException.class);
    }

    @Test
    void findByCode_giveawayIsAbsent_returnsEmpty() throws ValidationException, MappingException {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.empty());

        assertThat(giveawayService.findByCode(code, tx)).isNotPresent();
    }

    @Test
    void optInGiveaway_codeIsNull_throwsException() {
        var tx = Mockito.mock(Transaction.class);
        assertThatThrownBy(() -> giveawayService.optInGiveaway(null, new InfluencerAccount(), tx))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void optInGiveaway_accountIsNull_throwsException() {
        var tx = Mockito.mock(Transaction.class);
        assertThatThrownBy(() -> giveawayService.optInGiveaway(PlatformUtil.randomUUID(), null, tx))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void optInGiveaway_notPlayableAccount_throwsException() {
        var tx = Mockito.mock(Transaction.class);
        var giveawayCode = PlatformUtil.randomUUID();
        var account = new InfluencerAccount();
        account.setActive(false);
        assertThatThrownBy(() -> giveawayService.optInGiveaway(giveawayCode, account, tx))
                .isInstanceOf(NotPlayableAccountException.class);
    }

    @Test
    void optInGiveaway_giveawayNotFound_throwsException() {
        var tx = Mockito.mock(Transaction.class);
        var giveawayCode = PlatformUtil.randomUUID();
        var account = new InfluencerAccount();
        account.setActive(true);
        when(giveawayRepo.findByCode(giveawayCode, tx)).thenReturn(Optional.empty());
        assertThatThrownBy(() -> giveawayService.optInGiveaway(giveawayCode, account, tx))
                .isInstanceOf(GiveawayNotFoundOrInactiveException.class);
    }

    @Test
    void optInGiveaway_accountAlreadySubscribed_throwsException() {
        var tx = Mockito.mock(Transaction.class);
        var giveawayCode = PlatformUtil.randomUUID();
        var giveawayId = 1L;
        var accountId = 1L;
        var brandId = 1;

        var brand = new InfluencerBrand();
        brand.setId(Integer.valueOf(brandId));

        var account = new InfluencerAccount();
        account.setId(accountId);
        account.setActive(true);
        account.setBrand(brand);

        var influencerAccount = new InfluencerAccount();
        influencerAccount.setBrand(brand);

        var giveaway = new Giveaway()
                .setId(giveawayId)
                .setStatus(ACTIVE.name())
                .setInfluencer(new Influencer()
                        .setAccount(influencerAccount));
        when(giveawayRepo.findByCode(giveawayCode, tx)).thenReturn(Optional.of(giveaway));
        when(giveawaySubscriberRepo.findByGiveawayIdAndAccountId(giveawayId, accountId, tx))
                .thenReturn(Optional.of(new GiveawaySubscriber()));

        assertThatThrownBy(() -> giveawayService.optInGiveaway(giveawayCode, account, tx))
                .isInstanceOf(GiveawaySubscriberAlreadyExistsException.class);
    }

    @Test
    void optInGiveaway_ownerTriesToOptIn_throwsException() {
        var tx = Mockito.mock(Transaction.class);
        var giveawayCode = PlatformUtil.randomUUID();
        var giveawayId = 1L;
        var accountId = 1L;
        var brandId = 1;

        var brand = new InfluencerBrand();
        brand.setId(Integer.valueOf(brandId));

        var account = new InfluencerAccount();
        account.setId(accountId);
        account.setActive(true);
        account.setBrand(brand);

        var giveaway = new Giveaway()
                .setId(giveawayId)
                .setStatus(ACTIVE.name())
                .setInfluencer(new Influencer()
                        .setAccount(account));
        when(giveawayRepo.findByCode(giveawayCode, tx)).thenReturn(Optional.of(giveaway));

        assertThatThrownBy(() -> giveawayService.optInGiveaway(giveawayCode, account, tx))
                .isInstanceOf(OwnerCanNotOptInGiveawayException.class);
    }

    @Test
    void optInGiveaway_differentBrands_throwsException() {
        var tx = Mockito.mock(Transaction.class);
        var giveawayCode = PlatformUtil.randomUUID();
        var giveawayId = 1L;
        var accountId = 1L;

        var influencerBrand = new InfluencerBrand();
        influencerBrand.setId(1);

        var accountBrand = new InfluencerBrand();
        accountBrand.setId(2);

        var account = new InfluencerAccount();
        account.setId(accountId);
        account.setActive(true);
        account.setBrand(accountBrand);

        var influencerAccount = new InfluencerAccount();
        influencerAccount.setBrand(influencerBrand);

        var giveaway = new Giveaway()
                .setId(giveawayId)
                .setStatus(ACTIVE.name())
                .setInfluencer(new Influencer()
                        .setAccount(influencerAccount));
        when(giveawayRepo.findByCode(giveawayCode, tx))
                .thenReturn(Optional.of(giveaway));

        assertThatThrownBy(() -> giveawayService.optInGiveaway(giveawayCode, account, tx))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void optInGiveaway_happyPath() throws ValidationException {
        var tx = Mockito.mock(Transaction.class);
        var giveawayCode = PlatformUtil.randomUUID();
        var giveawayId = 1L;
        var accountId = 1L;
        var brandId = 1;

        var brand = new InfluencerBrand();
        brand.setId(Integer.valueOf(brandId));

        var account = new InfluencerAccount();
        account.setId(accountId);
        account.setActive(true);
        account.setBrand(brand);

        var influencerAccount = new InfluencerAccount();
        influencerAccount.setBrand(brand);

        var giveaway = new Giveaway()
                .setId(giveawayId)
                .setStatus(ACTIVE.name())
                .setInfluencer(new Influencer()
                        .setAccount(influencerAccount));
        when(giveawayRepo.findByCode(giveawayCode, tx))
                .thenReturn(Optional.of(giveaway));
        when(giveawaySubscriberRepo.findByGiveawayIdAndAccountId(giveawayId, accountId, tx))
                .thenReturn(Optional.empty());

        giveawayService.optInGiveaway(giveawayCode, account, tx);

        verify(giveawaySubscriberRepo).save(any(), any());
    }

    @Test
    void finalizeGiveawayV2_giveawayCodeIsNull_throwsException() {
        var tx = Mockito.mock(Transaction.class);
        assertThatThrownBy(() -> giveawayService.finalizeGiveawayV2(null, 0, tx))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void finalizeGiveawayV2_giveawayIsAbsent_throwsException() {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> giveawayService.finalizeGiveawayV2(code, 0, tx))
                .isInstanceOf(GiveawayNotFoundOrInactiveException.class);
    }

    @Test
    void finalizeGiveawayV2_giveawayIsInactive_throwsException() {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        var giveaway = new Giveaway().setStatus(GiveawayStatus.CLOSED.name());
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));

        assertThatThrownBy(() -> giveawayService.finalizeGiveawayV2(code, 0, tx))
                .isInstanceOf(GiveawayNotFoundOrInactiveException.class);
    }

    @Test
    void finalizeGiveawayV2_notAnOwner_throwsException() {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        var account = new InfluencerAccount();
        account.setId(2L);
        var influencer = new Influencer()
                .setAccount(account)
                .setNickname("influencerNickname")
                .setActive(true);
        var giveaway = new Giveaway()
                .setInfluencer(influencer)
                .setStatus(GiveawayStatus.ACTIVE.name());
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));

        assertThatThrownBy(() -> giveawayService.finalizeGiveawayV2(code, 1L, tx))
                .isInstanceOf(NotAuthorizedToFinalizeGiveawayException.class);
    }

    @Test
    void finalizeGiveawayV2_happyPath() throws ValidationException, ApplicationException, MappingException {
        var code = PlatformUtil.randomUUID();
        var nowTimestamp = System.currentTimeMillis();
        var now = new Date(nowTimestamp);
        var tx = Mockito.mock(Transaction.class);
        var account = new InfluencerAccount();
        account.setId(1L);
        var influencer = new Influencer()
                .setAccount(account)
                .setNickname("influencerNickname")
                .setActive(true);

        var giveaway = new Giveaway()
                .setInfluencer(influencer)
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setWinnersNumber(1)
                .setSubscribers(List.of());
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));
        when(giveawayRepo.save(any(), any())).thenReturn(
                new Giveaway()
                        .setCode(code)
                        .setInfluencer(influencer)
                        .setPrizeByType(Map.of(
                                GiveawayPrizeType.GC.name(), new GiveawayPrizePayload(BigDecimal.TEN),
                                GiveawayPrizeType.SC.name(), new GiveawayPrizePayload(BigDecimal.ONE)
                        ))
                        .setWinnersNumber(1)
                        .setStatus(GiveawayStatus.CLOSED.name())
                        .setCreatedAt(now)
                        .setEndedAt(now));

        var savedGiveaway = giveawayService.finalizeGiveawayV2(code, 1L, tx);
        assertThat(savedGiveaway)
                .hasFieldOrPropertyWithValue("code", code)
                .hasFieldOrPropertyWithValue("gcPrize", BigDecimal.TEN)
                .hasFieldOrPropertyWithValue("scPrize", BigDecimal.ONE)
                .hasFieldOrPropertyWithValue("winnersNumber", 1)
                .hasFieldOrPropertyWithValue("status", GiveawayStatus.CLOSED)
                .hasFieldOrPropertyWithValue("createdAt", nowTimestamp)
                .hasFieldOrPropertyWithValue("endedAt", nowTimestamp)
                .hasFieldOrProperty("subscriberIds")
                .hasFieldOrProperty("subscribers")
                .hasFieldOrProperty("influencer")
                .hasFieldOrProperty("winnerIds");
        verify(giveawaySubscriberRepo).saveAll(any(), any());
    }

    @Test
    void cancelGiveaway_codeIsNull_throwsException() {
        assertThatThrownBy(() -> giveawayService.cancelGiveaway(null, 0, null))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void cancelGiveaway_giveawayIsAbsent_throwsException() {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> giveawayService.cancelGiveaway(code, 0, tx))
                .isInstanceOf(GiveawayNotFoundOrInactiveException.class);
    }

    @Test
    void cancelGiveaway_giveawayIsInactive_throwsException() {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        var giveaway = new Giveaway().setStatus(GiveawayStatus.CLOSED.name());
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));

        assertThatThrownBy(() -> giveawayService.cancelGiveaway(code, 0, tx))
                .isInstanceOf(GiveawayNotFoundOrInactiveException.class);
    }

    @Test
    void cancelGiveaway_notAnOwner_throwsException() {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        var account = new InfluencerAccount();
        account.setId(2L);
        var influencer = new Influencer()
                .setAccount(account)
                .setNickname("influencerNickname")
                .setActive(true);
        var giveaway = new Giveaway()
                .setInfluencer(influencer)
                .setStatus(GiveawayStatus.ACTIVE.name());
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));

        assertThatThrownBy(() -> giveawayService.cancelGiveaway(code, 1L, tx))
                .isInstanceOf(NotAuthorizedToFinalizeGiveawayException.class);
    }

    @Test
    void cancelGiveaway_mappingIssue_throwsException() {
        var code = PlatformUtil.randomUUID();
        var nowTimestamp = System.currentTimeMillis();
        var now = new Date(nowTimestamp);
        var tx = Mockito.mock(Transaction.class);
        var account = new InfluencerAccount();
        account.setId(1L);
        var influencer = new Influencer()
                .setAccount(account)
                .setNickname("influencerNickname")
                .setActive(true);

        var gameCode = "gameCode";
        var gameName = "gameName";
        var gameProviderCode = "gameProviderCode";
        var betValue = new BigDecimal("0.1");
        var giveaway = new Giveaway()
                .setCode(code)
                .setInfluencer(influencer)
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setWinnersNumber(1)
                .setSubscribers(List.of())
                .setPrizeByType(Map.of(
                        GiveawayPrizeType.GC.name(), new GiveawayPrizePayload(BigDecimal.TEN),
                        GiveawayPrizeType.SC.name(), new GiveawayPrizePayload(BigDecimal.ONE),
                        GiveawayPrizeType.FREE_SPIN.name(), new GiveawayPrizePayload(
                                gameCode, gameName, gameProviderCode, 1, betValue, code.toString()
                        )
                ));
        
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));
        when(giveawayRepo.save(any(), any())).thenReturn(
                new Giveaway()
                        .setCode(code)
                        .setInfluencer(influencer)
                        .setWinnersNumber(1)
                        .setStatus(GiveawayStatus.CANCELED.name())
                        .setCreatedAt(now)
                        .setEndedAt(now));

        assertThatThrownBy(() -> giveawayService.cancelGiveaway(code, 1L, tx))
                .isInstanceOf(MappingException.class);
    }

    @Test
    void cancelGiveaway_happyPath() throws ValidationException, ApplicationException, MappingException {
        var code = PlatformUtil.randomUUID();
        var nowTimestamp = System.currentTimeMillis();
        var now = new Date(nowTimestamp);
        var tx = Mockito.mock(Transaction.class);
        var account = new InfluencerAccount();
        account.setId(1L);
        var influencer = new Influencer()
                .setAccount(account)
                .setNickname("influencerNickname")
                .setActive(true);

        var gameCode = "gameCode";
        var gameName = "gameName";
        var gameProviderCode = "gameProviderCode";
        var betValue = new BigDecimal("0.1");
        var giveaway = new Giveaway()
                .setCode(code)
                .setInfluencer(influencer)
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setWinnersNumber(1)
                .setSubscribers(List.of())
                .setPrizeByType(Map.of(
                        GiveawayPrizeType.GC.name(), new GiveawayPrizePayload(BigDecimal.TEN),
                        GiveawayPrizeType.SC.name(), new GiveawayPrizePayload(BigDecimal.ONE),
                        GiveawayPrizeType.FREE_SPIN.name(), new GiveawayPrizePayload(
                                gameCode, gameName, gameProviderCode, 1, betValue, code.toString()
                        )
                ))
                .setCreatedAt(now);
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));
        when(giveawayRepo.save(any(), any())).thenReturn(
                new Giveaway()
                        .setCode(code)
                        .setInfluencer(influencer)
                        .setWinnersNumber(1)
                        .setStatus(GiveawayStatus.CANCELED.name())
                        .setCreatedAt(now)
                        .setEndedAt(now));

        var savedGiveaway = giveawayService.cancelGiveaway(code, 1L, tx);
        assertThat(savedGiveaway)
                .hasFieldOrPropertyWithValue("code", code)
                .hasFieldOrPropertyWithValue("gcPrize", BigDecimal.TEN)
                .hasFieldOrPropertyWithValue("scPrize", BigDecimal.ONE)
                .hasFieldOrPropertyWithValue("gameCode", gameCode)
                .hasFieldOrPropertyWithValue("freeSpinNumber", 1)
                .hasFieldOrPropertyWithValue("betValue", betValue)
                .hasFieldOrPropertyWithValue("freeSpinCampaignCode", code.toString())
                .hasFieldOrPropertyWithValue("winnersNumber", 1)
                .hasFieldOrPropertyWithValue("status", GiveawayStatus.CANCELED)
                .hasFieldOrPropertyWithValue("createdAt", nowTimestamp)
                .hasFieldOrProperty("endedAt")
                .hasFieldOrProperty("subscribers")
                .hasFieldOrProperty("influencer");

        verify(influencerBudgetSpendRepo).save(any(), any());
    }

    @Test
    void chooseAndMarkWinners_happyPath() {
        var giveaway = new Giveaway().setCode(PlatformUtil.randomUUID());
        var acc1 = new InfluencerAccount();
        acc1.setId(1L);
        var acc2 = new InfluencerAccount();
        acc1.setId(2L);
        var acc3 = new InfluencerAccount();
        acc1.setId(3L);
        var acc4 = new InfluencerAccount();
        acc1.setId(4L);
        var acc5 = new InfluencerAccount();
        acc1.setId(5L);

        var subscribers = List.of(
                new GiveawaySubscriber(giveaway, acc1),
                new GiveawaySubscriber(giveaway, acc2),
                new GiveawaySubscriber(giveaway, acc3),
                new GiveawaySubscriber(giveaway, acc4),
                new GiveawaySubscriber(giveaway, acc5));

        var winners = giveawayService.chooseAndMarkWinners(subscribers, 3);

        assertThat(winners).hasSize(3);
        var winner1 = winners.get(0);
        var winner2 = winners.get(1);
        var winner3 = winners.get(2);
        assertThat(winner1).hasFieldOrPropertyWithValue("winner", true);
        assertThat(winner2).hasFieldOrPropertyWithValue("winner", true);
        assertThat(winner3).hasFieldOrPropertyWithValue("winner", true);
    }

    @Test
    void chooseAndMarkWinners_numberOfWinnersIsGraterThatSubscribers_eachAccIsAWinner() {
        var giveaway = new Giveaway().setCode(PlatformUtil.randomUUID());
        var acc1 = new InfluencerAccount();
        acc1.setId(1L);
        var acc2 = new InfluencerAccount();
        acc1.setId(2L);

        var subscribers = List.of(
                new GiveawaySubscriber(giveaway, acc1),
                new GiveawaySubscriber(giveaway, acc2));

        var winners = giveawayService.chooseAndMarkWinners(subscribers, 3);

        assertThat(winners).hasSize(2);
        var winner1 = winners.get(0);
        var winner2 = winners.get(1);
        assertThat(winner1).hasFieldOrPropertyWithValue("winner", true);
        assertThat(winner2).hasFieldOrPropertyWithValue("winner", true);
    }

    @Test
    void getNumberOfSubscribers_codeIsNull_throwsException() {
        assertThatThrownBy(() -> giveawayService.getNumberOfSubscribers(null, null))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void getNumberOfSubscribers_giveawayNotFound_throwsException() {
        var giveawayCode = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);

        when(giveawayRepo.findByCode(giveawayCode, tx)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> giveawayService.getNumberOfSubscribers(giveawayCode, tx))
                .isInstanceOf(GiveawayNotFoundOrInactiveException.class);
    }

    @Test
    void getNumberOfSubscribers_happyPath() throws ValidationException {
        var giveawayCode = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);

        var giveawayId = 1L;
        when(giveawayRepo.findByCode(giveawayCode, tx)).thenReturn(Optional.of(
                new Giveaway().setId(giveawayId)));
        when(giveawaySubscriberRepo.getNumberOfSubscribers(giveawayId, tx)).thenReturn(3);

        assertThat(giveawayService.getNumberOfSubscribers(giveawayCode, tx)).isEqualTo(3);
    }

    @Test
    void findByCodeForRequestor_codeIsNull_throwsException() {
        var tx = Mockito.mock(Transaction.class);
        assertThatThrownBy(() -> giveawayService.findByCodeForRequestor(null, 1L, tx))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void findByCodeForRequestor_giveawayIsAbsent_returnsEmpty() throws ValidationException, MappingException {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.empty());

        assertThat(giveawayService.findByCodeForRequestor(code, 1L, tx)).isNotPresent();
    }

    @Test
    void findByCodeForRequestor_requestorAccountNotFound_throwsException() {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        var nowTimestamp = System.currentTimeMillis();
        var now = new Date(nowTimestamp);
        var account = new InfluencerAccount();
        account.setId(1L);
        var influencer = new Influencer()
                .setAccount(account)
                .setNickname("influencerNickname")
                .setActive(true);
        var giveaway = new Giveaway()
                .setCode(code)
                .setInfluencer(influencer)
                .setPrizeByType(Map.of(
                        GiveawayPrizeType.GC.name(), new GiveawayPrizePayload(BigDecimal.TEN),
                        GiveawayPrizeType.SC.name(), new GiveawayPrizePayload(BigDecimal.ONE)
                ))
                .setWinnersNumber(1)
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setCreatedAt(now)
                .setEndedAt(now);
        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));
        when(accountRepo.findByRemoteId(1L, tx)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> giveawayService.findByCodeForRequestor(code, 1L, tx))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void findByCodeForRequestor_influencerAccountNotFound_throwsException() {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        var nowTimestamp = System.currentTimeMillis();
        var now = new Date(nowTimestamp);
        var account = new InfluencerAccount();
        account.setId(1L);
        var influencer = new Influencer()
                .setAccount(account)
                .setNickname("influencerNickname")
                .setActive(true);
        var giveaway = new Giveaway()
                .setCode(code)
                .setInfluencer(influencer)
                .setPrizeByType(Map.of(
                        GiveawayPrizeType.GC.name(), new GiveawayPrizePayload(BigDecimal.TEN),
                        GiveawayPrizeType.SC.name(), new GiveawayPrizePayload(BigDecimal.ONE)
                ))
                .setWinnersNumber(1)
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setCreatedAt(now)
                .setEndedAt(now);

        var requestorAccount = new InfluencerAccount();
        requestorAccount.setId(2L);

        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));
        when(accountRepo.findByRemoteId(2L, tx)).thenReturn(Optional.of(requestorAccount));
        when(accountRepo.findById(1L, tx)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> giveawayService.findByCodeForRequestor(code, 2L, tx))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void findByCodeForRequestor_missingBrandInformation_throwsException() {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        var nowTimestamp = System.currentTimeMillis();
        var now = new Date(nowTimestamp);
        var influencerAccount = new InfluencerAccount();
        influencerAccount.setId(1L);
        var influencer = new Influencer()
                .setAccount(influencerAccount)
                .setNickname("influencerNickname")
                .setActive(true);
        var giveaway = new Giveaway()
                .setCode(code)
                .setInfluencer(influencer)
                .setPrizeByType(Map.of(
                        GiveawayPrizeType.GC.name(), new GiveawayPrizePayload(BigDecimal.TEN),
                        GiveawayPrizeType.SC.name(), new GiveawayPrizePayload(BigDecimal.ONE)
                ))
                .setWinnersNumber(1)
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setCreatedAt(now)
                .setEndedAt(now);

        var requestorAccount = new InfluencerAccount();
        requestorAccount.setId(2L);

        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));
        when(accountRepo.findByRemoteId(2L, tx)).thenReturn(Optional.of(requestorAccount));
        when(accountRepo.findById(1L, tx)).thenReturn(Optional.of(influencerAccount));

        assertThatThrownBy(() -> giveawayService.findByCodeForRequestor(code, 2L, tx))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void findByCodeForRequestor_differentBrands_throwsException() {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        var nowTimestamp = System.currentTimeMillis();
        var now = new Date(nowTimestamp);

        var influencerBrand = new InfluencerBrand();
        influencerBrand.setId(1);

        var requestorBrand = new InfluencerBrand();
        requestorBrand.setId(2);

        var influencerAccount = new InfluencerAccount();
        influencerAccount.setId(1L);
        influencerAccount.setBrand(influencerBrand);

        var influencer = new Influencer()
                .setAccount(influencerAccount)
                .setNickname("influencerNickname")
                .setActive(true);

        var giveaway = new Giveaway()
                .setCode(code)
                .setInfluencer(influencer)
                .setPrizeByType(Map.of(
                        GiveawayPrizeType.GC.name(), new GiveawayPrizePayload(BigDecimal.TEN),
                        GiveawayPrizeType.SC.name(), new GiveawayPrizePayload(BigDecimal.ONE)
                ))
                .setWinnersNumber(1)
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setCreatedAt(now)
                .setEndedAt(now);

        var requestorAccount = new InfluencerAccount();
        requestorAccount.setId(2L);
        requestorAccount.setBrand(requestorBrand);

        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));
        when(accountRepo.findByRemoteId(2L, tx)).thenReturn(Optional.of(requestorAccount));
        when(accountRepo.findById(1L, tx)).thenReturn(Optional.of(influencerAccount));

        assertThatThrownBy(() -> giveawayService.findByCodeForRequestor(code, 2L, tx))
                .isInstanceOf(ValidationException.class);
    }

    @Test
    void findByCodeForRequestor_happyPath() throws ValidationException, MappingException {
        var code = PlatformUtil.randomUUID();
        var tx = Mockito.mock(Transaction.class);
        var nowTimestamp = System.currentTimeMillis();
        var now = new Date(nowTimestamp);

        var brand = new InfluencerBrand();
        brand.setId(1);

        var influencerAccount = new InfluencerAccount();
        influencerAccount.setId(1L);
        influencerAccount.setBrand(brand);

        var influencer = new Influencer()
                .setAccount(influencerAccount)
                .setNickname("influencerNickname")
                .setActive(true);

        var giveaway = new Giveaway()
                .setCode(code)
                .setInfluencer(influencer)
                .setPrizeByType(Map.of(
                        GiveawayPrizeType.GC.name(), new GiveawayPrizePayload(BigDecimal.TEN),
                        GiveawayPrizeType.SC.name(), new GiveawayPrizePayload(BigDecimal.ONE)
                ))
                .setWinnersNumber(1)
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setCreatedAt(now)
                .setEndedAt(now);

        var requestorAccount = new InfluencerAccount();
        requestorAccount.setId(2L);
        requestorAccount.setBrand(brand);

        when(giveawayRepo.findByCode(code, tx)).thenReturn(Optional.of(giveaway));
        when(accountRepo.findByRemoteId(2L, tx)).thenReturn(Optional.of(requestorAccount));
        when(accountRepo.findById(1L, tx)).thenReturn(Optional.of(influencerAccount));

        assertThat(giveawayService.findByCodeForRequestor(code, 2L, tx)).get()
                .isEqualTo(
                        GiveawayDto.builder()
                                .code(code)
                                .gcPrize(BigDecimal.TEN)
                                .scPrize(BigDecimal.ONE)
                                .winnersNumber(giveaway.getWinnersNumber())
                                .status(GiveawayStatus.ACTIVE)
                                .createdAt(nowTimestamp)
                                .endedAt(nowTimestamp)
                                .subscribers(List.of())
                                .subscriberIds(Set.of())
                                .subscriberRemoteIds(Set.of())
                                .winnerIds(Set.of())
                                .winnerRemoteIds(Set.of())
                                .influencer(new InfluencerDto(
                                        influencerAccount.getId(),
                                        influencer.getNickname(),
                                        influencer.isActive()))
                                .build());
    }
}
