package influencer.services;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.JpaManager;
import com.turbospaces.ebean.TracedEbeanTransaction;

import influencer.dto.CreateCoinGiveawayPayload;
import influencer.dto.GiveawayDto;
import influencer.dto.GiveawayPrizeType;
import influencer.dto.GiveawayStatus;
import influencer.dto.InfluencerDto;
import influencer.model.InfluencerAccount;
import influencer.model.Giveaway;
import influencer.model.GiveawayPrizePayload;
import influencer.model.Influencer;
import influencer.model.BudgetSpend;
import influencer.model.InfluencerLimits;
import influencer.model.InfluencerPermissions;
import influencer.repo.GiveawayRepo;
import influencer.repo.BudgetSpendRepo;
import influencer.repo.InfluencerRepo;
import influencer.services.exception.AccountIsNotAnInfluencerException;
import influencer.services.exception.ActiveGiveawayAlreadyExistsException;
import influencer.services.exception.GiveawayWeeklyBudgetReachedException;
import influencer.services.exception.NewGiveawayDailyLimitReachedException;
import influencer.services.exception.NotAuthorizedException;
import influencer.services.exception.ValidationException;
import influencer.services.validation.GenericValidator;

@ExtendWith(MockitoExtension.class)
class CoinGiveawayCreatorServiceTest {

    private static final long ACCOUNT_ID = 1L;
    private static final int WINNERS_NUMBER = 1;

    Influencer influencer;
    Giveaway giveaway;

    @Mock
    TracedEbeanTransaction transaction;
    @Mock
    JpaManager ebean;
    @Mock
    GenericValidator<CreateCoinGiveawayPayload> validator;
    @Mock
    InfluencerRepo influencerRepo;
    @Mock
    BudgetSpendRepo budgetSpendRepo;
    @Mock
    GiveawayRepo giveawayRepo;

    CoinGiveawayCreatorService coinGiveawayCreatorService;

    @BeforeEach
    void setUp() {
        var coinGiveawayMapper = new CoinGiveawayMapper();
        this.coinGiveawayCreatorService = new CoinGiveawayCreatorService(
                ebean, 
                validator, 
                coinGiveawayMapper, 
                influencerRepo, 
                budgetSpendRepo,
                giveawayRepo);
        this.influencer = new Influencer()
                .setId(ACCOUNT_ID)
                .setNickname("nickname")
                .setActive(true);
        this.giveaway = new Giveaway()
                .setId(1L)
                .setCode(PlatformUtil.randomUUID())
                .setInfluencer(influencer)
                .setCreatedAt(new Date())
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setWinnersNumber(WINNERS_NUMBER)
                .setPrizeByType(Map.of(
                        GiveawayPrizeType.GC.name(), new GiveawayPrizePayload(BigDecimal.ONE),
                        GiveawayPrizeType.SC.name(), new GiveawayPrizePayload(BigDecimal.ONE)
                ));

    }

    @Test
    void create_validationIssues_throwsException() {
        when(validator.validate(any())).thenReturn(Map.of("payload", "empty"));

        assertThatThrownBy(() -> coinGiveawayCreatorService.create(new CreateCoinGiveawayPayload(), transaction))
                .isInstanceOf(ValidationException.class)
                .hasFieldOrPropertyWithValue("validationIssuesMap", Map.of("payload", "empty"));
    }

    @Test
    void create_accountIsNotAnInfluencer_throwsException() {
        var request = new CreateCoinGiveawayPayload(ACCOUNT_ID, 1);
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> coinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(AccountIsNotAnInfluencerException.class);
    }

    @Test
    void create_accountAlreadyHasAnActiveGiveaway_throwsException() {
        var request = new CreateCoinGiveawayPayload(ACCOUNT_ID, 1);
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.of(new Giveaway()));

        assertThatThrownBy(() -> coinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(ActiveGiveawayAlreadyExistsException.class);
    }

    @Test
    void create_noPermissions_throwsException() {
        var request = new CreateCoinGiveawayPayload(ACCOUNT_ID, 1);
        influencer.setPermissions(
                new InfluencerPermissions(false, false));
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.empty());

        assertThatThrownBy(() -> coinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(NotAuthorizedException.class);
    }

    @Test
    void create_reachedGiveawayDailyLimit_throwsException() {
        var request = new CreateCoinGiveawayPayload(ACCOUNT_ID, 1);
        influencer.setPermissions(
                new InfluencerPermissions(true, false));
        influencer.setLimits(new InfluencerLimits(BigDecimal.ONE, 1));
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.empty());
        when(giveawayRepo.countByOwnerIdAndCreatedAtBetweenAndNotCanceled(any(), any(), any(), any()))
                .thenReturn(1);

        assertThatThrownBy(() -> coinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(NewGiveawayDailyLimitReachedException.class);
    }

    @Test
    void create_reachedInfluencerBudgetWeeklyLimit_throwsException() {
        var request = new CreateCoinGiveawayPayload(ACCOUNT_ID, 1, BigDecimal.ONE, BigDecimal.ONE);
        influencer.setPermissions(
                new InfluencerPermissions(true, false));
        influencer.setLimits(new InfluencerLimits(BigDecimal.ONE, 1));
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.empty());
        when(giveawayRepo.countByOwnerIdAndCreatedAtBetweenAndNotCanceled(any(), any(), any(), any()))
                .thenReturn(0);
        when(budgetSpendRepo.findAmountByInfluencerIdAndAllocatedAtBetween(anyLong(), any(), any(), any()))
                .thenReturn(List.of(BigDecimal.ONE));

        assertThatThrownBy(() -> coinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(GiveawayWeeklyBudgetReachedException.class);
    }

    @Test
    void create_cannotSave_throwsException() {
        var request = new CreateCoinGiveawayPayload(ACCOUNT_ID, 1, BigDecimal.ONE, BigDecimal.ONE);
        influencer.setPermissions(
                new InfluencerPermissions(true, false));
        influencer.setLimits(new InfluencerLimits(BigDecimal.ONE, 1));
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.empty());
        when(giveawayRepo.countByOwnerIdAndCreatedAtBetweenAndNotCanceled(any(), any(), any(), any()))
                .thenReturn(0);
        when(budgetSpendRepo.findAmountByInfluencerIdAndAllocatedAtBetween(anyLong(), any(), any(), any()))
                .thenReturn(List.of(BigDecimal.ZERO));
        when(giveawayRepo.save(any(), any())).thenReturn(giveaway);
        when(budgetSpendRepo.save(any(), any())).thenThrow(new RuntimeException());


        assertThatThrownBy(() -> coinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(RuntimeException.class);
    }

    @Test
    void create_happyPath() throws Exception {
        var request = new CreateCoinGiveawayPayload(ACCOUNT_ID, 1, BigDecimal.ONE, BigDecimal.ONE);
        influencer.setPermissions(
                new InfluencerPermissions(true, false));
        influencer.setLimits(new InfluencerLimits(BigDecimal.ONE, 1));
        var account = new InfluencerAccount();
        account.setId(ACCOUNT_ID);
        influencer.setAccount(account);
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.empty());
        when(giveawayRepo.countByOwnerIdAndCreatedAtBetweenAndNotCanceled(any(), any(), any(), any()))
                .thenReturn(0);
        when(budgetSpendRepo.findAmountByInfluencerIdAndAllocatedAtBetween(anyLong(), any(), any(), any()))
                .thenReturn(List.of(BigDecimal.ZERO));
        when(giveawayRepo.save(any(), any())).thenReturn(giveaway);
        when(budgetSpendRepo.save(any(), any())).thenReturn(new BudgetSpend());

        var savedGiveaway = coinGiveawayCreatorService.create(request, transaction);

        var expected = GiveawayDto.builder()
                .code(giveaway.getCode())
                .gcPrize(BigDecimal.ONE)
                .scPrize(BigDecimal.ONE)
                .winnersNumber(1)
                .status(GiveawayStatus.ACTIVE)
                .createdAt(giveaway.getCreatedAt().getTime())
                .influencer(new InfluencerDto(ACCOUNT_ID, influencer.getNickname(), true))
                .build();
        assertThat(savedGiveaway).isEqualTo(expected);
    }
}
