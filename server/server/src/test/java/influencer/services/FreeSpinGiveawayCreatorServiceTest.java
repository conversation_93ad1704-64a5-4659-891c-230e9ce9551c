package influencer.services;

import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.JpaManager;
import com.turbospaces.ebean.TracedEbeanTransaction;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import influencer.dto.CreateFreeSpinGiveawayPayload;
import influencer.dto.GiveawayDto;
import influencer.dto.GiveawayStatus;
import influencer.dto.GiveawayPrizeType;
import influencer.dto.InfluencerDto;
import influencer.model.InfluencerAccount;
import influencer.model.InfluencerBrand;
import influencer.model.Giveaway;
import influencer.model.GiveawayPrizePayload;
import influencer.model.Influencer;
import influencer.model.BudgetSpend;
import influencer.model.InfluencerLimits;
import influencer.model.InfluencerPermissions;
import influencer.repo.GiveawayRepo;
import influencer.repo.BudgetSpendRepo;
import influencer.repo.InfluencerRepo;
import influencer.services.exception.NewGiveawayDailyLimitReachedException;
import influencer.services.exception.AccountIsNotAnInfluencerException;
import influencer.services.exception.ActiveGiveawayAlreadyExistsException;
import influencer.services.exception.GiveawayWeeklyBudgetReachedException;
import influencer.services.exception.NotAuthorizedException;
import influencer.services.exception.ValidationException;
import influencer.services.validation.GenericValidator;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FreeSpinGiveawayCreatorServiceTest {

    private static final long ACCOUNT_ID = 1L;
    private static final String GAME_CODE = "gameCode";
    private static final String GAME_NAME = "gameCode";
    private static final String GAME_PROVIDER_CODE = "gameProviderCode";
    private static final String FREE_SPIN_CAMP_CODE = "campCode";
    private static final int FREE_SPIN_NUMBER = 1;
    private static final BigDecimal FREE_SPIN_BET_VALUE = new BigDecimal("0.4");
    private static final int WINNERS_NUMBER = 1;

    Influencer influencer;
    Giveaway giveaway;

    @Mock
    TracedEbeanTransaction transaction;
    @Mock
    JpaManager ebean;
    @Mock
    GenericValidator<CreateFreeSpinGiveawayPayload> validator;
    @Mock
    InfluencerRepo influencerRepo;
    @Mock
    BudgetSpendRepo influencerBudgetSpendRepo;
    @Mock
    GiveawayRepo giveawayRepo;

    FreeSpinGiveawayCreatorService freeSpinGiveawayCreatorService;

    @BeforeEach
    void setUp() {
        var freeSpinGiveawayMapper = new FreeSpinGiveawayMapper();
        this.freeSpinGiveawayCreatorService = new FreeSpinGiveawayCreatorService(
                ebean,
                validator,
                freeSpinGiveawayMapper,
                influencerRepo,
                influencerBudgetSpendRepo,
                giveawayRepo);

        this.influencer = new Influencer()
                .setId(ACCOUNT_ID)
                .setNickname("nickname")
                .setActive(true);
        this.giveaway = new Giveaway()
                .setId(1L)
                .setCode(PlatformUtil.randomUUID())
                .setInfluencer(influencer)
                .setCreatedAt(new Date())
                .setStatus(GiveawayStatus.ACTIVE.name())
                .setWinnersNumber(WINNERS_NUMBER)
                .setPrizeByType(Map.of(
                        GiveawayPrizeType.FREE_SPIN.name(),
                        new GiveawayPrizePayload(
                                GAME_CODE,
                                GAME_NAME,
                                GAME_PROVIDER_CODE,
                                FREE_SPIN_NUMBER,
                                FREE_SPIN_BET_VALUE,
                                FREE_SPIN_CAMP_CODE)));

    }

    @Test
    void create_validationIssues_throwsException() {
        when(validator.validate(any())).thenReturn(Map.of("payload", "empty"));

        assertThatThrownBy(
                () -> freeSpinGiveawayCreatorService.create(new CreateFreeSpinGiveawayPayload(), transaction))
                .isInstanceOf(ValidationException.class)
                .hasFieldOrPropertyWithValue("validationIssuesMap", Map.of("payload", "empty"));
    }

    @Test
    void create_accountIsNotAnInfluencer_throwsException() {
        var request = new CreateFreeSpinGiveawayPayload(ACCOUNT_ID, WINNERS_NUMBER);
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> freeSpinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(AccountIsNotAnInfluencerException.class);
    }

    @Test
    void create_accountAlreadyHasAnActiveGiveaway_throwsException() {
        var request = new CreateFreeSpinGiveawayPayload(ACCOUNT_ID, WINNERS_NUMBER);
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.of(new Giveaway()));

        assertThatThrownBy(() -> freeSpinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(ActiveGiveawayAlreadyExistsException.class);
    }

    @Test
    void create_noPermissions_throwsException() {
        var request = new CreateFreeSpinGiveawayPayload(ACCOUNT_ID, WINNERS_NUMBER);
        influencer.setPermissions(new InfluencerPermissions(false, false));
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.empty());

        assertThatThrownBy(() -> freeSpinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(NotAuthorizedException.class);
    }

    @Test
    void create_reachedGiveawayDailyLimit_throwsException() {
        var request = new CreateFreeSpinGiveawayPayload(ACCOUNT_ID, WINNERS_NUMBER);
        influencer.setPermissions(
                new InfluencerPermissions(false, true));
        influencer.setLimits(new InfluencerLimits(BigDecimal.ONE, 1));
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.empty());
        when(giveawayRepo.countByOwnerIdAndCreatedAtBetweenAndNotCanceled(any(), any(), any(), any()))
                .thenReturn(1);

        assertThatThrownBy(() -> freeSpinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(NewGiveawayDailyLimitReachedException.class);
    }

    @Test
    void create_reachedInfluencerBudgetWeeklyLimit_throwsException() {
        var request = createFreeSpinGiveawayPayload();
        influencer.setPermissions(
                new InfluencerPermissions(false, true));
        influencer.setLimits(new InfluencerLimits(BigDecimal.ONE, 1));
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.empty());
        when(giveawayRepo.countByOwnerIdAndCreatedAtBetweenAndNotCanceled(any(), any(), any(), any()))
                .thenReturn(0);
        when(influencerBudgetSpendRepo.findAmountByInfluencerIdAndAllocatedAtBetween(anyLong(), any(), any(),
                any()))
                .thenReturn(List.of(BigDecimal.ONE));

        assertThatThrownBy(() -> freeSpinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(GiveawayWeeklyBudgetReachedException.class);
    }

    @Test
    void create_cannotSave_throwsException() throws Exception {
        var request = createFreeSpinGiveawayPayload();
        influencer.setPermissions(
                new InfluencerPermissions(false, true));
        influencer.setLimits(new InfluencerLimits(BigDecimal.ONE, 1));
        var brand = new InfluencerBrand();
        var account = new InfluencerAccount();
        account.setId(ACCOUNT_ID);
        account.setBrand(brand);
        influencer.setAccount(account);
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.empty());
        when(giveawayRepo.countByOwnerIdAndCreatedAtBetweenAndNotCanceled(any(), any(), any(), any()))
                .thenReturn(0);
        when(influencerBudgetSpendRepo.findAmountByInfluencerIdAndAllocatedAtBetween(anyLong(), any(), any(),
                any()))
                .thenReturn(List.of(BigDecimal.ZERO));
        when(giveawayRepo.save(any(), any())).thenReturn(giveaway);
        when(influencerBudgetSpendRepo.save(any(), any())).thenThrow(new RuntimeException());

        assertThatThrownBy(() -> freeSpinGiveawayCreatorService.create(request, transaction))
                .isInstanceOf(RuntimeException.class);
    }

    @Test
    void create_happyPath() throws Exception {
        var request = createFreeSpinGiveawayPayload();
        influencer.setPermissions(
                new InfluencerPermissions(false, true));
        influencer.setLimits(new InfluencerLimits(BigDecimal.ONE, 1));
        var brand = new InfluencerBrand();
        var account = new InfluencerAccount();
        account.setId(ACCOUNT_ID);
        account.setBrand(brand);
        influencer.setAccount(account);
        when(validator.validate(request)).thenReturn(Map.of());
        when(influencerRepo.findByAccountId(ACCOUNT_ID)).thenReturn(Optional.of(influencer));
        when(giveawayRepo.findByAccountIdAndStatus(ACCOUNT_ID, GiveawayStatus.ACTIVE.name(), transaction))
                .thenReturn(Optional.empty());
        when(giveawayRepo.countByOwnerIdAndCreatedAtBetweenAndNotCanceled(any(), any(), any(), any()))
                .thenReturn(0);
        when(influencerBudgetSpendRepo.findAmountByInfluencerIdAndAllocatedAtBetween(anyLong(), any(), any(),
                any()))
                .thenReturn(List.of(BigDecimal.ZERO));
        when(giveawayRepo.save(any(), any())).thenReturn(giveaway);
        when(influencerBudgetSpendRepo.save(any(), any())).thenReturn(new BudgetSpend());

        var savedGiveaway = freeSpinGiveawayCreatorService.create(request, transaction);

        var expected = GiveawayDto.builder()
                .code(giveaway.getCode())
                .gameCode(GAME_CODE)
                .gameName(GAME_NAME)
                .gameProviderCode(GAME_PROVIDER_CODE)
                .freeSpinCampaignCode(FREE_SPIN_CAMP_CODE)
                .freeSpinNumber(FREE_SPIN_NUMBER)
                .betValue(FREE_SPIN_BET_VALUE)
                .winnersNumber(WINNERS_NUMBER)
                .status(GiveawayStatus.ACTIVE)
                .createdAt(giveaway.getCreatedAt().getTime())
                .influencer(new InfluencerDto(ACCOUNT_ID, influencer.getNickname(), true))
                .build();
        assertThat(savedGiveaway).isEqualTo(expected);
    }

    private CreateFreeSpinGiveawayPayload createFreeSpinGiveawayPayload() {
        return new CreateFreeSpinGiveawayPayload(
                ACCOUNT_ID,
                PlatformUtil.randomUUID(),
                WINNERS_NUMBER,
                GAME_CODE,
                GAME_NAME,
                GAME_PROVIDER_CODE,
                FREE_SPIN_NUMBER,
                FREE_SPIN_CAMP_CODE,
                FREE_SPIN_BET_VALUE);
    }
}
