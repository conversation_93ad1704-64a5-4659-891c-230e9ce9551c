package tournament.util;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static tournament.IdentityUtil.IP_COUNTRY;
import static tournament.IdentityUtil.REMOTE_IP;
import static tournament.InitialTestDataGenerator.TEST_BRAND_1;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.UUID;

import io.ebean.Transaction;

import org.apache.commons.lang3.time.DateUtils;
import org.assertj.core.api.Assertions;
import org.jetbrains.annotations.NotNull;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import com.google.protobuf.Message;
import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.TransactionalRequest;

import api.v1.PlatformSpec;
import common.JwtTags;
import common.proto.TTournamentConfig;
import common.proto.TTournamentIcon;
import common.proto.TTournamentStatus;
import core.model.CoreBrand;
import io.jsonwebtoken.Jwts;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;
import tournament.IdentityUtil;
import tournament.TournamentExternalIdentityManager;
import tournament.api.v1.TCancelTournamentRequest;
import tournament.api.v1.TCancelTournamentResponse;
import tournament.api.v1.TForceCompleteTournamentRequest;
import tournament.api.v1.TForceCompleteTournamentResponse;
import tournament.api.v1.TCreateOrUpdateTournamentResponse;
import tournament.api.v1.TCreateOrUpdateTournamentResult;
import tournament.api.v1.TCreateTournamentRequest;
import tournament.api.v1.TFeatureFlagRequest;
import tournament.api.v1.TFeatureFlagResponse;
import tournament.api.v1.TGetTournamentGamesRequest;
import tournament.api.v1.TGetTournamentGamesResponse;
import tournament.api.v1.TGetTournamentsRequest;
import tournament.api.v1.TGetTournamentsResponse;
import tournament.api.v1.TJoinTournamentRequest;
import tournament.api.v1.TJoinTournamentResponse;
import tournament.handlers.admin.CancelTournamentRequestHandlerTournament;
import tournament.handlers.admin.CreateTournamentRequestHandler;
import tournament.handlers.admin.ForceCompleteTournamentRequestHandler;
import tournament.handlers.features.GetFeatureFlagRequestHandler;
import tournament.handlers.games.GetTournamentGamesRequestHandler;
import tournament.handlers.GetTournamentsRequestHandler;
import tournament.handlers.JoinTournamentRequestHandler;
import tournament.model.Tournament;
import tournament.model.TournamentAccount;
import tournament.model.TournamentBrand;
import tournament.model.TournamentGame;
import tournament.model.TournamentJoin;
import tournament.model.TournamentPrize;
import tournament.model.TournamentReward;
import tournament.repo.TournamentEbeanJpaManager;
import tournament.services.admin.TournamentGameAdminService;
import uam.api.v1.Identity;
import uam.api.v1.IdentityBySessionToken;

@Slf4j
public class TournamentTestHelper {
    @Lazy
    @Autowired
    private MockUtil mockUtil;

    @Lazy
    @Autowired
    private CreateTournamentRequestHandler upsertHandler;

    @Lazy
    @Autowired
    private CancelTournamentRequestHandlerTournament cancellationHandler;

    @Lazy
    @Autowired
    private ForceCompleteTournamentRequestHandler forceCompleteHandler;

    @Lazy
    @Autowired
    private GetTournamentsRequestHandler getTournamentsHandler;

    @Lazy
    @Autowired
    private GetTournamentGamesRequestHandler getTournamentGamesHandler;

    @Lazy
    @Autowired
    private JoinTournamentRequestHandler joinHandler;

    @Lazy
    @Autowired
    private GetFeatureFlagRequestHandler featureFlagsHandler;

    @Lazy
    @Autowired
    private TournamentGameAdminService tournamentGameAdminService;

    @Autowired
    private TournamentEbeanJpaManager ebean;

    @Autowired
    private TournamentExternalIdentityManager identityManager;

    public TCreateOrUpdateTournamentResponse createTournament(TTournamentStatus status,
            Instant startDate,
            Instant endDate,
            Instant upcomingDate) throws Throwable {
        TTournamentConfig config = createConfig();
        var brands = List.of(TEST_BRAND_1);
        return createTournament("Test title " + new Date(), config, status, startDate, upcomingDate, endDate, brands);
    }

    public TCreateOrUpdateTournamentResponse createTournament(TTournamentStatus status,
            Instant startDate,
            Instant endDate) throws Throwable {
        TTournamentConfig config = createConfig();
        var brands = List.of(TEST_BRAND_1);
        return createTournament("Test title " + new Date(), config, status, startDate, endDate, brands);
    }

    private static TTournamentConfig createConfig() {
        return TTournamentConfig.newBuilder()
                .setCurrency("SC")
                .setDebitReward(1)
                .setCreditReward(2)
                .setHighestMultiplierReward(77)
                .build();
    }

    public Tournament createTournament() throws Throwable {
        // Generate random tournament ID for test isolation
        var createTournamentRequest = TCreateTournamentRequest.newBuilder()
                .setTitle("Tournament " + new Random().nextInt(100000000))
                .setStatus(TTournamentStatus.RUNNING)
                .setIcon(TTournamentIcon.newBuilder().setIconUrl("https://example.com/icon.png").build())
                .setStartDate(Instant.now().minus(1, ChronoUnit.HOURS).toEpochMilli())
                .setEndDate(Instant.now().plus(1, ChronoUnit.HOURS).toEpochMilli())
                .setUpcomingDate(Instant.now().minus(Duration.ofHours(24)).toEpochMilli())
                .setConfig(
                        TTournamentConfig.newBuilder()
                                .setCurrency("SC")
                                .setDebitReward(1)
                                .setCreditReward(2)
                                .setHighestMultiplierReward(77)
                                .build())
                .addBrands(TEST_BRAND_1)
                .setOwnerBrand(TEST_BRAND_1)
                .build();

        TCreateOrUpdateTournamentResponse tournament = createTournament(createTournamentRequest);
        return getTournamentById(tournament.getId());
    }

    public TCreateOrUpdateTournamentResponse createTournament(String title, TTournamentConfig config,
            TTournamentStatus status, Instant startDate,
            Instant endDate) throws Throwable {

        var brands = List.of(TEST_BRAND_1);

        return createTournament(title, config, status, startDate, endDate, brands);
    }

    public TCreateOrUpdateTournamentResponse createTournament(String title,
            TTournamentConfig config,
            TTournamentStatus status,
            Instant startDate,
            Instant endDate,
            List<String> brands) throws Throwable {
        return createTournament(title, config, status, startDate, endDate, null, brands);
    }

    public TCreateOrUpdateTournamentResponse createTournament(String title,
            TTournamentConfig config,
            TTournamentStatus status,
            Instant startDate,
            Instant endDate,
            Instant upcomingDate,
            List<String> brands) throws Throwable {
        String brand = brands.getFirst();
        var requestBuilder = TCreateTournamentRequest.newBuilder()
                .setTitle(title)
                .setStatus(status)
                .setStartDate(startDate.toEpochMilli())
                .setEndDate(endDate.toEpochMilli())
                .setConfig(config)
                .setIcon(TTournamentIcon.newBuilder().setIconUrl("/tournaments/bulls_bg.png").build())
                .addAllBrands(brands)
                .setAdminId(randomAdminIdForBrand(brand))
                .setOwnerBrand(brand);

        if (upcomingDate != null) {
            requestBuilder.setUpcomingDate(upcomingDate.toEpochMilli());
        }

        return createTournament(requestBuilder.build());
    }

    public TCreateOrUpdateTournamentResponse createTournament(TCreateTournamentRequest req) throws Throwable {
        // Build proto request

        // Prepare the response builder
        var respBuilder = TCreateOrUpdateTournamentResponse.newBuilder();

        var cmd = Mockito.spy(createTransactionalRequest(TCreateTournamentRequest.class, req, respBuilder));
        cmd.setRoutingKey(AsciiString.cached(PlatformUtil.randomUUID().toString()));

        upsertHandler.apply(cmd);

        var resp = respBuilder.build();

        assertThat(resp.getResult()).isEqualTo(TCreateOrUpdateTournamentResult.CREATED);
        assertThat(resp.getId()).isGreaterThanOrEqualTo(1);

        return resp;
    }

    public TCancelTournamentResponse cancelTournament(TCancelTournamentRequest req) throws Throwable {
        var respBuilder = TCancelTournamentResponse.newBuilder();

        var cmd = Mockito.spy(createTransactionalRequest(TCancelTournamentRequest.class, req, respBuilder));
        cmd.setRoutingKey(AsciiString.cached(PlatformUtil.randomUUID().toString()));

        cancellationHandler.apply(cmd);

        return respBuilder.build();
    }

    public TForceCompleteTournamentResponse forceCompleteTournament(TForceCompleteTournamentRequest req) throws Throwable {
        var respBuilder = TForceCompleteTournamentResponse.newBuilder();

        var cmd = Mockito.spy(createTransactionalRequest(TForceCompleteTournamentRequest.class, req, respBuilder));
        cmd.setRoutingKey(AsciiString.cached(PlatformUtil.randomUUID().toString()));

        forceCompleteHandler.apply(cmd);

        return respBuilder.build();
    }

    @SuppressWarnings("java:S119")
    public <REQ extends Message, RESP extends Message.Builder> TransactionalRequest<REQ, RESP> createTransactionalRequest(Class<REQ> reqType, REQ req,
            RESP respBuilder) {
        return mockUtil.toTransactionalRequest(reqType, req, respBuilder);
    }

    public Optional<TJoinTournamentResponse> tryJoinTournament(String code, Identity identity,
            AsciiString routingKey) {
        try {
            return Optional.ofNullable(joinTournament(code, identity, TEST_BRAND_1, routingKey));
        } catch (Throwable t) {
            log.info("Cannot join tournament with code [{}] for brand [{}]", code, TEST_BRAND_1, t);
            // tournament join can fail if brand is not enabled; this is expected
            return Optional.empty();
        }
    }

    public TJoinTournamentResponse joinTournament(String code, Identity identity, AsciiString routingKey) throws Throwable {
        return joinTournament(code, identity, TEST_BRAND_1, routingKey);
    }

    public TFeatureFlagResponse getFeatureFlagStatus(String brandName, PlatformSpec platform, Identity identity,
            AsciiString routingKey) throws Throwable {
        TFeatureFlagRequest request = TFeatureFlagRequest.newBuilder()
                .setBrandName(brandName)
                .setPlatform(platform.name())
                .setIdentity(identity)
                .build();

        var responseBuilder = TFeatureFlagResponse.newBuilder();
        var cmd = mockUtil.toTransactionalRequest(TFeatureFlagRequest.class, request, responseBuilder, routingKey);

        featureFlagsHandler.apply(cmd);
        return responseBuilder.build();
    }

    public TJoinTournamentResponse joinTournament(String code, Identity identity, String brandName,
            AsciiString routingKey) throws Throwable {
        var request = TJoinTournamentRequest.newBuilder()
                .setCode(code)
                .setIdentity(identity)
                .setBrandName(brandName)
                .build();

        var responseBuilder = TJoinTournamentResponse.newBuilder();
        var cmd = mockUtil.toTransactionalRequest(TJoinTournamentRequest.class, request, responseBuilder, routingKey);

        joinHandler.apply(cmd);
        var response = responseBuilder.build();

        Assertions.assertThat(response.getJoinedAt()).isNotZero();
        return response;
    }

    public TGetTournamentsResponse getTournaments(Identity identity, String brandName, AsciiString routingKey) throws Throwable {
        var request = TGetTournamentsRequest.newBuilder().setIdentity(identity).setBrandName(brandName).build();

        var responseBuilder = TGetTournamentsResponse.newBuilder();
        var cmd = mockUtil.toTransactionalRequest(TGetTournamentsRequest.class, request, responseBuilder, routingKey);

        getTournamentsHandler.apply(cmd);
        return responseBuilder.build();
    }

    public TGetTournamentGamesResponse getTournamentGames(UUID code, String brandName, AsciiString routingKey) throws Throwable {
        var request = TGetTournamentGamesRequest.newBuilder()
                .setTournamentCode(code.toString())
                .setBrandName(brandName)
                .build();

        var responseBuilder = TGetTournamentGamesResponse.newBuilder();
        var cmd = mockUtil.toTransactionalRequest(TGetTournamentGamesRequest.class, request, responseBuilder,
                routingKey);

        getTournamentGamesHandler.apply(cmd);
        return responseBuilder.build();
    }

    public Tournament getTournamentByCode(String code) {
        try (var tx = ebean.newReadOnlyTransaction()) {
            return ebean.tournamentRepo()
                    .findTournamentByCode(UUID.fromString(code), tx)
                    .orElseThrow(() -> new RuntimeException("Cannot get tournament by code " + code));
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    public Tournament getTournamentById(long id) {
        try (var tx = ebean.newReadOnlyTransaction()) {
            return ebean.tournamentRepo()
                    .findTournamentById(id, tx)
                    .orElseThrow(() -> new RuntimeException("Cannot get tournament by id " + id));
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Completely replaces the existing games for a tournament with the provided game codes.
     * Games not in the list will be cancelled, new games will be created, existing games will be activated.
     */
    public void setTournamentGames(long tournamentId, List<String> gameCodes) {
        var tournament = getTournamentById(tournamentId);
        try (var tx = ebean.newTransaction()) {
            tournamentGameAdminService.updateTournamentGames(gameCodes, tournament, tx);
            tx.commit();
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    public List<TournamentGame> getActiveGamesForTournament(long tournamentId) {
        try (var tx = ebean.newReadOnlyTransaction()) {
            return ebean.tournamentRepo()
                    .gamesForTournament(tournamentId, Set.of(TournamentGame.GameStatus.ACTIVE), tx);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    public uam.api.v1.Identity getIdentityByToken(long remoteAccountId, CoreBrand brand) throws Throwable {
        String token = identityManager.encrypt(brand, Jwts.builder()
                .setExpiration(DateUtils.addMinutes(new Date(), 30))
                .claim(JwtTags.JWT_BRAND, brand.getName())
                .claim(JwtTags.JWT_ROUTING_KEY_CLAIM, String.valueOf(remoteAccountId))
                .setSubject(String.valueOf(remoteAccountId)));
        return Identity.newBuilder()
                .setByToken(IdentityBySessionToken.newBuilder()
                        .setRemoteIp(REMOTE_IP)
                        .setIpCountry(IP_COUNTRY)
                        .setAccountId(remoteAccountId)
                        .setToken(token)
                        .setPlatform("WEB")
                        .build())
                .build();
    }

    public uam.api.v1.Identity getAnonymousIdentity() {
        return Identity.newBuilder()
                .setByAnonymous(IdentityUtil.getAnonymousIdentity())
                .build();
    }

    public List<TournamentGame> getAllGamesForTournament(long tournamentId) {
        try (var tx = ebean.newTransaction()) {
            List<TournamentGame> games = ebean.tournamentRepo()
                    .gamesForTournament(
                            tournamentId,
                            Set.of(TournamentGame.GameStatus.ACTIVE, TournamentGame.GameStatus.CANCELLED),
                            tx);
            tx.commit();
            return games;
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    public void createTournamentJoin(TournamentAccount account, Tournament tournament) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            TournamentJoin tournamentJoin = new TournamentJoin();
            tournamentJoin.setAccount(account);
            tournamentJoin.setTournament(tournament);
            tournamentJoin.setStatus(TournamentJoin.JoinStatus.JOINED);
            ebean.save(tournamentJoin, tx);
            tx.commit();
        }
    }

    public TournamentPrize createPrize(Tournament tournament, int startPosition, int endPosition) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            TournamentPrize prize = new TournamentPrize();
            prize.setTournament(tournament);
            prize.setStartPosition(startPosition);
            prize.setEndPosition(endPosition);
            prize.setScAmount(new BigDecimal("100.00"));
            prize.setGcAmount(new BigDecimal("0.00"));
            ebean.save(prize, tx);
            tx.commit();
            return prize;
        }
    }

    public TournamentReward createReward(TournamentPrize prize, TournamentBrand brand, String code) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            TournamentReward reward = new TournamentReward();
            reward.setPrize(prize);
            reward.setBrand(brand);
            reward.setCode(code);
            reward.setType(TournamentReward.Type.COIN);
            ebean.save(reward, tx);
            tx.commit();
            return reward;
        }
    }

    private static String randomAdminIdForBrand(String brand) {
        return brand + "_admin_" + PlatformUtil.randomUUID() + "@example.com";
    }

}
