package tournament.services.scoring.leaderboard;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

import java.time.Duration;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import com.turbospaces.redis.RedisClientFactoryBean;

import api.v1.EnhancedApplicationException;
import lombok.extern.slf4j.Slf4j;
import tournament.api.v1.TScoreSnapshot;
import tournament.dto.TournamentPlayerRankWithDetails;
import tournament.handlers.TournamentAbstractRequestHandlerTest;
import tournament.model.ScoreSnapshot;
import tournament.model.Tournament;
import tournament.model.TournamentAccount;
import tournament.services.scoring.leaderboard.cache.local.TournamentLeaderboardServiceLocalCache;
import tournament.services.scoring.leaderboard.cache.redis.LeaderboardRedisCacheChecker;
import tournament.util.TournamentTestHelper;

/**
 * Tests for the BasicLeaderboardService implementation.
 * These tests verify the functionality of a Redis-based leaderboard system
 * with timestamp-based tiebreaking for equal scores.
 */
@Slf4j
public class TournamentLeaderboardServiceTest extends TournamentAbstractRequestHandlerTest {
    private TournamentAccount account1;
    private TournamentAccount account2;
    private TournamentAccount account3;
    private TournamentAccount account4;
    @Autowired
    private RedisClientFactoryBean redisClientFactoryBean;
    private TournamentLeaderboardService spyLeaderboardService;
    private RedissonClient redissonClient;
    private Tournament tournament;
    @Autowired
    private TournamentTestHelper tournamentTestHelper;
    @Autowired
    private LeaderboardRedisCacheChecker cacheChecker;
    @Autowired
    private TournamentLeaderboardServiceLocalCache leaderboardServiceLocalCache;

    @BeforeEach
    void setUp() throws Throwable {
        redissonClient = redisClientFactoryBean.getObject();
        spyLeaderboardService = Mockito.spy(leaderboardService);

        tournament = tournamentTestHelper.createTournament();
        account1 = createTestAccount();
        account2 = createTestAccount();
        account3 = createTestAccount();
        account4 = createTestAccount();
    }

    private void clearCache() {
        redissonClient.getKeys().flushall();
    }

    @AfterEach
    void tearDown() {
        clearCache();
    }

    @Test
    @DisplayName("Should save and retrieve a score snapshot with all fields intact")
    void shouldSaveAndRetrieveScoreSnapshot() throws Throwable {
        // Given
        TScoreSnapshot scoreSnapshot = createScoreSnapshot(150.0);

        // When
        boolean result = spyLeaderboardService.upsertScoreSnapshot(tournament, account1, scoreSnapshot);
        Optional<TScoreSnapshot> retrievedSnapshotOpt = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();

        // Then - Verify that upsertScoreSnapshot returns true for successful saves
        assertTrue(result, "upsertScoreSnapshot should return true when successfully saving a new score snapshot");

        // Then
        assertTrue(retrievedSnapshotOpt.isPresent(), "Score snapshot should be present in Redis");
        TScoreSnapshot retrievedSnapshot = retrievedSnapshotOpt.get();

        assertNotNull(retrievedSnapshot, "Retrieved snapshot should not be null");
        assertEquals(scoreSnapshot.getScore(), retrievedSnapshot.getScore(), "Score values should match");
        assertEquals(scoreSnapshot.getTotalCredit(), retrievedSnapshot.getTotalCredit(), "Total credit should match");
        assertEquals(scoreSnapshot.getTotalDebit(), retrievedSnapshot.getTotalDebit(), "Total debit should match");
        assertTrue(retrievedSnapshot.getTimestamp() > 0, "Timestamp should be set by the Lua script");

        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());

        Optional<TScoreSnapshot> retrievedAfterCacheFailureOpt = spyLeaderboardService.getScoreSnapshot(tournament,
                account1).scoreSnapshot();

        // Verify that we can still retrieve the snapshot after cache failure
        assertTrue(retrievedAfterCacheFailureOpt.isPresent(),
                "Score snapshot should be present in Redis after cache failure");
        TScoreSnapshot retrievedAfterCacheFailure = retrievedAfterCacheFailureOpt.get();

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService).getScoreSnapshotFromDb(Mockito.eq(tournament), Mockito.eq(account1));

        // Verify that the values match the original snapshot
        assertEquals(retrievedSnapshot.getScore(), retrievedAfterCacheFailure.getScore(),
                "Score should match after cache failure");
        assertEquals(retrievedSnapshot.getTotalCredit(), retrievedAfterCacheFailure.getTotalCredit(),
                "Total credit should match after cache failure");
        assertEquals(retrievedSnapshot.getTotalDebit(), retrievedAfterCacheFailure.getTotalDebit(),
                "Total debit should match after cache failure");
        assertEquals(retrievedSnapshot.getTimestamp(), retrievedAfterCacheFailure.getTimestamp(),
                "Timestamp should match after cache failure");

        try (var tx = ebean.newTransaction()) {
            Optional<ScoreSnapshot> dbSnapshotOpt = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);

            // Validate that the score is saved in the database
            assertTrue(dbSnapshotOpt.isPresent(), "Score snapshot should be present in the database");
            ScoreSnapshot dbSnapshot = dbSnapshotOpt.get();

            // Validate that the database values match the Redis values
            assertEquals(retrievedSnapshot.getScore(), dbSnapshot.getScore().doubleValue(),
                    "Database score should match Redis score");
            assertEquals(retrievedSnapshot.getTotalCredit(), dbSnapshot.getTotalCredit().doubleValue(),
                    "Database total credit should match Redis total credit");
            assertEquals(retrievedSnapshot.getTotalDebit(), dbSnapshot.getTotalDebit().doubleValue(),
                    "Database total debit should match Redis total debit");
            assertEquals(retrievedSnapshot.getTimestamp(), dbSnapshot.getTimestamp(),
                    "Database timestamp should match Redis timestamp");
        }
    }

    @Test
    @DisplayName("Should return empty optional when retrieving score for non-authorized user")
    void shouldReturnEmptyOptionalWhenRetrievingScoreForNonAuthorizedUser() throws Throwable {
        // When
        TournamentAccount nonExistingAccount = new TournamentAccount();
        nonExistingAccount.setId(8888L); // this account is not properly created

        Optional<TScoreSnapshot> retrievedSnapshot = spyLeaderboardService.getScoreSnapshot(tournament,
                nonExistingAccount).scoreSnapshot();

        // Then
        assertTrue(retrievedSnapshot.isEmpty(), "Should return empty optional for non-authorized user");
        Mockito.verify(spyLeaderboardService, Mockito.times(0)).getScoreSnapshotFromCache(Mockito.any(), Mockito.any());
        Mockito.verify(spyLeaderboardService, Mockito.times(0)).getScoreSnapshotFromDb(Mockito.any(), Mockito.any());
    }

    @Test
    @DisplayName("Should return empty optional when retrieving non-existent score snapshot")
    void shouldReturnEmptyOptionalForNonExistentScoreSnapshot() throws Throwable {
        // When
        TournamentAccount nonExistingAccount = createTestAccount();

        Optional<TScoreSnapshot> retrievedSnapshot = spyLeaderboardService.getScoreSnapshot(tournament,
                nonExistingAccount).scoreSnapshot();

        // Then
        assertTrue(retrievedSnapshot.isEmpty(),
                "Should return empty optional for non-existent score " + "snapshot from cache");

        // When
        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());

        Optional<TScoreSnapshot> retrievedSnapshotAfterCacheFailure = spyLeaderboardService.getScoreSnapshot(tournament,
                nonExistingAccount).scoreSnapshot();

        // Then
        assertTrue(retrievedSnapshotAfterCacheFailure.isEmpty(),
                "Should return empty optional for " + "non-existent score snapshot from db");

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService)
                .getScoreSnapshotFromDb(Mockito.eq(tournament), Mockito.eq(nonExistingAccount));
    }

    @Test
    @DisplayName("Should return top scores in correct order with proper ranking")
    void shouldReturnTopScores() throws Throwable {
        // Given
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(150.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(250.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account4, createScoreSnapshot(300.0));

        // When
        List<TournamentPlayerRankWithDetails> topEntries = spyLeaderboardService.getScoresRange(tournament, 1, 3, true);

        // Then
        assertEquals(3, topEntries.size(), "Should return exactly 3 entries");

        // Verify scores in descending order
        assertEquals(300.0, topEntries.get(0).getScoreDetails().getScore(), "First entry should have highest score");
        assertEquals(250.0, topEntries.get(1).getScoreDetails().getScore(),
                "Second entry should have second highest score");
        assertEquals(200.0, topEntries.get(2).getScoreDetails().getScore(),
                "Third entry should have third highest score");

        // Verify ranks
        assertEquals(1L, topEntries.get(0).getRank(), "First entry's rank should be 1");
        assertEquals(2L, topEntries.get(1).getRank(), "Second entry's rank should be 2");
        assertEquals(3L, topEntries.get(2).getRank(), "Third entry's rank should be 3");

        // Verify account IDs
        assertEquals(account4.getRemoteId(), topEntries.get(0).getRemoteAccountId(),
                "First entry should be " + "account 4");
        assertEquals(account2.getRemoteId(), topEntries.get(1).getRemoteAccountId(),
                "Second entry should be " + "account 2");
        assertEquals(account3.getRemoteId(), topEntries.get(2).getRemoteAccountId(),
                "Third entry should be " + "account 3");

        // When
        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoresRangeFromCacheZeroBased(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt());
        leaderboardServiceLocalCache.clearCache("test");

        List<TournamentPlayerRankWithDetails> topEntriesAfterCacheFailure = spyLeaderboardService.getScoresRange(
                tournament, 1, 3, true);
        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService)
                .getScoresRangeFromDbZeroBased(Mockito.eq(tournament), Mockito.eq(0), Mockito.eq(3));

        // Verify that we still get the same number of entries after cache failure
        assertEquals(3, topEntriesAfterCacheFailure.size(), "Should return exactly 3 entries after cache failure");

        // Verify that the entries match the original entries
        for (int i = 0; i < 3; i++) {
            assertEquals(topEntries.get(i).getScoreDetails().getScore(),
                    topEntriesAfterCacheFailure.get(i).getScoreDetails().getScore(),
                    "Score should match after cache failure for entry " + i);
            assertEquals(topEntries.get(i).getRank(), topEntriesAfterCacheFailure.get(i).getRank(),
                    "Rank should match after cache failure for entry " + i);
            assertEquals(topEntries.get(i).getRemoteAccountId(),
                    topEntriesAfterCacheFailure.get(i).getRemoteAccountId(),
                    "Account ID should match after cache failure for entry " + i);
        }

        try (var tx = ebean.newTransaction()) {
            // Get top scores from the database
            List<ScoreSnapshot> dbTopScores = ebean.scoreSnapshotRepo().getScoresRangeZeroBased(tournament, 0, 3, tx);

            // Validate that we have the same number of entries
            assertEquals(3, dbTopScores.size(), "Database should return exactly 3 entries");

            // Verify scores in descending order
            assertEquals(300.0, dbTopScores.get(0).getScore().doubleValue(),
                    "First DB entry should have highest score");
            assertEquals(250.0, dbTopScores.get(1).getScore().doubleValue(),
                    "Second DB entry should have second highest score");
            assertEquals(200.0, dbTopScores.get(2).getScore().doubleValue(),
                    "Third DB entry should have third highest score");

            // Verifaccounty account IDs match
            assertEquals(account4.getRemoteId(), dbTopScores.get(0).getAccount().getRemoteId(),
                    "First DB entry should be " + "account 4");
            assertEquals(account2.getRemoteId(), dbTopScores.get(1).getAccount().getRemoteId(),
                    "Second DB entry should be " + "account 2");
            assertEquals(account3.getRemoteId(), dbTopScores.get(2).getAccount().getRemoteId(),
                    "Third DB entry should be " + "account 3");

            // Verify that DB scores match Redis scores
            for (int i = 0; i < 3; i++) {
                assertEquals(topEntries.get(i).getScoreDetails().getScore(),
                        dbTopScores.get(i).getScore().doubleValue(),
                        "DB score should match Redis score for entry " + i);
                assertEquals(topEntries.get(i).getRemoteAccountId(), dbTopScores.get(i).getAccount().getRemoteId(),
                        "DB account ID should match Redis account ID for entry " + i);
            }
        }
    }

    @Test
    @DisplayName("Should return correctly score when limit is 1")
    void shouldReturnCorrectlyTopScoreWhenLimitIsOne() throws Throwable {
        // Given
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(150.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(250.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account4, createScoreSnapshot(300.0));

        // When
        List<TournamentPlayerRankWithDetails> topEntries = spyLeaderboardService.getScoresRange(tournament, 1, 1, true);

        // Then
        assertEquals(1, topEntries.size(), "Should return exactly 1 entry");

        // Verify the score is the highest
        assertEquals(300.0, topEntries.get(0).getScoreDetails().getScore(), "Entry should have the highest score");

        // Verify rank
        assertEquals(1L, topEntries.get(0).getRank(), "Entry's rank should be 1");

        // Verify account ID
        assertEquals(account4.getRemoteId(), topEntries.get(0).getRemoteAccountId(), "Entry should be account 4");

        // When
        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoresRangeFromCacheZeroBased(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt());
        leaderboardServiceLocalCache.clearCache("test");

        List<TournamentPlayerRankWithDetails> topEntriesAfterCacheFailure = spyLeaderboardService.getScoresRange(
                tournament, 1, 1, true);

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService).getScoresRangeFromDbZeroBased(Mockito.eq(tournament), Mockito.eq(0), Mockito.eq(1));

        // Verify that we still get the same number of entries after cache failure
        assertEquals(1, topEntriesAfterCacheFailure.size(), "Should return exactly 1 entry after cache failure");

        // Verify that the entry matches the original entry
        assertEquals(topEntries.get(0).getScoreDetails().getScore(),
                topEntriesAfterCacheFailure.get(0).getScoreDetails().getScore(),
                "Score should match after cache failure");
        assertEquals(topEntries.get(0).getRank(), topEntriesAfterCacheFailure.get(0).getRank(),
                "Rank should match after cache failure");
        assertEquals(topEntries.get(0).getRemoteAccountId(), topEntriesAfterCacheFailure.get(0).getRemoteAccountId(),
                "Account ID should match after cache failure");

        try (var tx = ebean.newTransaction()) {
            // Get top score from the database
            List<ScoreSnapshot> dbTopScores = ebean.scoreSnapshotRepo().getScoresRangeZeroBased(tournament, 0, 1, tx);

            // Validate that we have the same number of entries
            assertEquals(1, dbTopScores.size(), "Database should return exactly 1 entry");

            // Verify score is the highest
            assertEquals(300.0, dbTopScores.get(0).getScore().doubleValue(), "DB entry should have the highest score");

            // Verify account ID matches
            assertEquals(account4.getRemoteId(), dbTopScores.get(0).getAccount().getRemoteId(),
                    "DB entry should be account 4");

            // Verify that DB score matches Redis score
            assertEquals(topEntries.get(0).getScoreDetails().getScore(), dbTopScores.get(0).getScore().doubleValue(),
                    "DB score should match Redis score");
            assertEquals(topEntries.get(0).getRemoteAccountId(), dbTopScores.get(0).getAccount().getRemoteId(),
                    "DB account ID should match Redis account ID");
        }
    }

    @Test
    @DisplayName("Should return empty list when no scores exist in leaderboard")
    void shouldReturnEmptyListWhenNoScores() throws Exception {
        // When
        List<TournamentPlayerRankWithDetails> topEntries = spyLeaderboardService.getScoresRange(tournament, 1, 10, true);

        // Then
        assertTrue(topEntries.isEmpty(), "Should return empty list from cache when no scores exist");

        // When
        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoresRangeFromCacheZeroBased(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt());
        leaderboardServiceLocalCache.clearCache("Test scenario");

        List<TournamentPlayerRankWithDetails> topEntriesAfterCacheFailure = spyLeaderboardService.getScoresRange(
                tournament, 1, 10, true);

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService).getScoresRangeFromDbZeroBased(Mockito.any(), Mockito.anyInt(), Mockito.anyInt());

        // Then
        assertTrue(topEntriesAfterCacheFailure.isEmpty(), "Should return empty list from db " + "when no scores exist");
    }

    @Test
    @DisplayName("Should return correct player ranks with details for all participants")
    void shouldReturnPlayerRank() throws Throwable {
        // Given
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(150.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(250.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account4, createScoreSnapshot(300.0));

        // When
        Optional<TournamentPlayerRankWithDetails> optEntry1 = spyLeaderboardService.getPlayerRankWithDetails(tournament,
                account1).rankWithDetails();
        Optional<TournamentPlayerRankWithDetails> optEntry2 = spyLeaderboardService.getPlayerRankWithDetails(tournament,
                account2).rankWithDetails();
        Optional<TournamentPlayerRankWithDetails> optEntry3 = spyLeaderboardService.getPlayerRankWithDetails(tournament,
                account3).rankWithDetails();
        Optional<TournamentPlayerRankWithDetails> optEntry4 = spyLeaderboardService.getPlayerRankWithDetails(tournament,
                account4).rankWithDetails();

        // Then
        assertTrue(optEntry1.isPresent(), "Player rank details for account1 should be present");
        assertTrue(optEntry2.isPresent(), "Player rank details for account2 should be present");
        assertTrue(optEntry3.isPresent(), "Player rank details for account3 should be present");
        assertTrue(optEntry4.isPresent(), "Player rank details for account4 should be present");

        TournamentPlayerRankWithDetails entry1 = optEntry1.get();
        TournamentPlayerRankWithDetails entry2 = optEntry2.get();
        TournamentPlayerRankWithDetails entry3 = optEntry3.get();
        TournamentPlayerRankWithDetails entry4 = optEntry4.get();

        // Verify ranks
        assertEquals(4L, entry1.getRank(), "account1 should have rank 4");
        assertEquals(2L, entry2.getRank(), "account2 should have rank 2");
        assertEquals(3L, entry3.getRank(), "account3 should have rank 3");
        assertEquals(1L, entry4.getRank(), "account4 should have rank 1");

        // Verify scores
        assertEquals(150.0, entry1.getScoreDetails().getScore(), "account1 score should be 150.0");
        assertEquals(250.0, entry2.getScoreDetails().getScore(), "account2 score should be 250.0");
        assertEquals(200.0, entry3.getScoreDetails().getScore(), "account3 score should be 200.0");
        assertEquals(300.0, entry4.getScoreDetails().getScore(), "account4 score should be 300.0");

        // Verify account IDsaccount1.getRemoteId()
        assertEquals(account1.getRemoteId(), entry1.getRemoteAccountId(), "Entry1 should have account 1");
        assertEquals(account2.getRemoteId(), entry2.getRemoteAccountId(), "Entry2 should have account 2");
        assertEquals(account3.getRemoteId(), entry3.getRemoteAccountId(), "Entry3 should have account 3");
        assertEquals(account4.getRemoteId(), entry4.getRemoteAccountId(), "Entry4 should have account 4");

        // Reset the mock to avoid the simulated cache failure
        Mockito.reset(spyLeaderboardService);

        try (var tx = ebean.newTransaction()) {
            // Verify player ranks from database match those from Redis
            Optional<Integer> dbRank1 = ebean.scoreSnapshotRepo().getPlayerRank(tournament, account1, tx);
            Optional<Integer> dbRank2 = ebean.scoreSnapshotRepo().getPlayerRank(tournament, account2, tx);
            Optional<Integer> dbRank3 = ebean.scoreSnapshotRepo().getPlayerRank(tournament, account3, tx);
            Optional<Integer> dbRank4 = ebean.scoreSnapshotRepo().getPlayerRank(tournament, account4, tx);

            // Verify that database ranks are present
            assertTrue(dbRank1.isPresent(), "Database rank for account1 should be present");
            assertTrue(dbRank2.isPresent(), "Database rank for account2 should be present");
            assertTrue(dbRank3.isPresent(), "Database rank for account3 should be present");
            assertTrue(dbRank4.isPresent(), "Database rank for account4 should be present");

            // Verify that database ranks match Redis ranks
            assertEquals(entry1.getRank().intValue(), dbRank1.get().intValue(),
                    "Database rank should match Redis " + "rank for account1");
            assertEquals(entry2.getRank().intValue(), dbRank2.get().intValue(),
                    "Database rank should match Redis " + "rank for accoun2");
            assertEquals(entry3.getRank().intValue(), dbRank3.get().intValue(),
                    "Database rank should match Redis " + "rank for account3");
            assertEquals(entry4.getRank().intValue(), dbRank4.get().intValue(),
                    "Database rank should match Redis " + "rank for account4");

            // Verify that database scores match Redis scores
            Optional<ScoreSnapshot> dbSnapshot1 = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);
            Optional<ScoreSnapshot> dbSnapshot2 = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account2, tx);
            Optional<ScoreSnapshot> dbSnapshot3 = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account3, tx);
            Optional<ScoreSnapshot> dbSnapshot4 = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account4, tx);

            assertTrue(dbSnapshot1.isPresent(), "Database snapshot for account1 should be present");
            assertTrue(dbSnapshot2.isPresent(), "Database snapshot for account2 should be present");
            assertTrue(dbSnapshot3.isPresent(), "Database snapshot for account3 should be present");
            assertTrue(dbSnapshot4.isPresent(), "Database snapshot for account4 should be present");

            // Use delta to account for precision differences
            assertEquals(entry1.getScoreDetails().getScore(), dbSnapshot1.get().getScore().doubleValue(), 0.001,
                    "Database score should match Redis score for account1");
            assertEquals(entry2.getScoreDetails().getScore(), dbSnapshot2.get().getScore().doubleValue(), 0.001,
                    "Database score should match Redis score for account2");
            assertEquals(entry3.getScoreDetails().getScore(), dbSnapshot3.get().getScore().doubleValue(), 0.001,
                    "Database score should match Redis score for account3");
            assertEquals(entry4.getScoreDetails().getScore(), dbSnapshot4.get().getScore().doubleValue(), 0.001,
                    "Database score should match Redis score for account4");
        }
    }

    @Test
    @DisplayName("Should return empty optional when requesting rank details for non-existent player")
    void shouldReturnEmptyOptionalForNonExistentPlayer() throws Throwable {
        // Given
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(150.0));
        TournamentAccount nonExistingAccount = new TournamentAccount();
        nonExistingAccount.setId(999L);

        // When
        Optional<TournamentPlayerRankWithDetails> optEntry = spyLeaderboardService.getPlayerRankWithDetails(tournament,
                nonExistingAccount).rankWithDetails();

        // Then
        assertFalse(optEntry.isPresent(), "Should return empty optional from cache for non-existent player");

        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getPlayerRankFromCache(Mockito.anyLong(), Mockito.anyLong());

        // When
        Optional<TournamentPlayerRankWithDetails> optEntryAfterCacheFailure = spyLeaderboardService.getPlayerRankWithDetails(
                tournament, nonExistingAccount).rankWithDetails();

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService, Mockito.times(0))
                .getPlayerRankFromDb(Mockito.eq(tournament), Mockito.eq(nonExistingAccount));

        // Then
        assertFalse(optEntryAfterCacheFailure.isPresent(),
                "Should return empty optional from db " + "for non-existent player");
    }

    @Test
    @DisplayName("Should return empty optional when requesting rank for anonymous user")
    void shouldReturnNoRankForAnonymousUser() throws Throwable {
        // Given
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(150.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(250.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account4, createScoreSnapshot(300.0));

        // When
        Optional<Integer> playerRank = spyLeaderboardService.getPlayerRank(tournament, null).rank();

        // Then
        assertFalse(playerRank.isPresent(), "Should return empty optional from cache for anonymous user");

        // When
        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getPlayerRankFromCache(Mockito.anyLong(), Mockito.anyLong());

        // When
        Optional<Integer> playerRankAfterCacheFailure = spyLeaderboardService.getPlayerRank(tournament, null).rank();

        // Verify that the emulated exception was not thrown for null account ID
        // Note: The method should handle null account ID without calling the cache
        Mockito.verify(spyLeaderboardService, Mockito.never()).getPlayerRankFromDb(Mockito.any(), Mockito.any());

        // Then
        assertFalse(playerRankAfterCacheFailure.isPresent(),
                "Should return empty optional from db " + "for anonymous user");
    }

    @Test
    @DisplayName("Should rank users by timestamp when scores are equal (first to achieve wins)")
    void shouldRankUsersByTimestampWhenScoresAreEqual() throws Throwable {
        // Given - save scores in sequence with delays to ensure different timestamps
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(200.0));
        Thread.sleep(5);

        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));
        Thread.sleep(5);

        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(200.0));

        // When
        List<TournamentPlayerRankWithDetails> topEntries = spyLeaderboardService.getScoresRange(tournament, 1, 3, true);

        // Then
        assertEquals(3, topEntries.size(), "Should return 3 entries");

        // Verify ranks - users who reached the score first should have higher ranks
        assertEquals(1L, topEntries.get(0).getRank(), "First entry should have rank 1");
        assertEquals(2L, topEntries.get(1).getRank(), "Second entry should have rank 2");
        assertEquals(3L, topEntries.get(2).getRank(), "Third entry should have rank 3");

        // Verify account IDs - earlier submissions should rank higher
        assertEquals(account1.getRemoteId(), topEntries.get(0).getRemoteAccountId(),
                "First entry should be account1 (earliest)");
        assertEquals(account2.getRemoteId(), topEntries.get(1).getRemoteAccountId(),
                "Second entry should be account2" + " (second " + "earliest)");
        assertEquals(account3.getRemoteId(), topEntries.get(2).getRemoteAccountId(),
                "Third entry should be account3 " + "(latest)");

        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoresRangeFromCacheZeroBased(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt());
        leaderboardServiceLocalCache.clearCache("test");

        List<TournamentPlayerRankWithDetails> topEntriesAfterCacheFailure = spyLeaderboardService.getScoresRange(tournament, 1, 3, true);

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService).getScoresRangeFromDbZeroBased(Mockito.eq(tournament), Mockito.eq(0),
                Mockito.eq(3));

        // Verify that we still get the same number of entries after cache failure
        assertEquals(3, topEntriesAfterCacheFailure.size(), "Should return 3 entries after cache failure");

        // Verify that the ranks match the original ranks after cache failure
        assertEquals(1L, topEntriesAfterCacheFailure.get(0).getRank(),
                "First entry should have rank 1 after cache failure");
        assertEquals(2L, topEntriesAfterCacheFailure.get(1).getRank(),
                "Second entry should have rank 2 after cache failure");
        assertEquals(3L, topEntriesAfterCacheFailure.get(2).getRank(),
                "Third entry should have rank 3 after cache failure");

        // Verify that the account IDs match the original account IDs after cache failure
        assertEquals(account1.getRemoteId(), topEntriesAfterCacheFailure.get(0).getRemoteAccountId(),
                "First entry should be account 1 (earliest) after cache failure");
        assertEquals(account2.getRemoteId(), topEntriesAfterCacheFailure.get(1).getRemoteAccountId(),
                "Second entry should be account 2 (second earliest) after cache failure");
        assertEquals(account3.getRemoteId(), topEntriesAfterCacheFailure.get(2).getRemoteAccountId(),
                "Third entry should be account 3 (latest) after cache failure");

        try (var tx = ebean.newTransaction()) {
            // Get top scores from the database
            List<ScoreSnapshot> dbTopScores = ebean.scoreSnapshotRepo().getScoresRangeZeroBased(tournament, 0, 3, tx);

            // Validate that we have the same number of entries
            assertEquals(3, dbTopScores.size(), "Database should return exactly 3 entries");

            // Verify that all scores are equal (200.0)
            for (ScoreSnapshot snapshot : dbTopScores) {
                assertEquals(200.0, snapshot.getScore().doubleValue(), "All scores should be 200.0");
            }

            // Verify timestamps are in ascending order (earlier timestamps first)
            assertTrue(dbTopScores.get(0).getTimestamp() < dbTopScores.get(1).getTimestamp(),
                    "First entry should have earlier timestamp than second entry");
            assertTrue(dbTopScores.get(1).getTimestamp() < dbTopScores.get(2).getTimestamp(),
                    "Second entry should have earlier timestamp than third entry");

            // Verify account IDs match the expected order based on timestamps
            assertEquals(account1.getRemoteId(), dbTopScores.get(0).getAccount().getRemoteId(),
                    "First DB entry should be " + "account1 (earliest)");
            assertEquals(account2.getRemoteId(), dbTopScores.get(1).getAccount().getRemoteId(),
                    "Second DB entry " + "should be account 2 " + "(second earliest)");
            assertEquals(account3.getRemoteId(), dbTopScores.get(2).getAccount().getRemoteId(),
                    "Third DB entry " + "should be account 3 " + "(latest)");
        }
    }

    @Test
    @DisplayName("Should update timestamp when score changes to ensure proper ranking")
    void shouldUpdateTimestampWhenScoreChanges() throws Throwable {
        // Given
        TScoreSnapshot initialSnapshot = createScoreSnapshot(150.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, initialSnapshot);

        Optional<TScoreSnapshot> retrievedInitialSnapshot = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(retrievedInitialSnapshot.isPresent(), "Initial snapshot should be present");
        long initialTimestamp = retrievedInitialSnapshot.get().getTimestamp();

        Thread.sleep(5);

        // When - update with a different score
        TScoreSnapshot updatedSnapshot = createScoreSnapshot(200.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, updatedSnapshot);
        Optional<TScoreSnapshot> retrievedUpdatedSnapshotOpt = spyLeaderboardService.getScoreSnapshot(tournament,
                account1).scoreSnapshot();

        // Then
        assertTrue(retrievedUpdatedSnapshotOpt.isPresent(), "Updated snapshot should be present");
        TScoreSnapshot retrievedUpdatedSnapshot = retrievedUpdatedSnapshotOpt.get();
        assertEquals(200.0, retrievedUpdatedSnapshot.getScore(), "Score should be updated to 200.0");
        assertTrue(retrievedUpdatedSnapshot.getTimestamp() > initialTimestamp,
                "Timestamp should be updated for new score");

        // When
        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());
        Optional<TScoreSnapshot> retrievedAfterCacheFailure = spyLeaderboardService.getScoreSnapshot(tournament,
                account1).scoreSnapshot();

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService).getScoreSnapshotFromDb(Mockito.eq(tournament), Mockito.eq(account1));

        // Verify that we can still retrieve the snapshot after cache failure
        assertTrue(retrievedAfterCacheFailure.isPresent(), "Score snapshot should be retrievable after cache failure");
        TScoreSnapshot retrievedAfterCacheFailure2 = retrievedAfterCacheFailure.get();

        // Verify that the values match the updated snapshot
        assertEquals(retrievedUpdatedSnapshot.getScore(), retrievedAfterCacheFailure2.getScore(),
                "Score should match updated score after cache failure");
        assertEquals(retrievedUpdatedSnapshot.getTimestamp(), retrievedAfterCacheFailure2.getTimestamp(),
                "Timestamp should match updated timestamp after cache failure");
        assertTrue(retrievedAfterCacheFailure2.getTimestamp() > initialTimestamp,
                "Timestamp after cache failure should be greater than initial timestamp");

        try (var tx = ebean.newTransaction()) {
            // Verify that the score snapshot is saved in the database
            Optional<ScoreSnapshot> dbSnapshotOpt = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);

            // Validate that the score is saved in the database
            assertTrue(dbSnapshotOpt.isPresent(), "Score snapshot should be present in the database");
            ScoreSnapshot dbSnapshot = dbSnapshotOpt.get();

            // Validate that the database values match the Redis values
            assertEquals(200.0, dbSnapshot.getScore().doubleValue(), "Database score should match updated score");
            assertEquals(retrievedUpdatedSnapshot.getTimestamp(), dbSnapshot.getTimestamp(),
                    "Database timestamp should match Redis timestamp");
            assertTrue(dbSnapshot.getTimestamp() > initialTimestamp,
                    "Database timestamp should be updated for new score");
        }
    }

    @Test
    @DisplayName("Should preserve original timestamp when score doesn't change for fair ranking")
    void shouldPreserveTimestampWhenScoreDoesNotChange() throws Throwable {
        // Given
        TScoreSnapshot initialSnapshot = createScoreSnapshot(150.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, initialSnapshot);

        Optional<TScoreSnapshot> retrievedInitialSnapshotOpt = spyLeaderboardService.getScoreSnapshot(tournament,
                account1).scoreSnapshot();
        assertTrue(retrievedInitialSnapshotOpt.isPresent(), "Initial snapshot should be present");
        long initialTimestamp = retrievedInitialSnapshotOpt.get().getTimestamp();

        Thread.sleep(5);

        // When - update with the same score
        TScoreSnapshot sameScoreSnapshot = createScoreSnapshot(150.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, sameScoreSnapshot);
        Optional<TScoreSnapshot> retrievedUpdatedSnapshotOpt = spyLeaderboardService.getScoreSnapshot(tournament,
                account1).scoreSnapshot();

        // Then
        assertTrue(retrievedUpdatedSnapshotOpt.isPresent(), "Updated snapshot should be present");
        TScoreSnapshot retrievedUpdatedSnapshot = retrievedUpdatedSnapshotOpt.get();
        assertEquals(150.0, retrievedUpdatedSnapshot.getScore(), "Score should remain 150.0");
        assertEquals(initialTimestamp, retrievedUpdatedSnapshot.getTimestamp(),
                "Timestamp should be preserved for same score");

        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());
        Optional<TScoreSnapshot> retrievedAfterCacheFailureOpt = spyLeaderboardService.getScoreSnapshot(tournament,
                account1).scoreSnapshot();

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService).getScoreSnapshotFromDb(Mockito.eq(tournament), Mockito.eq(account1));

        // Verify that we can still retrieve the snapshot after cache failure
        assertTrue(retrievedAfterCacheFailureOpt.isPresent(),
                "Score snapshot should be retrievable after cache failure");
        TScoreSnapshot retrievedAfterCacheFailure = retrievedAfterCacheFailureOpt.get();

        // Verify that the values match the updated snapshot
        assertEquals(retrievedUpdatedSnapshot.getScore(), retrievedAfterCacheFailure.getScore(),
                "Score should match after cache failure");
        assertEquals(retrievedUpdatedSnapshot.getTimestamp(), retrievedAfterCacheFailure.getTimestamp(),
                "Timestamp should match after cache failure");
        assertEquals(initialTimestamp, retrievedAfterCacheFailure.getTimestamp(),
                "Timestamp after cache failure should be the same as initial timestamp");

        try (var tx = ebean.newTransaction()) {
            // Verify that the score snapshot is saved in the database
            Optional<ScoreSnapshot> dbSnapshotOpt = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);

            // Validate that the score is saved in the database
            assertTrue(dbSnapshotOpt.isPresent(), "Score snapshot should be present in the database");
            ScoreSnapshot dbSnapshot = dbSnapshotOpt.get();

            // Validate that the database values match the Redis values
            assertEquals(150.0, dbSnapshot.getScore().doubleValue(), "Database score should match Redis score");
            assertEquals(retrievedUpdatedSnapshot.getTimestamp(), dbSnapshot.getTimestamp(),
                    "Database timestamp should match Redis timestamp");
            assertEquals(initialTimestamp, dbSnapshot.getTimestamp(),
                    "Database timestamp should be preserved for same score");
        }
    }

    @Test
    @DisplayName("Should properly handle and preserve negative scores in leaderboard")
    void shouldHandleNegativeScores() throws Throwable {
        // Given
        TScoreSnapshot negativeScoreSnapshot = createScoreSnapshot(-50.0);

        // When
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, negativeScoreSnapshot);
        Optional<TScoreSnapshot> retrievedSnapshot = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();

        // Then
        assertTrue(retrievedSnapshot.isPresent(), "Snapshot with negative score should be retrievable");
        assertEquals(-50.0, retrievedSnapshot.get().getScore(), "Negative score should be preserved");

        // When
        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());
        Optional<TScoreSnapshot> retrievedAfterCacheFailureOpt = spyLeaderboardService.getScoreSnapshot(tournament,
                account1).scoreSnapshot();

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService).getScoreSnapshotFromDb(Mockito.eq(tournament), Mockito.eq(account1));

        assertTrue(retrievedAfterCacheFailureOpt.isPresent(),
                "Score snapshot should be retrievable after cache failure");
        TScoreSnapshot retrievedAfterCacheFailure = retrievedAfterCacheFailureOpt.get();

        // Verify that the values match the original snapshot
        assertEquals(retrievedSnapshot.get().getScore(), retrievedAfterCacheFailure.getScore(),
                "Negative score should be preserved after cache failure");
        assertEquals(retrievedSnapshot.get().getTimestamp(), retrievedAfterCacheFailure.getTimestamp(),
                "Timestamp should match after cache failure");

        try (var tx = ebean.newTransaction()) {
            // Verify that the score snapshot is saved in the database
            Optional<ScoreSnapshot> dbSnapshotOpt = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);

            // Validate that the score is saved in the database
            assertTrue(dbSnapshotOpt.isPresent(), "Score snapshot should be present in the database");
            ScoreSnapshot dbSnapshot = dbSnapshotOpt.get();

            // Validate that the database values match the Redis values
            assertEquals(-50.0, dbSnapshot.getScore().doubleValue(), "Database should preserve negative score");
            assertEquals(retrievedSnapshot.get().getTimestamp(), dbSnapshot.getTimestamp(),
                    "Database timestamp should match Redis timestamp");
        }
    }

    @Test
    @DisplayName("Should maintain separate leaderboards for different tournaments and handle cache TTL and DB fallback")
    void shouldHandleMultipleTournamentsIndependently() throws Throwable {
        // Given
        Tournament secondTournament = tournamentTestHelper.createTournament();

        // Add scores to first tournament
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));

        // Add different scores to second tournament
        spyLeaderboardService.upsertScoreSnapshot(secondTournament, account1, createScoreSnapshot(300.0));
        spyLeaderboardService.upsertScoreSnapshot(secondTournament, account2, createScoreSnapshot(400.0));

        // When
        Optional<TScoreSnapshot> firstTournamentAccount1 = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        Optional<TScoreSnapshot> secondTournamentAccount1 = spyLeaderboardService.getScoreSnapshot(secondTournament,
                account1).scoreSnapshot();

        // Then
        assertTrue(firstTournamentAccount1.isPresent(), "First tournament snapshot should exist");
        assertTrue(secondTournamentAccount1.isPresent(), "Second tournament snapshot should exist");
        assertEquals(100.0, firstTournamentAccount1.get().getScore(), "First tournament score should be 100.0");
        assertEquals(300.0, secondTournamentAccount1.get().getScore(), "Second tournament score should be 300.0");

        // Verify ranks are independent
        Optional<Integer> rankInFirstTournament = spyLeaderboardService.getPlayerRank(tournament, account1).rank();
        Optional<Integer> rankInSecondTournament = spyLeaderboardService.getPlayerRank(secondTournament, account1)
                .rank();

        assertTrue(rankInFirstTournament.isPresent(), "Rank in first tournament should exist");
        assertTrue(rankInSecondTournament.isPresent(), "Rank in second tournament should exist");
        assertEquals(2, rankInFirstTournament.get(), "Rank in first tournament should be 2");
        assertEquals(2, rankInSecondTournament.get(), "Rank in second tournament should be 2");

        // When
        // Instead of mocking getScoreSnapshotFromCache to throw an exception,
        // let's mark the cache as failed and verify that the service falls back to DB
        LeaderboardRedisCacheChecker cacheChecker = Mockito.mock(LeaderboardRedisCacheChecker.class);
        Mockito.when(cacheChecker.isInSyncWithDb()).thenReturn(false);
        Mockito.when(cacheChecker.dbFallbackEnabled()).thenReturn(true);

        // Create a new spyLeaderboardService with the mocked cacheChecker
        TournamentLeaderboardService failingLeaderboardService = Mockito.spy(
                new TournamentLeaderboardService(redisClientFactoryBean, ebean, cacheChecker, props, leaderboardServiceLocalCache));

        // Get scores using the failing spyLeaderboardService
        Optional<TScoreSnapshot> firstTournamentAccount1AfterCacheFailure = failingLeaderboardService.getScoreSnapshot(
                tournament, account1).scoreSnapshot();
        Optional<TScoreSnapshot> secondTournamentAccount1AfterCacheFailure = failingLeaderboardService.getScoreSnapshot(
                secondTournament, account1).scoreSnapshot();

        // Verify that fallback methods were executed
        Mockito.verify(failingLeaderboardService).getScoreSnapshotFromDb(Mockito.eq(tournament), Mockito.eq(account1));
        Mockito.verify(failingLeaderboardService)
                .getScoreSnapshotFromDb(Mockito.eq(secondTournament), Mockito.eq(account1));

        // Verify that we can still retrieve the snapshots after cache failure
        assertTrue(firstTournamentAccount1AfterCacheFailure.isPresent(),
                "First tournament snapshot should exist after cache failure");
        assertTrue(secondTournamentAccount1AfterCacheFailure.isPresent(),
                "Second tournament snapshot should exist after cache failure");

        // Verify that the scores match the original scores
        assertEquals(firstTournamentAccount1.get().getScore(),
                firstTournamentAccount1AfterCacheFailure.get().getScore(),
                "First tournament score should match after cache failure");
        assertEquals(secondTournamentAccount1.get().getScore(),
                secondTournamentAccount1AfterCacheFailure.get().getScore(),
                "Second tournament score should match after cache failure");

        // Verify ranks are still independent after cache failure
        Optional<Integer> rankInFirstTournamentAfterCacheFailure = spyLeaderboardService.getPlayerRank(tournament,
                account1).rank();
        Optional<Integer> rankInSecondTournamentAfterCacheFailure = spyLeaderboardService.getPlayerRank(
                secondTournament, account1).rank();

        assertTrue(rankInFirstTournamentAfterCacheFailure.isPresent(),
                "Rank in first tournament should exist after cache failure");
        assertTrue(rankInSecondTournamentAfterCacheFailure.isPresent(),
                "Rank in second tournament should exist after cache failure");
        assertEquals(rankInFirstTournament.get(), rankInFirstTournamentAfterCacheFailure.get(),
                "Rank in first tournament should match after cache failure");
        assertEquals(rankInSecondTournament.get(), rankInSecondTournamentAfterCacheFailure.get(),
                "Rank in second tournament should match after cache failure");

        try (var tx = ebean.newTransaction()) {
            // Verify that the score snapshots are saved in the database for both tournaments
            Optional<ScoreSnapshot> dbSnapshot1FirstTournament = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);
            Optional<ScoreSnapshot> dbSnapshot1SecondTournament = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(secondTournament, account1, tx);

            // Validate that the scores are saved in the database
            assertTrue(dbSnapshot1FirstTournament.isPresent(),
                    "Score snapshot for first tournament should be present in the database");
            assertTrue(dbSnapshot1SecondTournament.isPresent(),
                    "Score snapshot for second tournament should be present in the database");

            // Validate that the database values match the expected values
            assertEquals(100.0, dbSnapshot1FirstTournament.get().getScore().doubleValue(),
                    "Database score for first tournament should be 100.0");
            assertEquals(300.0, dbSnapshot1SecondTournament.get().getScore().doubleValue(),
                    "Database score for second tournament should be 300.0");

            // Verify ranks from database match those from Redis
            Optional<Integer> dbRankInFirstTournament = ebean.scoreSnapshotRepo()
                    .getPlayerRank(tournament, account1, tx);
            Optional<Integer> dbRankInSecondTournament = ebean.scoreSnapshotRepo()
                    .getPlayerRank(secondTournament, account1, tx);

            assertTrue(dbRankInFirstTournament.isPresent(), "Database rank in first tournament should exist");
            assertTrue(dbRankInSecondTournament.isPresent(), "Database rank in second tournament should exist");
            assertEquals(rankInFirstTournament.get(), dbRankInFirstTournament.get(),
                    "Database rank should match Redis rank for first tournament");
            assertEquals(rankInSecondTournament.get(), dbRankInSecondTournament.get(),
                    "Database rank should match Redis rank for second tournament");
        }

        // Reset mock
        Mockito.reset(spyLeaderboardService);

        // Test cache TTL functionality
        // ===========================

        // Create a tournament that ended recently (within cache TTL period)
        java.time.Instant recentEndDate = java.time.Instant.now().minus(1, java.time.temporal.ChronoUnit.HOURS);
        java.time.Instant recentStartDate = recentEndDate.minus(2, java.time.temporal.ChronoUnit.HOURS);

        common.proto.TTournamentStatus endedStatus = common.proto.TTournamentStatus.ENDED;
        var recentResponse = tournamentTestHelper.createTournament(endedStatus, recentStartDate, recentEndDate);
        Tournament recentlyEndedTournament = tournamentTestHelper.getTournamentById(recentResponse.getId());

        // Add score to recently ended tournament
        spyLeaderboardService.upsertScoreSnapshot(recentlyEndedTournament, account1, createScoreSnapshot(500.0));

        // Verify score is available in cache
        Optional<TScoreSnapshot> recentTournamentScore = spyLeaderboardService.getScoreSnapshot(recentlyEndedTournament,
                account1).scoreSnapshot();
        assertTrue(recentTournamentScore.isPresent(),
                "Score should be available in cache for recently ended tournament");
        assertEquals(500.0, recentTournamentScore.get().getScore(), "Score should be 500.0");

        // Spy on the methods to verify they're called
        Mockito.doCallRealMethod().when(spyLeaderboardService).getScoreSnapshot(Mockito.any(), Mockito.any());

        // Get the score again to verify cache is used
        spyLeaderboardService.getScoreSnapshot(recentlyEndedTournament, account1);

        // Verify that cache was used (not DB)
        Mockito.verify(spyLeaderboardService, Mockito.never())
                .getScoreSnapshotFromDb(Mockito.eq(recentlyEndedTournament), Mockito.eq(account1));

        // Reset mock
        Mockito.reset(spyLeaderboardService);

        // Test DB fallback before cache expiry
        // ===================================

        // Create a tournament that is nearing cache expiry (within switchover period)
        // Use hardcoded values for cache TTL and switchover period
        // Cache TTL: 7 days in seconds
        long cacheTtlSeconds = 7 * 24 * 60 * 60;
        // Switchover period: 2 hours in seconds
        long switchoverPeriodSeconds = 2 * 60 * 60;

        // Calculate an end date that puts us in the switchover period
        java.time.Instant now = java.time.Instant.now();
        java.time.Instant expiringEndDate = now.minus(cacheTtlSeconds - switchoverPeriodSeconds + 60,
                // +60 seconds to ensure we're in the window
                java.time.temporal.ChronoUnit.SECONDS);
        java.time.Instant expiringStartDate = expiringEndDate.minus(2, java.time.temporal.ChronoUnit.HOURS);

        var expiringResponse = tournamentTestHelper.createTournament(endedStatus, expiringStartDate, expiringEndDate);
        Tournament expiringTournament = tournamentTestHelper.getTournamentById(expiringResponse.getId());

        // Add score to expiring tournament
        spyLeaderboardService.upsertScoreSnapshot(expiringTournament, account1, createScoreSnapshot(600.0));

        // Spy on the methods to verify they're called
        Mockito.doCallRealMethod().when(spyLeaderboardService).getScoreSnapshot(Mockito.any(), Mockito.any());

        // Get the score to verify DB is used directly
        Optional<TScoreSnapshot> expiringTournamentScore = spyLeaderboardService.getScoreSnapshot(expiringTournament,
                account1).scoreSnapshot();

        // Verify score is available
        assertTrue(expiringTournamentScore.isPresent(), "Score should be available for expiring tournament");
        assertEquals(600.0, expiringTournamentScore.get().getScore(), "Score should be 600.0");

        // Verify that DB was used directly (not cache)
        Mockito.verify(spyLeaderboardService, Mockito.never())
                .getScoreSnapshotFromCache(Mockito.eq(expiringTournament.getId()), Mockito.eq(account1.getRemoteId()));
        Mockito.verify(spyLeaderboardService)
                .getScoreSnapshotFromDb(Mockito.eq(expiringTournament), Mockito.eq(account1));
    }

    @Test
    @DisplayName("Should properly handle and preserve high precision scores")
    void shouldHandleHighPrecisionScores() throws Throwable {
        // Given
        double highPrecisionScore = 123.45;
        TScoreSnapshot scoreSnapshot = createScoreSnapshot(highPrecisionScore);

        // When
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, scoreSnapshot);
        Optional<TScoreSnapshot> retrievedSnapshot = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();

        // Then
        assertTrue(retrievedSnapshot.isPresent(), "High precision score snapshot should be retrievable");
        assertEquals(highPrecisionScore, retrievedSnapshot.get().getScore(),
                "High precision score should be preserved exactly");

        // Reset the mock to avoid the simulated cache failure
        Mockito.reset(spyLeaderboardService);

        try (var tx = ebean.newTransaction()) {
            // Verify that the score snapshot is saved in the database
            Optional<ScoreSnapshot> dbSnapshotOpt = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);

            // Validate that the score is saved in the database
            assertTrue(dbSnapshotOpt.isPresent(), "Score snapshot should be present in the database");
            ScoreSnapshot dbSnapshot = dbSnapshotOpt.get();

            // Validate that the database values match the Redis values with a larger delta to account for database
            // rounding
            assertEquals(highPrecisionScore, dbSnapshot.getScore().doubleValue(), 0.001,
                    "Database should preserve high precision score");
            assertEquals(retrievedSnapshot.get().getTimestamp(), dbSnapshot.getTimestamp(),
                    "Database timestamp should match Redis timestamp");
        }
    }

    @Test
    @DisplayName("Should handle complete replacement of score snapshot with new values")
    void shouldHandleCompleteReplacement() throws Throwable {
        // Given
        TScoreSnapshot initialSnapshot = createCustomScoreSnapshot(100.0, 200, 50, 2.0, 10.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, initialSnapshot);

        // When - completely different snapshot values
        TScoreSnapshot replacementSnapshot = createCustomScoreSnapshot(150.0, 300, 75, 3.0, 15.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, replacementSnapshot);

        Optional<TScoreSnapshot> retrievedSnapshot = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();

        // Then
        assertTrue(retrievedSnapshot.isPresent(), "Replacement snapshot should be retrievable");
        TScoreSnapshot snapshot = retrievedSnapshot.get();
        assertEquals(150.0, snapshot.getScore(), "Score should be updated");
        assertEquals(300, snapshot.getTotalCredit(), "Total credit should be updated");
        assertEquals(75, snapshot.getTotalDebit(), "Total debit should be updated");
        assertEquals(3.0, snapshot.getHighestMultiplier(), "Highest multiplier should be updated");
        assertEquals(15.0, snapshot.getTotalMultiplier(), "Total multiplier should be updated");

        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());
        Optional<TScoreSnapshot> retrievedAfterCacheFailureOpt = spyLeaderboardService.getScoreSnapshot(tournament,
                account1).scoreSnapshot();

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService).getScoreSnapshotFromDb(Mockito.eq(tournament), Mockito.eq(account1));

        // Verify that we can still retrieve the snapshot after cache failure
        assertTrue(retrievedAfterCacheFailureOpt.isPresent(),
                "Replacement snapshot should be retrievable after cache failure");
        TScoreSnapshot snapshotAfterCacheFailure = retrievedAfterCacheFailureOpt.get();

        // Verify that the values match the replacement snapshot
        assertEquals(snapshot.getScore(), snapshotAfterCacheFailure.getScore(),
                "Score should match after cache failure");
        assertEquals(snapshot.getTotalCredit(), snapshotAfterCacheFailure.getTotalCredit(),
                "Total credit should match after cache failure");
        assertEquals(snapshot.getTotalDebit(), snapshotAfterCacheFailure.getTotalDebit(),
                "Total debit should match after cache failure");
        assertEquals(snapshot.getHighestMultiplier(), snapshotAfterCacheFailure.getHighestMultiplier(),
                "Highest multiplier should match after cache failure");
        assertEquals(snapshot.getTotalMultiplier(), snapshotAfterCacheFailure.getTotalMultiplier(),
                "Total multiplier should match after cache failure");
        assertEquals(snapshot.getTimestamp(), snapshotAfterCacheFailure.getTimestamp(),
                "Timestamp should match after cache failure");

        try (var tx = ebean.newTransaction()) {
            // Verify that the score snapshot is saved in the database
            Optional<ScoreSnapshot> dbSnapshotOpt = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);

            // Validate that the score is saved in the database
            assertTrue(dbSnapshotOpt.isPresent(), "Score snapshot should be present in the database");
            ScoreSnapshot dbSnapshot = dbSnapshotOpt.get();

            // Validate that the database values match the Redis values
            assertEquals(150.0, dbSnapshot.getScore().doubleValue(), "Database score should be updated");
            assertEquals(300, dbSnapshot.getTotalCredit().doubleValue(), "Database total credit should be updated");
            assertEquals(75, dbSnapshot.getTotalDebit().doubleValue(), "Database total debit should be updated");
            assertEquals(3.0, dbSnapshot.getHighestMultiplier().doubleValue(),
                    "Database highest multiplier should be updated");
            assertEquals(15.0, dbSnapshot.getTotalMultiplier().doubleValue(),
                    "Database total multiplier should be updated");
            assertEquals(snapshot.getTimestamp(), dbSnapshot.getTimestamp(),
                    "Database timestamp should match Redis timestamp");
        }
    }

    @Test
    @DisplayName("Should handle large numeric scores with appropriate rounding")
    void shouldHandleExtremelyHighScores() throws Throwable {
        // Given
        double extremelyHighScore = 1_234_567_890.42;
        TScoreSnapshot scoreSnapshot = createScoreSnapshot(extremelyHighScore);

        // When
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, scoreSnapshot);
        Optional<TScoreSnapshot> retrievedSnapshot = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();

        // Then
        assertTrue(retrievedSnapshot.isPresent(), "Extremely high score snapshot should be retrievable");

        // Check with delta to account for rounding to 2 decimal places
        assertEquals(extremelyHighScore, retrievedSnapshot.get().getScore(), 0.001,
                "Score should be preserved with rounding to 2 decimal places");

        // Emulate cache failure and verify that the service still works by fetching from DB
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());
        Optional<TScoreSnapshot> retrievedAfterCacheFailureOpt = spyLeaderboardService.getScoreSnapshot(tournament,
                account1).scoreSnapshot();

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService).getScoreSnapshotFromDb(Mockito.eq(tournament), Mockito.eq(account1));

        // Verify that we can still retrieve the snapshot after cache failure
        assertTrue(retrievedAfterCacheFailureOpt.isPresent(),
                "Extremely high score snapshot should be retrievable after cache failure");
        TScoreSnapshot retrievedAfterCacheFailure = retrievedAfterCacheFailureOpt.get();

        // Verify that the values match the original snapshot
        assertEquals(retrievedSnapshot.get().getScore(), retrievedAfterCacheFailure.getScore(), 0.001,
                "Extremely high score should be preserved after cache failure");
        assertEquals(retrievedSnapshot.get().getTimestamp(), retrievedAfterCacheFailure.getTimestamp(),
                "Timestamp should match after cache failure");

        try (var tx = ebean.newTransaction()) {
            // Verify that the score snapshot is saved in the database
            Optional<ScoreSnapshot> dbSnapshotOpt = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);

            // Validate that the score is saved in the database
            assertTrue(dbSnapshotOpt.isPresent(), "Score snapshot should be present in the database");
            ScoreSnapshot dbSnapshot = dbSnapshotOpt.get();

            // Validate that the database values match the Redis values
            assertEquals(extremelyHighScore, dbSnapshot.getScore().doubleValue(), 0.001,
                    "Database should preserve extremely high score with appropriate rounding");
            assertEquals(retrievedSnapshot.get().getTimestamp(), dbSnapshot.getTimestamp(),
                    "Database timestamp should match Redis timestamp");
        }
    }

    @Test
    @DisplayName("Should set cache TTL based on tournament end date")
    void shouldSetCacheTtlBasedOnTournamentEndDate() throws Throwable {
        // Given - Create a tournament that ended recently
        java.time.Instant endDate = java.time.Instant.now().minus(1, java.time.temporal.ChronoUnit.HOURS);
        java.time.Instant startDate = endDate.minus(2, java.time.temporal.ChronoUnit.HOURS);

        // Create tournament with specific dates
        common.proto.TTournamentStatus status = common.proto.TTournamentStatus.ENDED;
        var response = tournamentTestHelper.createTournament(status, startDate, endDate);
        Tournament endedTournament = tournamentTestHelper.getTournamentById(response.getId());

        // When - Save a score snapshot
        TScoreSnapshot scoreSnapshot = createScoreSnapshot(150.0);
        spyLeaderboardService.upsertScoreSnapshot(endedTournament, account1, scoreSnapshot);

        // Then - Verify that the score snapshot is saved to both cache and DB
        Optional<TScoreSnapshot> retrievedFromCache = spyLeaderboardService.getScoreSnapshot(endedTournament, account1)
                .scoreSnapshot();
        assertTrue(retrievedFromCache.isPresent(),
                "Score snapshot should be present in cache for recently ended tournament");

        // Verify DB fallback works
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());
        Optional<TScoreSnapshot> retrievedFromDb = spyLeaderboardService.getScoreSnapshot(endedTournament, account1)
                .scoreSnapshot();
        assertTrue(retrievedFromDb.isPresent(), "Score snapshot should be present in DB for recently ended tournament");

        // Reset mock
        Mockito.reset(spyLeaderboardService);

        // Given - Create a tournament that ended long ago (beyond TTL period)
        java.time.Instant longAgoEndDate = java.time.Instant.now()
                .minus(props.SCORE_CACHE_TTL_EXPIRATION_AFTER_TOURNAMENT_END.get().toSeconds() + 3600,
                        java.time.temporal.ChronoUnit.SECONDS);
        java.time.Instant longAgoStartDate = longAgoEndDate.minus(2, java.time.temporal.ChronoUnit.HOURS);

        var longAgoResponse = tournamentTestHelper.createTournament(status, longAgoStartDate, longAgoEndDate);
        Tournament longAgoEndedTournament = tournamentTestHelper.getTournamentById(longAgoResponse.getId());

        // When - Try to save a score snapshot
        spyLeaderboardService.upsertScoreSnapshot(longAgoEndedTournament, account1, scoreSnapshot);

        // Then - Verify that the score snapshot is saved to DB but not to cache
        // Spy on the saveScoreSnapshotToCache method to verify it's not called
        Mockito.doReturn(Optional.empty())
                .when(spyLeaderboardService)
                .saveScoreSnapshotToCache(Mockito.eq(longAgoEndedTournament), Mockito.eq(account1), Mockito.any());

        // Verify that the score is saved to DB
        try (var tx = ebean.newTransaction()) {
            Optional<ScoreSnapshot> dbSnapshotOpt = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(longAgoEndedTournament, account1, tx);
            assertTrue(dbSnapshotOpt.isPresent(),
                    "Score snapshot should be present in DB for long ago ended tournament");
        }

        // Verify that the service directly uses DB for lookup
        Optional<TScoreSnapshot> retrievedForLongAgoTournament = spyLeaderboardService.getScoreSnapshot(
                longAgoEndedTournament, account1).scoreSnapshot();
        assertTrue(retrievedForLongAgoTournament.isPresent(),
                "Score snapshot should be retrievable for long ago ended tournament");

        // Verify that the service used DB lookup directly
        Mockito.verify(spyLeaderboardService, Mockito.never())
                .getScoreSnapshotFromCache(Mockito.eq(longAgoEndedTournament.getId()),
                        Mockito.eq(account1.getRemoteId()));
        Mockito.verify(spyLeaderboardService)
                .getScoreSnapshotFromDb(Mockito.eq(longAgoEndedTournament), Mockito.eq(account1));
    }

    @Test
    @DisplayName("Should switch to DB lookup before cache expiry")
    void shouldSwitchToDbLookupBeforeCacheExpiry() throws Throwable {
        // Given - Create a tournament that will expire soon (within the switchover period)
        java.time.Duration switchoverPeriod = props.SCORE_CACHE_SWITCH_TO_DB_LOOKUP_BEFORE_CACHE_EXPIRY.get();
        java.time.Duration cacheTtl = props.SCORE_CACHE_TTL_EXPIRATION_AFTER_TOURNAMENT_END.get();

        // Calculate an end date that puts us in the switchover period
        // End date is in the past, but recent enough that cache hasn't expired yet
        // But it's close enough to expiry that we should switch to DB lookup
        java.time.Instant now = java.time.Instant.now();
        java.time.Instant endDate = now.minus(cacheTtl.toSeconds() - switchoverPeriod.toSeconds() + 60,
                // +60 seconds to ensure we're in the window
                java.time.temporal.ChronoUnit.SECONDS);
        java.time.Instant startDate = endDate.minus(2, java.time.temporal.ChronoUnit.HOURS);

        common.proto.TTournamentStatus status = common.proto.TTournamentStatus.ENDED;
        var response = tournamentTestHelper.createTournament(status, startDate, endDate);
        Tournament expiringTournament = tournamentTestHelper.getTournamentById(response.getId());

        // When - Save a score snapshot
        TScoreSnapshot scoreSnapshot = createScoreSnapshot(150.0);
        spyLeaderboardService.upsertScoreSnapshot(expiringTournament, account1, scoreSnapshot);

        // Then - Verify that the service switches to DB lookup
        // Reset any previous mock interactions
        Mockito.reset(spyLeaderboardService);

        // Spy on the actual methods to verify they're called
        Mockito.doCallRealMethod().when(spyLeaderboardService).getScoreSnapshot(Mockito.any(), Mockito.any());

        // Get the score snapshot
        Optional<TScoreSnapshot> retrieved = spyLeaderboardService.getScoreSnapshot(expiringTournament, account1)
                .scoreSnapshot();

        // Verify that the score snapshot is retrieved
        assertTrue(retrieved.isPresent(), "Score snapshot should be retrievable for expiring tournament");

        // Verify that the service used DB lookup directly
        Mockito.verify(spyLeaderboardService, Mockito.never())
                .getScoreSnapshotFromCache(Mockito.eq(expiringTournament.getId()), Mockito.eq(account1.getRemoteId()));
        Mockito.verify(spyLeaderboardService)
                .getScoreSnapshotFromDb(Mockito.eq(expiringTournament), Mockito.eq(account1));
    }

    @Test
    @DisplayName("Should not save score snapshot with score 0 to database and cache")
    void shouldNotSaveZeroScoreSnapshot() throws Throwable {
        // Given - Create a score snapshot with score = 0
        TScoreSnapshot zeroScoreSnapshot = createScoreSnapshot(0.0);

        // Add some other players with non-zero scores for context
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(50.0));

        // When - Attempt to save the zero score snapshot
        boolean result = spyLeaderboardService.upsertScoreSnapshot(tournament, account1, zeroScoreSnapshot);

        // Then - Verify that upsertScoreSnapshot returns false for zero scores
        assertFalse(result, "upsertScoreSnapshot should return false for zero scores");

        // Then - Verify the score snapshot is NOT saved to cache
        Optional<TScoreSnapshot> cachedSnapshot = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertFalse(cachedSnapshot.isPresent(), "Score snapshot with score 0 should not be present in cache");

        // Verify the player does NOT have a rank
        Optional<Integer> playerRank = spyLeaderboardService.getPlayerRank(tournament, account1).rank();
        assertFalse(playerRank.isPresent(), "Player with score 0 should not have a rank");

        // Verify player rank with details is NOT available
        Optional<TournamentPlayerRankWithDetails> rankWithDetails = spyLeaderboardService.getPlayerRankWithDetails(
                tournament, account1).rankWithDetails();
        assertFalse(rankWithDetails.isPresent(), "Player rank with details should not be present for score 0");

        // Verify the score snapshot is NOT saved to database
        try (var tx = ebean.newTransaction()) {
            Optional<ScoreSnapshot> dbSnapshot = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);
            assertFalse(dbSnapshot.isPresent(), "Score snapshot with score 0 should not be present in database");

            // Verify database rank is not present
            Optional<Integer> dbRank = ebean.scoreSnapshotRepo().getPlayerRank(tournament, account1, tx);
            assertFalse(dbRank.isPresent(), "Database rank should not be present for player with score 0");
        }

        // Verify that only non-zero scores appear in leaderboard
        List<TournamentPlayerRankWithDetails> topScores = spyLeaderboardService.getScoresRange(tournament, 1, 10, true);
        assertEquals(2, topScores.size(), "Only 2 players with non-zero scores should be in top scores");

        // Verify that zero score account is not in top scores
        boolean zeroScoreAccountFound = topScores.stream()
                .anyMatch(entry -> entry.getRemoteAccountId().equals(account1.getRemoteId()));

        assertFalse(zeroScoreAccountFound, "Zero score account should not be present in top scores");

        // Verify the leaderboard only contains the expected non-zero score players
        List<Long> expectedAccountIds = List.of(account2.getRemoteId(), account3.getRemoteId());
        List<Long> actualAccountIds = topScores.stream()
                .map(TournamentPlayerRankWithDetails::getRemoteAccountId)
                .toList();

        assertTrue(actualAccountIds.containsAll(expectedAccountIds),
                "Top scores should contain only non-zero score accounts");

        // Verify ranks are properly assigned to remaining players (account2 should be rank 1, account3 rank 2)
        TournamentPlayerRankWithDetails account2Entry = topScores.stream()
                .filter(entry -> entry.getRemoteAccountId().equals(account2.getRemoteId()))
                .findFirst()
                .orElse(null);

        TournamentPlayerRankWithDetails account3Entry = topScores.stream()
                .filter(entry -> entry.getRemoteAccountId().equals(account3.getRemoteId()))
                .findFirst()
                .orElse(null);

        assertNotNull(account2Entry, "Account2 should be in top scores");
        assertNotNull(account3Entry, "Account3 should be in top scores");

        assertEquals(1L, account2Entry.getRank().longValue(), "Account2 should have rank 1");
        assertEquals(2L, account3Entry.getRank().longValue(), "Account3 should have rank 2");
        assertEquals(100.0, account2Entry.getScoreDetails().getScore(), 0.001, "Account2 score should be 100.0");
        assertEquals(50.0, account3Entry.getScoreDetails().getScore(), 0.001, "Account3 score should be 50.0");
    }

    @Test
    @DisplayName("Should handle cache circuit breaker opening and auto-recovery")
    void shouldHandleCacheCircuitBreakerOpeningAndAutoRecovery() throws Throwable {
        // Given - Save a score snapshot initially
        TScoreSnapshot scoreSnapshot = createScoreSnapshot(150.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, scoreSnapshot);

        // Verify initial state - cache should be working
        assertTrue(cacheChecker.cacheCircuitBreakerClosed(), "Circuit breaker should be initially closed");
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should be initially in sync");

        // When - Simulate cache going out of sync
        cacheChecker.setCacheOutOfSyncWithDb();

        // Then - Circuit breaker should open and cache should be marked as out of sync
        assertFalse(cacheChecker.cacheCircuitBreakerClosed(), "Circuit breaker should be open after cache out of sync");
        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should be marked as out of sync");

        // Verify that operations fall back to database
        Optional<TScoreSnapshot> retrievedSnapshot = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(retrievedSnapshot.isPresent(), "Should still retrieve snapshot from database");

        // When - Mark cache as back in sync (simulating recovery)
        cacheRecoveryService.checkAndRecoverCache();

        // Then - Circuit breaker should close and cache should be back in sync
        assertTrue(cacheChecker.cacheCircuitBreakerClosed(), "Circuit breaker should be closed after recovery");
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should be back in sync after recovery");
    }

    @Test
    @DisplayName("Should handle cache sync status transitions correctly")
    void shouldHandleCacheSyncStatusTransitionsCorrectly() {
        // Given - Initial state
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should start in sync");

        // When - Mark cache as out of sync
        cacheChecker.setCacheOutOfSyncWithDb();

        // Then - Cache should be out of sync
        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should be out of sync");

        // When - Mark cache as back in sync
        cacheRecoveryService.checkAndRecoverCache();

        // Then - Cache should be back in sync
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should be back in sync");

        // When - Mark cache as out of sync again
        cacheChecker.setCacheOutOfSyncWithDb();

        // Then - Cache should be out of sync again
        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should be out of sync again");
    }

    @Test
    @DisplayName("Should fall back to database when DB fallback is disabled")
    void shouldFallBackToDatabaseWhenDbFallbackDisabled() throws Throwable {
        // Given - Create a new cache checker with DB fallback disabled
        LeaderboardRedisCacheChecker disabledFallbackChecker = new LeaderboardRedisCacheChecker(props,
                redisClientFactoryBean);
        LeaderboardRedisCacheChecker spyDisabledFallbackChecker = Mockito.spy(disabledFallbackChecker);
        Mockito.doReturn(false).when(spyDisabledFallbackChecker).dbFallbackEnabled();

        TournamentLeaderboardService serviceWithDisabledFallback = new TournamentLeaderboardService(
                redisClientFactoryBean, ebean, spyDisabledFallbackChecker, props, leaderboardServiceLocalCache);

        // Save a score snapshot
        TScoreSnapshot scoreSnapshot = createScoreSnapshot(150.0);
        serviceWithDisabledFallback.upsertScoreSnapshot(tournament, account1, scoreSnapshot);

        // When - DB fallback is disabled, cache should always be considered in sync
        assertTrue(spyDisabledFallbackChecker.isInSyncWithDb(),
                "Cache should be considered in sync when DB fallback is disabled");

        // Even if we mark cache as out of sync, it should still be considered in sync
        spyDisabledFallbackChecker.setCacheOutOfSyncWithDb();
        assertTrue(spyDisabledFallbackChecker.isInSyncWithDb(),
                "Cache should still be considered in sync when DB fallback is disabled");

        // Verify that operations still work (using cache)
        Optional<TScoreSnapshot> retrievedSnapshot = serviceWithDisabledFallback.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(retrievedSnapshot.isPresent(), "Should retrieve snapshot when DB fallback is disabled");
    }

    @Test
    @DisplayName("Should handle multiple cache failure types gracefully")
    void shouldHandleMultipleCacheFailureTypesGracefully() throws Throwable {
        // Given - Save a score snapshot initially
        TScoreSnapshot scoreSnapshot = createScoreSnapshot(150.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, scoreSnapshot);

        // Test 1: Redis connection timeout
        Mockito.doThrow(new java.util.concurrent.TimeoutException("Redis timeout"))
                .when(spyLeaderboardService)
                .getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());

        Optional<TScoreSnapshot> retrieved1 = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(retrieved1.isPresent(), "Should handle Redis timeout and fall back to DB");

        // Reset mock for next test
        Mockito.reset(spyLeaderboardService);
        spyLeaderboardService = Mockito.spy(
                new TournamentLeaderboardService(redisClientFactoryBean, ebean, cacheChecker, props, leaderboardServiceLocalCache));

        // Test 2: Redis connection refused
        Mockito.doThrow(new java.net.ConnectException("Connection refused"))
                .when(spyLeaderboardService)
                .getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());

        Optional<TScoreSnapshot> retrieved2 = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(retrieved2.isPresent(), "Should handle connection refused and fall back to DB");

        // Reset mock for next test
        Mockito.reset(spyLeaderboardService);
        spyLeaderboardService = Mockito.spy(
                new TournamentLeaderboardService(redisClientFactoryBean, ebean, cacheChecker, props, leaderboardServiceLocalCache));

        // Test 3: Redis serialization error
        Mockito.doThrow(new com.fasterxml.jackson.core.JsonProcessingException("Serialization error") {}).when(spyLeaderboardService)
                .getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());

        Optional<TScoreSnapshot> retrieved3 = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(retrieved3.isPresent(), "Should handle serialization error and fall back to DB");
    }

    @Test
    @DisplayName("Should handle cache out of sync detection during operations")
    void shouldHandleCacheOutOfSyncDetectionDuringOperations() throws Throwable {
        // Given - Save a score snapshot and ensure cache is in sync
        TScoreSnapshot scoreSnapshot = createScoreSnapshot(150.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, scoreSnapshot);
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should be in sync initially");

        // When - Simulate cache becoming out of sync during operation
        // This could happen if cache data is corrupted or inconsistent
        Mockito.doAnswer(invocation -> {
            // Mark cache as out of sync when trying to read from it
            cacheChecker.setCacheOutOfSyncWithDb();
            throw new RuntimeException("Cache data inconsistent");
        }).when(spyLeaderboardService).getScoreSnapshotFromCache(Mockito.anyLong(), Mockito.anyLong());

        // Then - Operation should detect the issue and fall back to DB
        Optional<TScoreSnapshot> retrieved = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(retrieved.isPresent(), "Should retrieve from DB after cache sync issue detected");
        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should be marked as out of sync");

        // Verify that subsequent operations use DB fallback
        Mockito.verify(spyLeaderboardService).getScoreSnapshotFromDb(Mockito.eq(tournament), Mockito.eq(account1));
    }

    @Test
    @DisplayName("Should handle concurrent cache operations during sync status changes")
    void shouldHandleConcurrentCacheOperationsDuringSyncStatusChanges() throws Throwable {
        // Given - Save multiple score snapshots
        TScoreSnapshot snapshot1 = createScoreSnapshot(150.0);
        TScoreSnapshot snapshot2 = createScoreSnapshot(200.0);
        TScoreSnapshot snapshot3 = createScoreSnapshot(100.0);

        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, snapshot1);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, snapshot2);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, snapshot3);

        // When - Simulate concurrent operations with sync status changes
        // Thread 1: Mark cache as out of sync
        cacheChecker.setCacheOutOfSyncWithDb();

        // Thread 2: Try to read data (should fall back to DB)
        Optional<TScoreSnapshot> retrieved1 = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(retrieved1.isPresent(), "Should retrieve data despite sync status change");

        // Thread 3: Mark cache as back in sync
        cacheRecoveryService.checkAndRecoverCache();

        // Thread 4: Try to read data again (should use cache if available)
        Optional<TScoreSnapshot> retrieved2 = spyLeaderboardService.getScoreSnapshot(tournament, account2)
                .scoreSnapshot();
        assertTrue(retrieved2.isPresent(), "Should retrieve data after sync status restored");

        // Verify that leaderboard operations still work correctly
        List<TournamentPlayerRankWithDetails> topScores = spyLeaderboardService.getScoresRange(tournament, 1, 10, true);
        assertEquals(3, topScores.size(), "Should return all scores despite sync status changes");

        // Verify correct ranking
        assertEquals(account2.getRemoteId(), topScores.get(0).getRemoteAccountId(), "Account2 should be rank 1");
        assertEquals(account1.getRemoteId(), topScores.get(1).getRemoteAccountId(), "Account1 should be rank 2");
        assertEquals(account3.getRemoteId(), topScores.get(2).getRemoteAccountId(), "Account3 should be rank 3");
    }

    @Test
    @DisplayName("Should handle cache recovery simulation with partial failures")
    void shouldHandleCacheRecoverySimulationWithPartialFailures() throws Throwable {
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(150.0));
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should be in sync after first snapshot");

        Mockito.doReturn(Optional.empty())
                .when(spyLeaderboardService)
                .saveScoreSnapshotToCache(Mockito.eq(tournament), Mockito.eq(account2), Mockito.any());
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));
        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should be out of sync after second snapshot");

        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(100.0));
        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should be out of sync after third snapshot");

        // Try to recover by reading all data (this would trigger fallback to DB)
        Optional<TScoreSnapshot> retrieved1 = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        Optional<TScoreSnapshot> retrieved2 = spyLeaderboardService.getScoreSnapshot(tournament, account2)
                .scoreSnapshot();
        Optional<TScoreSnapshot> retrieved3 = spyLeaderboardService.getScoreSnapshot(tournament, account3)
                .scoreSnapshot();
        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should be out of sync after getting all snapshots");

        // Then - All data should be retrievable from DB despite cache issues
        assertTrue(retrieved1.isPresent(), "Should retrieve account1 data from cache");
        assertTrue(retrieved2.isPresent(), "Should retrieve account2 data from DB despite cache save failure");
        assertTrue(retrieved3.isPresent(), "Should retrieve account3 data from DB");

        // Verify that leaderboard still works correctly
        List<TournamentPlayerRankWithDetails> topScores = spyLeaderboardService.getScoresRange(tournament, 1, 10, true);
        assertEquals(3, topScores.size(), "Should return all scores despite partial recovery failure");
        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should be out of sync after getting top scores");

        // When - Mark cache as recovered (simulating successful recovery service run)
        cacheRecoveryService.checkAndRecoverCache();

        // Then - Cache should be back in sync
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should be back in sync after recovery");

        // Try to recover by reading all data (this would trigger fallback to DB)
        Optional<TScoreSnapshot> retrieved1_2 = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        Optional<TScoreSnapshot> retrieved2_2 = spyLeaderboardService.getScoreSnapshot(tournament, account2)
                .scoreSnapshot();
        Optional<TScoreSnapshot> retrieved3_2 = spyLeaderboardService.getScoreSnapshot(tournament, account3)
                .scoreSnapshot();

        // Then - All data should be retrievable from DB despite cache issues
        assertTrue(retrieved1_2.isPresent(), "Should retrieve account1 data from cache");
        assertTrue(retrieved2_2.isPresent(), "Should retrieve account2 data from cache");
        assertTrue(retrieved3_2.isPresent(), "Should retrieve account3 data from cache");

        // Verify that leaderboard still works correctly
        List<TournamentPlayerRankWithDetails> topScores_2 = spyLeaderboardService.getScoresRange(tournament, 1, 10, true);
        assertEquals(3, topScores_2.size(), "Should return all scores from cache after recovery");
    }

    @Test
    @DisplayName("Should handle Redis sync status persistence correctly")
    void shouldHandleRedisSyncStatusPersistenceCorrectly() {
        // Given - Initial state with cache in sync
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should start in sync");

        // When - Mark cache as out of sync
        cacheChecker.setCacheOutOfSyncWithDb();

        // Then - Cache should be marked as out of sync
        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should be out of sync");

        // When
        cacheRecoveryService.checkAndRecoverCache();

        // Then - Cache should be back in sync
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should be back in sync");
    }

    @Test
    @DisplayName("Should handle cache operations when circuit breaker is open")
    void shouldHandleCacheOperationsWhenCircuitBreakerIsOpen() throws Throwable {
        // Given - Save initial data
        TScoreSnapshot scoreSnapshot = createScoreSnapshot(150.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, scoreSnapshot);

        // When - Open circuit breaker by marking cache as out of sync
        cacheChecker.setCacheOutOfSyncWithDb();
        assertFalse(cacheChecker.cacheCircuitBreakerClosed(), "Circuit breaker should be open");

        // Then - All operations should fall back to database
        Optional<TScoreSnapshot> retrieved = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(retrieved.isPresent(), "Should retrieve from DB when circuit breaker is open");

        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should not be used when circuit breaker is open");

        TScoreSnapshot newSnapshot = createScoreSnapshot(250.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, newSnapshot);

        Optional<TScoreSnapshot> retrievedAfterUpdate = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(retrievedAfterUpdate.isPresent(), "Should retrieve updated data from DB");
        assertEquals(250.0, retrievedAfterUpdate.get().getScore(), 0.001, "Should have updated score");

        // Verify leaderboard operations work
        List<TournamentPlayerRankWithDetails> topScores = spyLeaderboardService.getScoresRange(tournament, 1, 10, true);
        assertEquals(1, topScores.size(), "Should return scores from DB when circuit breaker is open");
        assertEquals(250.0, topScores.get(0).getScoreDetails().getScore(), 0.001, "Should have correct score");
    }

    @Test
    @DisplayName("Should continue inserting score snapshots when cache goes out of sync during operations")
    void shouldContinueInsertingScoreSnapshotsWhenCacheGoesOutOfSyncDuringOperations() throws Throwable {
        // Given - Cache starts in sync
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should start in sync");

        // Insert initial score snapshot successfully
        TScoreSnapshot initialSnapshot = createScoreSnapshot(100.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, initialSnapshot);

        // Verify initial snapshot was saved to both cache and DB
        Optional<TScoreSnapshot> cachedSnapshot = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(cachedSnapshot.isPresent(), "Initial snapshot should be in cache");
        assertEquals(100.0, cachedSnapshot.get().getScore(), "Initial score should match");

        // Verify it's also in the database
        try (var tx = ebean.newTransaction()) {
            Optional<ScoreSnapshot> dbSnapshot = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);
            assertTrue(dbSnapshot.isPresent(), "Initial snapshot should be in database");
            assertEquals(100.0, dbSnapshot.get().getScore().doubleValue(), "DB score should match");
        }

        // When - Simulate cache going out of sync during operations
        log.info("Simulating cache going out of sync...");
        cacheChecker.setCacheOutOfSyncWithDb();
        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should be marked as out of sync");

        // Continue inserting score snapshots after cache goes out of sync
        TScoreSnapshot updatedSnapshot1 = createScoreSnapshot(150.0);
        TScoreSnapshot updatedSnapshot2 = createScoreSnapshot(200.0);

        // These operations should continue to work despite cache being out of sync
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, updatedSnapshot1);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, updatedSnapshot2);

        // Then - Verify operations continued successfully

        // 1. Verify that data is still being saved to database
        try (var tx = ebean.newTransaction()) {
            Optional<ScoreSnapshot> dbSnapshot1 = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);
            Optional<ScoreSnapshot> dbSnapshot2 = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account2, tx);

            assertTrue(dbSnapshot1.isPresent(), "Updated snapshot for account1 should be in database");
            assertTrue(dbSnapshot2.isPresent(), "New snapshot for account2 should be in database");

            assertEquals(150.0, dbSnapshot1.get().getScore().doubleValue(),
                    "Account1 updated score should be in database");
            assertEquals(200.0, dbSnapshot2.get().getScore().doubleValue(), "Account2 score should be in database");
        }

        // 2. Verify that reads fall back to database when cache is out of sync
        Optional<TScoreSnapshot> retrievedSnapshot1 = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        Optional<TScoreSnapshot> retrievedSnapshot2 = spyLeaderboardService.getScoreSnapshot(tournament, account2)
                .scoreSnapshot();

        assertTrue(retrievedSnapshot1.isPresent(), "Should retrieve account1 snapshot from database fallback");
        assertTrue(retrievedSnapshot2.isPresent(), "Should retrieve account2 snapshot from database fallback");

        assertEquals(150.0, retrievedSnapshot1.get().getScore(), "Retrieved score should match latest update");
        assertEquals(200.0, retrievedSnapshot2.get().getScore(), "Retrieved score should match inserted value");

        // 3. Verify that leaderboard operations also work with database fallback
        List<TournamentPlayerRankWithDetails> topScores = spyLeaderboardService.getScoresRange(tournament, 1, 10, true);
        assertFalse(topScores.isEmpty(), "Should retrieve top scores from database when cache is out of sync");

        // Find our accounts in the results
        Optional<TournamentPlayerRankWithDetails> account1Rank = topScores.stream()
                .filter(rank -> rank.getRemoteAccountId().equals(account1.getRemoteId()))
                .findFirst();
        Optional<TournamentPlayerRankWithDetails> account2Rank = topScores.stream()
                .filter(rank -> rank.getRemoteAccountId().equals(account2.getRemoteId()))
                .findFirst();

        assertTrue(account1Rank.isPresent(), "Account1 should appear in top scores from database");
        assertTrue(account2Rank.isPresent(), "Account2 should appear in top scores from database");

        assertEquals(150.0, account1Rank.get().getScoreDetails().getScore(),
                "Account1 score in leaderboard should match");
        assertEquals(200.0, account2Rank.get().getScoreDetails().getScore(),
                "Account2 score in leaderboard should match");

        // Account2 should rank higher than Account1 (200 > 150)
        assertTrue(account2Rank.get().getRank() < account1Rank.get().getRank(),
                "Account2 should rank higher than Account1");

        // 4. Verify that individual rank queries also work
        Optional<Integer> account1RankValue = spyLeaderboardService.getPlayerRank(tournament, account1).rank();
        Optional<Integer> account2RankValue = spyLeaderboardService.getPlayerRank(tournament, account2).rank();

        assertTrue(account1RankValue.isPresent(), "Should get account1 rank from database");
        assertTrue(account2RankValue.isPresent(), "Should get account2 rank from database");

        assertTrue(account2RankValue.get() < account1RankValue.get(),
                "Account2 rank should be better (lower number) than Account1");

        // 5. When cache is restored to sync, operations should continue normally
        cacheRecoveryService.checkAndRecoverCache();
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should be back in sync");

        // Insert another score snapshot
        TScoreSnapshot finalSnapshot = createScoreSnapshot(250.0);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, finalSnapshot);

        // Verify the final snapshot works normally
        Optional<TScoreSnapshot> finalRetrieved = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        assertTrue(finalRetrieved.isPresent(), "Final snapshot should be retrievable");
        assertEquals(250.0, finalRetrieved.get().getScore(), "Final score should match");

        // Verify it's in both cache and database
        try (var tx = ebean.newTransaction()) {
            Optional<ScoreSnapshot> finalDbSnapshot = ebean.scoreSnapshotRepo()
                    .findByTournamentAndAccount(tournament, account1, tx);
            assertTrue(finalDbSnapshot.isPresent(), "Final snapshot should be in database");
            assertEquals(250.0, finalDbSnapshot.get().getScore().doubleValue(), "Final DB score should match");
        }
    }

    @Test
    @DisplayName("Should handle emulated cache outage scenarios with score updates before, during, and after outage")
    void shouldHandleEmulatedCacheOutageWithScoreUpdates() throws Throwable {
        // Given - Cache is initially available and functioning normally
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should start in sync");

        // Phase 1: Initial state - Cache available, scores updated successfully
        log.info("Phase 1: Testing initial cache availability and score updates");
        TScoreSnapshot initialSnapshot1 = createScoreSnapshot(100.0);
        TScoreSnapshot initialSnapshot2 = createScoreSnapshot(150.0);

        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, initialSnapshot1);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, initialSnapshot2);

        // Verify initial scores are saved and retrievable
        Optional<TScoreSnapshot> retrieved1 = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        Optional<TScoreSnapshot> retrieved2 = spyLeaderboardService.getScoreSnapshot(tournament, account2)
                .scoreSnapshot();

        assertTrue(retrieved1.isPresent(), "Initial score for account1 should be retrievable");
        assertTrue(retrieved2.isPresent(), "Initial score for account2 should be retrievable");
        assertEquals(100.0, retrieved1.get().getScore(), "Account1 initial score should match");
        assertEquals(150.0, retrieved2.get().getScore(), "Account2 initial score should match");

        // Verify cache is still in sync after initial operations
        assertTrue(cacheChecker.isInSyncWithDb(), "Cache should remain in sync after initial operations");

        // Phase 1 verification: Cache and DB should be consistent before outage
        log.info("Phase 1: Verifying cache and database consistency before outage");
        verifyCacheDbConsistency("before outage");

        // Phase 2: Configure emulated outage
        log.info("Phase 2: Configuring emulated Redis outage");
        long currentTime = System.currentTimeMillis();
        long outageStart = currentTime + 100;
        long outageEnd = outageStart + 2000;

        // Configure the emulated outage via the property
        String outageTimestampsStr = outageStart + "," + outageEnd;
        cfg.setDefaultProperty(
                props.NON_PROD_ONLY_TOURNAMENT_SCORE_CACHE_EMULATED_REDIS_OUTAGE_FOR_TEST_START_END_UNIX_TIMESTAMPS_CSV.getKey(),
                outageTimestampsStr);

        // The property change should trigger the configuration processing automatically via the subscriber
        // Wait for the property change to be processed using Awaitility
        await().atMost(Duration.ofSeconds(5)).pollInterval(Duration.ofMillis(50)).untilAsserted(() -> {
            // Verify cache is marked as out of sync due to emulated outage configuration
            assertFalse(cacheChecker.isInSyncWithDb(),
                    "Cache should be marked as out of sync after outage configuration");
        });

        // Phase 3: Wait for outage to start and test score updates during outage
        log.info("Phase 3: Waiting for outage to start and testing score updates during outage");

        // Wait for outage window to begin
        while (System.currentTimeMillis() < outageStart) {
            Thread.sleep(10);
        }

        // Verify we're now in the outage window
        assertTrue(System.currentTimeMillis() >= outageStart, "Should be in outage window");
        assertTrue(System.currentTimeMillis() <= outageEnd, "Should still be within outage window");

        // During outage: Score updates should continue to work (fallback to DB)
        TScoreSnapshot duringOutageSnapshot1 = createScoreSnapshot(200.0);
        TScoreSnapshot duringOutageSnapshot2 = createScoreSnapshot(250.0);
        TScoreSnapshot duringOutageSnapshot3 = createScoreSnapshot(175.0); // New account

        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, duringOutageSnapshot1);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, duringOutageSnapshot2);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, duringOutageSnapshot3);

        // Verify scores are still retrievable during outage (should use DB fallback)
        Optional<TScoreSnapshot> duringOutageRetrieved1 = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        Optional<TScoreSnapshot> duringOutageRetrieved2 = spyLeaderboardService.getScoreSnapshot(tournament, account2)
                .scoreSnapshot();
        Optional<TScoreSnapshot> duringOutageRetrieved3 = spyLeaderboardService.getScoreSnapshot(tournament, account3)
                .scoreSnapshot();

        assertTrue(duringOutageRetrieved1.isPresent(), "Score should be retrievable during outage via DB fallback");
        assertTrue(duringOutageRetrieved2.isPresent(), "Score should be retrievable during outage via DB fallback");
        assertTrue(duringOutageRetrieved3.isPresent(), "Score should be retrievable during outage via DB fallback");

        assertEquals(200.0, duringOutageRetrieved1.get().getScore(), "Account1 score should be updated during outage");
        assertEquals(250.0, duringOutageRetrieved2.get().getScore(), "Account2 score should be updated during outage");
        assertEquals(175.0, duringOutageRetrieved3.get().getScore(), "Account3 score should be saved during outage");

        // Phase 3 verification: Cache and DB should NOT be consistent during outage
        log.info("Phase 3: Verifying cache and database inconsistency during outage");
        verifyCacheDbInconsistency("during outage");

        // Verify cache recovery is skipped during outage window
        cacheRecoveryService.checkAndRecoverCache();
        assertFalse(cacheChecker.isInSyncWithDb(), "Cache should remain out of sync during outage window");

        // Phase 4: Wait for outage to end and verify cache recovery
        log.info("Phase 4: Waiting for outage to end and testing cache recovery");

        // Wait for outage window to end
        while (System.currentTimeMillis() <= outageEnd) {
            Thread.sleep(100);
        }

        // Verify we're now past the outage window
        assertTrue(System.currentTimeMillis() > outageEnd, "Should be past outage window");

        // Clear the outage configuration to allow recovery
        cfg.setDefaultProperty(
                props.NON_PROD_ONLY_TOURNAMENT_SCORE_CACHE_EMULATED_REDIS_OUTAGE_FOR_TEST_START_END_UNIX_TIMESTAMPS_CSV.getKey(),
                "");

        // Trigger cache recovery after outage ends
        cacheRecoveryService.checkAndRecoverCache();

        // Wait for cache recovery to complete
        await().atMost(Duration.ofSeconds(30)).pollInterval(Duration.ofMillis(100)).untilAsserted(() -> {
            assertTrue(cacheChecker.isInSyncWithDb(), "Cache should be back in sync after recovery");
        });

        // Phase 4 verification: Cache and DB should be consistent again after recovery
        log.info("Phase 4: Verifying cache and database consistency after recovery");
        verifyCacheDbConsistency("after recovery");

        // Phase 5: Test score updates after outage recovery
        log.info("Phase 5: Testing score updates after outage recovery");

        TScoreSnapshot postOutageSnapshot1 = createScoreSnapshot(300.0);
        TScoreSnapshot postOutageSnapshot2 = createScoreSnapshot(275.0);
        TScoreSnapshot postOutageSnapshot4 = createScoreSnapshot(225.0); // New account

        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, postOutageSnapshot1);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, postOutageSnapshot2);
        spyLeaderboardService.upsertScoreSnapshot(tournament, account4, postOutageSnapshot4);

        // Verify all scores are retrievable after recovery
        Optional<TScoreSnapshot> postOutageRetrieved1 = spyLeaderboardService.getScoreSnapshot(tournament, account1)
                .scoreSnapshot();
        Optional<TScoreSnapshot> postOutageRetrieved2 = spyLeaderboardService.getScoreSnapshot(tournament, account2)
                .scoreSnapshot();
        Optional<TScoreSnapshot> postOutageRetrieved4 = spyLeaderboardService.getScoreSnapshot(tournament, account4)
                .scoreSnapshot();

        assertTrue(postOutageRetrieved1.isPresent(), "Score should be retrievable after outage recovery");
        assertTrue(postOutageRetrieved2.isPresent(), "Score should be retrievable after outage recovery");
        assertTrue(postOutageRetrieved4.isPresent(), "Score should be retrievable after outage recovery");

        assertEquals(300.0, postOutageRetrieved1.get().getScore(), "Account1 score should be updated after recovery");
        assertEquals(275.0, postOutageRetrieved2.get().getScore(), "Account2 score should be updated after recovery");
        assertEquals(225.0, postOutageRetrieved4.get().getScore(), "Account4 score should be saved after recovery");

        // Final verification of leaderboard functionality
        List<TournamentPlayerRankWithDetails> topScores = spyLeaderboardService.getScoresRange(tournament, 1, 5, true);
        assertFalse(topScores.isEmpty(), "Top scores should be available after full recovery");

        // Verify rankings are correct (sorted by score descending)
        assertEquals(300.0, topScores.get(0).getScoreDetails().getScore(), "Top score should be 300.0");
        assertEquals(275.0, topScores.get(1).getScoreDetails().getScore(), "Second score should be 275.0");

        // Final cache vs database consistency check
        verifyCacheDbConsistency("final verification");

        log.info("Successfully completed emulated cache outage test with score updates");
    }

    private void verifyCacheDbConsistency(String phase) throws Exception {
        log.info("Verifying cache and database consistency - {}", phase);

        // Compare score snapshots for all test accounts
        verifyAccountConsistency(account1, "Account1", true);
        verifyAccountConsistency(account2, "Account2", true);
        verifyAccountConsistency(account3, "Account3", true);
        verifyAccountConsistency(account4, "Account4", true);

        // Compare top scores from cache vs database
        List<TournamentPlayerRankWithDetails> cacheTopScores = spyLeaderboardService.getScoresRangeFromCacheZeroBased(
                tournament.getId(), 0, 9);
        List<TournamentPlayerRankWithDetails> dbTopScores = spyLeaderboardService.getScoresRangeFromDbZeroBased(tournament, 0,
                9);

        assertEquals(cacheTopScores.size(), dbTopScores.size(),
                "Number of top scores should match between cache and database during " + phase);

        for (int i = 0; i < cacheTopScores.size(); i++) {
            TournamentPlayerRankWithDetails cacheEntry = cacheTopScores.get(i);
            TournamentPlayerRankWithDetails dbEntry = dbTopScores.get(i);

            assertEquals(cacheEntry.getRemoteAccountId(), dbEntry.getRemoteAccountId(),
                    "Account ID should match at rank " + (i + 1) + " during " + phase);
            assertEquals(cacheEntry.getRank(), dbEntry.getRank(),
                    "Rank should match for account " + cacheEntry.getRemoteAccountId() + " during " + phase);
            assertEquals(cacheEntry.getScoreDetails().getScore(), dbEntry.getScoreDetails().getScore(), 0.001,
                    "Score should match for account " + cacheEntry.getRemoteAccountId() + " during " + phase);
        }

        log.info("Cache and database consistency verification completed successfully - {}", phase);
    }

    private void verifyCacheDbInconsistency(String phase) {
        log.info("Verifying cache and database inconsistency - {}", phase);

        boolean foundInconsistency = false;
        String inconsistencyDetails = "";

        try {
            // Check if any account shows inconsistency between cache and DB
            foundInconsistency |= verifyAccountInconsistency(account1, "Account1");
            foundInconsistency |= verifyAccountInconsistency(account2, "Account2");
            foundInconsistency |= verifyAccountInconsistency(account3, "Account3");

            // Try to compare top scores from cache vs database
            List<TournamentPlayerRankWithDetails> cacheTopScores = spyLeaderboardService.getScoresRangeFromCacheZeroBased(
                    tournament.getId(), 0, 9);
            List<TournamentPlayerRankWithDetails> dbTopScores = spyLeaderboardService.getScoresRangeFromDbZeroBased(tournament, 1,
                    10);

            if (cacheTopScores.size() != dbTopScores.size()) {
                foundInconsistency = true;
                inconsistencyDetails += String.format("Top scores count differs: cache=%d, db=%d; ",
                        cacheTopScores.size(), dbTopScores.size());
            }

            // Check if any scores or rankings differ
            int minSize = Math.min(cacheTopScores.size(), dbTopScores.size());
            for (int i = 0; i < minSize; i++) {
                TournamentPlayerRankWithDetails cacheEntry = cacheTopScores.get(i);
                TournamentPlayerRankWithDetails dbEntry = dbTopScores.get(i);

                if (!cacheEntry.getRemoteAccountId().equals(dbEntry.getRemoteAccountId()) || !cacheEntry.getRank()
                        .equals(dbEntry.getRank())
                        || Math.abs(
                                cacheEntry.getScoreDetails().getScore() - dbEntry.getScoreDetails().getScore()) > 0.001) {
                    foundInconsistency = true;
                    inconsistencyDetails += String.format("Rank %d differs: cache=%s(score=%.3f), db=%s(score=%.3f); ",
                            i + 1, cacheEntry.getRemoteAccountId(), cacheEntry.getScoreDetails().getScore(),
                            dbEntry.getRemoteAccountId(), dbEntry.getScoreDetails().getScore());
                }
            }

        } catch (Exception e) {
            // Cache access failures during outage are expected and indicate inconsistency
            foundInconsistency = true;
            inconsistencyDetails += "Cache access failed (expected during outage): " + e.getMessage();
            log.info("Cache access failed as expected during outage: {}", e.getMessage());
        }

        assertTrue(foundInconsistency,
                "Cache and database should be inconsistent " + phase + ". Details: " + inconsistencyDetails);

        log.info("Cache and database inconsistency verified successfully - {}: {}", phase, inconsistencyDetails);
    }

    private boolean verifyAccountInconsistency(TournamentAccount account, String accountName) {
        try {
            Optional<TScoreSnapshot> cacheSnapshot = spyLeaderboardService.getScoreSnapshotFromCache(tournament.getId(),
                    account.getRemoteId());
            Optional<TScoreSnapshot> dbSnapshot = spyLeaderboardService.getScoreSnapshotFromDb(tournament, account);

            // If both exist, check if they differ
            if (cacheSnapshot.isPresent() && dbSnapshot.isPresent()) {
                TScoreSnapshot cacheData = cacheSnapshot.get();
                TScoreSnapshot dbData = dbSnapshot.get();

                boolean inconsistent = hasScoreSnapshotDifferences(cacheData, dbData, accountName);

                if (inconsistent) {
                    log.info("{} shows inconsistency between cache and database", accountName);
                    return true;
                }
            }

            // If one exists but not the other, that's also inconsistency
            if (cacheSnapshot.isPresent() != dbSnapshot.isPresent()) {
                log.info("{} shows inconsistency: cache present={}, db present={}", accountName,
                        cacheSnapshot.isPresent(), dbSnapshot.isPresent());
                return true;
            }

            return false;

        } catch (Exception e) {
            // Cache access failures indicate inconsistency
            log.info("{} cache access failed (indicating inconsistency): {}", accountName, e.getMessage());
            return true;
        }
    }

    private void verifyAccountConsistency(TournamentAccount account, String accountName, boolean shouldMatch) throws Exception {
        Optional<TScoreSnapshot> cacheSnapshot = spyLeaderboardService.getScoreSnapshotFromCache(tournament.getId(),
                account.getRemoteId());
        Optional<TScoreSnapshot> dbSnapshot = spyLeaderboardService.getScoreSnapshotFromDb(tournament, account);

        if (dbSnapshot.isPresent()) {
            assertTrue(cacheSnapshot.isPresent(),
                    accountName + " score snapshot should exist in cache if it exists in database");

            TScoreSnapshot cacheData = cacheSnapshot.get();
            TScoreSnapshot dbData = dbSnapshot.get();

            // Compare ALL fields from TScoreSnapshot
            compareScoreSnapshots(cacheData, dbData, accountName);

            // Compare player ranks
            Optional<Integer> cacheRank = spyLeaderboardService.getPlayerRankFromCache(tournament.getId(),
                    account.getRemoteId());
            Optional<Integer> dbRank = spyLeaderboardService.getPlayerRankFromDb(tournament, account);

            assertEquals(cacheRank, dbRank, accountName + " player rank should match between cache and database");
        } else {
            assertFalse(cacheSnapshot.isPresent(),
                    accountName + " should not exist in cache if it doesn't exist in database");
        }
    }

    /**
     * Compares all fields of two TScoreSnapshot objects and asserts they are equal.
     * Used for consistency verification where we expect the objects to match exactly.
     */
    private void compareScoreSnapshots(TScoreSnapshot cacheData, TScoreSnapshot dbData, String context) {
        // Required fields (always present)
        assertEquals(cacheData.getScore(), dbData.getScore(), 0.001,
                context + " score should match between cache and database");
        assertEquals(cacheData.getTotalDebit(), dbData.getTotalDebit(), 0.001,
                context + " totalDebit should match between cache and database");
        assertEquals(cacheData.getTimestamp(), dbData.getTimestamp(),
                context + " timestamp should match between cache and database");

        // Optional fields - compare based on presence
        assertEquals(cacheData.hasTotalCredit(), dbData.hasTotalCredit(),
                context + " totalCredit presence should match between cache and database");
        if (cacheData.hasTotalCredit() && dbData.hasTotalCredit()) {
            assertEquals(cacheData.getTotalCredit(), dbData.getTotalCredit(), 0.001,
                    context + " totalCredit value should match between cache and database");
        }

        assertEquals(cacheData.hasHighestMultiplier(), dbData.hasHighestMultiplier(),
                context + " highestMultiplier presence should match between cache and database");
        if (cacheData.hasHighestMultiplier() && dbData.hasHighestMultiplier()) {
            assertEquals(cacheData.getHighestMultiplier(), dbData.getHighestMultiplier(), 0.001,
                    context + " highestMultiplier value should match between cache and database");
        }

        assertEquals(cacheData.hasTotalMultiplier(), dbData.hasTotalMultiplier(),
                context + " totalMultiplier presence should match between cache and database");
        if (cacheData.hasTotalMultiplier() && dbData.hasTotalMultiplier()) {
            assertEquals(cacheData.getTotalMultiplier(), dbData.getTotalMultiplier(), 0.001,
                    context + " totalMultiplier value should match between cache and database");
        }

        assertEquals(cacheData.hasWinsInRow3(), dbData.hasWinsInRow3(),
                context + " winsInRow3 presence should match between cache and database");
        if (cacheData.hasWinsInRow3() && dbData.hasWinsInRow3()) {
            assertEquals(cacheData.getWinsInRow3(), dbData.getWinsInRow3(),
                    context + " winsInRow3 value should match between cache and database");
        }

        assertEquals(cacheData.hasWinsInRow4(), dbData.hasWinsInRow4(),
                context + " winsInRow4 presence should match between cache and database");
        if (cacheData.hasWinsInRow4() && dbData.hasWinsInRow4()) {
            assertEquals(cacheData.getWinsInRow4(), dbData.getWinsInRow4(),
                    context + " winsInRow4 value should match between cache and database");
        }

        assertEquals(cacheData.hasWinsInRow5(), dbData.hasWinsInRow5(),
                context + " winsInRow5 presence should match between cache and database");
        if (cacheData.hasWinsInRow5() && dbData.hasWinsInRow5()) {
            assertEquals(cacheData.getWinsInRow5(), dbData.getWinsInRow5(),
                    context + " winsInRow5 value should match between cache and database");
        }

        assertEquals(cacheData.hasLossesInRow3(), dbData.hasLossesInRow3(),
                context + " lossesInRow3 presence should match between cache and database");
        if (cacheData.hasLossesInRow3() && dbData.hasLossesInRow3()) {
            assertEquals(cacheData.getLossesInRow3(), dbData.getLossesInRow3(),
                    context + " lossesInRow3 value should match between cache and database");
        }

        assertEquals(cacheData.hasLossesInRow5(), dbData.hasLossesInRow5(),
                context + " lossesInRow5 presence should match between cache and database");
        if (cacheData.hasLossesInRow5() && dbData.hasLossesInRow5()) {
            assertEquals(cacheData.getLossesInRow5(), dbData.getLossesInRow5(),
                    context + " lossesInRow5 value should match between cache and database");
        }

        assertEquals(cacheData.hasLossesInRow7(), dbData.hasLossesInRow7(),
                context + " lossesInRow7 presence should match between cache and database");
        if (cacheData.hasLossesInRow7() && dbData.hasLossesInRow7()) {
            assertEquals(cacheData.getLossesInRow7(), dbData.getLossesInRow7(),
                    context + " lossesInRow7 value should match between cache and database");
        }

        assertEquals(cacheData.hasLossesInRow11(), dbData.hasLossesInRow11(),
                context + " lossesInRow11 presence should match between cache and database");
        if (cacheData.hasLossesInRow11() && dbData.hasLossesInRow11()) {
            assertEquals(cacheData.getLossesInRow11(), dbData.getLossesInRow11(),
                    context + " lossesInRow11 value should match between cache and database");
        }
    }

    /**
     * Checks if two TScoreSnapshot objects have any differences.
     * Used for inconsistency detection where we want to identify if objects differ.
     * Returns true if differences are found, false if they are identical.
     */
    private boolean hasScoreSnapshotDifferences(TScoreSnapshot cacheData, TScoreSnapshot dbData, String context) {
        StringBuilder differences = new StringBuilder();

        // Required fields (always present)
        if (Math.abs(cacheData.getScore() - dbData.getScore()) > 0.001) {
            differences.append(String.format("score(cache=%.3f, db=%.3f); ", cacheData.getScore(), dbData.getScore()));
        }

        if (Math.abs(cacheData.getTotalDebit() - dbData.getTotalDebit()) > 0.001) {
            differences.append(String.format("totalDebit(cache=%.3f, db=%.3f); ", cacheData.getTotalDebit(),
                    dbData.getTotalDebit()));
        }

        if (cacheData.getTimestamp() != dbData.getTimestamp()) {
            differences.append(
                    String.format("timestamp(cache=%d, db=%d); ", cacheData.getTimestamp(), dbData.getTimestamp()));
        }

        // Optional fields - check presence and values
        if (cacheData.hasTotalCredit() != dbData.hasTotalCredit()) {
            differences.append(String.format("totalCredit presence(cache=%s, db=%s); ", cacheData.hasTotalCredit(),
                    dbData.hasTotalCredit()));
        } else if (cacheData.hasTotalCredit() && dbData.hasTotalCredit() && Math.abs(
                cacheData.getTotalCredit() - dbData.getTotalCredit()) > 0.001) {
                    differences.append(String.format("totalCredit(cache=%.3f, db=%.3f); ", cacheData.getTotalCredit(),
                            dbData.getTotalCredit()));
                }

        if (cacheData.hasHighestMultiplier() != dbData.hasHighestMultiplier()) {
            differences.append(
                    String.format("highestMultiplier presence(cache=%s, db=%s); ", cacheData.hasHighestMultiplier(),
                            dbData.hasHighestMultiplier()));
        } else if (cacheData.hasHighestMultiplier() && dbData.hasHighestMultiplier() && Math.abs(
                cacheData.getHighestMultiplier() - dbData.getHighestMultiplier()) > 0.001) {
                    differences.append(
                            String.format("highestMultiplier(cache=%.3f, db=%.3f); ", cacheData.getHighestMultiplier(),
                                    dbData.getHighestMultiplier()));
                }

        if (cacheData.hasTotalMultiplier() != dbData.hasTotalMultiplier()) {
            differences.append(
                    String.format("totalMultiplier presence(cache=%s, db=%s); ", cacheData.hasTotalMultiplier(),
                            dbData.hasTotalMultiplier()));
        } else if (cacheData.hasTotalMultiplier() && dbData.hasTotalMultiplier() && Math.abs(
                cacheData.getTotalMultiplier() - dbData.getTotalMultiplier()) > 0.001) {
                    differences.append(String.format("totalMultiplier(cache=%.3f, db=%.3f); ", cacheData.getTotalMultiplier(),
                            dbData.getTotalMultiplier()));
                }

        if (cacheData.hasWinsInRow3() != dbData.hasWinsInRow3()) {
            differences.append(String.format("winsInRow3 presence(cache=%s, db=%s); ", cacheData.hasWinsInRow3(),
                    dbData.hasWinsInRow3()));
        } else if (cacheData.hasWinsInRow3() && dbData.hasWinsInRow3() && cacheData.getWinsInRow3() != dbData.getWinsInRow3()) {
            differences.append(
                    String.format("winsInRow3(cache=%d, db=%d); ", cacheData.getWinsInRow3(), dbData.getWinsInRow3()));
        }

        if (cacheData.hasWinsInRow4() != dbData.hasWinsInRow4()) {
            differences.append(String.format("winsInRow4 presence(cache=%s, db=%s); ", cacheData.hasWinsInRow4(),
                    dbData.hasWinsInRow4()));
        } else if (cacheData.hasWinsInRow4() && dbData.hasWinsInRow4() && cacheData.getWinsInRow4() != dbData.getWinsInRow4()) {
            differences.append(
                    String.format("winsInRow4(cache=%d, db=%d); ", cacheData.getWinsInRow4(), dbData.getWinsInRow4()));
        }

        if (cacheData.hasWinsInRow5() != dbData.hasWinsInRow5()) {
            differences.append(String.format("winsInRow5 presence(cache=%s, db=%s); ", cacheData.hasWinsInRow5(),
                    dbData.hasWinsInRow5()));
        } else if (cacheData.hasWinsInRow5() && dbData.hasWinsInRow5() && cacheData.getWinsInRow5() != dbData.getWinsInRow5()) {
            differences.append(
                    String.format("winsInRow5(cache=%d, db=%d); ", cacheData.getWinsInRow5(), dbData.getWinsInRow5()));
        }

        if (cacheData.hasLossesInRow3() != dbData.hasLossesInRow3()) {
            differences.append(String.format("lossesInRow3 presence(cache=%s, db=%s); ", cacheData.hasLossesInRow3(),
                    dbData.hasLossesInRow3()));
        } else if (cacheData.hasLossesInRow3() && dbData.hasLossesInRow3() && cacheData.getLossesInRow3() != dbData.getLossesInRow3()) {
            differences.append(String.format("lossesInRow3(cache=%d, db=%d); ", cacheData.getLossesInRow3(),
                    dbData.getLossesInRow3()));
        }

        if (cacheData.hasLossesInRow5() != dbData.hasLossesInRow5()) {
            differences.append(String.format("lossesInRow5 presence(cache=%s, db=%s); ", cacheData.hasLossesInRow5(),
                    dbData.hasLossesInRow5()));
        } else if (cacheData.hasLossesInRow5() && dbData.hasLossesInRow5() && cacheData.getLossesInRow5() != dbData.getLossesInRow5()) {
            differences.append(String.format("lossesInRow5(cache=%d, db=%d); ", cacheData.getLossesInRow5(),
                    dbData.getLossesInRow5()));
        }

        if (cacheData.hasLossesInRow7() != dbData.hasLossesInRow7()) {
            differences.append(String.format("lossesInRow7 presence(cache=%s, db=%s); ", cacheData.hasLossesInRow7(),
                    dbData.hasLossesInRow7()));
        } else if (cacheData.hasLossesInRow7() && dbData.hasLossesInRow7() && cacheData.getLossesInRow7() != dbData.getLossesInRow7()) {
            differences.append(String.format("lossesInRow7(cache=%d, db=%d); ", cacheData.getLossesInRow7(),
                    dbData.getLossesInRow7()));
        }

        if (cacheData.hasLossesInRow11() != dbData.hasLossesInRow11()) {
            differences.append(String.format("lossesInRow11 presence(cache=%s, db=%s); ", cacheData.hasLossesInRow11(),
                    dbData.hasLossesInRow11()));
        } else if (cacheData.hasLossesInRow11() && dbData.hasLossesInRow11() && cacheData.getLossesInRow11() != dbData.getLossesInRow11()) {
            differences.append(String.format("lossesInRow11(cache=%d, db=%d); ", cacheData.getLossesInRow11(),
                    dbData.getLossesInRow11()));
        }

        boolean hasDifferences = differences.length() > 0;
        if (hasDifferences) {
            log.info("{} TScoreSnapshot differences: {}", context, differences.toString());
        }

        return hasDifferences;
    }

    @Test
    @DisplayName("Should return scores within specific range correctly")
    void shouldReturnScoresWithinSpecificRange() throws Throwable {
        // Given - Create 6 accounts with different scores
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0)); // Rank 6
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0)); // Rank 5
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(300.0)); // Rank 4
        spyLeaderboardService.upsertScoreSnapshot(tournament, account4, createScoreSnapshot(400.0)); // Rank 3

        TournamentAccount account5 = createTestAccount();
        TournamentAccount account6 = createTestAccount();
        spyLeaderboardService.upsertScoreSnapshot(tournament, account5, createScoreSnapshot(500.0)); // Rank 2
        spyLeaderboardService.upsertScoreSnapshot(tournament, account6, createScoreSnapshot(600.0)); // Rank 1

        // When - Get scores from rank 2 to 4 (startingRank=2, limit=3)
        List<TournamentPlayerRankWithDetails> rangeEntries = spyLeaderboardService.getScoresRange(tournament, 2, 3, true);

        // Then
        assertEquals(3, rangeEntries.size(), "Should return exactly 3 entries");

        // Verify scores in descending order (ranks 2, 3, 4)
        assertEquals(500.0, rangeEntries.get(0).getScoreDetails().getScore(), "First entry should have score 500");
        assertEquals(400.0, rangeEntries.get(1).getScoreDetails().getScore(), "Second entry should have score 400");
        assertEquals(300.0, rangeEntries.get(2).getScoreDetails().getScore(), "Third entry should have score 300");

        // Verify ranks
        assertEquals(2L, rangeEntries.get(0).getRank(), "First entry's rank should be 2");
        assertEquals(3L, rangeEntries.get(1).getRank(), "Second entry's rank should be 3");
        assertEquals(4L, rangeEntries.get(2).getRank(), "Third entry's rank should be 4");

        // Verify account IDs
        assertEquals(account5.getRemoteId(), rangeEntries.get(0).getRemoteAccountId(),
                "First entry should be account5");
        assertEquals(account4.getRemoteId(), rangeEntries.get(1).getRemoteAccountId(),
                "Second entry should be account4");
        assertEquals(account3.getRemoteId(), rangeEntries.get(2).getRemoteAccountId(),
                "Third entry should be account3");
    }

    @Test
    @DisplayName("Should return single entry when range limit is 1")
    void shouldReturnSingleEntryWhenRangeLimitIsOne() throws Throwable {
        // Given
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(300.0));

        // When - Get only the second-ranked entry (startingRank=2, limit=1)
        List<TournamentPlayerRankWithDetails> rangeEntries = spyLeaderboardService.getScoresRange(tournament, 2, 1, true);

        // Then
        assertEquals(1, rangeEntries.size(), "Should return exactly 1 entry");
        assertEquals(200.0, rangeEntries.get(0).getScoreDetails().getScore(), "Entry should have score 200");
        assertEquals(2L, rangeEntries.get(0).getRank(), "Entry's rank should be 2");
        assertEquals(account2.getRemoteId(), rangeEntries.get(0).getRemoteAccountId(), "Entry should be account2");
    }

    @Test
    @DisplayName("Should return empty list when starting rank exceeds available entries")
    void shouldReturnEmptyListWhenStartingRankExceedsAvailableEntries() throws Throwable {
        // Given - Only 3 accounts
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(300.0));

        // When - Try to get entries starting from rank 5 (startingRank=5, limit=2)
        List<TournamentPlayerRankWithDetails> rangeEntries = spyLeaderboardService.getScoresRange(tournament, 5, 2, true);

        // Then
        assertTrue(rangeEntries.isEmpty(), "Should return empty list when starting rank exceeds available entries");
    }

    @Test
    @DisplayName("Should return partial results when range exceeds available entries")
    void shouldReturnPartialResultsWhenRangeExceedsAvailableEntries() throws Throwable {
        // Given - Only 4 accounts
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(300.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account4, createScoreSnapshot(400.0));

        // When - Try to get 3 entries starting from rank 3 (only 2 entries available: ranks 3 and 4)
        List<TournamentPlayerRankWithDetails> rangeEntries = spyLeaderboardService.getScoresRange(tournament, 3, 3, true);

        // Then
        assertEquals(2, rangeEntries.size(), "Should return only available entries (2 instead of requested 3)");
        assertEquals(200.0, rangeEntries.get(0).getScoreDetails().getScore(), "First entry should have score 200");
        assertEquals(100.0, rangeEntries.get(1).getScoreDetails().getScore(), "Second entry should have score 100");
        assertEquals(3L, rangeEntries.get(0).getRank(), "First entry's rank should be 3");
        assertEquals(4L, rangeEntries.get(1).getRank(), "Second entry's rank should be 4");
    }

    @Test
    @DisplayName("Should handle range at the beginning of leaderboard")
    void shouldHandleRangeAtBeginningOfLeaderboard() throws Throwable {
        // Given
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(300.0));

        // When - Get top 2 entries (startingRank=1, limit=2)
        List<TournamentPlayerRankWithDetails> rangeEntries = spyLeaderboardService.getScoresRange(tournament, 1, 2, true);

        // Then
        assertEquals(2, rangeEntries.size(), "Should return exactly 2 entries");
        assertEquals(300.0, rangeEntries.get(0).getScoreDetails().getScore(), "First entry should have highest score");
        assertEquals(200.0, rangeEntries.get(1).getScoreDetails().getScore(),
                "Second entry should have second highest score");
        assertEquals(1L, rangeEntries.get(0).getRank(), "First entry's rank should be 1");
        assertEquals(2L, rangeEntries.get(1).getRank(), "Second entry's rank should be 2");
    }

    @Test
    @DisplayName("Should handle range at the end of leaderboard")
    void shouldHandleRangeAtEndOfLeaderboard() throws Throwable {
        // Given
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(300.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account4, createScoreSnapshot(400.0));

        // When - Get last 2 entries (startingRank=3, limit=2)
        List<TournamentPlayerRankWithDetails> rangeEntries = spyLeaderboardService.getScoresRange(tournament, 3, 2, true);

        // Then
        assertEquals(2, rangeEntries.size(), "Should return exactly 2 entries");
        assertEquals(200.0, rangeEntries.get(0).getScoreDetails().getScore(), "First entry should have score 200");
        assertEquals(100.0, rangeEntries.get(1).getScoreDetails().getScore(), "Second entry should have score 100");
        assertEquals(3L, rangeEntries.get(0).getRank(), "First entry's rank should be 3");
        assertEquals(4L, rangeEntries.get(1).getRank(), "Second entry's rank should be 4");
    }

    @Test
    @DisplayName("Should handle cache failure gracefully for range queries")
    void shouldHandleCacheFailureGracefullyForRangeQueries() throws Throwable {
        // Given
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(300.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account4, createScoreSnapshot(400.0));

        // When - Simulate cache failure and get range
        Mockito.doThrow(new RuntimeException("Simulated cache failure"))
                .when(spyLeaderboardService)
                .getScoresRangeFromCacheZeroBased(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt());

        List<TournamentPlayerRankWithDetails> rangeEntries = spyLeaderboardService.getScoresRange(tournament, 2, 2, true);

        // Then
        assertEquals(2, rangeEntries.size(), "Should return exactly 2 entries from DB fallback");
        assertEquals(300.0, rangeEntries.get(0).getScoreDetails().getScore(), "First entry should have score 300");
        assertEquals(200.0, rangeEntries.get(1).getScoreDetails().getScore(), "Second entry should have score 200");
        assertEquals(2L, rangeEntries.get(0).getRank(), "First entry's rank should be 2");
        assertEquals(3L, rangeEntries.get(1).getRank(), "Second entry's rank should be 3");

        // Verify that fallback method was executed
        Mockito.verify(spyLeaderboardService).getScoresRangeFromDbZeroBased(Mockito.eq(tournament), Mockito.eq(1),
                Mockito.eq(2));
    }

    @Test
    @DisplayName("Should return empty list for range query when no scores exist")
    void shouldReturnEmptyListForRangeQueryWhenNoScoresExist() throws Throwable {
        // Given - No scores added to tournament

        // When - Try to get range
        List<TournamentPlayerRankWithDetails> rangeEntries = spyLeaderboardService.getScoresRange(tournament, 1, 5, true);

        // Then
        assertTrue(rangeEntries.isEmpty(), "Should return empty list when no scores exist");
    }

    @Test
    @DisplayName("Should throw exception when startRank is less than 1")
    void shouldThrowExceptionWhenStartRankIsLessThanOne() {
        // Given - startRank < 1
        int invalidStartRank = 0;
        int validLimit = 10;

        // When & Then
        EnhancedApplicationException exception = assertThrows(EnhancedApplicationException.class, () -> {
            spyLeaderboardService.getScoresRange(tournament, invalidStartRank, validLimit, true);
        });

        assertEquals("Start rank must be greater or equal to 1", exception.getMessage());
    }

    @Test
    @DisplayName("Should throw exception when limit is less than 1")
    void shouldThrowExceptionWhenLimitIsLessThanOne() {
        // Given - limit < 1
        int validStartRank = 1;
        int invalidLimit = 0;

        // When & Then
        EnhancedApplicationException exception = assertThrows(EnhancedApplicationException.class, () -> {
            spyLeaderboardService.getScoresRange(tournament, validStartRank, invalidLimit, true);
        });

        assertEquals("Limit must be greater or equal to 1", exception.getMessage());
    }

    @Test
    @DisplayName("Should throw exception when limit exceeds 100")
    void shouldThrowExceptionWhenLimitExceedsMaximum() {
        // Given - limit > 100
        int validStartRank = 1;
        int invalidLimit = 101;

        // When & Then
        EnhancedApplicationException exception = assertThrows(EnhancedApplicationException.class, () -> {
            spyLeaderboardService.getScoresRange(tournament, validStartRank, invalidLimit, true);
        });

        assertEquals("Limit must not exceed 100", exception.getMessage());
    }

    @Test
    @DisplayName("Should throw exception when startRank is negative")
    void shouldThrowExceptionWhenStartRankIsNegative() {
        // Given - startRank is negative
        int invalidStartRank = -5;
        int validLimit = 10;

        // When & Then
        EnhancedApplicationException exception = assertThrows(EnhancedApplicationException.class, () -> {
            spyLeaderboardService.getScoresRange(tournament, invalidStartRank, validLimit, true);
        });

        assertEquals("Start rank must be greater or equal to 1", exception.getMessage());
    }

    @Test
    @DisplayName("Should throw exception when limit is negative")
    void shouldThrowExceptionWhenLimitIsNegative() {
        // Given - limit is negative
        int validStartRank = 1;
        int invalidLimit = -10;

        // When & Then
        EnhancedApplicationException exception = assertThrows(EnhancedApplicationException.class, () -> {
            spyLeaderboardService.getScoresRange(tournament, validStartRank, invalidLimit, true);
        });

        assertEquals("Limit must be greater or equal to 1", exception.getMessage());
    }

    @Test
    @DisplayName("Should use local cache when skipLocalCache=false and cache hit occurs")
    void shouldUseLocalCacheWhenSkipLocalCacheIsFalseAndCacheHit() throws Throwable {
        // Given - Setup test data
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(300.0));

        // Clear local cache to start with clean state
        leaderboardServiceLocalCache.clearCache("test setup");

        // Get initial cache stats
        long initialHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        long initialMissCount = leaderboardServiceLocalCache.getCacheStats().missCount();

        // When - First call should miss cache and populate it
        List<TournamentPlayerRankWithDetails> firstCall = spyLeaderboardService.getScoresRange(tournament, 1, 3, false);

        // Verify first call populated cache (cache miss)
        long afterFirstCallHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        long afterFirstCallMissCount = leaderboardServiceLocalCache.getCacheStats().missCount();

        assertEquals(initialHitCount, afterFirstCallHitCount, "First call should not increase hit count");
        assertEquals(initialMissCount + 1, afterFirstCallMissCount, "First call should increase miss count by 1");

        // When - Second call with same parameters should hit cache
        List<TournamentPlayerRankWithDetails> secondCall = spyLeaderboardService.getScoresRange(tournament, 1, 3, false);

        // Then - Verify cache hit occurred
        long afterSecondCallHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        long afterSecondCallMissCount = leaderboardServiceLocalCache.getCacheStats().missCount();

        assertEquals(afterFirstCallHitCount + 1, afterSecondCallHitCount, "Second call should increase hit count by 1");
        assertEquals(afterFirstCallMissCount, afterSecondCallMissCount, "Second call should not increase miss count");

        // Verify both calls return same data
        assertEquals(firstCall.size(), secondCall.size(), "Both calls should return same number of entries");
        assertEquals(firstCall.get(0).getScoreDetails().getScore(), secondCall.get(0).getScoreDetails().getScore(),
                "Both calls should return same score data");
    }

    @Test
    @DisplayName("Should miss local cache when skipLocalCache=false and cache is empty")
    void shouldMissLocalCacheWhenSkipLocalCacheIsFalseAndCacheIsEmpty() throws Throwable {
        // Given - Setup test data
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));

        // Clear local cache to ensure cache miss
        leaderboardServiceLocalCache.clearCache("test setup");

        // Get initial cache stats
        long initialHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        long initialMissCount = leaderboardServiceLocalCache.getCacheStats().missCount();

        // When - Call with skipLocalCache=false on empty cache
        List<TournamentPlayerRankWithDetails> result = spyLeaderboardService.getScoresRange(tournament, 1, 2, false);

        // Then - Verify cache miss occurred
        long afterCallHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        long afterCallMissCount = leaderboardServiceLocalCache.getCacheStats().missCount();

        assertEquals(initialHitCount, afterCallHitCount, "Call should not increase hit count");
        assertEquals(initialMissCount + 1, afterCallMissCount, "Call should increase miss count by 1");

        // Verify data was returned correctly
        assertEquals(2, result.size(), "Should return 2 entries");
        assertEquals(200.0, result.get(0).getScoreDetails().getScore(), "First entry should have highest score");
        assertEquals(100.0, result.get(1).getScoreDetails().getScore(), "Second entry should have second highest score");
    }

    @Test
    @DisplayName("Should ignore local cache when skipLocalCache=true")
    void shouldIgnoreLocalCacheWhenSkipLocalCacheIsTrue() throws Throwable {
        // Given - Setup test data
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));

        // Clear local cache and populate it with first call
        leaderboardServiceLocalCache.clearCache("test setup");
        spyLeaderboardService.getScoresRange(tournament, 1, 2, false); // This should populate cache

        // Get cache stats after population
        long afterPopulationHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        long afterPopulationMissCount = leaderboardServiceLocalCache.getCacheStats().missCount();

        // When - Call with skipLocalCache=true (should ignore cache)
        List<TournamentPlayerRankWithDetails> result = spyLeaderboardService.getScoresRange(tournament, 1, 2, true);

        // Then - Verify cache was ignored (no hit or miss should be recorded for this call)
        long afterSkipCallHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        long afterSkipCallMissCount = leaderboardServiceLocalCache.getCacheStats().missCount();

        assertEquals(afterPopulationHitCount, afterSkipCallHitCount, "skipLocalCache=true should not increase hit count");
        assertEquals(afterPopulationMissCount, afterSkipCallMissCount, "skipLocalCache=true should not increase miss count");

        // Verify data was still returned correctly
        assertEquals(2, result.size(), "Should return 2 entries");
        assertEquals(200.0, result.get(0).getScoreDetails().getScore(), "First entry should have highest score");
        assertEquals(100.0, result.get(1).getScoreDetails().getScore(), "Second entry should have second highest score");
    }

    @Test
    @DisplayName("Should populate local cache after retrieval from Redis/DB regardless of skipLocalCache parameter")
    void shouldPopulateLocalCacheAfterRetrievalRegardlessOfSkipParameter() throws Throwable {
        // Given - Setup test data
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(300.0));

        // Clear local cache to start fresh
        leaderboardServiceLocalCache.clearCache("test setup");

        // When - Call with skipLocalCache=true (should still populate cache after retrieval)
        spyLeaderboardService.getScoresRange(tournament, 1, 3, true);

        // Then - Verify cache was populated by making another call with skipLocalCache=false
        long beforeSecondCallHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        List<TournamentPlayerRankWithDetails> secondCall = spyLeaderboardService.getScoresRange(tournament, 1, 3, false);
        long afterSecondCallHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();

        assertEquals(beforeSecondCallHitCount + 1, afterSecondCallHitCount,
                "Second call should hit cache, proving first call populated it");

        // Verify data consistency
        assertEquals(3, secondCall.size(), "Should return 3 entries");
        assertEquals(300.0, secondCall.get(0).getScoreDetails().getScore(), "First entry should have highest score");
    }

    @Test
    @DisplayName("Should handle different cache keys for different parameters")
    void shouldHandleDifferentCacheKeysForDifferentParameters() throws Throwable {
        // Given - Setup test data
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account2, createScoreSnapshot(200.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account3, createScoreSnapshot(300.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament, account4, createScoreSnapshot(400.0));

        // Clear local cache
        leaderboardServiceLocalCache.clearCache("test setup");

        long initialMissCount = leaderboardServiceLocalCache.getCacheStats().missCount();

        // When - Make calls with different parameters (different cache keys)
        spyLeaderboardService.getScoresRange(tournament, 1, 2, false); // Cache key: tournamentId:1:2
        spyLeaderboardService.getScoresRange(tournament, 2, 2, false); // Cache key: tournamentId:2:2
        spyLeaderboardService.getScoresRange(tournament, 1, 3, false); // Cache key: tournamentId:1:3

        // All should be cache misses since they have different keys
        long afterCallsMissCount = leaderboardServiceLocalCache.getCacheStats().missCount();
        assertEquals(initialMissCount + 3, afterCallsMissCount, "All three calls should result in cache misses");

        // When - Repeat the same calls (should hit cache now)
        long beforeRepeatedCallsHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        spyLeaderboardService.getScoresRange(tournament, 1, 2, false);
        spyLeaderboardService.getScoresRange(tournament, 2, 2, false);
        spyLeaderboardService.getScoresRange(tournament, 1, 3, false);

        // Then - All should be cache hits
        long afterRepeatedCallsHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        assertEquals(beforeRepeatedCallsHitCount + 3, afterRepeatedCallsHitCount,
                "All three repeated calls should result in cache hits");
    }

    @Test
    @DisplayName("Should handle local cache with different tournaments independently")
    void shouldHandleLocalCacheWithDifferentTournamentsIndependently() throws Throwable {
        // Given - Setup data for two different tournaments
        Tournament tournament2 = tournamentTestHelper.createTournament();

        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, createScoreSnapshot(100.0));
        spyLeaderboardService.upsertScoreSnapshot(tournament2, account2, createScoreSnapshot(200.0));

        // Clear local cache
        leaderboardServiceLocalCache.clearCache("test setup");

        long initialMissCount = leaderboardServiceLocalCache.getCacheStats().missCount();

        // When - Make calls for both tournaments with same startRank and limit
        spyLeaderboardService.getScoresRange(tournament, 1, 1, false);
        spyLeaderboardService.getScoresRange(tournament2, 1, 1, false);

        // Then - Both should be cache misses (different tournament IDs = different cache keys)
        long afterCallsMissCount = leaderboardServiceLocalCache.getCacheStats().missCount();
        assertEquals(initialMissCount + 2, afterCallsMissCount, "Both calls should result in cache misses");

        // When - Repeat calls for same tournaments
        long beforeRepeatedCallsHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        spyLeaderboardService.getScoresRange(tournament, 1, 1, false);
        spyLeaderboardService.getScoresRange(tournament2, 1, 1, false);

        // Then - Both should be cache hits
        long afterRepeatedCallsHitCount = leaderboardServiceLocalCache.getCacheStats().hitCount();
        assertEquals(beforeRepeatedCallsHitCount + 2, afterRepeatedCallsHitCount,
                "Both repeated calls should result in cache hits");
    }

    @Test
    @DisplayName("Should skip saving identical score snapshots (except timestamp) to DB and cache")
    void shouldSkipSavingIdenticalScoreSnapshotsExceptTimestamp() throws Throwable {
        // Given - Create a comprehensive score snapshot with all fields set
        TScoreSnapshot initialSnapshot = createCustomScoreSnapshot(150.0, 100, 50, 5.5, 3.2)
                .toBuilder()
                .setWinsInRow3(2)
                .setWinsInRow4(1)
                .setWinsInRow5(0)
                .setLossesInRow3(1)
                .setLossesInRow5(0)
                .setLossesInRow7(0)
                .setLossesInRow11(0)
                .setTimestamp(1000L)
                .build();

        // Save the initial snapshot
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, initialSnapshot);

        // Reset the spy to clear previous invocations
        Mockito.clearInvocations(spyLeaderboardService);

        // When - Create an identical snapshot with only different timestamp
        TScoreSnapshot identicalSnapshot = initialSnapshot.toBuilder()
                .setTimestamp(2000L) // Different timestamp
                .build();

        // Attempt to save the identical snapshot
        boolean result = spyLeaderboardService.upsertScoreSnapshot(tournament, account1, identicalSnapshot);

        // Then - Verify that upsertScoreSnapshot returns false for identical snapshots
        assertFalse(result, "upsertScoreSnapshot should return false for identical snapshots (except timestamp)");

        // Then - Verify that cache and DB save operations were NOT called
        // The method should return early due to deep equality check
        Mockito.verify(spyLeaderboardService, Mockito.never())
                .saveScoreSnapshotToCache(Mockito.eq(tournament), Mockito.eq(account1), Mockito.any(TScoreSnapshot.class));
        Mockito.verify(spyLeaderboardService, Mockito.never())
                .saveScoreSnapshotToDb(Mockito.eq(tournament), Mockito.eq(account1), Mockito.any(TScoreSnapshot.class), Mockito.any(), Mockito.any());

        // Verify that the score snapshot in DB remains unchanged (still has original timestamp logic)
        var retrievedSnapshot = spyLeaderboardService.getScoreSnapshotFromDb(tournament, account1);
        assertTrue(retrievedSnapshot.isPresent(), "Score snapshot should exist in DB");
        assertEquals(150.0, retrievedSnapshot.get().getScore(), "Score should remain unchanged");
        assertEquals(100, retrievedSnapshot.get().getTotalCredit(), "TotalCredit should remain unchanged");
        assertEquals(50, retrievedSnapshot.get().getTotalDebit(), "TotalDebit should remain unchanged");
    }

    @Test
    @DisplayName("Should save score snapshots when they differ in any field other than timestamp")
    void shouldSaveScoreSnapshotsWhenTheyDifferInFieldsOtherThanTimestamp() throws Throwable {
        // Given - Create initial score snapshot
        TScoreSnapshot initialSnapshot = createCustomScoreSnapshot(150.0, 100, 50, 5.5, 3.2)
                .toBuilder()
                .setWinsInRow3(2)
                .setTimestamp(1000L)
                .build();

        // Save the initial snapshot
        spyLeaderboardService.upsertScoreSnapshot(tournament, account1, initialSnapshot);

        // Reset the spy to clear previous invocations
        Mockito.clearInvocations(spyLeaderboardService);

        // When - Create a snapshot that differs in one field (score)
        TScoreSnapshot differentSnapshot = initialSnapshot.toBuilder()
                .setScore(200.0) // Different score
                .setTimestamp(2000L) // Different timestamp
                .build();

        // Attempt to save the different snapshot
        boolean result = spyLeaderboardService.upsertScoreSnapshot(tournament, account1, differentSnapshot);

        // Then - Verify that upsertScoreSnapshot returns true for different snapshots
        assertTrue(result, "upsertScoreSnapshot should return true when snapshots differ in fields other than timestamp");

        // Then - Verify that cache and DB save operations WERE called
        Mockito.verify(spyLeaderboardService, Mockito.times(1))
                .saveScoreSnapshotToCache(Mockito.eq(tournament), Mockito.eq(account1), Mockito.eq(differentSnapshot));
        Mockito.verify(spyLeaderboardService, Mockito.times(1))
                .saveScoreSnapshotToDb(Mockito.eq(tournament), Mockito.eq(account1), Mockito.eq(differentSnapshot), Mockito.any(), Mockito.any());

        // Verify that the score snapshot in DB was updated
        var retrievedSnapshot = spyLeaderboardService.getScoreSnapshotFromDb(tournament, account1);
        assertTrue(retrievedSnapshot.isPresent(), "Score snapshot should exist in DB");
        assertEquals(200.0, retrievedSnapshot.get().getScore(), "Score should be updated");
    }
}
