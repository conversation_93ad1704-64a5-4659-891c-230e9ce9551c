<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <include resource="logback-base-levels.xml" />
    <appender name="CONSOLE-SHORT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %-5level %logger{35} %replace( [%replace(%X{traceId}){'^.+$', 'traceId=$0'}%replace(%X{tournamentId}){'^.+$', ', tournamentId=$0'}%replace(%X{playerId}){'^.+$', ', playerId=$0'}] ){'\[\s*,?\s*\]', ''} %msg%n
            </pattern>
        </encoder>
    </appender>

    <logger name="org.apache.commons.beanutils" level="INFO" />

    <logger name="org.springframework" level="INFO" />
    <logger name="org.springframework.jdbc.datasource.init.ScriptUtils" level="INFO" />
    <logger name="org.springframework.test.context.cache" level="INFO" />

    <logger name="io.ebean.internal" level="INFO" />
    <logger name="io.ebean.SQL" level="TRACE" />
    <logger name="io.ebean.TXN" level="TRACE" />
    <logger name="io.ebean.SUM" level="TRACE" />
    <logger name="io.ebean.cache.ALL" level="TRACE" />
    <logger name="io.ebean.cache.QUERY" level="TRACE" />
    <logger name="io.ebean.cache.BEAN" level="TRACE" />
    <logger name="io.ebean.cache.COLL" level="TRACE" />
    <logger name="io.ebean.cache.NATKEY" level="TRACE" />
    <logger name="io.ebean.cache.TABLEMOD" level="TRACE" />

    <logger name="org.redisson" level="INFO" />
    <logger name="jdk.event.security" level="INFO" />
    <logger name="sun.rmi.transport.tcp" level="INFO" />

    <logger name="com.zaxxer.hikari.HikariConfig" level="INFO" />

    <logger name="org.flywaydb.core.internal.sqlscript" level="INFO" />

    <logger name="com.turbospaces.ebean.ReplicatedServerQueryCache" level="WARN" />
    <logger name="com.turbospaces.ebean.MockCacheManager" level="INFO" />
    <logger name="com.turbospaces.ebean.LocalEbeanCache" level="INFO" />
    <logger name="com.turbospaces.ebean.TracedEbeanTransaction" level="INFO" />

    <root>
        <level value="DEBUG" />
        <appender-ref ref="CONSOLE-SHORT" />
    </root>
</configuration>