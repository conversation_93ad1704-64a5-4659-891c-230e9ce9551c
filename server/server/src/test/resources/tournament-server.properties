cloud.application.app-id=${project.artifactId}
cloud.application.name=${project.groupId}:${project.artifactId}:${project.version}

feature-flag.enabled-status-by-brand-web-csv=playfame:false,mcluck:false,hellomillions:false,sportsmillions:false,scratchful:false,\
  alltimeslots:false,spinblitz:false,randomizer:false,jackpota:false,bluedream:true
feature-flag.enabled-status-by-brand-ios-csv=playfame:false,mcluck:false,hellomillions:false,sportsmillions:false,scratchful:false,\
  alltimeslots:false,spinblitz:false,randomizer:false,jackpota:false,bluedream:true
feature-flag.enabled-status-by-brand-android-csv=playfame:false,mcluck:false,hellomillions:false,sportsmillions:false,scratchful:false,\
  alltimeslots:false,spinblitz:false,randomizer:false,jackpota:false,bluedream:true
feature-flag.whitelisted-account-ids-override-csv=777777,********
tournament.score-cache.db-fallback.enabled=true

# always try to sync leaderboard cache if out of sync, no cooldown period
tournament.score-cache.recovery-from-db.min-delay-since-last-success-seconds=0

# control when lifecycle job runs directly in tests
tournament.lifecycle-job.enabled=false
tournament.lifecycle-job.run-on-start=false