package tournament.services.events;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import api.v1.EnhancedApplicationException;
import io.ebeaninternal.api.SpiTransaction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tournament.TournamentServerProperties;
import tournament.api.v1.TScoreSnapshot;
import tournament.dto.ComparableGameRound;
import tournament.model.BrandTournament;
import tournament.model.Tournament;
import tournament.model.TournamentAccount;
import tournament.model.TournamentBrand;
import tournament.model.TournamentConfig;
import tournament.model.TournamentGame;
import tournament.model.TournamentGameRound;
import tournament.model.TournamentGameRoundUnfiltered;
import tournament.model.TournamentJoin;
import tournament.replication.TournamentGameRoundSink;
import tournament.repo.TournamentJpaManager;
import tournament.services.cache.CacheableGameRoundRepo;
import tournament.services.flags.FeatureFlagService;
import tournament.services.notifications.TournamentsScoreUpdatedNotificationSender;
import tournament.services.scoring.ScoreCalculator;
import tournament.services.scoring.leaderboard.LeaderboardService;
import tournament.util.TournamentMdc;
import uam.api.v1.internal.GameRoundEvent;

@Slf4j
@RequiredArgsConstructor
public class DefaultTournamentGameRoundEventsProcessor implements TournamentGameRoundEventsProcessor {
    /**
     * buffer is needed for a case when received rounds are actually an update for
     * already existing rounds (i.e. spins within a round); since we do not know
     * when a round
     * is truly over, we might be receiving updates for already completed rounds:
     * say we have
     * 10 rounds already stored, and limit is set to 10 first rounds for a
     * tournament. When we
     * receive more rounds in message, they might actually be updates to existing
     * rounds. To know
     * this for sure, we will have to keep all rounds in memory, merge them, define
     * which rounds are
     * update for existing (so can be merged), check whether after this merge we
     * will be within rounds
     * limit etc. This is doable, but for the scope of PoC easier approach will be
     * to just write rounds
     * over limit (allowing for some buffer), and then do exact limit cutoff in
     * memory. When we will add
     * proper caching, this approach will most likely change
     */
    private static final int ROUNDS_OVER_LIMIT_BUFFER = 20;
    private final TournamentJpaManager ebean;
    private final ScoreCalculator scoreCalculator;
    private final LeaderboardService leaderboardService;
    private final CacheableGameRoundRepo cachedGameRoundRepo;
    private final FeatureFlagService featureFlagService;
    private final TournamentsScoreUpdatedNotificationSender notificationSender;
    private final TournamentServerProperties serverProperties;

    private static FilteredAndUnfilteredRounds applyTournamentSpecificFilters(UnfilteredTournamentRounds nonFilteredRoundsForTournament) {
        Map<Tournament, Set<ComparableGameRound>> filteredRounds = new HashMap<>();
        List<ComparableGameRound> allRounds = new ArrayList<>();

        for (Map.Entry<Tournament, List<ComparableGameRound>> entry : nonFilteredRoundsForTournament.unfilteredRounds.entrySet()) {
            Tournament tournament = entry.getKey();
            List<ComparableGameRound> rounds = entry.getValue();
            ScoreCalculator.RoundsFilteringResult roundsFilteringResult = ScoreCalculator.applyTournamentConfigFilters(
                    rounds, tournament.getTournamentConfig(), nonFilteredRoundsForTournament.player.getRemoteId());
            allRounds.addAll(roundsFilteringResult.unfilteredRounds());
            filteredRounds.put(tournament, roundsFilteringResult.filteredRounds());
        }
        return new FilteredAndUnfilteredRounds(filteredRounds, allRounds);
    }

    @Override
    public void processGameRoundEvents(List<TournamentGameRoundSink.GameRoundEventWrapper> events) {
        List<TournamentGame> tournamentGames;
        Set<Long> tournamentIds;
        List<Long> idsOfAllJoinedUsers;

        // these queries are supposed to be returned from query cache, as entities are changes infrequently
        try (var tx = ebean.newReadOnlyTransaction()) {
            List<Tournament> activeTournaments = ebean.tournamentRepo().tournamentsEligibleForGameRoundsProcessing(tx);
            if (activeTournaments.isEmpty()) {
                log.debug("No active tournaments. Ignoring [{}] rounds ", events.size());
                return;
            }

            tournamentIds = activeTournaments.stream()
                    .map(Tournament::getId)
                    .collect(Collectors.toSet());

            tournamentGames = ebean.tournamentRepo()
                    .gamesForTournaments(tournamentIds, tx);

            if (tournamentGames.isEmpty()) {
                log.debug("No games in active tournaments. Ignoring [{}] rounds ", events.size());
                return;
            }

        } catch (Throwable e) {
            log.error("Failed to check active tournaments and games. Ignoring [{}] rounds ", events.size());
            return;
        }

        Set<String> gameCodes = tournamentGames.stream()
                .map(TournamentGame::getCode)
                .collect(Collectors.toSet());

        // first level of filtering: consider only games which are added to active tournaments. This should
        // be a relatively cheap and fast filter, and active tournaments/games assigned to them do not change
        // often, and therefore should be query-cached easily
        Map<Long, List<GameRoundEvent>> eventsGroupedByAccount = events.stream()
                .map(TournamentGameRoundSink.GameRoundEventWrapper::payload)
                .filter(Objects::nonNull)
                .filter(event -> {
                    String gameCode = event.getProductInfo().getCode();
                    if (!gameCodes.contains(gameCode)) {
                        log.debug("Ignoring game round for game [{}] and account [{}]: not a tournament game",
                                gameCode,
                                event.getAccount().getId());
                        return false;
                    }
                    return true;
                })
                .collect(groupingBy(event -> event.getAccount().getId()));

        try (var tx = ebean.newReadOnlyTransaction()) {
            // get all tournament joins for ALL users for all active tournaments. This might be quite a big list,
            // but the idea is that it will be cached via ebean query cache (invalidated on each join, but this is
            // still somewhat acceptable); later in the code this list of joined users will be used to filter out
            // users by ID who have not joined any tournament. We are doing this user filtering in memory intentionally,
            // not as a part of DB query - cause otherwise it won't be cache-friendly
            idsOfAllJoinedUsers = ebean.tournamentRepo()
                    .allJoinedUserIdsForTournaments(tournamentIds, tx);
            if (idsOfAllJoinedUsers.isEmpty()) {
                log.debug("No active tournament joins. Ignoring [{}] rounds ", events.size());
                return;
            }

            int loadedJoinsSize = idsOfAllJoinedUsers.size();
            Integer loadedJoinsThreshold = serverProperties.TOTAL_LOADED_ALL_JOINS_THRESHOLD_ERROR.get();
            if (loadedJoinsSize > loadedJoinsThreshold) {
                // FIXME Rough estimation for 50k joins (throughout ALL active tournaments) is 20-30 MB.
                // This is the amount of data which application will pull from DB on EACH new join
                log.error("Fetched more than [{}] tournament joins. This might pose performance issue", loadedJoinsSize);
            }

        } catch (Throwable e) {
            log.error("Failed to check active tournament joins. Ignoring [{}] rounds ", events.size(), e);
            return;
        }

        Map<Tournament, List<TournamentGame>> gamesForTournaments = tournamentGames.stream()
                .collect(groupingBy(TournamentGame::getTournament));

        List<UnfilteredTournamentRounds> unfilteredTournamentRounds = eventsGroupedByAccount.entrySet()
                .stream()
                .map(entry -> roundsMappedToTournament(entry.getKey(), entry.getValue(), gamesForTournaments,
                        idsOfAllJoinedUsers, tournamentIds))
                .flatMap(Optional::stream)
                .toList();

        List<RoundsForPersisting> roundsForPersisting = unfilteredTournamentRounds.stream()
                .map(this::filterRoundsForScoreCalculation)
                .toList();

        // store only filtered rounds (which take part in score calculation)
        persistRoundsForScoreCalculation(roundsForPersisting);

        // update user's score and send WS notification about score update to FE
        roundsForPersisting.stream()
                .map(RoundsForPersisting::filteredRounds)
                .map(reloadStoredRounds())
                .flatMap(Optional::stream)
                // FIXME: Leaderboard gets updated even when score does not change
                // see https://patrianna.atlassian.net/browse/GE-3185
                .map(this::updateLeaderBoard)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .forEach(notificationSender::sendUpdatedScoreNotification);

        // store all received rounds, which were mapped to user and tournament: both the ones that impact score
        // and ones that don't (due to failing some of the tournament's configuration rules); all raw rounds are
        // required for BI processing
        List<TournamentGameRoundUnfiltered> rawRounds = roundsForPersisting.stream()
                .flatMap(r -> r.unfilteredRounds.stream())
                .map(ComparableGameRound::mapToUnfiltered)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
        persistUnfilteredGameRounds(rawRounds);
    }

    /**
     * Load all rounds for a tournament for user - we need ALL round to re-calculate a new score. Normally all rounds
     * these will be stored in-memory, with loading from DB as a fallback
     */
    private Function<FilteredTournamentRounds, Optional<FilteredTournamentRounds>> reloadStoredRounds() {
        return rounds -> {
            try (var tx = ebean.newReadOnlyTransaction()) {
                Map<Tournament, List<TournamentGameRound>> roundsForTournament = rounds.filteredRounds;

                Map<Tournament, List<TournamentGameRound>> reloadedRounds = roundsForTournament.keySet()
                        .stream()
                        .collect(Collectors.toMap(Function.identity(),
                                tournament -> loadRoundsForTournament(tournament, rounds.player.getRemoteId(),
                                        tx)));
                return Optional.of(new FilteredTournamentRounds(rounds.player, reloadedRounds));

            } catch (Throwable e) {
                log.error("Failed to reload all stored rounds for player [{}]", rounds.player.getRemoteId());
                return Optional.empty();
            }
        };
    }

    private Optional<UnfilteredTournamentRounds> roundsMappedToTournament(long playerRemoteId,
            List<GameRoundEvent> gameRounds,
            Map<Tournament, List<TournamentGame>> gamesForTournaments,
            List<Long> idsOfJoinedUsers,
            Set<Long> activeTournamentIds) {

        log.debug("Received [{}] rounds for account [{}]. Checking joins",
                gameRounds.size(),
                playerRemoteId);

        // in-memory filtering for user joins
        boolean userHasJoinedAnyActiveTournaments = idsOfJoinedUsers.stream()
                .anyMatch(userRemoteId -> Objects.equals(userRemoteId, playerRemoteId));

        if (!userHasJoinedAnyActiveTournaments) {
            log.debug("No joins for account [{}]. Ignoring [{}] rounds",
                    playerRemoteId, gameRounds.size());
            return Optional.empty();
        }

        // now load heavier user join objects - including tournament and tournament config
        TournamentAccount player;
        List<TournamentJoin> tournamentJoinsForUser;
        try (var tx = ebean.newReadOnlyTransaction()) {
            tournamentJoinsForUser = ebean.tournamentRepo()
                    .findTournamentJoinsForUser(playerRemoteId, activeTournamentIds, tx);

            if (tournamentJoinsForUser.isEmpty()) {
                log.error("Loaded no joins for account [{}]. Ignoring [{}] rounds",
                        playerRemoteId, gameRounds.size());
                return Optional.empty();
            }

            player = tournamentJoinsForUser.getFirst().getAccount();
            if (player == null || player.getBrand() == null) {
                // this would be totally strange, but better to double-check
                Optional<TournamentAccount> foundPlayerOpt = ebean.accountRepo().accountByRemoteId(playerRemoteId, tx);
                if (foundPlayerOpt.isEmpty()) {
                    // this is actually an error - account should be present at this point, if user
                    // was able to join
                    log.error("Player not found by remote id [{}]. Ignoring [{}] rounds",
                            playerRemoteId, gameRounds.size());
                    return Optional.empty();
                }
                player = foundPlayerOpt.get();
            }

        } catch (Throwable e) {
            log.error("Failed to load tournament joins for player [{}]", playerRemoteId, e);
            return Optional.empty();
        }

        // apply feature flag check for each brand in the tournaments the user has joined
        String brandName = player.getBrand().getName();
        boolean backendEnabled = featureFlagService.isBackendEnabledForBrand(brandName, player);
        if (!backendEnabled) {
            log.info("Account [{}] for brand [{}] which is not enabled for tournaments and there is no " +
                    "whitelisting override. Ignoring [{}] rounds",
                    player.getRemoteId(), brandName, gameRounds.size());
            return Optional.empty();
        }

        // check which games are currently active for user's tournaments
        Map<String, List<Tournament>> gamesAndTournaments = tournamentJoinsForUser.stream()
                .map(TournamentJoin::getTournament)
                .filter(gamesForTournaments::containsKey)
                .flatMap(tournament -> gamesForTournaments.get(tournament).stream()
                        .map(tournamentGame -> Map.entry(tournamentGame.getCode(), tournament)))
                .collect(groupingBy(
                        Map.Entry::getKey,
                        mapping(Map.Entry::getValue, toList())));

        if (gamesAndTournaments.isEmpty()) {
            log.debug(
                    "Account [{}] has no active games in joined tournaments. Ignoring [{}] rounds",
                    playerRemoteId, gameRounds.size());
            return Optional.empty();
        }

        Map<Tournament, List<ComparableGameRound>> nonFilteredRounds = groupReceivedRoundsByTournament(gameRounds,
                gamesAndTournaments, player);

        return Optional.of(new UnfilteredTournamentRounds(player, nonFilteredRounds));
    }

    private RoundsForPersisting filterRoundsForScoreCalculation(UnfilteredTournamentRounds nonFilteredRounds) {
        TournamentAccount player = nonFilteredRounds.player;
        Long playerRemoteId = player.getRemoteId();
        FilteredAndUnfilteredRounds filteredAndUnfilteredRounds = applyTournamentSpecificFilters(nonFilteredRounds);

        Map<Tournament, List<TournamentGameRound>> roundsToStore = new HashMap<>();

        try (var tx = ebean.newReadOnlyTransaction()) {
            {
                for (Map.Entry<Tournament, Set<ComparableGameRound>> entry : filteredAndUnfilteredRounds.filteredRounds.entrySet()) {
                    Tournament tournament = entry.getKey();
                    var tournamentId = tournament.getId();
                    Set<ComparableGameRound> receivedRounds = entry.getValue();

                    TournamentConfig tournamentConfig = tournament.getTournamentConfig();
                    // for each of the received tournaments check if round limit is present
                    // when considering if new rounds should be persisted and used for calculation
                    Integer roundsLimit = tournamentConfig.getLimitRoundsInTournament();
                    if (roundsLimit != null) {
                        TournamentConfig.LimitRoundsMode limitRoundsMode = tournamentConfig.getLimitRoundsMode();
                        if (TournamentConfig.LimitRoundsMode.FIRST == limitRoundsMode) {
                            int storedRoundsPerTournament;
                            try {
                                storedRoundsPerTournament = cachedGameRoundRepo
                                        .countGameRoundsForTournament(playerRemoteId, tournament.getId(), tx);
                            } catch (Throwable e) {
                                log.error("Failed to count stored rounds for tournament [{}] and player [{}]",
                                        tournamentId, playerRemoteId, e);
                                continue; // skip rounds for this particular tournament but continue with remaining ones
                            }

                            if (storedRoundsPerTournament >= roundsLimit + ROUNDS_OVER_LIMIT_BUFFER) {
                                log.info(
                                        "Player [{}] exceeded round limit [{}] plus allowed buffer for tournament "
                                                + "[{}]. Ignoring [{}] rounds",
                                        playerRemoteId, roundsLimit, tournamentId, receivedRounds.size());
                                continue; // Skip this tournament
                            }
                        }
                    }

                    List<TournamentGameRound> roundsForScoreCalculation = receivedRounds.stream()
                            .map(ComparableGameRound::getGameRound)
                            .filter(Objects::nonNull)
                            .toList();
                    roundsToStore.put(tournament, roundsForScoreCalculation);
                }
            }

        } catch (Throwable e) {
            log.error("Failed to filter rounds for score calculation", e);
        }

        FilteredTournamentRounds filteredTournamentRounds = new FilteredTournamentRounds(player, roundsToStore);
        return new RoundsForPersisting(filteredTournamentRounds, filteredAndUnfilteredRounds.unfilteredRounds);
    }

    private List<TournamentGameRound> loadRoundsForTournament(Tournament tournament, long playerRemoteId,
            SpiTransaction tx) {
        TournamentMdc.addTournament(tournament);
        TournamentMdc.addPlayerId(playerRemoteId);

        // retrieve all stored rounds for the tournament - they will include both previous rounds and new ones, received
        // via current game round events
        List<TournamentGameRound> storedRounds = cachedGameRoundRepo
                .getGameRoundsForTournament(playerRemoteId, tournament, tx);
        log.info("Retrieved [{}] stored rounds for account [{}] and tournament [{}]", storedRounds.size(),
                playerRemoteId, tournament.getId());

        if (log.isTraceEnabled()) {
            storedRounds.forEach(
                    round -> log.trace("Retrieved stored round for session [{}] for tournament [{}] and account [{}]",
                            round.getSessionId(),
                            round.getTournament().getId(), round.getAccount().getRemoteId()));
        }
        return storedRounds;
    }

    private void persistRoundsForScoreCalculation(List<RoundsForPersisting> roundsForPersisting) {
        try {
            CacheableGameRoundRepo.SaveRoundsResult result = cachedGameRoundRepo.saveGameRounds(
                    roundsForPersisting);
            int actuallySaved = result.actuallySaved();
            int requestedToSave = result.requestedToSave();
            if (requestedToSave != 0) {
                log.info("Upserted {} ([{}] of [{}]) game rounds to DB",
                        actuallySaved == requestedToSave ? "all" : "not all", actuallySaved,
                        requestedToSave);
            }

        } catch (Exception e) {
            log.error("Failed to upsert game rounds for calculation", e);
            throw e;
        }
    }

    private void persistUnfilteredGameRounds(List<TournamentGameRoundUnfiltered> roundsToUpsert) {
        int countOfRoundsToUpsert = roundsToUpsert.size();

        try {
            int actuallyUpsertedCount = cachedGameRoundRepo.saveUnfilteredGameRounds(roundsToUpsert);
            if (countOfRoundsToUpsert != 0) {
                log.info("Upserted {} ([{}] of [{}]) unfiltered game rounds to DB",
                        actuallyUpsertedCount == countOfRoundsToUpsert ? "all" : "not all", actuallyUpsertedCount,
                        countOfRoundsToUpsert);
            }

        } catch (Exception e) {
            log.error("Failed to upsert unfiltered game rounds", e);
            throw e;
        }
    }

    private Optional<UpdatedTournamentScores> updateLeaderBoard(FilteredTournamentRounds rounds) {
        TournamentAccount player = rounds.player;
        Long playerRemoteId = player.getRemoteId();
        TournamentMdc.addPlayerId(playerRemoteId);
        Map<Tournament, List<TournamentGameRound>> filteredRounds = rounds.filteredRounds;
        List<Tournament> tournamentsWithUpdatedScores = new ArrayList<>();

        log.debug("Updating leaderboard for account [{}]", playerRemoteId);
        for (var tournamentAndRounds : filteredRounds.entrySet()) {
            Tournament tournament = tournamentAndRounds.getKey();
            TournamentMdc.addTournament(tournament);
            List<TournamentGameRound> tournamentRounds = tournamentAndRounds.getValue();

            TScoreSnapshot scoreForUserAndTournament = scoreCalculator.calculateScore(tournamentRounds, tournament,
                    playerRemoteId);

            log.info("Calculated score for tournament [{}] and account [{}]: [{}]", tournament.getId(), playerRemoteId,
                    scoreForUserAndTournament);

            try {

                double score = scoreForUserAndTournament.getScore();
                if (Double.compare(score, 0.0) == 0) {
                    // if a snapshot has a zero score, it should not be persisted to leaderboard
                    return Optional.empty();
                }
                boolean updatedScore = leaderboardService.upsertScoreSnapshot(tournament, player,
                        scoreForUserAndTournament);
                if (updatedScore) {
                    tournamentsWithUpdatedScores.add(tournament);
                }

            } catch (EnhancedApplicationException e) {
                log.error("Failed to save score snapshot for tournament [{}] and player [{}]", tournament.getId(),
                        playerRemoteId, e);
            }
        }
        return Optional.of(new UpdatedTournamentScores(player, tournamentsWithUpdatedScores));
    }

    private Map<Tournament, List<ComparableGameRound>> groupReceivedRoundsByTournament(
            List<GameRoundEvent> gameRoundEvents,
            Map<String, List<Tournament>> gamesAndTournaments,
            TournamentAccount player) {

        // convert events to comparable format and group by tournament
        return gameRoundEvents.stream()
                .map(event -> getTournamentRoundInEvent(gamesAndTournaments, player, event))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(groupingBy(TournamentRoundInEvent::tournament,
                        mapping(TournamentRoundInEvent::roundInEvent, toList())));
    }

    private static Optional<TournamentRoundInEvent> getTournamentRoundInEvent(Map<String, List<Tournament>> gamesAndTournaments, TournamentAccount player,
            GameRoundEvent event) {
        String gameCode = event.getProductInfo().getCode();
        List<Tournament> tournamentsForGame = gamesAndTournaments.get(gameCode);

        if (tournamentsForGame == null || tournamentsForGame.isEmpty()) {
            return Optional.empty();
        }

        Optional<Tournament> tournamentOptional = tryToFilterByBrand(player, event, tournamentsForGame, gameCode);
        if (tournamentOptional.isEmpty()) {
            return Optional.empty();
        }

        Tournament tournament = tournamentOptional.get();

        long eventTime = event.getAt();
        if (!matchesTournamentTimeFrame(eventTime, tournament)) {
            return Optional.empty();
        }

        ComparableGameRound round = ComparableGameRound.fromRoundEvent(player, tournament, gameCode, event);
        return Optional.of(new TournamentRoundInEvent(tournament, gameCode, round));
    }

    public static boolean matchesTournamentTimeFrame(long eventTime, Tournament tournament) {
        // Add 10 seconds to end date to cover potential delay in receiving events from game provider on CRM side
        return eventTime >= tournament.getStartDate().toEpochMilli() &&
                eventTime <= tournament.getEndDate().toEpochMilli() + 10_000;
    }

    private static Optional<Tournament> tryToFilterByBrand(TournamentAccount player, GameRoundEvent event,
            List<Tournament> tournamentsForGame, String gameCode) {
        if (tournamentsForGame.size() == 1) {
            return Optional.ofNullable(tournamentsForGame.getFirst());
        }

        Map<String, List<String>> gameCodeToBrandMatches = new HashMap<>();
        long eventTime = event.getAt();

        List<Tournament> filteredTournaments = tournamentsForGame.stream()
                .filter(tournament -> {
                    TournamentBrand playerBrand = player.getBrand();
                    TournamentBrand ownerBrand = tournament.getOwnerBrand();
                    Long tournamentId = tournament.getId();

                    List<String> brandMatches = gameCodeToBrandMatches.computeIfAbsent(gameCode, k -> new ArrayList<>());

                    if (Objects.equals(playerBrand.getName(), ownerBrand.getName())) {
                        brandMatches.add("tournament_id: " + tournamentId + ", owner brand: " + ownerBrand.getName());
                        return true;
                    }

                    List<BrandTournament> brandTournaments = tournament.getBrandTournaments();
                    if (brandTournaments.isEmpty()) {
                        return false;
                    }

                    Optional<BrandTournament> matchedTournamentBrands = brandTournaments.stream()
                            .filter(brandTournament -> {
                                TournamentBrand tournamentBrand = brandTournament.getBrand();
                                return Objects.equals(tournamentBrand.getName(), playerBrand.getName());
                            })
                            .findAny();

                    if (matchedTournamentBrands.isPresent()) {
                        TournamentBrand matchedBrand = matchedTournamentBrands.get().getBrand();
                        brandMatches.add("tournament_id: " + tournamentId + ", associated brand: " + matchedBrand.getName());
                        return true;
                    }

                    return false;
                })
                .toList();

        if (filteredTournaments.isEmpty()) {
            log.error("Error while filtering tournaments [{}] for user [{}] and game [{}]. Skipping game round for " +
                    "session [{}]",
                    tournamentsForGame.stream().map(Tournament::getId).toList(), player.getRemoteId(), gameCode,
                    event.getSessionId());
            return Optional.empty();
        }

        if (filteredTournaments.size() == 1) {
            return Optional.of(filteredTournaments.getFirst());
        }

        // Additional filtering by the time range of the tournament
        List<Tournament> tournamentsInTimeFrame = filteredTournaments.stream()
                .filter(tournament -> matchesTournamentTimeFrame(eventTime, tournament))
                .toList();

        if (tournamentsInTimeFrame.isEmpty()) {
            log.debug("No tournaments match time frame for event at {} for user [{}] and game [{}]. Session [{}]",
                    eventTime, player.getRemoteId(), gameCode, event.getSessionId());
            return Optional.empty();
        }

        if (tournamentsInTimeFrame.size() == 1) {
            return Optional.of(tournamentsInTimeFrame.getFirst());
        }

        // Let's try to select a RUNNING tournament
        List<Tournament> runningTournaments = tournamentsInTimeFrame.stream()
                .filter(tournament -> tournament.getStatus() == Tournament.TournamentStatus.RUNNING)
                .toList();

        if (runningTournaments.size() == 1) {
            log.error("Multiple tournaments matched for game [{}] and user [{}], but selected RUNNING tournament [{}] " +
                    "over ENDED ones for session [{}]",
                    gameCode, player.getRemoteId(), runningTournaments.getFirst().getId(), event.getSessionId());
            return Optional.of(runningTournaments.getFirst());
        }

        // If we failed with RUNNING, try the ENDED tournament
        List<Tournament> endedTournaments = tournamentsInTimeFrame.stream()
                .filter(tournament -> tournament.getStatus() == Tournament.TournamentStatus.ENDED)
                .toList();

        if (endedTournaments.size() == 1) {
            log.error("Multiple tournaments matched for game [{}] and user [{}], but selected ENDED tournament [{}] " +
                    "as no single RUNNING tournament found for session [{}]",
                    gameCode, player.getRemoteId(), endedTournaments.getFirst().getId(), event.getSessionId());
            return Optional.of(endedTournaments.getFirst());
        }

        String debugInfo = gameCodeToBrandMatches.entrySet().stream()
                .flatMap(entry -> entry.getValue().stream())
                .map(match -> "[" + match + "]")
                .collect(Collectors.joining(", "));

        log.error("Failed to assign event to any tournament for game [{}] and user [{}]. " +
                "Multiple tournaments matched after time frame filtering: {}. " +
                "ALL tournaments: {}, tournaments in time frame: {}, RUNNING tournaments: {}, ENDED tournaments: {}. " +
                "Cannot resolve collision - skipping event for session [{}]",
                gameCode, player.getRemoteId(), debugInfo,
                filteredTournaments.size(), tournamentsInTimeFrame.size(), runningTournaments.size(), endedTournaments.size(), event.getSessionId());

        return Optional.empty();
    }

    private record TournamentRoundInEvent(Tournament tournament, String gameCode, ComparableGameRound roundInEvent) {}

    /**
     * Rounds which matched to user's joined tournament and game, without any
     * tournament config filters applied to them. These are useful to store data
     * for BI and for debugging purposes
     */
    public record UnfilteredTournamentRounds(TournamentAccount player,
            Map<Tournament, List<ComparableGameRound>> unfilteredRounds) {}

    /**
     * Rounds which matched to user's joined tournament and game with tournament config
     * filters applied (to whatever extent possible). These will be a base for user's
     * score calculation
     */
    public record FilteredTournamentRounds(TournamentAccount player,
            Map<Tournament, List<TournamentGameRound>> filteredRounds) {}

    public record FilteredAndUnfilteredRounds(Map<Tournament, Set<ComparableGameRound>> filteredRounds,
            List<ComparableGameRound> unfilteredRounds) {}

    public record RoundsForPersisting(FilteredTournamentRounds filteredRounds,
            List<ComparableGameRound> unfilteredRounds) {}

    /**
     * Used to indicate in which tournaments scores were updated as a result of processing
     * a batch of game round events
     */
    public record UpdatedTournamentScores(TournamentAccount player, List<Tournament> tournaments) {}
}
