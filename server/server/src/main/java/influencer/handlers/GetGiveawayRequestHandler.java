package influencer.handlers;

import java.util.UUID;

import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;
import com.turbospaces.ebean.JpaManager;

import influencer.InfluencerIdentityManager;
import influencer.InfluencerServerProperties;
import influencer.api.v1.GetGiveawayRequest;
import influencer.api.v1.GetGiveawayRequestorInfo;
import influencer.api.v1.GetGiveawayResponse;
import influencer.dto.GiveawayDto;
import influencer.repo.AccountRepo;
import influencer.services.GiveawayService;
import influencer.services.Merger;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class GetGiveawayRequestHandler
        extends AbstractAuthAwareRequestHandler<GetGiveawayRequest, GetGiveawayResponse.Builder>
        implements ModifyHandler<GetGiveawayRequest, GetGiveawayResponse.Builder> {

    private final GiveawayService giveawayService;
    private final AccountRepo accountRepo;
    private final Merger<GetGiveawayResponse.Builder, GiveawayDto> giveawayResponseMerger;

    public GetGiveawayRequestHandler(
            InfluencerServerProperties props,
            JpaManager ebean,
            InfluencerIdentityManager sessionManager,
            GiveawayService giveawayService,
            AccountRepo accountRepo,
            Merger<GetGiveawayResponse.Builder, GiveawayDto> giveawayResponseMerger) {
        super(props, ebean, sessionManager);
        this.giveawayService = giveawayService;
        this.accountRepo = accountRepo;
        this.giveawayResponseMerger = giveawayResponseMerger;
    }

    @Override
    public void apply(TransactionalRequest<GetGiveawayRequest, GetGiveawayResponse.Builder> cmd) throws Throwable {
        var req = cmd.request();
        var reply = cmd.reply();
        var identity = req.getIdentity();
        var requestorAccountId = identity.getByToken().getAccountId();

        try (var tx = ebean.newReadOnlyTransaction()) {
            var giveawayOpt = giveawayService.findByCodeForRequestor(UUID.fromString(req.getCode()), requestorAccountId, tx);
            if (giveawayOpt.isPresent()) {
                var giveaway = giveawayOpt.get();
                reply = giveawayResponseMerger.merge(reply, giveaway);
                reply.setRequestorInfo(GetGiveawayRequestorInfo.newBuilder()
                        .setSubscriber(giveaway.getSubscriberRemoteIds().contains(requestorAccountId))
                        .setWinner(giveaway.getWinnerRemoteIds().contains(requestorAccountId)));
            }
        }
    }
}
