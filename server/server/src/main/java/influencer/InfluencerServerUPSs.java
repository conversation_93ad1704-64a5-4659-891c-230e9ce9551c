package influencer;


import com.google.common.collect.ImmutableSet;
import com.turbospaces.ups.UPSs;

import common.UPSsCollection;

public class InfluencerServerUPSs extends ImmutableSet.Builder<String> implements UPSsCollection {
    {
        //
        // ~ common
        //
        add(UPSs.INFRA_CORE);
        add(UPSs.INFRA_SERVER);
        add(UPSs.REDIS);

        //
        //  influencer specific
        //
        add(InfluencerProto.INFLUENCER_POSTGRES_OWNER);
        add(InfluencerProto.INFLUENCER_POSTGRES_APP);
    }
}

