package influencer.di;

import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;
import influencer.repo.BrandGiveawayPrizeConfigRepo;
import influencer.repo.BrandRepo;
import influencer.repo.InfluencerGiveawayPrizeConfigRepo;
import influencer.services.DefaultGiveawayPrizeConfigService;
import influencer.services.GiveawayPrizeConfigService;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafka;

import com.turbospaces.ebean.JpaManager;

import influencer.InfluencerServerProperties;
import influencer.dto.CreateCoinGiveawayPayload;
import influencer.dto.CreateFreeSpinGiveawayPayload;
import influencer.dto.GiveawayDto;
import influencer.handlers.AbstractRequestHandler;
import influencer.replication.listener.AccountCreateEventListener;
import influencer.repo.AccountReferralAggrRepo;
import influencer.repo.AccountReferralRepo;
import influencer.repo.AccountRepo;
import influencer.repo.BudgetSpendRepo;
import influencer.repo.GiveawayRepo;
import influencer.repo.GiveawaySubscriberRepo;
import influencer.repo.InfluencerRepo;
import influencer.repo.MilestoneSchemeRepo;
import influencer.repo.MilestoneStepRepo;
import influencer.repo.ReachedMilestoneStepRepo;
import influencer.services.CoinGiveawayMapper;
import influencer.services.DefaultGiveawayMapper;
import influencer.services.DefaultGiveawayService;
import influencer.services.DefaultInfluencerService;
import influencer.services.DefaultRafMilestoneService;
import influencer.services.DefaultRafService;
import influencer.services.DefaultSendGridEmailService;
import influencer.services.EmailService;
import influencer.services.FreeSpinGiveawayMapper;
import influencer.services.GetGiveawayResponseMerger;
import influencer.services.GetActiveGiveawayResponseMerger;
import influencer.services.GetActiveGiveawayByAccountResponseMerger;
import influencer.services.GiveawayCreatorService;
import influencer.services.CoinGiveawayCreatorService;
import influencer.services.FreeSpinGiveawayCreatorService;
import influencer.services.GiveawayService;
import influencer.services.InfluencerService;
import influencer.services.Merger;
import influencer.services.RafMilestoneService;
import influencer.services.RafService;
import influencer.services.validation.CreateCoinGiveawayPayloadValidator;
import influencer.services.validation.CreateFreeSpinGiveawayPayloadValidator;
import influencer.services.validation.GenericValidator;
import uam.api.UamServiceApi;
import influencer.api.v1.GetActiveGiveawayByAccountResponse;
import influencer.api.v1.GetActiveGiveawayResponse;
import influencer.api.v1.GetGiveawayResponse;

@Configuration
@Import({
        CommonInfluencerDiModule.class,
        InfluencerDatabaseDiModule.class,
        InfluencerKafkaDiModule.class,
        InfluencerServiceApiDiModule.class,
        InfluencerAcceptorsDiModule.class,
        InfluencerRepoDiModule.class
})
@ComponentScan(basePackageClasses = {
        AbstractRequestHandler.class,
        AccountCreateEventListener.class
})
@EnableKafka
public class InfluencerServerDiModule {

    @Bean
    public InfluencerService influencerService(
            JpaManager ebean,
            InfluencerServerProperties props,
            AccountRepo accountRepo,
            InfluencerRepo influencerRepo) {
        return new DefaultInfluencerService(ebean, props, accountRepo, influencerRepo);
    }

    @Bean
    public EmailService emailService(UamServiceApi uamServiceApi) {
        return new DefaultSendGridEmailService(uamServiceApi);
    }

    @Bean
    public GenericValidator<CreateCoinGiveawayPayload> createCoinGiveawayPayloadValidator() {
        return new CreateCoinGiveawayPayloadValidator();
    }

    @Bean
    public GenericValidator<CreateFreeSpinGiveawayPayload> createFreeSpinGiveawayPayloadValidator() {
        return new CreateFreeSpinGiveawayPayloadValidator();
    }

    @Bean
    public DefaultGiveawayMapper defaultGiveawayMapper() {
        return new DefaultGiveawayMapper();
    }

    @Bean
    public CoinGiveawayMapper coinGiveawayMapper() {
        return new CoinGiveawayMapper();
    }

    @Bean
    public FreeSpinGiveawayMapper freeSpinGiveawayMapper() {
        return new FreeSpinGiveawayMapper();
    }

    @Bean
    public GiveawayService giveawayService(
            GiveawayRepo giveawayRepo,
            GiveawaySubscriberRepo giveawaySubscriberRepo,
            BudgetSpendRepo budgetSpendRepo,
            AccountRepo accountRepo,
            DefaultGiveawayMapper giveawayMapper) {
        return new DefaultGiveawayService(
                giveawayRepo,
                giveawaySubscriberRepo,
                budgetSpendRepo,
                accountRepo,
                giveawayMapper);
    }

    @Bean
    public GiveawayCreatorService<CreateCoinGiveawayPayload> coinGiveawayCreatorService(
            JpaManager ebean,
            CoinGiveawayMapper coinGiveawayMapper,
            GenericValidator<CreateCoinGiveawayPayload> createCoinGiveawayPayloadValidator,
            InfluencerRepo influencerRepo,
            BudgetSpendRepo budgetSpendRepo,
            GiveawayRepo giveawayRepo) {
        return new CoinGiveawayCreatorService(
                ebean,
                createCoinGiveawayPayloadValidator,
                coinGiveawayMapper,
                influencerRepo,
                budgetSpendRepo,
                giveawayRepo);
    }

    @Bean
    public GiveawayCreatorService<CreateFreeSpinGiveawayPayload> freeSpinGiveawayCreatorService(
            JpaManager ebean,
            FreeSpinGiveawayMapper freeSpinGiveawayMapper,
            GenericValidator<CreateFreeSpinGiveawayPayload> createFreeSpinGiveawayPayloadValidator,
            InfluencerRepo influencerRepo,
            BudgetSpendRepo budgetSpendRepo,
            GiveawayRepo giveawayRepo) {
        return new FreeSpinGiveawayCreatorService(
                ebean,
                createFreeSpinGiveawayPayloadValidator,
                freeSpinGiveawayMapper,
                influencerRepo,
                budgetSpendRepo,
                giveawayRepo);
    }

    @Bean
    public GiveawayPrizeConfigService giveawayPrizeConfigService(
            BrandGiveawayPrizeConfigRepo brandGiveawayPrizeConfigRepo,
            BrandRepo brandRepo,
            InfluencerGiveawayPrizeConfigRepo influencerGiveawayPrizeConfigRepo,
            InfluencerRepo influencerRepo) {
        return new DefaultGiveawayPrizeConfigService(
                brandGiveawayPrizeConfigRepo,
                brandRepo,
                influencerGiveawayPrizeConfigRepo,
                influencerRepo);
    }

    @Bean
    public RafMilestoneService milestoneService(
            InfluencerServerProperties props,
            AccountRepo accountRepo,
            BrandRepo brandRepo,
            MilestoneSchemeRepo milestoneSchemeRepo,
            MilestoneStepRepo milestoneStepRepo,
            ReachedMilestoneStepRepo reachedMilestoneStepRepo,
            AccountReferralAggrRepo accountReferralAggrRepo) {
        return new DefaultRafMilestoneService(
                props,
                accountRepo,
                brandRepo,
                milestoneSchemeRepo,
                milestoneStepRepo,
                reachedMilestoneStepRepo,
                accountReferralAggrRepo);
    }

    @Bean
    public RafService rafService(
            AccountReferralRepo accountReferralRepo,
            AccountReferralAggrRepo accountReferralAggrRepo) {
        return new DefaultRafService(accountReferralRepo, accountReferralAggrRepo);
    }

    @Bean
    public Merger<GetGiveawayResponse.Builder, GiveawayDto> giveawayResponseMerger() {
        return new GetGiveawayResponseMerger();
    }

    @Bean
    public Merger<GetActiveGiveawayResponse.Builder, GiveawayDto> activeGiveawayResponseMerger() {
        return new GetActiveGiveawayResponseMerger();
    }

    @Bean
    public Merger<GetActiveGiveawayByAccountResponse.Builder, GiveawayDto> activeGiveawayByAccountResponseMerger() {
        return new GetActiveGiveawayByAccountResponseMerger();
    }

    @Bean
    public PlatformExecutorService platformExecutorService(
            InfluencerServerProperties props,
            MeterRegistry meterRegistry) {
        return new DefaultPlatformExecutorService(props, meterRegistry);
    }
}
