package influencer.services;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import api.v1.ApplicationException;
import io.ebean.Transaction;
import influencer.dto.GiveawayDto;
import influencer.model.InfluencerAccount;
import influencer.model.GiveawaySubscriber;
import influencer.services.exception.MappingException;
import influencer.services.exception.ValidationException;

public interface GiveawayService {

    Optional<GiveawayDto> findByCode(UUID giveawayCode, Transaction tx) throws ValidationException, MappingException;

    Optional<GiveawayDto> findByCodeForRequestor(UUID giveawayCode, long requestorAccountId, Transaction tx) throws ValidationException, MappingException;

    Optional<GiveawayDto> findByStatusActiveAndOwnerRemoteId(Long ownerRemoteId, Transaction tx) throws ValidationException, MappingException;

    void optInGiveaway(UUID giveawayCode, InfluencerAccount account, Transaction tx) throws ValidationException;

    GiveawayDto finalizeGiveawayV2(UUID giveawayCode, long accountId, Transaction tx)
            throws ValidationException, MappingException;

    GiveawayDto cancelGiveaway(UUID giveawayCode, long accountId, Transaction tx)
            throws ValidationException, ApplicationException, MappingException;

    List<GiveawaySubscriber> chooseAndMarkWinners(List<GiveawaySubscriber> subscribers, int numberOfWinners);

    int getNumberOfSubscribers(UUID giveawayCode, Transaction tx) throws ValidationException;

    int getNumberOfGiveawaysCreatedToday(long influencerId, Transaction tx);

    BigDecimal getBudgetSpendCurrentWeek(long influencerId, Transaction tx);

    boolean isUserOptedInToGiveaway(UUID giveawayCode, long accountId, Transaction tx);
}
