package influencer.services;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.UUID;
import influencer.InfluencerServerProperties;
import influencer.dto.MilestoneStepDto;
import influencer.model.AccountReferralAggr;
import influencer.model.InfluencerAccount;
import influencer.model.MilestoneScheme;
import influencer.model.MilestoneStep;
import influencer.model.ReachedMilestoneStep;
import influencer.repo.AccountReferralAggrRepo;
import influencer.repo.AccountRepo;
import influencer.repo.BrandRepo;
import influencer.repo.MilestoneSchemeRepo;
import influencer.repo.MilestoneStepRepo;
import influencer.repo.ReachedMilestoneStepRepo;
import influencer.services.exception.BrandNotFoundException;
import influencer.services.exception.MilestoneSchemaNotFoundException;
import influencer.services.exception.MilestoneStepNotFoundException;
import influencer.services.exception.ValidationException;
import io.ebean.DuplicateKeyException;
import io.ebean.Transaction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
public class DefaultRafMilestoneService implements RafMilestoneService {

    public static final long MILLIS_PER_DAY = 86_400_000L;

    private final InfluencerServerProperties props;
    private final AccountRepo accountRepo;
    private final BrandRepo brandRepo;
    private final MilestoneSchemeRepo milestoneSchemeRepo;
    private final MilestoneStepRepo milestoneStepRepo;
    private final ReachedMilestoneStepRepo reachedMilestoneStepRepo;
    private final AccountReferralAggrRepo accountReferralAggrRepo;

    @Override
    public MilestoneScheme createMilestoneSchema(String brandName, Transaction tx) throws ValidationException {
        var brand = brandRepo.findByName(brandName, tx).orElseThrow(BrandNotFoundException::new);
        var schema = new MilestoneScheme().setBrand(brand);
        return milestoneSchemeRepo.save(schema, tx);
    }

    @Override
    public MilestoneScheme activateMilestoneSchema(Long schemaId, Transaction tx) throws ValidationException {
        var schema = milestoneSchemeRepo.findById(schemaId, tx).orElseThrow(MilestoneSchemaNotFoundException::new);
        var steps = milestoneStepRepo.findBySchemaId(schemaId, tx);

        if (steps == null || steps.isEmpty()) {
            throw new ValidationException("Cannot publish milestone schema without steps defined");
        }

        var schemasToDeactivate = milestoneSchemeRepo.findByBrandIdAndActive(schema.getBrand().getId(), true, tx)
                .stream()
                .map(it -> it.setActive(false))
                .toList();
        milestoneSchemeRepo.saveAll(schemasToDeactivate, tx);

        schema.setPublished(true);
        schema.setActive(true);
        milestoneSchemeRepo.save(schema.setPublished(true).setActive(true), tx);

        return schema;
    }

    @Override
    public MilestoneStep createMilestoneStep(Long schemaId, int qualifiedChildrenNumber, String rewardCode, Transaction tx) throws ValidationException {
        var schema = milestoneSchemeRepo.findById(schemaId, tx).orElseThrow(MilestoneSchemaNotFoundException::new);

        if (schema.isPublished()) {
            throw new ValidationException("Cannot add step to published milestone schema");
        }

        var step = new MilestoneStep()
                .setSchema(schema)
                .setQualifiedChildrenNumber(qualifiedChildrenNumber)
                .setRewardCode(UUID.fromString(rewardCode));

        try {
            milestoneStepRepo.save(step, tx);
        } catch (DuplicateKeyException _) {
            throw new ValidationException("Step with the same reward code already exists in the schema");
        }
        return step;
    }

    @Override
    public MilestoneStep updateMilestoneStep(Long schemaId, Long stepId, int qualifiedChildrenNumber, String rewardCode, Transaction tx) throws ValidationException {
        var step = milestoneStepRepo.findById(stepId, tx).orElseThrow(MilestoneStepNotFoundException::new);
        var schema = step.getSchema();

        if (!schemaId.equals(schema.getId())) {
            throw new ValidationException("Step does not belong to the specified schema");
        }
        if (schema.isPublished()) {
            throw new ValidationException("Cannot add step to published milestone schema");
        }

        step.setQualifiedChildrenNumber(qualifiedChildrenNumber)
            .setRewardCode(UUID.fromString(rewardCode));
        try {
            milestoneStepRepo.save(step, tx);
        } catch (DuplicateKeyException _) {
            throw new ValidationException("Step with the same reward code already exists in the schema");
        }
        return step;
    }

    @Override
    public SortedSet<MilestoneStepDto> getPersonalizedMilestoneSchema(long accountId) throws ValidationException {
        log.info("[{}] Starting to build personalized milestone schema", accountId);

        var account = accountRepo.findById(accountId)
                .orElseThrow(() -> new ValidationException());
        log.debug("[{}] Found account with brand: {}", accountId, account.getBrand().getName());

        var brand = account.getBrand();
        var schemas = milestoneSchemeRepo.findByBrandIdAndActive(brand.getId(), true);
        log.debug("[{}] Found {} active milestone schemas for brand {}", accountId, schemas.size(), brand.getName());

        List<MilestoneStep> steps = List.of();
        if(schemas.isEmpty()) {
            log.info("[{}] Can't build personalized milestone schema. There is no active milestone schema for {}.", accountId, brand.getName());
        } else {
            var schema = schemas.getFirst();
            steps = milestoneStepRepo.findBySchemaIdAndOrderById(schema.getId());
            log.debug("[{}] Found {} milestone steps in schema {}", accountId, steps.size(), schema.getId());
        }

        var numberOfQualifiedReferredAccounts = accountReferralAggrRepo.findByAccountId(accountId)
                .map(AccountReferralAggr::getQualifiedLvl1)
                .orElse(0);
        log.debug("[{}] Account has {} qualified referred accounts", accountId, numberOfQualifiedReferredAccounts);

        var reachedSteps = reachedMilestoneStepRepo.findByAccountIdOrderedById(accountId).stream()
                .map(it -> new MilestoneStepDto(
                        it.getId(),
                        Optional.ofNullable(it.getStep()).map(MilestoneStep::getQualifiedChildrenNumber).orElse(0),
                        it.getCreatedAt(),
                        it.getClaimedAt(),
                        it.getExpiresAt()))
                .toList();
        log.debug("[{}] Found {} reached milestone steps", accountId, reachedSteps.size());

        // Start with all passed steps
        var result = new TreeSet<MilestoneStepDto>(reachedSteps);

        // Add only future steps that exceed the number of already qualified referred accounts
        for (var step : steps) {
            if (step.getQualifiedChildrenNumber() > numberOfQualifiedReferredAccounts) {
                result.add(new MilestoneStepDto(step.getQualifiedChildrenNumber()));
            }
        }
        log.info("[{}] Built personalized milestone schema with {} total steps ({} reached, {} future)", 
                accountId, result.size(), reachedSteps.size(), result.size() - reachedSteps.size());

        return result;
    }

    @Override
    public void registerReachedMilestoneStepIfQualified(InfluencerAccount account, int numberOfQualifiedReferredAccounts) {
        var accountId = account.getId();
        var brand = account.getBrand();
        var schemas = milestoneSchemeRepo.findByBrandIdAndActive(brand.getId(), true);
        log.debug("[{}] Found {} active milestone schemas for brand {}", accountId, schemas.size(), brand.getName());

        List<MilestoneStep> steps = List.of();
        if(schemas.isEmpty()) {
            log.info("[{}] Cannot register reached milestone steps: no active milestone schema for brand '{}'.", accountId, brand.getName());
        } else {
            var schema = schemas.getFirst();
            steps = milestoneStepRepo.findBySchemaIdAndOrderById(schema.getId());
            log.debug("[{}] Found {} milestone steps in schema {}", accountId, steps.size(), schema.getId());
        }

        for (var step : steps) {
            if (numberOfQualifiedReferredAccounts == step.getQualifiedChildrenNumber()) {
                var rewardTtlInDays = props.REACHED_MILESTONE_STEP_REWARD_TTL_IN_DAYS.get().longValue();
                var rewardTtlMs = rewardTtlInDays * MILLIS_PER_DAY;
                var now = System.currentTimeMillis();
                var reachedStep = new ReachedMilestoneStep()
                        .setAccount(account)
                        .setStep(step)
                        .setExpiresAt(new Date(now + rewardTtlMs));
                reachedMilestoneStepRepo.save(reachedStep);
                log.info("[{}] Registered reached milestone step: qualifiedChildrenNumber={}", accountId, step.getQualifiedChildrenNumber());
                return;
            }
        }
    }


    @Override
    public Optional<ReachedMilestoneStep> findAndValidateStepForClaiming(Long accountId, Long milestoneStepId, Transaction tx) {
        var reachedStepsOrdered = reachedMilestoneStepRepo.findByAccountIdAndClaimedNullOrderedById(accountId, tx);
        var currentTime = new Date();

        log.debug("[{}] Found {} milestone steps for account", accountId, reachedStepsOrdered.size());

        if (reachedStepsOrdered.isEmpty()) {
            log.info("[{}] All milestone steps are already claimed for account", accountId);
            return Optional.empty();
        }

        ReachedMilestoneStep stepToClaim = null;
        ReachedMilestoneStep earliestStep = null;

        for (ReachedMilestoneStep step : reachedStepsOrdered) {
            if (isStepExpired(step, currentTime)) {
                log.debug("[{}] Skipping expired step: {}", accountId, step.getId());
                continue;
            }

            if (earliestStep == null) {
                earliestStep = step;
            } else if (step.getCreatedAt().before(earliestStep.getCreatedAt())) {
                earliestStep = step;
            }

            if (step.getId().equals(milestoneStepId)) {
                stepToClaim = step;
            }
        }

        if (stepToClaim == null) {
            log.info("[{}] Step {} is not available to claim for account", accountId, milestoneStepId);
            return Optional.empty();
        }

        if (stepToClaim.getId() > earliestStep.getId()) {
            log.info("[{}] Step {} cannot be claimed because step {} must be claimed first for account",
                    accountId, milestoneStepId, earliestStep.getId());
            return Optional.empty();
        }

        if (isStepExpired(stepToClaim, currentTime)) {
            log.info("[{}] Step {} cannot be claimed because it is expired", accountId, milestoneStepId);
            return Optional.empty();
        }

        return Optional.of(stepToClaim);
    }

    @Override
    public void markMilestoneStepAsClaimed(ReachedMilestoneStep step, Transaction tx) {
        var now = new Date();
        step.setClaimedAt(now);
        reachedMilestoneStepRepo.save(step, tx);
        log.info("[{}] Milestone step {} was successfully claimed at {}", step.getAccount().getId(), step.getId(), now);
    }

    private boolean isStepExpired(ReachedMilestoneStep step, Date currentTime) {
        return step.getExpiresAt() != null && step.getExpiresAt().before(currentTime);
    }
}
