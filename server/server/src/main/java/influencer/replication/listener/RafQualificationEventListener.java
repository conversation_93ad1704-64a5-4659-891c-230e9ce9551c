package influencer.replication.listener;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import influencer.model.InfluencerAccount;
import influencer.repo.AccountRepo;
import influencer.repo.BrandRepo;
import influencer.services.RafMilestoneService;
import influencer.services.RafService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import uam.api.v1.internal.RafQualificationEvent;

@Slf4j
@Service
@RequiredArgsConstructor
public class RafQualificationEventListener implements EventListener<RafQualificationEvent> {

    private final BrandRepo brandRepo;
    private final AccountRepo accountRepo;
    private final RafService rafService;
    private final RafMilestoneService milestoneService;

    public void doOnEvent(RafQualificationEvent event) {
        log.trace("IN RafQualificationEvent {}", event);

        var referrer = event.getReferrer();
        var referrerBrand = referrer.getBrandName();
        var referred = event.getReferred();
        var referredBrand = referred.getBrandName();

        if (StringUtils.isNotBlank(referrerBrand) && !referrerBrand.equals(referredBrand)) {
            log.warn("[{}:{}] Skipping RAF qualification: different brands ({}:{})", 
                referrer.getId(), 
                referred.getId(),
                referrerBrand, 
                referredBrand);
            return;
        }

        var brandOpt = brandRepo.findByName(referrerBrand);
        if (brandOpt.isEmpty()) {
            log.warn("[{}:{}] Skipping RAF qualification: brand {} not found",
                    referrer.getId(),
                    referred.getId(),
                    referrerBrand);
            return;
        }

        var brand = brandOpt.get();
        log.debug("Processing RAF qualification for brand {}", brand.getName());

        var referrerAccount = accountRepo.findByRemoteId(referrer.getId())
                .orElseGet(() -> {
                    var account = new InfluencerAccount();
                    account.setRemoteId(referrer.getId());
                    account.setBrand(brand);
                    account.setHash(referrer.getRoutingKey());
                    account.setActive(true);
                    var saved = accountRepo.save(account);
                    log.info("[{}] Created new referrer account", saved.getId());
                    return saved;
                });

        var referredAccount = accountRepo.findByRemoteId(referred.getId())
                .orElseGet(() -> {
                    var account = new InfluencerAccount();
                    account.setRemoteId(referred.getId());
                    account.setBrand(brand);
                    account.setHash(referred.getRoutingKey());
                    account.setActive(true);
                    var saved = accountRepo.save(account);
                    log.info("[{}] Created new referred account", saved.getId());
                    return saved;
                });
            
        var accountReferralAggrOpt = rafService.registerPurchaseQualification(referrerAccount, referredAccount, event.getQualificationLevel());
        if (accountReferralAggrOpt.isPresent() && event.getQualificationLevel() == 1) {
            milestoneService.registerReachedMilestoneStepIfQualified(referrerAccount, accountReferralAggrOpt.get().getQualifiedLvl1());
        }           
    }
}
