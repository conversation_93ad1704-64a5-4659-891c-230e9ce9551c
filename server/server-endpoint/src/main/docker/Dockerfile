FROM bellsoft/liberica-runtime-container:jdk-${maven.compiler.source}-slim-glibc

ENV ENV_PORT=${PORT:-8089}
ENV ENV_YOURKIT_PORT=${YOURKIT_PORT:-10000}
ENV ENV_JGROUPS_PORT=${JGROUPS_PORT:-6900}
ENV ENV_INDEX=${INDEX:-0}
ENV ENV_JAVA_OPTIONS="${jvm.gc.options}"
ENV ENV_JAVA_OPTIONS_COMMON="${jvm.common.options}"
ENV ENV_JMX_OPTIONS="${jvm.jmx.options}"
ENV ENV_HEAPDUMPPATH=${HEAPDUMPPATH:-/logs/dump.hprof}

ADD ${project.artifactId}-${project.version}-exec.jar /workspace/uber.jar

#  ENV ENV_YOURKIT_VERSION=${YOURKIT_VERSION:-2022.3}
#  RUN wget https://www.yourkit.com/download/docker/YourKit-JavaProfiler-${ENV_YOURKIT_VERSION}-docker.zip -P /tmp/ && unzip /tmp/YourKit-JavaProfiler-${ENV_YOURKIT_VERSION}-docker.zip -d /workspace && rm /tmp/YourKit-JavaProfiler-${ENV_YOURKIT_VERSION}-docker.zip
#  ENV ENV_YOURKIT_OPTIONS="-agentpath:/workspace/YourKit-JavaProfiler-${ENV_YOURKIT_VERSION}/bin/linux-x86-64/libyjpagent.so=port=$ENV_YOURKIT_PORT,disablealloc,disablenatives,disablestacktelemetry,exceptions=disable,listen=all"

CMD ["/bin/sh", "-c", "java -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${ENV_HEAPDUMPPATH} -Duser.timezone=UTC -Dorg.springframework.boot.logging.LoggingSystem=none -Djgroups.bind_port=$ENV_JGROUPS_PORT -Dcloud.application.host=$HOSTNAME -Dcloud.application.port=$ENV_PORT -Dcloud.application.instance_index=$ENV_INDEX ${jvm.unsafe.options} $ENV_JAVA_OPTIONS $ENV_JAVA_OPTIONS_COMMON -jar /workspace/uber.jar"]