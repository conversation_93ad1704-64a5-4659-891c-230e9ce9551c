package engagement;

import static loyalty.LoyaltyProto.LOYALTY_POSTGRES_OWNER;
import static offerchain.OfferChainProto.OFFERCHAIN_POSTGRES_OWNER;
import static quest.QuestProto.QUEST_POSTGRES_OWNER;
import static randomreward.RandomRewardProto.RANDOM_REWARD_POSTGRES_OWNER;
import static reward.RewardProto.REWARD_POSTGRES_OWNER;

import java.util.regex.Pattern;

import loyalty.LoyaltyEntities;
import offerchain.OfferChainEntities;
import org.flywaydb.core.api.configuration.FluentConfiguration;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.GcpCloudConnector;
import org.springframework.cloud.SmartCloudConnector;
import org.springframework.cloud.WildcardUPSFilter;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.cloud.service.common.RedisServiceInfo;
import org.springframework.util.ResourceUtils;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.turbospaces.api.CommonTopics;
import com.turbospaces.api.Topic;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.EngagementApplicationConfig;
import com.turbospaces.healthcheck.KafkaHealthCheck;
import com.turbospaces.healthcheck.RedisHealthCheck;
import com.turbospaces.plugins.FlywayBootstrapInitializer;
import com.turbospaces.plugins.KafkaBootstrapInitializer;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import engagement.di.EngagementServerDiModule;
import loyalty.LoyaltyServerProperties;
import loyalty.LoyaltyServerUPSs;
import loyalty.LoyaltyServerWildcardUPSs;
import loyalty.api.LoyaltyTopics;
import loyalty.di.LoyaltyEmbeddedServerDiModule;
import model.Schemas;
import offerchain.OfferChainServerProperties;
import offerchain.OfferChainServerUPSs;
import offerchain.OfferChainServerWildcardUPSs;
import offerchain.api.OfferChainTopics;
import offerchain.di.OfferChainEmbeddedServerDiModule;
import quest.QuestServerProperties;
import quest.QuestServerUPSs;
import quest.QuestServerWildcardUPSs;
import quest.di.QuestEmbeddedServerDiModule;
import randomreward.RandomRewardEntities;
import randomreward.RandomRewardServerProperties;
import randomreward.RandomRewardServerUPSs;
import randomreward.RandomRewardServerWildcardUPSs;
import randomreward.api.RandomRewardTopics;
import randomreward.di.RandomRewardServerDiModule;
import reward.RewardEntities;
import reward.RewardServerProperties;
import reward.RewardServerUPSs;
import reward.RewardServerWildcardUPSs;
import reward.RewardTopics;
import reward.di.RewardServerDiModule;

public class EngagementServerStarter {
    public static void main(String[] args) throws Throwable {
        var upsList = ImmutableSet.<String>builder()
                .addAll(new EngagementServerUPSs().build())
                .addAll(new LoyaltyServerUPSs().build())
                .addAll(new RewardServerUPSs().build())
                .addAll(new QuestServerUPSs().build())
                .addAll(new OfferChainServerUPSs().build())
                .addAll(new RandomRewardServerUPSs().build())
                .build();
        var wildcardList = ImmutableSet.<Pattern>builder()
                .addAll(new EngagementServerWildcardUPSs().build())
                .addAll(new LoyaltyServerWildcardUPSs().build())
                .addAll(new RewardServerWildcardUPSs().build())
                .addAll(new QuestServerWildcardUPSs().build())
                .addAll(new OfferChainServerWildcardUPSs().build())
                .addAll(new RandomRewardServerWildcardUPSs().build())
                .build();
        run(new EngagementApplicationConfig(), new GcpCloudConnector(new WildcardUPSFilter(upsList, wildcardList)), args);
    }

    public static SimpleBootstrap run(ApplicationConfig cfg, SmartCloudConnector connector, String[] args) throws Throwable {
        SimpleBootstrap bootstrap = bootstrap(cfg,  connector);
        bootstrap.run(args); // ~ start
        return bootstrap;
    }

    public static SimpleBootstrap bootstrap(ApplicationConfig cfg, SmartCloudConnector connector) throws Throwable {
        EngagementServerProperties props = new EngagementServerProperties(cfg.factory());
        cfg.loadDefaultPropsFromResource(ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "engagement-server.properties"));
        cfg.setDefaultProperty(props.KAFKA_MAX_POLL_CONCURRENCY.getKey(), 1);
        cfg.setDefaultProperty(props.KAFKA_COMPRESSION_TYPE.getKey(), "lz4");
        cfg.setDefaultProperty(props.KAFKA_DO_NOT_QUEUE_PER_READ_ONLY_ACTOR.getKey(), true);
        cfg.setDefaultProperty(props.QUARTZ_JOBSTORE_IS_CLUSTERED.getKey(), true);
        cfg.setDefaultProperty(props.APP_NOTIFY_TOPIC.getKey(), EngagementTopics.NOTIFY.name().toString());

        SimpleBootstrap bootstrap = new SimpleBootstrap(
                props,
                connector,
                EngagementServerDiModule.class,
                LoyaltyEmbeddedServerDiModule.class,
                QuestEmbeddedServerDiModule.class,
                OfferChainEmbeddedServerDiModule.class,
                RewardServerDiModule.class,
                RandomRewardServerDiModule.class
        );
        DynamicCloud cloud = bootstrap.cloud();

        PostgresqlServiceInfo ocposi = UPSs.findRequiredServiceInfoByName(cloud, OFFERCHAIN_POSTGRES_OWNER);
        PostgresqlServiceInfo lposi = UPSs.findRequiredServiceInfoByName(cloud, LOYALTY_POSTGRES_OWNER);
        PostgresqlServiceInfo rposi = UPSs.findRequiredServiceInfoByName(cloud, REWARD_POSTGRES_OWNER);
        PostgresqlServiceInfo qposi = UPSs.findRequiredServiceInfoByName(cloud, QUEST_POSTGRES_OWNER);
        PostgresqlServiceInfo rrposi = UPSs.findRequiredServiceInfoByName(cloud, RANDOM_REWARD_POSTGRES_OWNER);
        KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
        RedisServiceInfo rsi = UPSs.findRequiredServiceInfoByName(cloud, UPSs.REDIS);

        //
        // ~ auto create topics (configuration part)
        //
        var topics = createTopics(props);
        for (Topic topic : topics) {
            topic.configure(cfg);
        }

        //
        // ~ health checks
        //
        registerHealthchecks(bootstrap, topics, rsi);

        //
        // ~ Extensions
        //
        bootstrap.addBootstrapRegistryInitializer(new KafkaBootstrapInitializer(bootstrap.keyStore(), props, ksi, topics.toArray(new Topic[0])));

        bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, () -> "db/offerchain-migration",  ocposi, new OfferChainEntities(), Schemas.OFFER_CHAIN) {
            @Override
            protected void configureMigration(FluentConfiguration config) {
                super.configureMigration(config);

                var fragement = new OfferChainServerProperties(cfg.factory());
                ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                m.put(fragement.OFFERCHAIN_PARTITIONING_DEFAULT_PRECISION.getKey(), fragement.OFFERCHAIN_PARTITIONING_DEFAULT_PRECISION.get());
                m.put(fragement.OFFERCHAIN_PARTITIONING_DEFAULT_SCALE.getKey(), Integer.toString(fragement.OFFERCHAIN_PARTITIONING_DEFAULT_SCALE.get()));

                config.placeholders(m.build());
                config.baselineOnMigrate(true);
                config.baselineVersion("0");
            }
        });
        bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, () -> "db/loyalty-migration", lposi, new LoyaltyEntities(), Schemas.LOYALTY) {
            @Override
            protected void configureMigration(FluentConfiguration config) {
                super.configureMigration(config);

                var fragement = new LoyaltyServerProperties(cfg.factory());
                ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                m.put(fragement.PARTITIONING_DEFAULT_LOYALTY_PRECISION.getKey(), fragement.PARTITIONING_DEFAULT_LOYALTY_PRECISION.get());
                m.put(fragement.PARTITIONING_DEFAULT_LOYALTY_SCALE.getKey(), Integer.toString(fragement.PARTITIONING_DEFAULT_LOYALTY_SCALE.get()));

                config.placeholders(m.build());
            }
        });
        bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, () -> "db/reward-migration", rposi, new RewardEntities(), Schemas.REWARD) {
            @Override
            protected void configureMigration(FluentConfiguration config) {
                super.configureMigration(config);

                var fragment = new RewardServerProperties(cfg.factory());
                ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                m.put(fragment.PARTITIONING_DEFAULT_REWARD_PRECISION.getKey(), fragment.PARTITIONING_DEFAULT_REWARD_PRECISION.get());
                m.put(fragment.PARTITIONING_DEFAULT_REWARD_SCALE.getKey(), Integer.toString(fragment.PARTITIONING_DEFAULT_REWARD_SCALE.get()));

                config.placeholders(m.build());
                config.baselineOnMigrate(true);
                config.baselineVersion("0");
            }
        });
        bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, () -> "db/questline-migration", qposi, new QuestEntities(), Schemas.QUESTLINE) {
            @Override
            protected void configureMigration(FluentConfiguration config) {
                super.configureMigration(config);

                var fragment = new QuestServerProperties(cfg.factory());
                ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                m.put(fragment.PARTITIONING_DEFAULT_QUEST_PRECISION.getKey(), fragment.PARTITIONING_DEFAULT_QUEST_PRECISION.get());
                m.put(fragment.PARTITIONING_DEFAULT_QUEST_SCALE.getKey(), Integer.toString(fragment.PARTITIONING_DEFAULT_QUEST_SCALE.get()));

                config.placeholders(m.build());
                config.baselineOnMigrate(true);
                config.baselineVersion("0");
            }
        });
        bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, () -> "db/random-reward-migration", rrposi, new RandomRewardEntities(), Schemas.RANDOM_REWARD) {
            @Override
            protected void configureMigration(FluentConfiguration config) {
                super.configureMigration(config);

                var rrprops = new RandomRewardServerProperties(cfg.factory());
                ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                m.put(rrprops.PARTITIONING_DEFAULT_RANDOM_REWARD_PRECISION.getKey(), rrprops.PARTITIONING_DEFAULT_RANDOM_REWARD_PRECISION.get());
                m.put(rrprops.PARTITIONING_DEFAULT_RANDOM_REWARD_SCALE.getKey(), Integer.toString(rrprops.PARTITIONING_DEFAULT_RANDOM_REWARD_SCALE.get()));

                config.placeholders(m.build());
                config.baselineOnMigrate(true);
                config.baselineVersion("0");
            }
        });

        return bootstrap;
    }

    public static ImmutableList<Topic> createTopics(EngagementServerProperties props) {
        return ImmutableList.<Topic> builder()
                .add(LoyaltyTopics.REQ)
                .add(LoyaltyTopics.READ_REQ)
                .add(RewardTopics.REQ)
                .add(RewardTopics.READ_REQ)
                .add(RewardTopics.REWARD_INTEGRATION_SINK_DEADLETTER)
                .add(QuestTopics.REQ)
                .add(QuestTopics.READ_REQ)
                .add(OfferChainTopics.REQ)
                .add(OfferChainTopics.READ_REQ)
                .add(OfferChainTopics.OFFERCHAIN_SINK_DEADLETTER)
                .add(RandomRewardTopics.REQ)
                .add(RandomRewardTopics.READ_REQ)
                .addAll(CommonTopics.asNotifies(props))
                .add(CommonTopics.asEvents(props))
                .build();
    }

    public static void registerHealthchecks(SimpleBootstrap bootstrap, ImmutableList<Topic> topics, RedisServiceInfo rsi) throws Exception {
        KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.KAFKA);
        HealthCheckRegistry hltr = bootstrap.healthCheckRegistry();
        hltr.register("kafka-check", new KafkaHealthCheck(ksi, bootstrap.keyStore(), topics.toArray(new Topic[0])));
        hltr.register("redis-check", new RedisHealthCheck(rsi));
    }
}
