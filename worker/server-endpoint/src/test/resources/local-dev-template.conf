cloud {
  application {
    space_name = kiev1-dev
  }
}

service.jwt.uri="3AvKNCEPYiZocTVbvDOZiNt3zouw2flsmKJPUBxLXA09BXIF29nG69KaSyEneT1X"

service.postgres-slave-read-only.uri = "postgres://app_user:app_user@127.0.0.1:5432/defaultdb"
service.kafka.uri = "kafka://127.0.0.1:9092"
service.redis.uri = "redis://localhost:6379"

service.engagement-crm-postgres-owner.uri = "postgres://app_owner:app_owner@127.0.0.1:5432/engagement"
service.engagement-crm-postgres-app.uri = "postgres://app_user:app_user@127.0.0.1:5432/engagement"
service.engagement-postgres-slave-read-only.uri = "postgres://app_user:app_user@127.0.0.1:5432/engagement"
service.engagement-crm-quartz-app.uri = "postgres://quartz_user:quartz_user@127.0.0.1:5432/engagement"

service.loyalty-postgres-app.uri = "postgres://app_user:app_user@127.0.0.1:5432/engagement"
service.loyalty-postgres-owner.uri = "postgres://app_owner:app_owner@127.0.0.1:5432/engagement"
service.loyalty-postgres-slave-read-only.uri = "postgres://app_user:app_user@127.0.0.1:5432/engagement"

service.quest-postgres-app.uri = "postgres://app_user:app_user@127.0.0.1:5432/engagement"
service.quest-postgres-owner.uri = "postgres://app_owner:app_owner@127.0.0.1:5432/engagement"
service.quest-postgres-slave-read-only.uri = "postgres://app_user:app_user@127.0.0.1:5432/engagement"

service.offerchain-postgres-app.uri = "postgres://app_user:app_user@127.0.0.1:5432/engagement"
service.offerchain-postgres-owner.uri = "postgres://app_owner:app_owner@127.0.0.1:5432/engagement"
service.offerchain-postgres-slave-read-only.uri = "postgres://app_user:app_user@127.0.0.1:5432/engagement"
service.offerchain-quartz-app.uri = "postgres://quartz_user:quartz_user@127.0.0.1:5432/engagement"

service.random-reward-postgres-owner.uri = "postgres://app_owner:app_owner@127.0.0.1:5432/engagement"
service.random-reward-postgres-app.uri = "postgres://app_user:app_user@127.0.0.1:5432/engagement"
service.random-reward-postgres-slave-read-only.uri = "postgres://app_user:app_user@127.0.0.1:5432/engagement"

offer-chain-instances.create.rate.limit.count=5
offer-chain-instances.create.rate.limit.timeout=PT30S

#
# system
#
app.data.start-clean=false
app.shutdown-hook.enabled=false

#
# kafka
#
kafka.system-exit.on-queue-full=false
kafka.nack.on-queue-full=false

#
# Jobs
#
quartz.enforce-disabled-jobs.enabled=false

#
# spring
#
spring.output.ansi.enabled=detect

# bloomreach
service.pulsz-bloomreach.uri = "https://yt1b4tfxktaxmserkypcwpf6kj4p1kgvi0lumkweqiwwb5inoracn4s8gsq9abrb:<EMAIL>?projectToken=64c24144-7545-11ec-9bce-1e40b090f5ed&apiToken=cc80d533-b9ca-4c66-b1dc-9a121c4506f9"

# flows is used instead of bloomreach for some partners
service.pulsz-flows.uri = "https://user_api_flows:<EMAIL>"