package bi.flows;

import static io.github.resilience4j.core.IntervalFunction.ofExponentialBackoff;

import java.io.InputStream;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

import org.apache.http.HttpStatus;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.clients.consumer.Consumer;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.support.Acknowledgment;

import com.google.common.collect.LinkedListMultimap;
import com.google.common.collect.Multimap;
import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.AsyncSerialContextWorker;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.sink.AbstractSink;
import com.turbospaces.mdc.MdcUtil;
import com.turbospaces.metrics.MetricTags;
import com.turbospaces.metrics.Metrics;

import bi.flows.sink.AccountFieldUpdateFlowsReplicator;
import bi.flows.sink.AccountUpdateFlowsReplicator;
import bi.flows.sink.EmailVerificationFlowsReplicator;
import bi.flows.sink.GameplayFlowsReplicator;
import bi.flows.sink.InviteAcceptedFlowsReplicator;
import bi.flows.sink.LowBalanceFlowsReplicator;
import bi.flows.sink.OptOutFlowsReplicator;
import bi.flows.sink.PhoneNumberVerificationFlowsReplicator;
import bi.flows.sink.PurchaseFlowsReplicator;
import bi.flows.sink.RedeemConfirmFlowsReplicator;
import bi.flows.sink.RedeemMoneyFlowsReplicator;
import bi.flows.sink.RedeemStatusUpdateFlowsReplicator;
import bi.flows.sink.SignInFlowsReplicator;
import bi.flows.sink.SignUpFlowsReplicator;
import bi.flows.sink.SinkReplicator;
import fraud.api.v1.PhoneNumberVerificationEvent;
import io.github.resilience4j.core.IntervalFunction;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;
import io.netty.util.AsciiString;
import payment.api.v1.PaymentOrderCompletedEvent;
import payment.api.v1.RedeemConfirmEvent;
import payment.api.v1.RedeemStatusUpdateEvent;
import reactor.core.publisher.Flux;
import uam.api.v1.internal.AccountCreateEvent;
import uam.api.v1.internal.AccountFieldUpdateEvent;
import uam.api.v1.internal.AccountLowBalanceEvent;
import uam.api.v1.internal.AccountSignInEvent;
import uam.api.v1.internal.AccountUpdateEvent;
import uam.api.v1.internal.EmailVerificationEvent;
import uam.api.v1.internal.GameplayTransactionsEvent;
import uam.api.v1.internal.InviteAcceptedEvent;
import uam.api.v1.internal.OptOutArbitrationEvent;
import uam.api.v1.internal.RedeemMoneyEvent;

public class FlowsSink extends AbstractSink {
    private final Tag sinkNameTag = Tag.of(MetricTags.NAME, getClass().getSimpleName());

    private final AtomicReference<Semaphore> throttler = new AtomicReference<>(new Semaphore(Short.MAX_VALUE));
    private final RetryRegistry retryRegistry;
    private final MeterRegistry meterRegistry;
    private final DefaultPlatformExecutorService executor;
    private final ScheduledExecutorService scheduler;
    private final SignUpFlowsReplicator signUpReplicator;
    private final SignInFlowsReplicator signInReplicator;
    private final EmailVerificationFlowsReplicator emailVerificationReplicator;
    private final PhoneNumberVerificationFlowsReplicator phoneVerificationReplicator;
    private final LowBalanceFlowsReplicator lowBalanceReplicator;
    private final AccountFieldUpdateFlowsReplicator accountFieldUpdateReplicator;
    private final AccountUpdateFlowsReplicator accountUpdateFlowsReplicator;
    private final PurchaseFlowsReplicator purchaseReplicator;
    private final RedeemMoneyFlowsReplicator redeemMoneyReplicator;
    private final RedeemConfirmFlowsReplicator redeemConfirmReplicator;
    private final RedeemStatusUpdateFlowsReplicator redeemStatusUpdateEvent;
    private final OptOutFlowsReplicator optOutFlowsReplicator;
    private final GameplayFlowsReplicator gameplayFlowsReplicator;
    private final InviteAcceptedFlowsReplicator inviteAcceptedFlowsReplicator;
    private Duration initialInterval;
    private Duration maxInterval;
    private int maxAttempts;

    private Retry retry;

    public FlowsSink(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RetryRegistry retryRegistry,
            KafkaListenerEndpointRegistry registry,
            Property<Boolean> enabled,
            Property<Duration> initialIntervalProp,
            Property<Duration> maxIntervalProp,
            Property<Integer> maxAttemptProp,
            SignUpFlowsReplicator signUpReplicator,
            SignInFlowsReplicator signInReplicator,
            EmailVerificationFlowsReplicator emailVerificationReplicator,
            PhoneNumberVerificationFlowsReplicator phoneVerificationReplicator,
            LowBalanceFlowsReplicator lowBalanceReplicator,
            AccountFieldUpdateFlowsReplicator accountFieldUpdateReplicator,
            AccountUpdateFlowsReplicator accountUpdateFlowsReplicator,
            PurchaseFlowsReplicator purchaseReplicator,
            RedeemMoneyFlowsReplicator redeemMoneyReplicator,
            RedeemConfirmFlowsReplicator redeemConfirmReplicator,
            RedeemStatusUpdateFlowsReplicator redeemStatusUpdateEvent,
            OptOutFlowsReplicator optOutFlowsReplicator,
            GameplayFlowsReplicator gameplayFlowsReplicator,
            InviteAcceptedFlowsReplicator inviteAcceptedFlowsReplicator) {
        super(registry, enabled);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.retryRegistry = Objects.requireNonNull(retryRegistry);
        this.signUpReplicator = Objects.requireNonNull(signUpReplicator);
        this.signInReplicator = Objects.requireNonNull(signInReplicator);
        this.emailVerificationReplicator = Objects.requireNonNull(emailVerificationReplicator);
        this.phoneVerificationReplicator = Objects.requireNonNull(phoneVerificationReplicator);
        this.lowBalanceReplicator = Objects.requireNonNull(lowBalanceReplicator);
        this.accountFieldUpdateReplicator = Objects.requireNonNull(accountFieldUpdateReplicator);
        this.accountUpdateFlowsReplicator = Objects.requireNonNull(accountUpdateFlowsReplicator);
        this.purchaseReplicator = Objects.requireNonNull(purchaseReplicator);
        this.redeemMoneyReplicator = Objects.requireNonNull(redeemMoneyReplicator);
        this.redeemStatusUpdateEvent = Objects.requireNonNull(redeemStatusUpdateEvent);
        this.redeemConfirmReplicator = Objects.requireNonNull(redeemConfirmReplicator);
        this.optOutFlowsReplicator = Objects.requireNonNull(optOutFlowsReplicator);
        this.gameplayFlowsReplicator = Objects.requireNonNull(gameplayFlowsReplicator);
        this.inviteAcceptedFlowsReplicator = Objects.requireNonNull(inviteAcceptedFlowsReplicator);
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry);
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
        this.initialInterval = Objects.requireNonNull(initialIntervalProp.get());
        this.maxInterval = Objects.requireNonNull(maxIntervalProp.get());
        this.maxAttempts = Objects.requireNonNull(maxAttemptProp.get());

        initialIntervalProp.subscribe(duration -> {
            if (Objects.nonNull(duration)) {
                initialInterval = duration;
                updateRetry();
            }
        });
        maxIntervalProp.subscribe(duration -> {
            if (Objects.nonNull(duration)) {
                maxInterval = duration;
                updateRetry();
            }
        });
        maxAttemptProp.subscribe(attemptCount -> {
            if (Objects.nonNull(attemptCount)) {
                maxAttempts = attemptCount;
                updateRetry();
            }
        });
    }

    @Override
    public void setBeanName(String name) {
        super.setBeanName(name);
        executor.setBeanName(name);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        executor.afterPropertiesSet();
        super.afterPropertiesSet();
        updateRetry();

        //
        // ~ subscribe for any pool changes (max keep-alive etc)
        //
        executor.asFlux().subscribe(new java.util.function.Consumer<ThreadPoolExecutor>() {
            @Override
            public void accept(ThreadPoolExecutor pool) {
                throttler.set(new Semaphore(pool.getMaximumPoolSize()));
            }
        });
    }

    @Override
    public void destroy() throws Exception {
        try {
            super.destroy();
        } finally {
            if (Objects.nonNull(executor)) {
                executor.destroy();
            }
            if (Objects.nonNull(scheduler)) {
                scheduler.shutdownNow();
            }
        }
    }

    @Override
    public void accept(Flux<KafkaWorkUnit> flux, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
        // Process messages reactively instead of blocking collection
        var processedMessages = flux
            .map(this::parseKafkaMessage)
            .filter(Objects::nonNull)
            .collectMultimap(
                pair -> pair.getLeft(), // accountId
                pair -> pair.getRight() // Pair<KafkaWorkUnit, Message>
            )
            .block(); // Only block once after reactive processing

        if (processedMessages == null || processedMessages.isEmpty()) {
            acknowledgment.acknowledge();
            return;
        }

        // Process account groups in parallel with improved concurrency
        var allReplyFutures = processAccountGroupsInParallel(processedMessages);

        // Wait for all processing to complete
        waitForCompletion(allReplyFutures);

        acknowledgment.acknowledge();
    }

    private List<CompletableFuture<Integer>> processAccountGroupsInParallel(
            Map<Long, Collection<Pair<KafkaWorkUnit, Message>>> processedMessages) {
        var allReplyFutures = new ArrayList<CompletableFuture<Integer>>();
        var semaphore = throttler.get();

        // Process each account group in parallel
        for (var entry : processedMessages.entrySet()) {
            var accountId = entry.getKey();
            var accountEvents = entry.getValue();

            // Submit account processing as a single async task
            var accountFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    semaphore.acquire();
                    return processAccountEvents(accountId, accountEvents);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while acquiring semaphore", e);
                } finally {
                    semaphore.release();
                }
            }, executor);

            allReplyFutures.add(accountFuture);
        }

        return allReplyFutures;
    }

    private Integer processAccountEvents(Long accountId, Collection<Pair<KafkaWorkUnit, Message>> accountEvents) {
        // Use parallel processing for events within the same account when possible
        var eventFutures = accountEvents.parallelStream()
            .map(unitMessage -> processEventAsync(accountId, unitMessage))
            .toList();

        // Wait for all events for this account to complete
        try {
            CompletableFuture.allOf(eventFutures.toArray(new CompletableFuture[0])).get();
            return HttpStatus.SC_OK;
        } catch (Exception e) {
            logger.error("Failed to process events for account {}", accountId, e);
            throw new RuntimeException("Event processing failed", e);
        }
    }

    private CompletableFuture<Integer> processEventAsync(Long accountId, Pair<KafkaWorkUnit, Message> unitMessage) {
        var unit = unitMessage.getLeft();
        var event = unitMessage.getRight();
        var recordTime = Timer.start(meterRegistry);

        return CompletableFuture.supplyAsync(() -> {
            try {
                AbstractSink.setMdc(unit, Any.pack(event));
                return processEventByType(event);
            } finally {
                MdcUtil.clearMdc(unit);
                recordTime.stop(meterRegistry.timer(
                    Metrics.REPLICATOR,
                    List.of(sinkNameTag, Tag.of(MetricTags.OPERATION, event.getClass().getSimpleName()))));
            }
        }, executor).thenCompose(result -> result);
    }

    private CompletableFuture<Integer> processEventByType(Message event) {
        return switch (event) {
            case AccountCreateEvent cast ->
                signUpReplicator.apply(cast).toCompletableFuture();
            case AccountSignInEvent cast ->
                signInReplicator.apply(cast).toCompletableFuture();
            case EmailVerificationEvent cast ->
                emailVerificationReplicator.apply(cast).toCompletableFuture();
            case PhoneNumberVerificationEvent cast ->
                phoneVerificationReplicator.apply(cast).toCompletableFuture();
            case AccountLowBalanceEvent cast ->
                lowBalanceReplicator.apply(cast).toCompletableFuture();
            case AccountFieldUpdateEvent cast ->
                accountFieldUpdateReplicator.apply(cast).toCompletableFuture();
            case AccountUpdateEvent cast ->
                accountUpdateFlowsReplicator.apply(cast).toCompletableFuture();
            case PaymentOrderCompletedEvent cast ->
                purchaseReplicator.apply(cast).toCompletableFuture();
            case RedeemMoneyEvent cast ->
                redeemMoneyReplicator.apply(cast).toCompletableFuture();
            case RedeemStatusUpdateEvent cast ->
                redeemStatusUpdateEvent.apply(cast).toCompletableFuture();
            case RedeemConfirmEvent cast ->
                redeemConfirmReplicator.apply(cast).toCompletableFuture();
            case OptOutArbitrationEvent cast ->
                optOutFlowsReplicator.apply(cast).toCompletableFuture();
            case GameplayTransactionsEvent cast ->
                gameplayFlowsReplicator.apply(cast).toCompletableFuture();
            case InviteAcceptedEvent cast ->
                inviteAcceptedFlowsReplicator.apply(cast).toCompletableFuture();
            default -> CompletableFuture.completedFuture(HttpStatus.SC_OK);
        };
    }

    private void waitForCompletion(List<CompletableFuture<Integer>> allReplyFutures) {
        try {
            CompletableFuture.allOf(allReplyFutures.toArray(new CompletableFuture[0])).get(5, TimeUnit.MINUTES);
        } catch (InterruptedException err) {
            Thread.currentThread().interrupt();
            ExceptionUtils.wrapAndThrow(err);
        } catch (ExecutionException err) {
            ExceptionUtils.wrapAndThrow(err.getCause());
        } catch (TimeoutException err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    /**
     * Parse Kafka message and extract account ID and event.
     * Returns null for unsupported message types.
     */
    private Pair<Long, Pair<KafkaWorkUnit, Message>> parseKafkaMessage(KafkaWorkUnit unit) {
        try (InputStream io = unit.value().openStream()) {
            Any.Builder anyb = Any.newBuilder();
            anyb.mergeFrom(io);
            Any any = anyb.build();

            Long accountId = extractAccountId(any);
            if (accountId != null) {
                Message message = unpackMessage(any);
                if (message != null) {
                    return Pair.of(accountId, Pair.of(unit, message));
                }
            }
            return null;
        } catch (Throwable err) {
            logger.warn("Failed to parse Kafka message", err);
            return null;
        }
    }

    private Long extractAccountId(Any any) {
        try {
            if (any.is(AccountCreateEvent.class)) {
                return any.unpack(AccountCreateEvent.class).getAccount().getId();
            } else if (any.is(AccountSignInEvent.class)) {
                return any.unpack(AccountSignInEvent.class).getAccount().getId();
            } else if (any.is(EmailVerificationEvent.class)) {
                return any.unpack(EmailVerificationEvent.class).getAccount().getId();
            } else if (any.is(PhoneNumberVerificationEvent.class)) {
                return any.unpack(PhoneNumberVerificationEvent.class).getAccountId();
            } else if (any.is(AccountLowBalanceEvent.class)) {
                return any.unpack(AccountLowBalanceEvent.class).getAccount().getId();
            } else if (any.is(AccountFieldUpdateEvent.class)) {
                return any.unpack(AccountFieldUpdateEvent.class).getAccount().getId();
            } else if (any.is(PaymentOrderCompletedEvent.class)) {
                return any.unpack(PaymentOrderCompletedEvent.class).getPaymentRouting().getId();
            } else if (any.is(RedeemMoneyEvent.class)) {
                return any.unpack(RedeemMoneyEvent.class).getPaymentAccountInfo().getAccountId();
            } else if (any.is(RedeemStatusUpdateEvent.class)) {
                return any.unpack(RedeemStatusUpdateEvent.class).getPaymentRouting().getId();
            } else if (any.is(RedeemConfirmEvent.class)) {
                return any.unpack(RedeemConfirmEvent.class).getPaymentRouting().getId();
            } else if (any.is(OptOutArbitrationEvent.class)) {
                return any.unpack(OptOutArbitrationEvent.class).getAccount().getId();
            } else if (any.is(GameplayTransactionsEvent.class)) {
                return any.unpack(GameplayTransactionsEvent.class).getAccount().getId();
            } else if (any.is(InviteAcceptedEvent.class)) {
                return any.unpack(InviteAcceptedEvent.class).getAccount().getId();
            } else if (any.is(AccountUpdateEvent.class)) {
                return any.unpack(AccountUpdateEvent.class).getAccount().getId();
            }
            return null;
        } catch (Exception e) {
            logger.warn("Failed to extract account ID", e);
            return null;
        }
    }

    private void updateRetry() {
        retry = retryRegistry.retry(sinkNameTag.getValue(), getCustomRetryConfig());
    }

    private RetryConfig getCustomRetryConfig() {
        return RetryConfig.custom()
                .maxAttempts(maxAttempts)
                .intervalFunction(ofExponentialBackoff(initialInterval, IntervalFunction.DEFAULT_MULTIPLIER, maxInterval))
                .build();
    }

    private Message unpackMessage(Any any) {
        try {
            if (any.is(AccountCreateEvent.class)) {
                return any.unpack(AccountCreateEvent.class);
            } else if (any.is(AccountSignInEvent.class)) {
                return any.unpack(AccountSignInEvent.class);
            } else if (any.is(EmailVerificationEvent.class)) {
                return any.unpack(EmailVerificationEvent.class);
            } else if (any.is(PhoneNumberVerificationEvent.class)) {
                return any.unpack(PhoneNumberVerificationEvent.class);
            } else if (any.is(AccountLowBalanceEvent.class)) {
                return any.unpack(AccountLowBalanceEvent.class);
            } else if (any.is(AccountFieldUpdateEvent.class)) {
                return any.unpack(AccountFieldUpdateEvent.class);
            } else if (any.is(PaymentOrderCompletedEvent.class)) {
                return any.unpack(PaymentOrderCompletedEvent.class);
            } else if (any.is(RedeemMoneyEvent.class)) {
                return any.unpack(RedeemMoneyEvent.class);
            } else if (any.is(RedeemStatusUpdateEvent.class)) {
                return any.unpack(RedeemStatusUpdateEvent.class);
            } else if (any.is(RedeemConfirmEvent.class)) {
                return any.unpack(RedeemConfirmEvent.class);
            } else if (any.is(OptOutArbitrationEvent.class)) {
                return any.unpack(OptOutArbitrationEvent.class);
            } else if (any.is(GameplayTransactionsEvent.class)) {
                return any.unpack(GameplayTransactionsEvent.class);
            } else if (any.is(InviteAcceptedEvent.class)) {
                return any.unpack(InviteAcceptedEvent.class);
            } else if (any.is(AccountUpdateEvent.class)) {
                return any.unpack(AccountUpdateEvent.class);
            }
            return null;
        } catch (Exception e) {
            logger.warn("Failed to unpack message", e);
            return null;
        }
    }

    private <T> Supplier<CompletionStage<Integer>> replicatorSupplier(SinkReplicator<T> replicator, T cast) {
        return () -> {
            try {
                return replicator.apply(cast);
            } catch (Throwable e) {
                logger.warn("replicator logic has failed", e);
                return CompletableFuture.failedStage(e);
            }
        };
    }
}
