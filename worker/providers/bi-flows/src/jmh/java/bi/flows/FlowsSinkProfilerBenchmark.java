package bi.flows;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.io.ByteArrayInputStream;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;
import org.openjdk.jmh.profile.AsyncProfiler;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.support.Acknowledgment;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.google.protobuf.Any;
import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.KafkaWorkUnitValue;

import bi.flows.sink.*;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import reactor.core.publisher.Flux;
import uam.api.v1.internal.AccountCreateEvent;
import uam.api.v1.internal.AccountInfo;
import uam.api.v1.internal.AccountSignInEvent;

/**
 * Detailed profiling benchmark to identify the exact bottlenecks in FlowsSink.
 * Uses WireMock to simulate realistic HTTP behavior and async profiler to 
 * identify where time is being spent.
 */
@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@State(Scope.Benchmark)
@Warmup(iterations = 2, time = 3, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 3, time = 5, timeUnit = TimeUnit.SECONDS)
@Fork(1)
public class FlowsSinkProfilerBenchmark {

    @Mock private ApplicationProperties props;
    @Mock private RetryRegistry retryRegistry;
    @Mock private KafkaListenerEndpointRegistry registry;
    @Mock private Property<Boolean> enabled;
    @Mock private Property<Duration> initialIntervalProp;
    @Mock private Property<Duration> maxIntervalProp;
    @Mock private Property<Integer> maxAttemptProp;
    @Mock private Acknowledgment acknowledgment;

    // Mock replicators
    @Mock private SignUpFlowsReplicator signUpReplicator;
    @Mock private SignInFlowsReplicator signInReplicator;
    @Mock private EmailVerificationFlowsReplicator emailVerificationReplicator;
    @Mock private PhoneNumberVerificationFlowsReplicator phoneVerificationReplicator;
    @Mock private LowBalanceFlowsReplicator lowBalanceReplicator;
    @Mock private AccountFieldUpdateFlowsReplicator accountFieldUpdateReplicator;
    @Mock private AccountUpdateFlowsReplicator accountUpdateFlowsReplicator;
    @Mock private PurchaseFlowsReplicator purchaseReplicator;
    @Mock private RedeemMoneyFlowsReplicator redeemMoneyReplicator;
    @Mock private RedeemConfirmFlowsReplicator redeemConfirmReplicator;
    @Mock private RedeemStatusUpdateFlowsReplicator redeemStatusUpdateEvent;
    @Mock private OptOutFlowsReplicator optOutFlowsReplicator;
    @Mock private GameplayFlowsReplicator gameplayFlowsReplicator;
    @Mock private InviteAcceptedFlowsReplicator inviteAcceptedFlowsReplicator;

    private MeterRegistry meterRegistry;
    private FlowsSink flowsSink;
    private WireMockServer wireMockServer;
    private HttpClient httpClient;
    private AtomicLong requestCounter = new AtomicLong();

    // Test scenarios that reproduce the production problem
    private List<KafkaWorkUnit> productionScenario;    // 30 events, 15 accounts (typical batch)
    private List<KafkaWorkUnit> heavyLoadScenario;     // 100 events, 50 accounts (heavy load)
    private List<KafkaWorkUnit> serialScenario;       // 20 events, 1 account (worst case)

    @Setup(Level.Trial)
    public void setupTrial() throws Exception {
        MockitoAnnotations.openMocks(this);
        
        // Start WireMock server to simulate third-party API
        wireMockServer = new WireMockServer(WireMockConfiguration.options()
            .port(8089)
            .asynchronousResponseEnabled(true)
            .asynchronousResponseThreads(50));
        
        wireMockServer.start();
        
        // Configure WireMock to respond with 100ms delay (simulating production)
        wireMockServer.stubFor(post(urlPathEqualTo("/v2/events/Registration"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("Content-Type", "application/json")
                .withBody("{\"status\":\"success\"}")
                .withFixedDelay(100))); // 100ms delay like production
        
        wireMockServer.stubFor(post(urlPathEqualTo("/v2/events/Login"))
            .willReturn(aResponse()
                .withStatus(200)
                .withHeader("Content-Type", "application/json")
                .withBody("{\"status\":\"success\"}")
                .withFixedDelay(100)));

        // Create HTTP client for making real HTTP calls
        httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(5))
            .build();
        
        meterRegistry = new SimpleMeterRegistry();
        
        // Setup mock properties
        when(enabled.get()).thenReturn(true);
        when(initialIntervalProp.get()).thenReturn(Duration.ofMillis(100));
        when(maxIntervalProp.get()).thenReturn(Duration.ofSeconds(1));
        when(maxAttemptProp.get()).thenReturn(3);

        // Setup mock replicators to make real HTTP calls to WireMock
        setupMockReplicators();

        flowsSink = new FlowsSink(
            props, meterRegistry, retryRegistry, registry, enabled,
            initialIntervalProp, maxIntervalProp, maxAttemptProp,
            signUpReplicator, signInReplicator, emailVerificationReplicator,
            phoneVerificationReplicator, lowBalanceReplicator, accountFieldUpdateReplicator,
            accountUpdateFlowsReplicator, purchaseReplicator, redeemMoneyReplicator,
            redeemConfirmReplicator, redeemStatusUpdateEvent, optOutFlowsReplicator,
            gameplayFlowsReplicator, inviteAcceptedFlowsReplicator
        );

        flowsSink.setBeanName("profiler-flows-sink");
        flowsSink.afterPropertiesSet();

        // Prepare test scenarios
        setupTestScenarios();
        
        System.out.println("=== FlowsSink Profiler Benchmark Setup Complete ===");
        System.out.println("WireMock server running on port 8089 with 100ms response delay");
        System.out.println("Test scenarios prepared:");
        System.out.println("  - Production: " + productionScenario.size() + " events");
        System.out.println("  - Heavy Load: " + heavyLoadScenario.size() + " events");
        System.out.println("  - Serial: " + serialScenario.size() + " events");
    }

    @TearDown(Level.Trial)
    public void tearDownTrial() throws Exception {
        if (wireMockServer != null) {
            wireMockServer.stop();
        }
        if (flowsSink != null) {
            flowsSink.destroy();
        }
        
        // Print final statistics
        System.out.println("=== Benchmark Complete ===");
        System.out.println("Total HTTP requests made: " + requestCounter.get());
        System.out.println("WireMock request count: " + wireMockServer.getAllServeEvents().size());
    }

    private void setupMockReplicators() {
        // Setup replicators to make real HTTP calls to WireMock
        when(signUpReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(signInReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Login"));
        
        // Other replicators use the same pattern
        when(emailVerificationReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(phoneVerificationReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(lowBalanceReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(accountFieldUpdateReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(accountUpdateFlowsReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(purchaseReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(redeemMoneyReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(redeemConfirmReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(redeemStatusUpdateEvent.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(optOutFlowsReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(gameplayFlowsReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
        when(inviteAcceptedFlowsReplicator.apply(any())).thenAnswer(inv -> makeHttpCall("/v2/events/Registration"));
    }

    private CompletionStage<Integer> makeHttpCall(String endpoint) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                long requestId = requestCounter.incrementAndGet();
                
                HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create("http://localhost:8089" + endpoint))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString("{\"accountId\":\"" + requestId + "\"}"))
                    .build();

                HttpResponse<String> response = httpClient.send(request, 
                    HttpResponse.BodyHandlers.ofString());
                
                return response.statusCode();
            } catch (Exception e) {
                System.err.println("HTTP call failed: " + e.getMessage());
                return 500;
            }
        });
    }

    private void setupTestScenarios() {
        // Production scenario: typical batch size with mixed accounts
        productionScenario = createTestWorkUnits(30, 15);
        
        // Heavy load scenario: large batch that would show the 3-second problem
        heavyLoadScenario = createTestWorkUnits(100, 50);
        
        // Serial scenario: many events for same account (worst case for current implementation)
        serialScenario = createTestWorkUnitsForSameAccount(20, 12345L);
    }

    /**
     * Benchmark the typical production scenario.
     * Expected: Should take ~3000ms with current implementation due to bottlenecks.
     * Third-party APIs respond in 100ms, but total processing takes much longer.
     */
    @Benchmark
    public void benchmarkProductionScenario(Blackhole bh) {
        Flux<KafkaWorkUnit> flux = Flux.fromIterable(productionScenario);
        long startTime = System.currentTimeMillis();
        
        flowsSink.accept(flux, acknowledgment, null);
        
        long duration = System.currentTimeMillis() - startTime;
        bh.consume(duration);
        
        // Log timing for analysis
        System.out.println("Production scenario completed in: " + duration + "ms");
    }

    /**
     * Benchmark heavy load scenario.
     * This should clearly demonstrate the scalability problem.
     */
    @Benchmark
    public void benchmarkHeavyLoadScenario(Blackhole bh) {
        Flux<KafkaWorkUnit> flux = Flux.fromIterable(heavyLoadScenario);
        long startTime = System.currentTimeMillis();
        
        flowsSink.accept(flux, acknowledgment, null);
        
        long duration = System.currentTimeMillis() - startTime;
        bh.consume(duration);
        
        System.out.println("Heavy load scenario completed in: " + duration + "ms");
    }

    /**
     * Benchmark serial processing scenario.
     * This tests the AsyncSerialContextWorker bottleneck.
     */
    @Benchmark
    public void benchmarkSerialScenario(Blackhole bh) {
        Flux<KafkaWorkUnit> flux = Flux.fromIterable(serialScenario);
        long startTime = System.currentTimeMillis();
        
        flowsSink.accept(flux, acknowledgment, null);
        
        long duration = System.currentTimeMillis() - startTime;
        bh.consume(duration);
        
        System.out.println("Serial scenario completed in: " + duration + "ms");
    }

    private List<KafkaWorkUnit> createTestWorkUnits(int eventCount, int uniqueAccounts) {
        List<KafkaWorkUnit> workUnits = new ArrayList<>();
        for (int i = 0; i < eventCount; i++) {
            long accountId = (i % uniqueAccounts) + 1000;
            if (i % 2 == 0) {
                workUnits.add(createSignUpWorkUnit(accountId));
            } else {
                workUnits.add(createSignInWorkUnit(accountId));
            }
        }
        return workUnits;
    }

    private List<KafkaWorkUnit> createTestWorkUnitsForSameAccount(int count, Long accountId) {
        List<KafkaWorkUnit> workUnits = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            if (i % 2 == 0) {
                workUnits.add(createSignUpWorkUnit(accountId));
            } else {
                workUnits.add(createSignInWorkUnit(accountId));
            }
        }
        return workUnits;
    }

    private KafkaWorkUnit createSignUpWorkUnit(Long accountId) {
        AccountInfo account = AccountInfo.newBuilder()
            .setId(accountId)
            .setBrandName("test-brand")
            .setRoutingKey("test-routing")
            .build();
        
        AccountCreateEvent event = AccountCreateEvent.newBuilder()
            .setAccount(account)
            .build();
        
        return createWorkUnit(Any.pack(event));
    }

    private KafkaWorkUnit createSignInWorkUnit(Long accountId) {
        AccountInfo account = AccountInfo.newBuilder()
            .setId(accountId)
            .setBrandName("test-brand")
            .setRoutingKey("test-routing")
            .build();
        
        AccountSignInEvent event = AccountSignInEvent.newBuilder()
            .setAccount(account)
            .build();
        
        return createWorkUnit(Any.pack(event));
    }

    private KafkaWorkUnit createWorkUnit(Any any) {
        byte[] data = any.toByteArray();
        KafkaWorkUnitValue value = () -> new ByteArrayInputStream(data);
        
        return KafkaWorkUnit.builder()
            .topic("test-topic")
            .partition(0)
            .offset(System.nanoTime())
            .key("test-key")
            .value(value)
            .build();
    }

    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
            .include(FlowsSinkProfilerBenchmark.class.getSimpleName())
            .addProfiler(AsyncProfiler.class)
            .jvmArgs("-XX:+UnlockDiagnosticVMOptions", "-XX:+DebugNonSafepoints")
            .build();

        new Runner(opt).run();
    }
}
