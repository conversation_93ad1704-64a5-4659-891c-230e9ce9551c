package bi.flows;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.io.ByteArrayInputStream;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.TimeUnit;

import org.apache.http.HttpStatus;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.support.Acknowledgment;

import com.google.protobuf.Any;
import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.KafkaWorkUnitValue;

import bi.flows.sink.*;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import reactor.core.publisher.Flux;
import uam.api.v1.internal.AccountCreateEvent;
import uam.api.v1.internal.AccountInfo;
import uam.api.v1.internal.AccountSignInEvent;

/**
 * JMH Benchmark to reproduce the FlowsSink performance problem.
 * This benchmark simulates the production scenario where third-party APIs
 * respond in 100ms but total processing takes 3+ seconds.
 */
@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@State(Scope.Benchmark)
@Warmup(iterations = 3, time = 2, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 3, timeUnit = TimeUnit.SECONDS)
@Fork(1)
public class FlowsSinkBenchmark {

    @Mock private ApplicationProperties props;
    @Mock private RetryRegistry retryRegistry;
    @Mock private KafkaListenerEndpointRegistry registry;
    @Mock private Property<Boolean> enabled;
    @Mock private Property<Duration> initialIntervalProp;
    @Mock private Property<Duration> maxIntervalProp;
    @Mock private Property<Integer> maxAttemptProp;
    @Mock private Acknowledgment acknowledgment;

    // Mock replicators
    @Mock private SignUpFlowsReplicator signUpReplicator;
    @Mock private SignInFlowsReplicator signInReplicator;
    @Mock private EmailVerificationFlowsReplicator emailVerificationReplicator;
    @Mock private PhoneNumberVerificationFlowsReplicator phoneVerificationReplicator;
    @Mock private LowBalanceFlowsReplicator lowBalanceReplicator;
    @Mock private AccountFieldUpdateFlowsReplicator accountFieldUpdateReplicator;
    @Mock private AccountUpdateFlowsReplicator accountUpdateFlowsReplicator;
    @Mock private PurchaseFlowsReplicator purchaseReplicator;
    @Mock private RedeemMoneyFlowsReplicator redeemMoneyReplicator;
    @Mock private RedeemConfirmFlowsReplicator redeemConfirmReplicator;
    @Mock private RedeemStatusUpdateFlowsReplicator redeemStatusUpdateEvent;
    @Mock private OptOutFlowsReplicator optOutFlowsReplicator;
    @Mock private GameplayFlowsReplicator gameplayFlowsReplicator;
    @Mock private InviteAcceptedFlowsReplicator inviteAcceptedFlowsReplicator;

    private MeterRegistry meterRegistry;
    private FlowsSink flowsSink;
    private MockHttpServer mockServer;

    // Test data for different scenarios
    private List<KafkaWorkUnit> smallBatch;      // 5 events, 5 accounts
    private List<KafkaWorkUnit> mediumBatch;     // 20 events, 10 accounts  
    private List<KafkaWorkUnit> largeBatch;      // 50 events, 25 accounts
    private List<KafkaWorkUnit> sameAccountBatch; // 10 events, 1 account

    @Setup(Level.Trial)
    public void setupTrial() throws Exception {
        MockitoAnnotations.openMocks(this);
        
        // Start mock HTTP server that simulates 100ms response time
        mockServer = new MockHttpServer(8089, 100); // 100ms delay
        mockServer.start();
        
        meterRegistry = new SimpleMeterRegistry();
        
        // Setup mock properties
        when(enabled.get()).thenReturn(true);
        when(initialIntervalProp.get()).thenReturn(Duration.ofMillis(100));
        when(maxIntervalProp.get()).thenReturn(Duration.ofSeconds(1));
        when(maxAttemptProp.get()).thenReturn(3);

        // Setup mock replicators to call our mock HTTP server
        setupMockReplicators();

        flowsSink = new FlowsSink(
            props, meterRegistry, retryRegistry, registry, enabled,
            initialIntervalProp, maxIntervalProp, maxAttemptProp,
            signUpReplicator, signInReplicator, emailVerificationReplicator,
            phoneVerificationReplicator, lowBalanceReplicator, accountFieldUpdateReplicator,
            accountUpdateFlowsReplicator, purchaseReplicator, redeemMoneyReplicator,
            redeemConfirmReplicator, redeemStatusUpdateEvent, optOutFlowsReplicator,
            gameplayFlowsReplicator, inviteAcceptedFlowsReplicator
        );

        flowsSink.setBeanName("benchmark-flows-sink");
        flowsSink.afterPropertiesSet();

        // Prepare test data
        setupTestData();
    }

    @TearDown(Level.Trial)
    public void tearDownTrial() throws Exception {
        if (mockServer != null) {
            mockServer.stop();
        }
        if (flowsSink != null) {
            flowsSink.destroy();
        }
    }

    private void setupMockReplicators() {
        // All replicators simulate HTTP calls to our mock server
        when(signUpReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(signInReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(emailVerificationReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(phoneVerificationReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(lowBalanceReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(accountFieldUpdateReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(accountUpdateFlowsReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(purchaseReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(redeemMoneyReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(redeemConfirmReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(redeemStatusUpdateEvent.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(optOutFlowsReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(gameplayFlowsReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
        when(inviteAcceptedFlowsReplicator.apply(any())).thenAnswer(inv -> simulateHttpCall());
    }

    private CompletionStage<Integer> simulateHttpCall() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Simulate HTTP call to mock server (100ms response time)
                mockServer.makeRequest();
                return HttpStatus.SC_OK;
            } catch (Exception e) {
                throw new RuntimeException("HTTP call failed", e);
            }
        });
    }

    private void setupTestData() {
        smallBatch = createTestWorkUnits(5, 5);
        mediumBatch = createTestWorkUnits(20, 10);
        largeBatch = createTestWorkUnits(50, 25);
        sameAccountBatch = createTestWorkUnitsForSameAccount(10, 12345L);
    }

    @Benchmark
    public void benchmarkSmallBatch() {
        Flux<KafkaWorkUnit> flux = Flux.fromIterable(smallBatch);
        flowsSink.accept(flux, acknowledgment, null);
    }

    @Benchmark
    public void benchmarkMediumBatch() {
        Flux<KafkaWorkUnit> flux = Flux.fromIterable(mediumBatch);
        flowsSink.accept(flux, acknowledgment, null);
    }

    @Benchmark
    public void benchmarkLargeBatch() {
        Flux<KafkaWorkUnit> flux = Flux.fromIterable(largeBatch);
        flowsSink.accept(flux, acknowledgment, null);
    }

    @Benchmark
    public void benchmarkSameAccount() {
        Flux<KafkaWorkUnit> flux = Flux.fromIterable(sameAccountBatch);
        flowsSink.accept(flux, acknowledgment, null);
    }

    private List<KafkaWorkUnit> createTestWorkUnits(int eventCount, int uniqueAccounts) {
        List<KafkaWorkUnit> workUnits = new ArrayList<>();
        for (int i = 0; i < eventCount; i++) {
            long accountId = (i % uniqueAccounts) + 1000;
            if (i % 2 == 0) {
                workUnits.add(createSignUpWorkUnit(accountId));
            } else {
                workUnits.add(createSignInWorkUnit(accountId));
            }
        }
        return workUnits;
    }

    private List<KafkaWorkUnit> createTestWorkUnitsForSameAccount(int count, Long accountId) {
        List<KafkaWorkUnit> workUnits = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            if (i % 2 == 0) {
                workUnits.add(createSignUpWorkUnit(accountId));
            } else {
                workUnits.add(createSignInWorkUnit(accountId));
            }
        }
        return workUnits;
    }

    private KafkaWorkUnit createSignUpWorkUnit(Long accountId) {
        AccountInfo account = AccountInfo.newBuilder()
            .setId(accountId)
            .setBrandName("test-brand")
            .setRoutingKey("test-routing")
            .build();
        
        AccountCreateEvent event = AccountCreateEvent.newBuilder()
            .setAccount(account)
            .build();
        
        return createWorkUnit(Any.pack(event));
    }

    private KafkaWorkUnit createSignInWorkUnit(Long accountId) {
        AccountInfo account = AccountInfo.newBuilder()
            .setId(accountId)
            .setBrandName("test-brand")
            .setRoutingKey("test-routing")
            .build();
        
        AccountSignInEvent event = AccountSignInEvent.newBuilder()
            .setAccount(account)
            .build();
        
        return createWorkUnit(Any.pack(event));
    }

    private KafkaWorkUnit createWorkUnit(Any any) {
        byte[] data = any.toByteArray();
        KafkaWorkUnitValue value = () -> new ByteArrayInputStream(data);
        
        return KafkaWorkUnit.builder()
            .topic("test-topic")
            .partition(0)
            .offset(System.nanoTime())
            .key("test-key")
            .value(value)
            .build();
    }

    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
            .include(FlowsSinkBenchmark.class.getSimpleName())
            .build();

        new Runner(opt).run();
    }

    /**
     * Simple mock HTTP server that simulates the third-party API
     * with configurable response delay.
     */
    private static class MockHttpServer {
        private final int port;
        private final long responseDelayMs;
        private volatile boolean running;

        public MockHttpServer(int port, long responseDelayMs) {
            this.port = port;
            this.responseDelayMs = responseDelayMs;
        }

        public void start() {
            running = true;
            System.out.println("Mock HTTP server started on port " + port + 
                             " with " + responseDelayMs + "ms response delay");
        }

        public void stop() {
            running = false;
            System.out.println("Mock HTTP server stopped");
        }

        public void makeRequest() throws InterruptedException {
            if (!running) {
                throw new IllegalStateException("Server not running");
            }
            
            // Simulate network latency + processing time
            Thread.sleep(responseDelayMs);
        }
    }
}
