syntax = "proto3";
package loyalty.api.v1;

import "api/v1/common.proto";
import "api/v1/identity.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message SaveLoyaltySystemRequest {
  LoyaltySystem loyaltySystem = 1;
  int64 xpVariantId = 2;
}

message SaveVariantRequest {
  XpVariant variant = 1;
}

message SaveVariantResponse {
  XpVariant variant = 1;
}

message UpdateVariantRequest {
  XpVariant variant = 1;
}

message UpdateVariantResponse {
  XpVariant variant = 1;
}

message GetVariantRequest {
  oneof identifier {
    int64 id = 1;
    string code = 2;
  }
}

message GetVariantResponse {
  XpVariant variant = 1;
}

message UpdateLoyaltySystemRequest {
  LoyaltySystem loyaltySystem = 1;
}

message GetLoyaltySystemsRequest {
  optional string loyaltySystemCode = 1;
  optional bool active = 2;
}

message GetLoyaltySystemsResponse {
  repeated LoyaltySystem loyaltySystem = 1;
}

message SaveLoyaltySystemResponse {
  LoyaltySystem loyaltySystem = 1;
}

message UpdateLoyaltySystemResponse {
  LoyaltySystem loyaltySystem = 1;
}

message GetAccountLoyaltySystemsRequest {
  .uam.api.v1.Identity identity = 1;
}

message SetAccountLoyaltySystemsStatusRequest {
  .uam.api.v1.Identity identity = 1;
  string brand = 2;
  string loyaltySystemCode = 3;
  optional LoyaltySystemStatusEnum startState = 4;
  LoyaltySystemStatusEnum status = 5;
}

message SetAllAccountLoyaltySystemsStatusRequest {
  string brand = 2;
  string loyaltySystemCode = 3;
  optional LoyaltySystemStatusEnum startState = 4;
  LoyaltySystemStatusEnum status = 5;
}

message SetAccountLoyaltySystemsStatusResponce {
  bool applied = 3;
}

message GetAccountLoyaltySystemsResponse {
  repeated AccountLoyaltySystem systems = 1;
}

message GetAccountVariantsResponse {
  repeated AccountVariant variant = 1;
}

message AccountVariant {
  string code = 1;
  string variantCode = 2;
  string displayName = 3;
  optional string iconLg = 4;
  optional string iconSm = 5;
  string points = 6;
  string abbreviation = 7;
}

message GetAccountVariantsRequest {
  .uam.api.v1.Identity identity = 1;
}

message AssignLoyaltySystemRequest {
  .uam.api.v1.Identity identity = 1;
  string brand = 2;
  string loyaltySystemCode = 3;
  bool silent = 4;
}

message AssignLoyaltySystemResponse {
  int64 accountLoyaltySystemId = 1;
  string accountLoyaltySystemCode = 2;
}

message UpdateAccountBalanceVariantRequest {
  .uam.api.v1.Identity identity = 1;
  XpPointsActionType actionType = 2;
  string xpPoints = 3;
  oneof identifier {
    string accountLoyaltySystemCode = 4;
    string variantCode = 5;
  }
  AccountLoyaltySystemContributionSource source = 6;
  optional string sourceReference = 7;
  string transactionCode = 8;
  optional .api.v1.Date at = 9;
  optional bool noMultipliers = 10;
}

message UpdateAccountBalanceVariantResponse {
  bool applied = 1;
  string xpPoints = 2;
}

enum XpPointsActionType {
  XP_POINTS_ACTION_ADD = 0;
  XP_POINTS_ACTION_REMOVE = 1;
  XP_POINTS_ACTION_EXPIRE = 2;
}

enum AccountLoyaltySystemContributionSource {
  PLAYER_CONTRIBUTION = 0;
  BACKOFFICE = 1;
  REWARD = 2;
  SYSTEM = 3;
}

message ExpireAccountBalanceVariantXpRequest {
  .uam.api.v1.Identity identity = 1;
  optional .api.v1.Date lastExpireBefore = 2;
  .api.v1.Date lastExpireAfter = 3;
  string accountBalanceVariantCode = 4;
}

message ExpireAccountBalanceVariantXpResponse {
  bool applied = 1;
  string xpPoints = 2;
}

message AccountLoyaltySystem {
  LoyaltySystemStatusEnum status = 1;
  LoyaltyLevelTypeEnum levelType = 2;
  optional LoyaltyLevel levelCurrent = 3;
  optional LoyaltyLevel levelNext = 4;
  string xpCurrent = 5;
  optional string detailsUrl = 6;
  string accountLoyaltySystemCode = 7;
  optional AccountVariant accountVariant = 8;
  optional int32  popupReminderIntervalDays = 9;
  optional int32  subLevelNumber = 10;
  optional int32  subLevelsInLevel = 11;
  optional string  subLevelNextXpRequired = 12;
}

enum LoyaltyLevelTypeEnum{
  NUMBERED = 0;
  NAMED = 1;
}

enum LoyaltySystemStatusEnum{
  ACTIVE = 0;
  INACTIVE = 1;
  SILENT = 2;
}

message LoyaltySystem {
  optional int64 id = 1;
  string name = 2;
  string brand = 3;
  string type = 4;
  bool active = 5;
  bool testOnly = 6;
  optional string detailsUrl = 7;
  repeated LoyaltyLevel levels = 8;
  repeated LoyaltyEventTrigger eventTriggers = 9;
  XpVariant xpVariant = 10;
  string code = 11;
  bool giveXpGoals = 12;
  optional string xpFirstGoal = 13;
  optional string xpEveryNextGoal = 14;
  repeated Placement placements = 15;
  bool locked = 16;
  optional int32  popupReminderIntervalDays = 17;
  optional int64 xpVariantId = 18;
}

message Placement {
  optional int64 placementId = 1;
  optional int64 placementTemplateId = 2;
  int64 loyaltySystemId = 3;
  string theme = 4;
  optional PlacementTypeEnum type = 7;
}

enum PlacementTypeEnum {
  IN_GAME_ICON = 0;
  HOME_CENTER_STAGE = 1;
  LOBBY_SWIMLINE = 2;
  SIDEBAR_WIDGET = 3;
}

message XpVariant {
  optional int64 id = 1;
  string name = 2;
  string displayName = 3;
  string abbreviation = 4;
  optional string iconUrlSm = 5;
  optional string iconUrlLg = 6;
  string colorPrimary = 7 [deprecated = true];
  string colorAccent = 8 [deprecated = true];
  string colorShadow = 9 [deprecated = true];
  string colorTone = 10 [deprecated = true];
  bool active = 11;
  string code = 12;
  optional int32 xpExpirationDays = 13;
  string brand = 14;
}

message LoyaltyLevel {
  optional int64 id = 1;
  string scope = 2;
  string xpRequired = 3;
  string internalName = 4;
  optional string xpMultiplier = 5;
  bool subLevel = 6;

  oneof type {
    NamedLoyaltySystemLevel namedLevel = 101;
    NumberedLoyaltySystemLevel numberedLevel = 102;
  }
}

message NamedLoyaltySystemLevel {
  string displayName = 1;
  string displayNameShort = 2;
  string iconUrlSm = 3;
  string iconUrlLg = 4;
}

message NumberedLoyaltySystemLevel {
  int32 rank = 1;
}

message LoyaltyEventTrigger {
  optional int64 id = 1;
  string name = 2;
  string threshold = 3;
  string currency = 4;
  string xpPerBaseUnit = 5;
  oneof type {
    PlayEventTrigger playTrigger = 101;
    PurchaseEventTrigger purchaseTrigger = 102;
  }
}

message PlayEventTrigger {
  repeated string gameCodes = 1;
  repeated string gameSuppliers = 2;
}

message PurchaseEventTrigger {

}

