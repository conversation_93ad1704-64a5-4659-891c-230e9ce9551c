syntax = "proto3";
package loyalty.api.v1;

import "api/v1/routing.proto";
import "loyalty/api/v1/loyalty.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message LoyaltyXpLevelUpdateEvent {
  .api.v1.AccountRoutingInfo routing = 1;
  string loyaltySystemCode = 2;
  string accountLoyaltySystemCode = 3;
  optional LoyaltyLevel levelNext = 4;
  optional LoyaltyLevel levelCurrent = 5;
  optional LoyaltyLevel levelPrevious = 6;
  bool firstTimeReachedLevel = 7;
  string xpCurrent = 8;
  int64 at = 9;
  string variantCode = 10;
  LevelTransitionType transitionType = 11;
  optional LoyaltyLevel mainLevelNext = 12;
  optional LoyaltyLevel mainLevelCurrent = 13;
}

message LoyaltyAccountBalanceUpdateEvent {
  .api.v1.AccountRoutingInfo routing = 1;
  string variantCode = 2;
  string variantAbbreviation = 3;
  string transactionCode = 4;
  AccountBalanceEventType eventType = 5;
  AccountBalanceSource source = 6;
  string sourceReference = 7;
  string xpCurrent = 8;
  string xpBefore = 9;
  string xpAmount = 10;
  int64 at = 11;
  optional string loyaltySystemCode = 12;
  optional string accountLoyaltySystemCode = 13;
}

enum LevelTransitionType {
    UNSPECIFIED = 0;
    UPGRADE = 1;
    DOWNGRADE = 2;
}

enum AccountBalanceEventType {
  ACCOUNT_BALANCE_EVENT_TYPE_UNSPECIFIED = 0;
  ACCOUNT_BALANCE_EVENT_TYPE_DEBIT = 1;
  ACCOUNT_BALANCE_EVENT_TYPE_CREDIT = 2;
  ACCOUNT_BALANCE_EVENT_TYPE_EXPIRE = 3;
}

enum AccountBalanceSource {
  ACCOUNT_BALANCE_SOURCE_UNSPECIFIED = 0;
  ACCOUNT_BALANCE_SOURCE_PLAYER_CONTRIBUTION = 1;
  ACCOUNT_BALANCE_SOURCE_BACKOFFICE = 2;
  ACCOUNT_BALANCE_SOURCE_REWARD = 3;
  ACCOUNT_BALANCE_SOURCE_SYSTEM = 4;
}
