syntax = "proto3";
package loyalty.api.v1;

import "api/v1/routing.proto";
import "loyalty/api/v1/loyalty.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message LoyaltyXpPointsUpdateNotification {
  .api.v1.AccountRoutingInfo routing = 1;
  optional string loyaltySystemCode = 2;
  optional string accountLoyaltySystemCode = 3;
  string xpBefore = 4;
  string xpCurrent = 5;
  optional string accountVariantCode = 6;
}

message LoyaltyXpLevelUpdateNotification {
  .api.v1.AccountRoutingInfo routing = 1;
  string loyaltySystemCode = 2;
  string accountLoyaltySystemCode = 3;
  optional LoyaltyLevel levelCurrent = 4;
  optional LoyaltyLevel levelNext = 5;
  optional int32  subLevelNumber = 6;
  optional int32  subLevelsInLevel = 7;
  optional string  subLevelNextXpRequired = 8;
}
