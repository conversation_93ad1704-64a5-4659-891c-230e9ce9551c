package admin.mapper;

import admin.loyalty.model.AccountLoyaltySystemStatus;
import admin.loyalty.model.LoyaltySystem;
import admin.loyalty.model.SaveLoyaltySystemRequest;
import admin.loyalty.model.XpVariant;
import admin.loyalty.model.event.EventTrigger;
import admin.loyalty.model.event.PlayEventTrigger;
import admin.loyalty.model.event.PurchaseEventTrigger;
import admin.loyalty.model.level.LoyaltySystemLevel;
import admin.loyalty.model.level.NamedLoyaltySystemLevel;
import admin.loyalty.model.level.NumberedLoyaltySystemLevel;
import common.utils.ProtoUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import loyalty.api.spec.LoyaltySystemLevelScopeSpec;
import loyalty.api.spec.LoyaltySystemLevelTypeSpec;
import loyalty.api.v1.LoyaltyEventTrigger;
import loyalty.api.v1.LoyaltyLevel;
import loyalty.api.v1.LoyaltySystemStatusEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Slf4j
@UtilityClass
public class LoyaltySystemMapper {

    public static loyalty.api.v1.SaveLoyaltySystemRequest toProto(SaveLoyaltySystemRequest request, String brand) {
        return loyalty.api.v1.SaveLoyaltySystemRequest.newBuilder()
                .setLoyaltySystem(toProto(request.getLoyaltySystem(), brand))
                .setXpVariantId(request.getXpVariantId())
                .build();
    }

    public static loyalty.api.v1.LoyaltySystem toProto(LoyaltySystem loyaltySystem, String brand) {
        var builder = loyalty.api.v1.LoyaltySystem.newBuilder()
                .setName(loyaltySystem.getName())
                .setBrand(brand)
                .setType(loyaltySystem.getType().code())
                .setActive(loyaltySystem.getActive())
                .setTestOnly(loyaltySystem.getTestOnly())
                .setGiveXpGoals(loyaltySystem.getGiveXpGoals())
                .addAllLevels(toProto(loyaltySystem.getLevels()))
                .addAllEventTriggers(
                        loyaltySystem.getEventTriggers().stream()
                                .map(LoyaltySystemMapper::toProto)
                                .collect(Collectors.toSet()));
        ProtoUtils.applyIfNotEmpty(loyaltySystem.getId(), builder::setId);
        ProtoUtils.applyIfNotEmpty(loyaltySystem.getCode(), builder::setCode);
        ProtoUtils.applyIfNotEmpty(loyaltySystem.getXpVariantId(), builder::setXpVariantId);
        if (loyaltySystem.getXpFirstGoal() != null) {
            builder.setXpFirstGoal(loyaltySystem.getXpFirstGoal().toString());
        }
        if (loyaltySystem.getXpEveryNextGoal() != null) {
            builder.setXpEveryNextGoal(loyaltySystem.getXpEveryNextGoal().toString());
        }
        if (loyaltySystem.getPlacements() != null) {
            builder.addAllPlacements(LoyaltyPlacementMapper.toPlacementList(loyaltySystem.getPlacements()));
        }
        if (loyaltySystem.getDetailsUrl() != null) {
            builder.setDetailsUrl(loyaltySystem.getDetailsUrl());
        }
        if (loyaltySystem.getPopupReminderIntervalDays() != null) {
            builder.setPopupReminderIntervalDays(loyaltySystem.getPopupReminderIntervalDays());
        }

        return builder.build();
    }

    public static loyalty.api.v1.LoyaltyEventTrigger toProto(EventTrigger eventTrigger) {
        var builder = loyalty.api.v1.LoyaltyEventTrigger.newBuilder()
                .setName(eventTrigger.getName());
        if (eventTrigger.getId() != null) {
            builder.setId(eventTrigger.getId());
        }
        if (eventTrigger.getThreshold() != null) {
            builder.setThreshold(eventTrigger.getThreshold().toString());
        }
        if (eventTrigger.getCurrency() != null) {
            builder.setCurrency(eventTrigger.getCurrency());
        }
        if (eventTrigger.getXpPerBaseUnit() != null) {
            builder.setXpPerBaseUnit(eventTrigger.getXpPerBaseUnit().toString());
        }

        if (eventTrigger instanceof PlayEventTrigger play) {
            var playTrigger = loyalty.api.v1.PlayEventTrigger.newBuilder();
            ProtoUtils.applyIfNotEmpty(play.getGameCodes(), playTrigger::addAllGameCodes);
            ProtoUtils.applyIfNotEmpty(play.getGameSuppliers(), playTrigger::addAllGameSuppliers);

            builder.setPlayTrigger(playTrigger.build());
        } else if (eventTrigger instanceof PurchaseEventTrigger) {
            builder.setPurchaseTrigger(
                    loyalty.api.v1.PurchaseEventTrigger.newBuilder().build());
        } else {
            throw new IllegalArgumentException(
                    "Unknown EventTrigger implementation: " + eventTrigger.getClass().getName());
        }

        return builder.build();
    }

    public static Iterable<LoyaltyLevel> toProto(List<LoyaltySystemLevel> levels) {
        var levelList = new ArrayList<LoyaltyLevel>();
        for (var level : levels) {
            var proto = LoyaltyLevel.newBuilder()
                    .setScope(level.getScope().code())
                    .setXpRequired(level.getXpRequired().toString())
                    .setSubLevel(level.isSubLevel())
                    .setInternalName(level.getInternalName());
            if (level.getXpMultiplier() != null) {
                proto.setXpMultiplier(level.getXpMultiplier().toString());
            }
            if (level.getId() != null) {
                proto.setId(level.getId());
            }
            if (level instanceof NamedLoyaltySystemLevel namedLevel) {
                proto.setNamedLevel(toProto(namedLevel));
            } else if (level instanceof NumberedLoyaltySystemLevel numberedLevel) {
                proto.setNumberedLevel(toProto(numberedLevel));
            } else {
                log.error("skipping unsupported level type: {}", level.getClass());
            }
            levelList.add(proto.build());
        }
        return levelList;
    }

    public static loyalty.api.v1.NamedLoyaltySystemLevel toProto(NamedLoyaltySystemLevel level) {
        var builder = loyalty.api.v1.NamedLoyaltySystemLevel.newBuilder();
        ProtoUtils.applyIfNotEmpty(level.getDisplayName(), builder::setDisplayName);
        ProtoUtils.applyIfNotEmpty(level.getDisplayNameShort(), builder::setDisplayNameShort);

        ProtoUtils.applyIfNotEmpty(level.getIconUrlSm(), builder::setIconUrlSm);
        ProtoUtils.applyIfNotEmpty(level.getIconUrlLg(), builder::setIconUrlLg);

        return builder.build();
    }

    public static loyalty.api.v1.NumberedLoyaltySystemLevel toProto(NumberedLoyaltySystemLevel level) {
        return loyalty.api.v1.NumberedLoyaltySystemLevel.newBuilder().setRank(level.getRank()).build();
    }

    public static LoyaltySystem toDto(loyalty.api.v1.LoyaltySystem proto) {
        var dto = new LoyaltySystem();
        dto.setId(proto.getId());
        dto.setCode(proto.getCode());
        dto.setName(proto.getName());
        dto.setType(LoyaltySystemLevelTypeSpec.fromString(proto.getType()));
        dto.setActive(proto.getActive());
        dto.setTestOnly(proto.getTestOnly());
        dto.setLocked(proto.getLocked());
        dto.setGiveXpGoals(proto.getGiveXpGoals());
        if (proto.hasDetailsUrl()) {
            dto.setDetailsUrl(proto.getDetailsUrl());
        }
        if (proto.hasPopupReminderIntervalDays()) {
            dto.setPopupReminderIntervalDays(proto.getPopupReminderIntervalDays());
        }
        if (proto.hasXpFirstGoal()) {
            dto.setXpFirstGoal(new BigDecimal(proto.getXpFirstGoal()));
        }
        if (proto.hasXpEveryNextGoal()) {
            dto.setXpEveryNextGoal(new BigDecimal(proto.getXpEveryNextGoal()));
        }
        dto.setXpVariant(toVariantDto(proto.getXpVariant()));
        dto.setLevels(proto.getLevelsList().stream().map(LoyaltySystemMapper::toDto).toList());
        dto.setEventTriggers(proto.getEventTriggersList().stream().map(LoyaltySystemMapper::toDto).toList());
        dto.setPlacements(proto.getPlacementsList().stream().map(LoyaltyPlacementMapper::toDto).toList());
        return dto;
    }

    public static LoyaltySystemLevel toDto(LoyaltyLevel proto) {
        var scope = LoyaltySystemLevelScopeSpec.fromString(proto.getScope());
        var xpRequired = new BigDecimal(proto.getXpRequired());
        var internalName = proto.getInternalName();
        var xpMultiplier = proto.hasXpMultiplier() ? new BigDecimal(proto.getXpMultiplier()) : BigDecimal.ONE;
        long id = proto.getId();
        if (proto.hasNamedLevel()) {
            return toNamedLevel(proto, scope, xpRequired, id, internalName, xpMultiplier);
        }
        if (proto.hasNumberedLevel()) {
            return toNumberedLevel(proto, scope, xpRequired, id, internalName, xpMultiplier);
        }
        throw new IllegalArgumentException("unknown level type: " + proto);
    }

    private static NamedLoyaltySystemLevel toNamedLevel(LoyaltyLevel proto, LoyaltySystemLevelScopeSpec scope,
            BigDecimal xpRequired, long id, String internalName, BigDecimal xpMultiplier) {
        var namedProto = proto.getNamedLevel();
        var lvl = new NamedLoyaltySystemLevel();
        lvl.setId(id);
        lvl.setInternalName(internalName);
        lvl.setXpMultiplier(xpMultiplier);
        lvl.setScope(scope);
        lvl.setXpRequired(xpRequired);
        lvl.setDisplayName(namedProto.getDisplayName());
        lvl.setSubLevel(proto.getSubLevel());
        if (!namedProto.getDisplayNameShort().isBlank()) {
            lvl.setDisplayNameShort(namedProto.getDisplayNameShort());
        }
        if (isNotBlank(namedProto.getIconUrlSm())) {
            lvl.setIconUrlSm(namedProto.getIconUrlSm());
        }
        if (isNotBlank(namedProto.getIconUrlLg())) {
            lvl.setIconUrlLg(namedProto.getIconUrlLg());
        }
        return lvl;
    }

    private static NumberedLoyaltySystemLevel toNumberedLevel(LoyaltyLevel proto, LoyaltySystemLevelScopeSpec scope,
            BigDecimal xpRequired, long id, String internalName, BigDecimal xpMultiplier) {
        var numberedLvlProto = proto.getNumberedLevel();
        var lvl = new NumberedLoyaltySystemLevel();
        lvl.setId(id);
        lvl.setRank(numberedLvlProto.getRank());
        lvl.setScope(scope);
        lvl.setXpRequired(xpRequired);
        lvl.setInternalName(internalName);
        lvl.setXpMultiplier(xpMultiplier);
        lvl.setSubLevel(proto.getSubLevel());
        return lvl;
    }

    public static XpVariant toVariantDto(loyalty.api.v1.XpVariant proto) {
        var dto = new XpVariant();
        dto.setId(proto.getId());
        dto.setName(proto.getName());
        dto.setCode(proto.getCode());
        dto.setDisplayName(proto.getDisplayName());
        dto.setAbbreviation(proto.getAbbreviation());
        if (proto.hasIconUrlLg()) {
            dto.setIconUrlLg(proto.getIconUrlLg());
        }
        if (proto.hasIconUrlSm()) {
            dto.setIconUrlSm(proto.getIconUrlSm());
        }
        dto.setActive(proto.getActive());
        if (proto.hasXpExpirationDays()) {
            dto.setXpExpirationDays(proto.getXpExpirationDays());
        }
        return dto;
    }

    public static EventTrigger toDto(LoyaltyEventTrigger proto) {
        if (proto.hasPlayTrigger()) {
            return toPlayTrigger(proto);
        }
        if (proto.hasPurchaseTrigger()) {
            return toPurchaseTrigger(proto);
        }
        throw new IllegalArgumentException("unknown trigger type: " + proto);
    }

    private static PlayEventTrigger toPlayTrigger(LoyaltyEventTrigger proto) {
        var dto = new PlayEventTrigger();
        dto.setId(proto.getId());
        dto.setName(proto.getName());
        dto.setXpPerBaseUnit(new BigDecimal(proto.getXpPerBaseUnit()));
        dto.setCurrency(proto.getCurrency());
        if (isNotBlank(proto.getThreshold())) {
            dto.setThreshold(new BigDecimal(proto.getThreshold()));
        }
        if (proto.getPlayTrigger().getGameCodesCount() > 0) {
            dto.setGameCodes(proto.getPlayTrigger().getGameCodesList());
        }
        if (proto.getPlayTrigger().getGameSuppliersCount() > 0) {
            dto.setGameSuppliers(proto.getPlayTrigger().getGameSuppliersList());
        }
        return dto;
    }

    private static PurchaseEventTrigger toPurchaseTrigger(LoyaltyEventTrigger proto) {
        var dto = new PurchaseEventTrigger();
        dto.setId(proto.getId());
        dto.setName(proto.getName());
        dto.setXpPerBaseUnit(new BigDecimal(proto.getXpPerBaseUnit()));
        dto.setCurrency(proto.getCurrency());
        if (isNotBlank(proto.getThreshold())) {
            dto.setThreshold(new BigDecimal(proto.getThreshold()));
        }
        return dto;
    }

    public static LoyaltySystemStatusEnum toAccountStatusProto(AccountLoyaltySystemStatus status) {
        return switch (status) {
            case ACTIVE -> LoyaltySystemStatusEnum.ACTIVE;
            case INACTIVE -> LoyaltySystemStatusEnum.INACTIVE;
            case SILENT -> LoyaltySystemStatusEnum.SILENT;
            default -> throw new IllegalArgumentException("Unknown AccountLoyaltySystemStatus: " + status);
        };
    }

    public static AccountLoyaltySystemStatus fromAccountStatusProto(LoyaltySystemStatusEnum status) {
        return switch (status) {
            case LoyaltySystemStatusEnum.ACTIVE -> AccountLoyaltySystemStatus.ACTIVE;
            case LoyaltySystemStatusEnum.INACTIVE -> AccountLoyaltySystemStatus.INACTIVE;
            case LoyaltySystemStatusEnum.SILENT -> AccountLoyaltySystemStatus.SILENT;
            default -> throw new IllegalArgumentException("Unknown AccountLoyaltySystemStatus: " + status);
        };
    }

    public static loyalty.api.v1.XpVariant toVariantProto(XpVariant variant, String brand) {
        loyalty.api.v1.XpVariant.Builder builder = loyalty.api.v1.XpVariant.newBuilder();
        if (variant.getId() != null) {
            builder.setId(variant.getId());
        }
        if (variant.getCode() != null) {
            builder.setCode(variant.getCode());
        }
        if (variant.getIconUrlLg() != null) {
            builder.setIconUrlLg(variant.getIconUrlLg());
        }
        if (variant.getIconUrlSm() != null) {
            builder.setIconUrlSm(variant.getIconUrlSm());
        }
        if (variant.getXpExpirationDays() != null) {
            builder.setXpExpirationDays(variant.getXpExpirationDays());
        }
        return builder
                .setName(variant.getName())
                .setBrand(brand)
                .setDisplayName(variant.getDisplayName())
                .setAbbreviation(variant.getAbbreviation())
                .setActive(variant.isActive())
                .build();
    }
}
