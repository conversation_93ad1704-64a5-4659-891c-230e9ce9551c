package admin.loyalty.model.level;

import com.fasterxml.jackson.annotation.JsonTypeName;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@JsonTypeName("named_level")
@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class NamedLoyaltySystemLevel extends LoyaltySystemLevel implements Serializable {

    @NotBlank
    private String displayName;
    private String displayNameShort;

    private String iconUrlSm;
    private String iconUrlLg;

}
