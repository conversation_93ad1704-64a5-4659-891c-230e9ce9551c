package admin.loyalty.model.level;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.DiscriminatorMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import loyalty.api.spec.LoyaltySystemLevelScopeSpec;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(value = NamedLoyaltySystemLevel.class, name = "named_level"),
        @JsonSubTypes.Type(value = NumberedLoyaltySystemLevel.class, name = "numbered_level")}
)
@Schema(
        description = "LoyaltyLevel object which can be one of the defined objects. " +
                "*'type'* field of the level, used for polymorphic deserialization. Possible values: **named_level, numbered_level**.",
        discriminatorProperty = "type",
        discriminatorMapping = {
                @DiscriminatorMapping(value = "named_level", schema = NamedLoyaltySystemLevel.class),
                @DiscriminatorMapping(value = "numbered_level", schema = NumberedLoyaltySystemLevel.class)
        },
        oneOf = {NamedLoyaltySystemLevel.class, NumberedLoyaltySystemLevel.class}
)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public abstract class LoyaltySystemLevel implements Serializable {

    private Long id;

    private String internalName;

    private BigDecimal xpMultiplier;

    @NotNull
    private LoyaltySystemLevelScopeSpec scope;

    @PositiveOrZero
    private BigDecimal xpRequired;

    private boolean subLevel;

}
