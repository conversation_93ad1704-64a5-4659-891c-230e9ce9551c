package loyalty.worker.flows.replicator;

import static loyalty.worker.LoyaltyWorkerProto.UPS_FLOWS;
import static loyalty.worker.flows.replicator.LoyaltyFlowsLevelTransitionReplicator.LOYALTY_LEVEL_TRANSITION_EVENT_TYPE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

import java.time.Instant;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.ConfigurableCloudConnector;
import org.springframework.cloud.DynamicCloud;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.PlainServiceInfo;

import api.v1.AccountRoutingInfo;
import engagement.flows.FlowsJaxRsClient;
import engagement.flows.data.FlowsEvent;
import loyalty.api.spec.LoyaltySystemLevelTypeSpec;
import loyalty.api.v1.LevelTransitionType;
import loyalty.api.v1.LoyaltyLevel;
import loyalty.api.v1.LoyaltyXpLevelUpdateEvent;
import loyalty.model.AccountBalanceVariant;
import loyalty.model.AccountLoyaltySystem;
import loyalty.model.LoyaltyAccount;
import loyalty.model.LoyaltyBrand;
import loyalty.model.LoyaltySystem;
import loyalty.model.config.Variant;
import loyalty.worker.flows.event.FlowsLevelTransitionEvent;

@ExtendWith(MockitoExtension.class)
class LoyaltyFlowsLevelTransitionReplicatorTest {

    private static final long ACCOUNT_ID = 123L;
    private static final String ACCOUNT_HASH = "routingKey";
    private static final String ACCOUNT_UUID = ACCOUNT_HASH + "/" + ACCOUNT_ID;
    private static final String BRAND = "bluedream";

    @Mock
    private FlowsJaxRsClient client;
    @Mock
    private ApplicationProperties props;
    @Captor
    private ArgumentCaptor<FlowsEvent> flowsEventArgumentCaptor;

    private LoyaltyFlowsLevelTransitionReplicator replicator;

    LoyaltyLevel levOne50 = LoyaltyLevel.newBuilder().setInternalName("Level1").setXpRequired("50.0").setScope("public").build();
    LoyaltyLevel levTwo100 = LoyaltyLevel.newBuilder().setInternalName("Level2").setXpRequired("100.0").setScope("public").build();
    LoyaltyLevel levThree250 = LoyaltyLevel.newBuilder().setInternalName("Level3").setXpRequired("250.0").setScope("public").build();

    @BeforeEach
    void setUp() {
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        cloud.addUps(new PlainServiceInfo(UPS_FLOWS,
                "https://user_api_flows:<EMAIL>"));
        replicator = new LoyaltyFlowsLevelTransitionReplicator(props, cloud, client);
    }

    @Test
    void shouldSendBloomreachEvent_LevelUpgrade() throws Throwable {
        AccountLoyaltySystem als = createAccountLoyaltySystem();
        var updateLevelEvent = createLevelUpdateEvent(als);

        replicator.apply(updateLevelEvent.build());

        verify(client).sendEventAsync(eq(LOYALTY_LEVEL_TRANSITION_EVENT_TYPE), flowsEventArgumentCaptor.capture(), any());
        FlowsEvent flowsEvent = flowsEventArgumentCaptor.getValue();
        assertEquals(ACCOUNT_UUID, flowsEvent.accountId);
        assertEquals(updateLevelEvent.getAt(), flowsEvent.eventTime);

        var event = assertInstanceOf(FlowsLevelTransitionEvent.class, flowsEvent);
        assertEquals(BRAND, event.brand);
        assertEquals(als.getLoyaltySystem().getCode().toString(), event.loyaltySystemCode);
        assertEquals(als.getAccountBalanceVariant().getVariant().getCode().toString(), event.variantCode);
        assertTrue(event.firstTimeReachedLevel);
        assertEquals("Level2".toLowerCase(), event.levelCurrent);
        assertEquals("Level1".toLowerCase(), event.levelBefore);
        assertEquals("upgrade", event.transitionType);
        assertEquals("public", event.levelScope);
        assertEquals(101.0, event.currentXp.doubleValue(), 0.001);
        assertEquals(149, event.neededXpToNextLevel.doubleValue(), 0.001);
    }

    @Test
    void shouldSendBloomreachEvent_LevelUpgradeNoNextLevel() throws Throwable {
        AccountLoyaltySystem als = createAccountLoyaltySystem();
        var updateLevelEvent = createLevelUpdateEvent(als);

        updateLevelEvent.clearLevelNext();

        replicator.apply(updateLevelEvent.build());

        verify(client).sendEventAsync(eq(LOYALTY_LEVEL_TRANSITION_EVENT_TYPE), flowsEventArgumentCaptor.capture(), any());

        var event = assertInstanceOf(FlowsLevelTransitionEvent.class, flowsEventArgumentCaptor.getValue());
        assertEquals("Level2".toLowerCase(), event.levelCurrent);
        assertEquals("Level1".toLowerCase(), event.levelBefore);
        assertEquals("upgrade", event.transitionType);
        assertEquals("public", event.levelScope);
        // if no next level, then the neededXpToNextLevel should be 0
        assertEquals(0.0, event.neededXpToNextLevel.doubleValue(), 0.001);
    }

    @Test
    void shouldSendBloomreachEvent_LevelDowngrade() throws Throwable {
        AccountLoyaltySystem als = createAccountLoyaltySystem();
        var updateLevelEvent = createLevelUpdateEvent(als);

        updateLevelEvent.setTransitionType(LevelTransitionType.DOWNGRADE);
        updateLevelEvent.setLevelPrevious(levTwo100);
        updateLevelEvent.setLevelCurrent(levOne50);
        updateLevelEvent.setLevelNext(levTwo100);
        updateLevelEvent.setXpCurrent("99.0");

        replicator.apply(updateLevelEvent.build());

        verify(client).sendEventAsync(eq(LOYALTY_LEVEL_TRANSITION_EVENT_TYPE), flowsEventArgumentCaptor.capture(), any());

        var event = assertInstanceOf(FlowsLevelTransitionEvent.class, flowsEventArgumentCaptor.getValue());
        assertEquals("Level1".toLowerCase(), event.levelCurrent);
        assertEquals("Level2".toLowerCase(), event.levelBefore);
        assertEquals("downgrade", event.transitionType);
        assertEquals("public", event.levelScope);
        assertEquals(99.0, event.currentXp.doubleValue(), 0.001);
        assertEquals(1.0, event.neededXpToNextLevel.doubleValue(), 0.001);
    }

    private LoyaltyXpLevelUpdateEvent.Builder createLevelUpdateEvent(AccountLoyaltySystem als) {
        var routing = AccountRoutingInfo.newBuilder()
                .setHash(ACCOUNT_HASH)
                .setId(ACCOUNT_ID)
                .setBrand(BRAND);

        var leveUpdateEvent = LoyaltyXpLevelUpdateEvent.newBuilder()
                .setRouting(routing)
                .setVariantCode(als.getAccountBalanceVariant().getVariant().getCode().toString())
                .setAccountLoyaltySystemCode(als.getCode().toString())
                .setLoyaltySystemCode(als.getLoyaltySystem().getCode().toString())
                .setXpCurrent("101.0000")
                .setTransitionType(LevelTransitionType.UPGRADE)
                .setAt(Instant.now().toEpochMilli());

        leveUpdateEvent.setFirstTimeReachedLevel(true);

        leveUpdateEvent.setLevelPrevious(levOne50);
        leveUpdateEvent.setLevelCurrent(levTwo100);
        leveUpdateEvent.setLevelNext(levThree250);

        return leveUpdateEvent;
    }

    private static AccountLoyaltySystem createAccountLoyaltySystem() {
        var brand = new LoyaltyBrand("bluedream");
        var account = new LoyaltyAccount(brand);
        account.setRemoteId(ACCOUNT_ID);
        account.setHash(ACCOUNT_HASH);
        var variant = new Variant(brand, "HDG", "Hot Dog", "hot_gog");
        var system = new LoyaltySystem(brand, "Test", variant, LoyaltySystemLevelTypeSpec.NAMED);
        var abv = new AccountBalanceVariant(account, variant);
        return new AccountLoyaltySystem(system, abv);
    }

}
