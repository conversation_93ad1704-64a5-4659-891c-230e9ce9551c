package loyalty.worker.flows.event;

import engagement.flows.data.FlowsEvent;
import java.math.BigDecimal;
import lombok.ToString;

@ToString
public class FlowsLevelTransitionEvent extends FlowsEvent {
    public String brand;
    public String loyaltySystemCode;
    public String variantCode;
    public String levelBefore;
    public String levelCurrent;
    public String levelNext;
    public String transitionType;
    public String levelScope;
    public boolean firstTimeReachedLevel;
    public BigDecimal currentXp;
    public BigDecimal neededXpToNextLevel;

    public boolean isSubLevelCurrent;
    public boolean isSubLevelNext;
    public String mainLevelCurrent;
    public String mainLevelNext;
    public BigDecimal neededXpToNextMainLevel;


//
//    The internal name of the current main level.
//
//    The internal name of the next main level (if there is a next main level).
//
//    The XP needed to reach the next main level (or the last level in total if there is no next main level)
}
