package offerchain.handlers;

import static api.util.ErrorDetailUtils.detail;
import static api.v1.Code.ERR_BAD_REQUEST;
import static api.v1.Reason.BAD_REQUEST;
import static offerchain.mapper.AccountMapper.toAccountRouting;
import static offerchain.mapper.OfferChainInstanceMapper.toOfferChainInstanceInfo;
import static offerchain.model.OfferChainInstance.Status.ASSIGNED;
import static offerchain.model.OfferChainInstance.Status.REPLACED;
import static offerchain.model.OfferChainInstanceSnapshot.getFirstPurchaseOffer;
import static offerchain.model.OfferChainMapItemType.PURCHASE;
import static offerchain.model.PlacementTypeSpec.INBOX_NOTIFICATION;

import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.springframework.stereotype.Service;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import api.v1.ApplicationException;
import api.v1.EnhancedApplicationException;
import api.v1.ForcementModeSpec;
import io.ebean.DuplicateKeyException;
import io.ebean.Transaction;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;
import offerchain.OfferChainExternalIdentityManager;
import offerchain.OfferChainJpaManager;
import offerchain.OfferChainServerProperties;
import offerchain.api.v1.CreateOfferChainInstanceRequest;
import offerchain.api.v1.CreateOfferChainInstanceResponse;
import offerchain.api.v1.OfferChainInstanceEvent;
import offerchain.model.OfferChainAccount;
import offerchain.model.OfferChainBrand;
import offerchain.model.OfferChainInstance;
import offerchain.model.OfferChainInstanceSnapshot;
import offerchain.model.OfferChainTemplate;
import offerchain.model.OfferChainUserProgress;
import offerchain.services.OfferChainFlowService;
import offerchain.services.OfferService;
import offerchain.services.PurchaseOffersService;
import uam.api.UamServiceApi;
import uam.api.v1.AccountPaymentInfo;
import uam.api.v1.AccountRestriction;
import uam.api.v1.GetAccountEngagementInfoRequest;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;

@Service
@Slf4j
public class CreateOfferChainInstanceRequestHandler
        extends AbstractAuthorizedRequestHandler<CreateOfferChainInstanceRequest, CreateOfferChainInstanceResponse.Builder>
        implements ModifyHandler<CreateOfferChainInstanceRequest, CreateOfferChainInstanceResponse.Builder> {

    private final UamServiceApi uamServiceApi;
    private final OfferChainFlowService offerChainFlowService;
    private final PurchaseOffersService purchaseOffersService;
    private final OfferService offerService;

    public CreateOfferChainInstanceRequestHandler(
            OfferChainServerProperties props,
            OfferChainJpaManager ebean,
            UamServiceApi uamServiceApi,
            OfferChainFlowService offerChainFlowService,
            PurchaseOffersService purchaseOffersService,
            OfferService offerService,
            OfferChainExternalIdentityManager identityManager,
            MeterRegistry meterRegistry,
            ApplicationProperties applicationProperties) {
        super(props, ebean, identityManager);
        Objects.requireNonNull(props);
        this.uamServiceApi = Objects.requireNonNull(uamServiceApi);
        this.offerChainFlowService = Objects.requireNonNull(offerChainFlowService);
        this.purchaseOffersService = Objects.requireNonNull(purchaseOffersService);
        this.offerService = Objects.requireNonNull(offerService);
    }

    @Override
    protected Set<Identity.TypeCase> authTypes() {
        return Set.of(Identity.TypeCase.BYACCOUNTID);
    }

    @Override
    public boolean isImmediateAcknowledge() {
        return true;
    }

    @Override
    public void apply(TransactionalRequest<CreateOfferChainInstanceRequest, CreateOfferChainInstanceResponse.Builder> cmd) throws Throwable {
        var request = cmd.request();
        var response = cmd.reply();
        validateRequest(request);
        var externalAccount = request.getExternalAccount();
        try {
            OfferChainTemplate template;
            OfferChainAccount account;
            OfferChainInstance newInstance;
            Optional<OfferChainInstance> existingInstance;
            Optional<OfferChainInstanceSnapshot.MapItem> existingInstanceActiveMapItem = Optional.empty();

            try (var tx = ebean.newTransaction()) {
                var brand = ebean.offerChainBrandRepo().requiredBrand(request.getBrandName(), tx);
                template = fetchAndValidateTemplate(request.getOfferChainTemplateCode(), brand, tx);
                var accountBrand = identityManager.brandOrCreate(externalAccount.getBrandName(), tx);
                account = identityManager.accountOrCreate(externalAccount.getAccountId(), externalAccount.getHash(), accountBrand, tx);
                existingInstance = ebean.offerChainRepo().findActiveInstanceByAccount(account, tx);

                if (existingInstance.isPresent()) {
                    if (request.getOverwriteExistingInstance()) {
                        existingInstanceActiveMapItem = offerChainFlowService.getActiveMapItem(existingInstance.get(), tx);
                        markInstanceAsReplaced(existingInstance.get(), tx);
                    } else {
                        response.setSuccess(false).setErrorMessage("An active or scheduled instance already exists for this account.");
                        return;
                    }
                }

                newInstance = createOfferChainInstance(account, brand, template, request.getAvailableFrom());
                ebean.offerChainRepo().saveInstance(newInstance, tx);
                log.trace("OfferChainInstance has been saved with id={} and code={}", newInstance.getId(), newInstance.getCode().toString());
                tx.commit();
            }

            applySegmentTags(template, existingInstance, newInstance, existingInstanceActiveMapItem, account);

            purchaseOffersService.activateNewInstance(
                newInstance,
                existingInstance.orElse(null),
                existingInstanceActiveMapItem.orElse(null));

            if (existingInstance.isPresent()) {
                sendNotification(cmd, template, existingInstance.get());
            }
            sendNotification(cmd, template, newInstance);

            response.setSuccess(true).setOfferChainInstanceCode(newInstance.getCode().toString());

        } catch (DuplicateKeyException dkex) {
            log.warn("DuplicateKeyException while trying to create OfferChainInstance!", dkex);
            response.setSuccess(false).setErrorMessage("Duplicate entry");
        } catch (EnhancedApplicationException e) {
            log.warn("Failed to create OfferChainInstance due to known application exception!", e);
            response.setSuccess(false).setErrorMessage(e.getMessage());
        } catch (Exception e) {
            log.warn("Failed to create OfferChainInstance!", e);
            response.setSuccess(false).setErrorMessage("Unexpected error: " + e.getMessage());
        }
    }

    private void sendNotification(TransactionalRequest<CreateOfferChainInstanceRequest, CreateOfferChainInstanceResponse.Builder> cmd,
                                  OfferChainTemplate offerChainTemplate,
                                  OfferChainInstance offerChainInstance) throws Throwable {
        boolean inboxNotificationPlacementIsPresent = offerChainInstance.hasPlacementTypeConfig(INBOX_NOTIFICATION);

        log.debug("sendNotification: template.hidden={}, instance.status={}, inboxNotificationPlacementIsPresent={}",
                offerChainTemplate.isHidden(), offerChainInstance.getStatus(), inboxNotificationPlacementIsPresent);

        if (offerChainTemplate.isHidden()) {
            log.debug("Template {} is hidden; skipping inbox notification for instance {}",
                    offerChainTemplate.getCode(), offerChainInstance.getCode());
            return;
        }
        if (offerChainInstance.getStatus() != ASSIGNED && offerChainInstance.getStatus() != REPLACED) {
            log.debug("Instance {} is in status {}; only ASSIGNED or REPLACED triggers notifications. Skipping.",
                    offerChainInstance.getCode(), offerChainInstance.getStatus());
            return;
        }
        if (!inboxNotificationPlacementIsPresent) {
            log.debug("Instance {} has no 'promotion_entry' placement; skipping notification",
                    offerChainInstance.getCode());
            return;
        }

        List<OfferChainUserProgress> userProgress;
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            userProgress = ebean.offerChainRepo().findOfferChainUserProgress(offerChainInstance, tx);
        }

        var purchaseOfferMapping = offerService.getPurchaseOffersMapping(List.of(offerChainInstance), offerChainInstance.getAccount());
        var event = OfferChainInstanceEvent.newBuilder()
                .setAccount(toAccountRouting(offerChainInstance.getAccount()))
                .setOfferChainInstanceInfo(toOfferChainInstanceInfo(offerChainInstance, userProgress, purchaseOfferMapping));

        cmd.eventStream(event);

        log.debug("Inbox notification sent for instance {} after it reached {}", offerChainInstance.getCode(), offerChainInstance.getStatus().name());
    }

    private void applySegmentTags(
            OfferChainTemplate template,
            Optional<OfferChainInstance> existingInstance,
            OfferChainInstance newInstance,
            Optional<OfferChainInstanceSnapshot.MapItem> existingInstanceActiveMapItem,
            OfferChainAccount account) throws Exception {
        //noinspection deprecation
        if (template.isRewardOfferOnly()) {
            return;
        }
        if (existingInstance.isPresent() && existingInstanceActiveMapItem.isPresent()) {
            var offerOpt = getFirstPurchaseOffer(existingInstanceActiveMapItem.get());
            if (offerOpt.isPresent()) {
                String oldOfferCode = offerOpt.get().getPurchaseOfferTemplateCode();
                purchaseOffersService.removePurchaseOfferSegmentTagsIfPresent(account, oldOfferCode);
            }
        }

        var firstMapItem = template.getFirstMapItem();
        if (newInstance.getStatus() == ASSIGNED && firstMapItem.getOfferChainMapItemType() == PURCHASE) {
            String newOfferCode = firstMapItem.getOffers().getFirst().getPurchaseOfferTemplateCode();
            purchaseOffersService.applyPurchaseOfferSegmentTagsIfPresent(account, newOfferCode);
        }
    }

    private AccountPaymentInfo obtainAccountInfo(CreateOfferChainInstanceRequest request) throws Exception {
        var apiRequest = GetAccountEngagementInfoRequest.newBuilder()
                .setIdentity(Identity.newBuilder()
                        .setByAccountId(IdentityByAccountId.newBuilder()
                                .setAccountId(request.getExternalAccount().getAccountId())))
                .build();
        return uamServiceApi.getAccountEngagementInfo(apiRequest, AsciiString.cached(request.getExternalAccount().getHash()))
                .thenVerifyOk()
                .get()
                .unpack()
                .getInfo();
    }

    private void checkAccountRestriction(AccountPaymentInfo infoInfo) throws ApplicationException {
        var accountMode = ForcementModeSpec.fromString(infoInfo.getMode());
        var restrictionsList = infoInfo.getRestrictionsList();

        if (restrictionsList.contains(AccountRestriction.NO_GAME)) {
            throw EnhancedApplicationException.of("Account has restriction: no game play.", ERR_BAD_REQUEST, BAD_REQUEST);
        }

        if (restrictionsList.contains(AccountRestriction.NO_PURCHASE)) {
            throw EnhancedApplicationException.of("Account has restriction: no purchase.", ERR_BAD_REQUEST, BAD_REQUEST);
        }

        if (!accountMode.isSweepstakeMode()) {
            throw EnhancedApplicationException.of("Account has restriction: not in sweepstakes mode.", ERR_BAD_REQUEST, BAD_REQUEST);
        }
    }

    private OfferChainTemplate fetchAndValidateTemplate(String templateCodeStr, OfferChainBrand brand, Transaction tx)
            throws EnhancedApplicationException {
        var templateCode = UUID.fromString(templateCodeStr);
        var templateOpt = ebean.offerChainRepo().findTemplateByCodeAndBrand(templateCode, brand, tx);
        if (templateOpt.isEmpty()) {
            throw EnhancedApplicationException.of("OfferChainTemplate not found by code: " + templateCode, ERR_BAD_REQUEST, BAD_REQUEST);
        }
        var template = templateOpt.get();
        if (!template.isActive()) {
            throw EnhancedApplicationException.of(
                    "Template [" + template.getName() + "] is not active",
                    ERR_BAD_REQUEST,
                    BAD_REQUEST);
        }
        return template;
    }

    private OfferChainInstance createOfferChainInstance(OfferChainAccount account,
                                                        OfferChainBrand brand,
                                                        OfferChainTemplate template,
                                                        String availableFrom) throws ApplicationException {
        var now = Instant.now();
        Instant availableFromDate = availableFrom != null && !availableFrom.isBlank() ? parseInstant(availableFrom) : now;
        return OfferChainInstance.create(brand, account, template, availableFromDate);
    }

    private void markInstanceAsReplaced(OfferChainInstance instance, Transaction tx) {
        instance.setStatus(REPLACED);
        ebean.offerChainRepo().saveInstance(instance, tx);
    }

    private void validateRequest(CreateOfferChainInstanceRequest request) throws Exception {
        if (request.getBrandName().isBlank()) {
            throw EnhancedApplicationException.of("Brand name is required", ERR_BAD_REQUEST, BAD_REQUEST)
                    .errorDetail(detail("brandName", request.getBrandName()));
        }
        if (request.getOfferChainTemplateCode().isBlank()) {
            throw EnhancedApplicationException.of("Offer chain template code is required", ERR_BAD_REQUEST, BAD_REQUEST)
                    .errorDetail(detail("offerChainTemplateCode", request.getOfferChainTemplateCode()));
        }
        validateUUID(request.getOfferChainTemplateCode(), "template code");
        if (request.hasAvailableFrom()) {
            parseInstant(request.getAvailableFrom());
        }
        checkAccountRestriction(obtainAccountInfo(request));
    }
}
