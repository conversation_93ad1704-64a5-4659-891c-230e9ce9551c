package offerchain.services;

import static java.time.temporal.ChronoUnit.DAYS;
import static offerchain.model.OfferChainMapItemType.PURCHASE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verifyNoInteractions;

import java.time.Instant;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.DynamicPropertyFactory;

import offerchain.OfferChainServerProperties;
import offerchain.model.OfferChainAccount;
import offerchain.model.OfferChainInstance;
import offerchain.model.OfferChainInstanceSnapshot.MapItem;
import payment.api.PaymentServiceApi;
import payment.api.v1.OfferReward;
import uam.api.UamServiceApi;

class PurchaseOffersServiceTest {

    private final DynamicPropertyFactory pf = ApplicationConfig.mock().factory();
    private final OfferChainServerProperties ocProps = new OfferChainServerProperties(pf);
    private final PaymentServiceApi paymentServiceApi = mock(PaymentServiceApi.class);
    private final PurchaseOffersService service = new PurchaseOffersService(
        mock(UamServiceApi.class), paymentServiceApi, ocProps
    );

    @Test
    void toOfferReward() {
        OfferChainInstance offerChainInstance = OfferChainInstance.builder()
            .availableFrom(Instant.now().minus(1, DAYS))
            .expiresAt(Instant.now().plus(1, DAYS))
            .build();
        OfferReward offerReward = PurchaseOffersService.toOfferReward("offer-code", offerChainInstance);
        assertThat(offerReward.getRef().getOfferCode()).isEqualTo("offer-code");
        assertThat(new Date(offerReward.getAvailableFrom())).isEqualTo(Date.from(offerChainInstance.getAvailableFrom()));
        assertThat(new Date(offerReward.getExpireAt())).isEqualTo(Date.from(offerChainInstance.getExpiresAt()));
        assertThat(offerReward.getMaxPurchaseCount()).isEqualTo(1);
    }

    @Test
    void toOfferReward_NoExpiration() {
        OfferChainInstance offerChainInstance = OfferChainInstance.builder()
            .build();
        OfferReward offerReward = PurchaseOffersService.toOfferReward("offer-code", offerChainInstance);
        assertThat(offerReward.getRef().getOfferCode()).isEqualTo("offer-code");
        assertThat(offerReward.hasAvailableFrom()).isFalse();
        assertThat(offerReward.hasExpireAt()).isFalse();
    }

    @Test
    void cancelOfferRewards_NoOffers() throws Exception {
        OfferChainAccount account = new OfferChainAccount();
        account.setId(1L);
        account.setRemoteId(2L);
        account.setHash("hash");
        OfferChainInstance offerChainInstance = OfferChainInstance.builder()
            .account(account)
            .build();
        MapItem activeItem = MapItem.builder()
            .offerChainMapItemType(PURCHASE.name())
            .offers(List.of())
            .build();
        service.cancelOfferRewards(offerChainInstance, activeItem);

        verifyNoInteractions(paymentServiceApi);
    }
}