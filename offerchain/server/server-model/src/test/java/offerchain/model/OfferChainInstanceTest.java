package offerchain.model;

import static offerchain.model.OfferChainMapItemType.FREE;
import static offerchain.model.OfferChainMapItemType.MULTIPLE_PURCHASE;
import static offerchain.model.OfferChainMapItemType.PURCHASE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import java.util.Arrays;

import org.junit.jupiter.api.Test;

import com.turbospaces.common.PlatformUtil;

import offerchain.model.OfferChainInstanceSnapshot.MapItem;
import offerchain.model.OfferChainInstanceSnapshot.MapItemOffer;

class OfferChainInstanceTest {

    @Test
    void getActiveOfferCodes_Purchase() {
        MapItem activeItem = createMapItem(PURCHASE,
            createPurchaseItemOffer("offer-2"),
            rewardItemOffer());
        OfferChainInstance instance = createInstance(
            createMapItem(FREE, rewardItemOffer()),
            activeItem,
            createMapItem(PURCHASE,
                createPurchaseItemOffer("offer-3"),
                rewardItemOffer()
            ));
        assertThat(instance.getActiveOfferCodes(activeItem)).containsExactly("offer-2");
    }

    @Test
    void getActiveOfferCodes_Free() {
        MapItem activeItem = createMapItem(FREE, rewardItemOffer());
        OfferChainInstance instance = createInstance(activeItem);
        assertThat(instance.getActiveOfferCodes(activeItem)).isEmpty();
    }

    @Test
    void getActiveOfferCodes_MultiplePurchase() {
        MapItem activeItem = createMapItem(MULTIPLE_PURCHASE,
            createPurchaseItemOffer("offer-1"),
            createPurchaseItemOffer("offer-2"),
            createPurchaseItemOffer(null));
        OfferChainInstance instance = createInstance(activeItem);
        assertThat(instance.getActiveOfferCodes(activeItem)).containsExactly("offer-1", "offer-2");

    }

    @Test
    void getInitialOfferCodes_Purchase() {
        OfferChainInstance instance = createInstance(
            createMapItem(PURCHASE,
                createPurchaseItemOffer("offer-1"),
                rewardItemOffer()),
            createMapItem(PURCHASE,
                createPurchaseItemOffer("offer-2"),
                rewardItemOffer()
            ));
        assertThat(instance.getInitialOfferCodes()).containsExactly("offer-1");
    }

    @Test
    void getInitialOfferCodes_NoOffers() {
        OfferChainInstance instance = createInstance(
            createMapItem(PURCHASE));
        assertThat(instance.getInitialOfferCodes()).isEmpty();
    }

    @Test
    void getInitialOfferCodes_Free() {
        OfferChainInstance instance = createInstance(
            createMapItem(FREE, rewardItemOffer()),
            createMapItem(PURCHASE,
                createPurchaseItemOffer("offer-2"),
                rewardItemOffer()
            ));

        assertThatThrownBy(instance::getInitialOfferCodes).isInstanceOf(IllegalStateException.class);
    }

    @Test
    void getInitialOfferCodes_MultiplePurchase() {
        OfferChainInstance instance = createInstance(
            createMapItem(MULTIPLE_PURCHASE,
                createPurchaseItemOffer("offer-1")),
            createMapItem(MULTIPLE_PURCHASE,
                createPurchaseItemOffer("offer-2")));
        assertThat(instance.getInitialOfferCodes()).containsExactly("offer-1", "offer-2");
    }

    private static MapItemOffer createPurchaseItemOffer(String offerCode) {
        return MapItemOffer.builder()
            .type(OfferChainMapItemOfferType.PURCHASE_OFFER.name())
            .purchaseOfferTemplateCode(offerCode).build();
    }

    private static MapItemOffer rewardItemOffer() {
        return MapItemOffer.builder().rewardCode(PlatformUtil.randomUUID()).build();
    }

    private static OfferChainInstance createInstance(MapItem... mapItems) {
        return OfferChainInstance.builder()
            .snapshot(OfferChainInstanceSnapshot.builder()
                .mapItems(Arrays.stream(mapItems).toList()).build()
            ).build();
    }

    private static MapItem createMapItem(OfferChainMapItemType mapItemType, MapItemOffer... mapItemOffers) {
        return MapItem.builder()
                .offerChainMapItemType(mapItemType.name())
                .offers(Arrays.stream(mapItemOffers).toList())
                .build();
    }
}
