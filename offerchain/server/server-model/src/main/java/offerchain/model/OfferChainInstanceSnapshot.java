package offerchain.model;

import static offerchain.model.OfferChainMapItemType.PURCHASE;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfferChainInstanceSnapshot {
    private String displayName;
    private String displayNameShort;
    private String displayTagline;
    private String displayDescription;
    private String iconInbox;
    private Instant availableFrom;
    private Instant expiresAt;
    private MapType mapType;
    private List<PlacementType> placementTypes;
    private List<MapItem> mapItems;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MapType {
        private UUID code;
        private String featureKey;
        private Long id;
        private String name;
        private Map<String, ParamValue> params;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PlacementType {
        private UUID code;
        private String name;
        private String featureKey;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MapItem {
        private Long id;
        private String name;
        private UUID code;
        private Integer position;
        private String displayTagline;
        private String displayTitle;
        private String displayDescription;
        private String backgroundImageUrl;
        private String smallOpenIconUrl;
        private String largeOpenIconUrl;
        private String smallDoneIconUrl;
        private String largeDoneIconUrl;
        private String offerChainMapItemType;
        private List<MapItemOffer> offers;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MapItemOffer {
        private Long id;
        private UUID code;
        private String type;

        private String backgroundUrl;
        private String displayTagline;
        private String displayDescription;

        private String smallOpenIconUrl;
        private String largeOpenIconUrl;
        private String smallDoneIconUrl;
        private String largeDoneIconUrl;

        private UUID rewardCode;
        // ~ drop after migration
        private Integer gcAmount;
        private Integer scAmount;
        private Integer freeSpinsAmount;
        private BigDecimal freeSpinsValue;
        private Integer freeSpinsBetLevel;
        private Integer freeSpinsGameId;
        private BigDecimal fallback1Value;
        private Integer fallback1Bet;
        private Integer fallback1Game;
        private BigDecimal fallback2Value;
        private Integer fallback2Bet;
        private Integer fallback2Game;
        private List<String> randomRewardIds;
        private String purchaseOfferTemplateCode;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MapItemType {
        private UUID code;
        private String featureKey;
    }

    public boolean isFirst(MapItem mapItem) {
        return mapItems.stream().mapToInt(MapItem::getPosition).min().orElse(-1) == mapItem.getPosition();
    }

    public static OfferChainInstanceSnapshot create(OfferChainTemplate template, Instant availableFromDate) {
        return OfferChainInstanceSnapshot.builder()
                .displayName(template.getDisplayName())
                .displayNameShort(template.getDisplayNameShort())
                .displayTagline(template.getDisplayTagline())
                .displayDescription(template.getDisplayDescription())
                .iconInbox(template.getIconInbox())
                .displayNameShort(template.getDisplayNameShort())
                .availableFrom(template.getAvailableFrom())
                .expiresAt(availableFromDate.plus(template.getExpiresAfterDays(), ChronoUnit.DAYS))
                .mapType(OfferChainInstanceSnapshot.MapType.builder()
                        .code(template.getMapType().getCode())
                        .featureKey(template.getMapType().getFeatureKey())
                        .build())
                .placementTypes(
                        template.getPlacements().stream()
                                .map(p -> {
                                    var pt = p.getPlacementType();
                                    return OfferChainInstanceSnapshot.PlacementType.builder()
                                            .code(pt.getCode())
                                            .featureKey(pt.getFeatureKey())
                                            .build();
                                })
                                .toList())
                .mapItems(
                        template.getMapItems().stream()
                                .map(mi -> MapItem.builder()
                                        .id(mi.getId())
                                        .name(mi.getName())
                                        .position(mi.getPosition())
                                        .displayTagline(mi.getDisplayTagline())
                                        .displayDescription(mi.getDisplayDescription())
                                        .code(mi.getCode())
                                        .offerChainMapItemType(mi.getOfferChainMapItemType().name())
                                        .displayTagline(mi.getDisplayTagline())
                                        .displayTitle(mi.getDisplayTitle())
                                        .displayDescription(mi.getDisplayDescription())
                                        .backgroundImageUrl(mi.getBackgroundImageUrl())
                                        .smallOpenIconUrl(mi.getSmallOpenIconUrl())
                                        .largeOpenIconUrl(mi.getLargeOpenIconUrl())
                                        .smallDoneIconUrl(mi.getSmallDoneIconUrl())
                                        .largeDoneIconUrl(mi.getLargeDoneIconUrl())
                                        .offers(mi.getOffers().stream()
                                                .map(OfferChainInstanceSnapshot::buildMapItemOffer)
                                                .toList())
                                        .build())
                                .toList())
                .build();
    }

    private static OfferChainInstanceSnapshot.MapItemOffer buildMapItemOffer(OfferChainMapItemOffer offer) {
        return OfferChainInstanceSnapshot.MapItemOffer.builder()
                .id(offer.getId())
                .type(offer.getType().name())
                .rewardCode(offer.getRewardCode())
                .gcAmount(offer.getGcAmount())
                .scAmount(offer.getScAmount())
                .freeSpinsAmount(offer.getFreeSpinsAmount())
                .freeSpinsValue(offer.getFreeSpinsValue())
                .freeSpinsBetLevel(offer.getFreeSpinsBetLevel())
                .freeSpinsGameId(offer.getFreeSpinsGameId())
                .fallback1Value(offer.getFallback1Value())
                .fallback1Bet(offer.getFallback1Bet())
                .fallback1Game(offer.getFallback1Game())
                .fallback2Value(offer.getFallback2Value())
                .fallback2Bet(offer.getFallback2Bet())
                .fallback2Game(offer.getFallback2Game())
                .code(offer.getCode())
                .randomRewardIds(offer.getRandomRewardIds())
                .purchaseOfferTemplateCode(offer.getPurchaseOfferTemplateCode())
                .build();
    }

    public static Optional<MapItemOffer> getFirstPurchaseOffer(MapItem mapItem) {
        var mapItemType = OfferChainMapItemType.valueOf(mapItem.getOfferChainMapItemType());
        if (mapItemType == PURCHASE) {
            return mapItem.getOffers().stream()
                    .filter(offer -> Objects.equals(offer.getType(), OfferChainMapItemOfferType.PURCHASE_OFFER.name()))
                    .findFirst();
        } else {
            return Optional.empty();
        }
    }

    public static List<MapItemOffer> getMultiplePurchaseOffers(MapItem mapItem) {
        var mapItemType = OfferChainMapItemType.valueOf(mapItem.getOfferChainMapItemType());
        if (mapItemType == OfferChainMapItemType.MULTIPLE_PURCHASE) {
            return mapItem.getOffers().stream()
                    .filter(offer -> Objects.equals(offer.getType(), OfferChainMapItemOfferType.PURCHASE_OFFER.name()))
                    .toList();
        }
        return List.of();
    }
}
