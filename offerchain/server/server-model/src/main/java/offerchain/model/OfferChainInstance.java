package offerchain.model;

import static com.google.common.base.Preconditions.checkNotNull;
import static com.google.common.base.Preconditions.checkState;
import static offerchain.model.OfferChainMapItemOfferType.PURCHASE_OFFER;
import static offerchain.model.OfferChainMapItemType.MULTIPLE_PURCHASE;
import static offerchain.model.OfferChainMapItemType.PURCHASE;

import java.time.Duration;
import java.time.Instant;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.turbospaces.common.PlatformUtil;

import io.ebean.annotation.DbEnumValue;
import io.ebean.annotation.DbJsonB;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;
import offerchain.model.OfferChainInstanceSnapshot.MapItem;
import offerchain.model.OfferChainInstanceSnapshot.MapItemOffer;

@Getter
@Setter
@Entity
@Builder
@Table(name = "offer_chain_instance", schema = Schemas.OFFER_CHAIN)
@NoArgsConstructor
@AllArgsConstructor
public class OfferChainInstance {
    private static final int DEFAULT_EXPIRATION_DAYS = 30;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false)
    private Instant expiresAt; // TODO make it consistent with template (availableTo)

    @OneToOne
    @JoinColumn(unique = true)
    private OfferChainMapItem mapItem; // TODO remove

    @ManyToOne(optional = false)
    @JoinColumn(nullable = false)
    private OfferChainAccount account;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private Status status;

    @ManyToOne(optional = false)
    @JoinColumn(nullable = false)
    private OfferChainTemplate offerChainTemplate;

    @Column(nullable = false, unique = true)
    private UUID code;

    @Column(nullable = false)
    private Instant availableFrom;

    @ManyToOne(optional = false)
    @JoinColumn(nullable = false)
    private OfferChainBrand brand;

    @DbJsonB
    @Column(name = "snapshot", columnDefinition = "jsonb")
    private OfferChainInstanceSnapshot snapshot;

    @Version
    private Integer version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public Optional<OfferChainInstanceSnapshot.MapItem> findMapItemByCode(String mapItemCode) {
        var targetCode = UUID.fromString(mapItemCode);

        return getSnapshot().getMapItems()
                .stream()
                .filter(mi -> mi.getCode().equals(targetCode))
                .findFirst();
    }

    public OfferChainInstanceSnapshot.MapItem getFirstMapItem() {
        return getSnapshot().getMapItems().stream()
                .min(Comparator.comparing(OfferChainInstanceSnapshot.MapItem::getPosition))
                .orElseThrow();
    }

    public boolean isMultiOptionalPurchaseOfferChainInstance() {
        return getSnapshot().getMapItems().stream().anyMatch(item -> MULTIPLE_PURCHASE.name().equalsIgnoreCase(item.getOfferChainMapItemType()));
    }

    public Optional<OfferChainInstanceSnapshot.MapItem> findMutiOptionalTargetMapItem(String offerCode) {
        return getSnapshot().getMapItems().stream()
                .filter(mapItem -> mapItem.getOffers().stream().anyMatch(offer -> offerCode.equals(offer.getPurchaseOfferTemplateCode())))
                .findAny();
    }

    public boolean hasPlacementTypeConfig(PlacementTypeSpec placementType) {
        return snapshot.getPlacementTypes().stream()
                .map(OfferChainInstanceSnapshot.PlacementType::getFeatureKey)
                .filter(StringUtils::isNotBlank)
                .map(PlacementTypeSpec::fromString)
                .flatMap(Optional::stream)
                .anyMatch(placementType::equals);
    }

    public static OfferChainInstance create(OfferChainBrand brand, OfferChainAccount account,
        OfferChainTemplate template,
        Instant availableFrom) {
        var now = Instant.now();
        var expirationDuration = Duration.ofDays(
            template.getExpiresAfterDays() != null && template.getExpiresAfterDays() > 0
                ? template.getExpiresAfterDays()
                : DEFAULT_EXPIRATION_DAYS);
        var initialState = availableFrom.isAfter(now)
            ? OfferChainInstance.Status.SCHEDULED
            : OfferChainInstance.Status.ASSIGNED;
        return OfferChainInstance.builder()
            .brand(brand)
            .code(PlatformUtil.randomUUID())
            .account(account)
            .offerChainTemplate(template)
            .status(initialState)
            .availableFrom(availableFrom)
            .expiresAt(now.plus(expirationDuration))
            .snapshot(OfferChainInstanceSnapshot.create(template, availableFrom))
            .build();

    }

    public List<String> getActiveOfferCodes(OfferChainInstanceSnapshot.MapItem activeMapItem) {
        if (activeMapItem == null) {
            return List.of();
        }
        if (PURCHASE.name().equals(activeMapItem.getOfferChainMapItemType())) {
            return getOfferCodes(activeMapItem);
        }
        return getOfferCodesForMultiplePurchase();
    }

    public boolean isUsesRewardOffers() {
        if (getSnapshot().getMapItems().isEmpty()) {
            return false;
        }
        MapItem firstMapItem = getSnapshot().getMapItems().getFirst();
        OfferChainMapItemType mapItemType = OfferChainMapItemType.fromString(firstMapItem.getOfferChainMapItemType());
        //noinspection deprecation
        return mapItemType == MULTIPLE_PURCHASE || (mapItemType == PURCHASE && getOfferChainTemplate().isRewardOfferOnly());
    }

    public List<String> getInitialOfferCodes() {
        checkNotNull(snapshot, "snapshot must not be null");
        List<MapItem> mapItems = snapshot.getMapItems();
        checkNotNull(mapItems, "mapItems must not be null");
        if (mapItems.isEmpty()) {
            return List.of();
        }
        MapItem firstMapItem = mapItems.getFirst();
        OfferChainMapItemType mapItemType = OfferChainMapItemType.fromString(firstMapItem.getOfferChainMapItemType());
        checkState(mapItemType.isPurchaseOrMultiplePurchase(), "Must be one of purchase mapItemType, but was: %s", mapItemType);
        if (mapItemType == PURCHASE) {
            return getOfferCodes(firstMapItem);
        }
        checkState(mapItemType == MULTIPLE_PURCHASE, "Must be multi-purchase mapItemType, but was: %s", mapItemType);
        return getOfferCodesForMultiplePurchase();
    }

    private List<String> getOfferCodesForMultiplePurchase() {
        if (snapshot == null) {
            return List.of();
        }
        return getSnapshot().getMapItems().stream()
            .filter(mapItem -> MULTIPLE_PURCHASE.name().equals(mapItem.getOfferChainMapItemType()))
            .flatMap(mapItem -> getOfferCodes(mapItem).stream())
            .toList();
    }

    // For some reason if I make this an instance method of OfferChainInstanceSnapshot.MapItem,
    // Jackson cannot deserialize OfferChainInstanceSnapshot
    public static List<String> getOfferCodes(MapItem mapItem) {
        return mapItem.getOffers().stream()
            .filter(mapItemOffer -> PURCHASE_OFFER.name().equals(mapItemOffer.getType()))
            .map(MapItemOffer::getPurchaseOfferTemplateCode)
            .filter(StringUtils::isNotBlank)
            .toList();
    }

    public enum Status {
        SCHEDULED,
        ASSIGNED,
        COMPLETED,
        RETRACTED,
        EXPIRED,
        REPLACED;

        private final String code = this.name().toLowerCase().intern();

        @DbEnumValue
        public String code() {
            return this.code;
        }

        @JsonCreator
        public static Status fromCode(String code) {
            for (Status status : Status.values()) {
                if (status.code.equalsIgnoreCase(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status: " + code);
        }

        public boolean canRetract() {
            return canTransitTo(RETRACTED);
        }

        public boolean canTransitTo(Status next) {
            return switch (this) {
                case SCHEDULED -> next == ASSIGNED || next == REPLACED || next == RETRACTED;
                case ASSIGNED -> next == EXPIRED || next == REPLACED || next == RETRACTED;
                case COMPLETED, RETRACTED, EXPIRED, REPLACED -> false;
            };
        }
    }
}
