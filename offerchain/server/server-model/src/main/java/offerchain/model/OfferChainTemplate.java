package offerchain.model;

import static java.util.Comparator.comparing;

import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import io.ebean.annotation.DbJsonB;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(name = "offer_chain_template", schema = Schemas.OFFER_CHAIN)
public class OfferChainTemplate {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false, unique = true)
    private UUID code;

    @Column(nullable = false, length = 128)
    private String name;

    @Column(nullable = false)
    private String displayName;

    @Column(nullable = false)
    private String displayNameShort;

    @Column
    private String displayTagline;

    @Column(length = 2048)
    private String displayDescription;

    @Column(length = 512)
    private String displayDescriptionShort;

    @Column(nullable = false)
    private boolean isActive = false;

    @Column(nullable = false)
    private boolean isTest = false;

    @Column(nullable = false)
    private boolean isHidden = false;

    @Column(nullable = false)
    private boolean isTimeLimited;

    @Column
    private Instant availableFrom;

    @Column
    private Instant availableTo;

    @Column(nullable = false)
    private Integer expiresAfterDays;

    @ManyToOne(optional = false)
    @JoinColumn(nullable = false)
    private OfferChainBrand brand;

    @Column(length = 2048)
    private String iconSmall;

    @Column(length = 2048)
    private String iconLarge;

    @Column(length = 2048)
    private String iconInbox;

    @Column(length = 2048)
    private String termUrl;

    @Column(length = 2048)
    private String arrowImgUrl;

    @ManyToOne(optional = false)
    @JoinColumn(nullable = false)
    private OfferChainMapType mapType;

    @OneToMany(mappedBy = "offerChainTemplate", cascade = CascadeType.ALL)
    private List<OfferChainTemplatePlacementType> placements;

    @OneToMany(mappedBy = "template", cascade = CascadeType.ALL)
    private List<OfferChainMapItem> mapItems;

    @DbJsonB
    @Column(name = "map_type_params", columnDefinition = "jsonb")
    private Map<String, ParamValue> mapTypeParams = new HashMap<>();

    @Column
    @Deprecated(since = "drop after MN-1365", forRemoval = true)
    private boolean rewardOfferOnly;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public OfferChainMapItem getFirstMapItem() {
        return getMapItems().stream().min(comparing(OfferChainMapItem::getPosition)).orElseThrow();
    }
}
