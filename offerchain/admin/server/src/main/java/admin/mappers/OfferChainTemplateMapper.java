package admin.mappers;

import static java.util.stream.Collectors.toMap;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import admin.models.offer.chain.common.AdminOfferChainMapItem;
import admin.models.offer.chain.common.AdminOfferChainMapItemOffer;
import admin.models.offer.chain.common.AdminOfferChainMapItemOfferType;
import admin.models.offer.chain.common.AdminOfferChainMapItemType;
import admin.models.offer.chain.common.AdminOfferChainMapTheme;
import admin.models.offer.chain.common.AdminOfferChainMapType;
import admin.models.offer.chain.common.AdminOfferChainTemplatePlacementTypeInfo;
import admin.models.offer.chain.common.ParamValue;
import admin.models.offer.chain.create.CreateOfferChainTemplateRequest;
import offerchain.api.v1.ApiOfferChainMapItem;
import offerchain.api.v1.ApiOfferChainMapItemOffer;
import offerchain.api.v1.ApiOfferChainMapItemOfferType;
import offerchain.api.v1.ApiOfferChainMapItemTypeItem;
import offerchain.api.v1.ApiOfferChainMapTheme;
import offerchain.api.v1.ApiOfferChainMapType;
import offerchain.api.v1.ApiOfferChainTemplateInfo;
import offerchain.api.v1.ApiOfferChainTemplatePlacementTypeInfo;
import offerchain.api.v1.ApiParamValue;
import offerchain.api.v1.NestedParams;
import offerchain.api.v1.StringList;

public class OfferChainTemplateMapper {
    public static ApiOfferChainTemplateInfo mapTemplate(CreateOfferChainTemplateRequest request) {
        ApiOfferChainTemplateInfo.Builder builder = ApiOfferChainTemplateInfo.newBuilder()
                .setName(request.getName())
                .setDisplayName(request.getDisplayName())
                .setDisplayNameShort(request.getDisplayNameShort())
                .setIsActive(request.getIsActive())
                .setIsTest(request.getIsTest())
                .setIsHidden(request.getIsHidden())
                .setIsTimeLimited(request.getIsTimeLimited())
                .setExpiresAfterDays(request.getExpiresAfterDays());

        if (request.getDisplayTagline() != null && !request.getDisplayTagline().isEmpty()) {
            builder.setDisplayTagline(request.getDisplayTagline());
        }
        if (request.getDisplayDescription() != null && !request.getDisplayDescription().isEmpty()) {
            builder.setDisplayDescription(request.getDisplayDescription());
        }
        if (request.getDisplayDescriptionShort() != null && !request.getDisplayDescriptionShort().isEmpty()) {
            builder.setDisplayDescriptionShort(request.getDisplayDescriptionShort());
        }
        if (request.getIconSmall() != null && !request.getIconSmall().isEmpty()) {
            builder.setIconSmall(request.getIconSmall());
        }
        if (request.getIconLarge() != null && !request.getIconLarge().isEmpty()) {
            builder.setIconLarge(request.getIconLarge());
        }
        if (request.getIconInbox() != null && !request.getIconInbox().isEmpty()) {
            builder.setIconInbox(request.getIconInbox());
        }
        if (request.getAvailableFrom() != null && !request.getAvailableFrom().isEmpty()) {
            builder.setAvailableFrom(request.getAvailableFrom());
        }
        if (request.getAvailableTo() != null && !request.getAvailableTo().isEmpty()) {
            builder.setAvailableTo(request.getAvailableTo());
        }
        if (request.getTermUrl() != null && !request.getTermUrl().isEmpty()) {
            builder.setTermUrl(request.getTermUrl());
        }
        if (request.getArrowImgUrl() != null && !request.getArrowImgUrl().isEmpty()) {
            builder.setArrowImgUrl(request.getArrowImgUrl());
        }

        builder.addAllPlacements(mapPlacements(request.getPlacements()));
        builder.setMapType(mapMapType(request.getMapType()));

        if (request.getMapTheme() != null) {
            builder.setMapTheme(mapMapTheme(request.getMapTheme()));
        }

        if (request.getRewardOfferOnly() != null) {
            builder.setRewardOfferOnly(request.getRewardOfferOnly());
        }

        builder.addAllItems(mapItems(request.getItems()));

        return builder.build();
    }

    private static List<ApiOfferChainMapItem> mapItems(List<AdminOfferChainMapItem> items) {
        return items.stream()
                .map(OfferChainTemplateMapper::mapItem)
                .toList();
    }

    private static ApiOfferChainMapItem mapItem(AdminOfferChainMapItem item) {
        var builder = ApiOfferChainMapItem.newBuilder();
        if (item.getId() != null) {
            builder.setId(item.getId());
        }
        builder.setName(item.getName());
        if (item.getCode() != null) {
            builder.setCode(item.getCode());
        }
        builder.setPosition(item.getPosition());
        builder.setType(mapItemType(item.getType()));

        if (item.getDisplayTagline() != null && !item.getDisplayTagline().isEmpty()) {
            builder.setDisplayTagline(item.getDisplayTagline());
        }
        if (item.getDisplayTitle() != null && !item.getDisplayTitle().isEmpty()) {
            builder.setDisplayTitle(item.getDisplayTitle());
        }
        if (item.getDisplayDescription() != null && !item.getDisplayDescription().isEmpty()) {
            builder.setDisplayDescription(item.getDisplayDescription());
        }
        if (item.getBackgroundImageUrl() != null && !item.getBackgroundImageUrl().isEmpty()) {
            builder.setBackgroundImageUrl(item.getBackgroundImageUrl());
        }
        if (item.getSmallOpenIconUrl() != null && !item.getSmallOpenIconUrl().isEmpty()) {
            builder.setSmallOpenIconUrl(item.getSmallOpenIconUrl());
        }
        if (item.getLargeOpenIconUrl() != null && !item.getLargeOpenIconUrl().isEmpty()) {
            builder.setLargeOpenIconUrl(item.getLargeOpenIconUrl());
        }
        if (item.getSmallDoneIconUrl() != null && !item.getSmallDoneIconUrl().isEmpty()) {
            builder.setSmallDoneIconUrl(item.getSmallDoneIconUrl());
        }
        if (item.getLargeDoneIconUrl() != null && !item.getLargeDoneIconUrl().isEmpty()) {
            builder.setLargeDoneIconUrl(item.getLargeDoneIconUrl());
        }

        if (item.getContent() != null && !item.getContent().isEmpty()) {
            builder.addAllContent(
                    item.getContent().stream()
                            .map(OfferChainTemplateMapper::mapOffer)
                            .toList());
        }

        return builder.build();
    }

    private static ApiOfferChainMapItemOffer mapOffer(AdminOfferChainMapItemOffer offer) {
        var builder = ApiOfferChainMapItemOffer.newBuilder().setType(mapOfferType(offer.getType()));
        if (offer.getRewardCode() != null) {
            builder.setRewardCode(offer.getRewardCode().toString());
        } else {
            validateFreeSpinsBetLevel(offer);

            // ~ drop after migration
            builder.setGcAmount(offer.getGcAmount() != null ? offer.getGcAmount() : 0)
                    .setScAmount(offer.getScAmount() != null ? offer.getScAmount() : 0)
                    .setFreeSpinsAmount(offer.getFreeSpinsAmount() != null ? offer.getFreeSpinsAmount() : 0)
                    .setFreeSpinsValue(offer.getFreeSpinsValue() != null ? offer.getFreeSpinsValue().toPlainString() : "0")
                    .setFreeSpinsBetLevel(offer.getFreeSpinsBetLevel() != null ? offer.getFreeSpinsBetLevel() : 0)
                    .setFreeSpinsGameId(offer.getFreeSpinsGameId() != null ? offer.getFreeSpinsGameId() : 0)
                    .setFallback1Value(offer.getFallback1Value() != null ? offer.getFallback1Value().toPlainString() : "0")
                    .setFallback1Bet(offer.getFallback1Bet() != null ? offer.getFallback1Bet() : 0)
                    .setFallback1Game(offer.getFallback1Game() != null ? offer.getFallback1Game() : 0)
                    .setFallback2Value(offer.getFallback2Value() != null ? offer.getFallback2Value().toPlainString() : "0")
                    .setFallback2Bet(offer.getFallback2Bet() != null ? offer.getFallback2Bet() : 0)
                    .setFallback2Game(offer.getFallback2Game() != null ? offer.getFallback2Game() : 0);
        }

        if (offer.getId() != null) {
            builder.setId(offer.getId());
        }
        if (offer.getCode() != null) {
            builder.setCode(offer.getCode().toString());
        }
        if (offer.getRandomRewardIds() != null) {
            builder.addAllRandomRewardIds(offer.getRandomRewardIds());
        }
        if (offer.getPurchaseOfferTemplateCode() != null) {
            builder.setPurchaseOfferTemplateCode(offer.getPurchaseOfferTemplateCode());
        }

        if (offer.getBackgroundUrl() != null) {
            builder.setBackgroundUrl(offer.getBackgroundUrl());
        }
        if (offer.getDisplayTagline() != null) {
            builder.setDisplayTagline(offer.getDisplayTagline());
        }
        if (offer.getDisplayDescription() != null) {
            builder.setDisplayDescription(offer.getDisplayDescription());
        }
        if (offer.getSmallOpenIconUrl() != null) {
            builder.setSmallOpenIconUrl(offer.getSmallOpenIconUrl());
        }
        if (offer.getLargeOpenIconUrl() != null) {
            builder.setLargeOpenIconUrl(offer.getLargeOpenIconUrl());
        }
        if (offer.getSmallDoneIconUrl() != null) {
            builder.setSmallDoneIconUrl(offer.getSmallDoneIconUrl());
        }
        if (offer.getLargeDoneIconUrl() != null) {
            builder.setLargeDoneIconUrl(offer.getLargeDoneIconUrl());
        }

        return builder.build();
    }

    private static ApiOfferChainMapItemTypeItem mapItemType(AdminOfferChainMapItemType itemType) {
        return switch (itemType) {
            case AdminOfferChainMapItemType.FREE -> ApiOfferChainMapItemTypeItem.FREE;
            case AdminOfferChainMapItemType.PURCHASE -> ApiOfferChainMapItemTypeItem.PURCHASE;
            case AdminOfferChainMapItemType.MULTIPLE_PURCHASE -> ApiOfferChainMapItemTypeItem.MULTIPLE_PURCHASE;
        };
    }

    private static ApiOfferChainMapItemOfferType mapOfferType(AdminOfferChainMapItemOfferType offerType) {
        return switch (offerType) {
            case SC -> ApiOfferChainMapItemOfferType.SC;
            case GC -> ApiOfferChainMapItemOfferType.GC;
            case FREE_SPINS -> ApiOfferChainMapItemOfferType.FREE_SPINS;
            case RANDOM_REWARD -> ApiOfferChainMapItemOfferType.RANDOM_REWARD;
            case PURCHASE_OFFER -> ApiOfferChainMapItemOfferType.PURCHASE_OFFER;
        };
    }

    private static List<ApiOfferChainTemplatePlacementTypeInfo> mapPlacements(List<AdminOfferChainTemplatePlacementTypeInfo> placements) {
        return placements.stream()
                .map(placement -> offerchain.api.v1.ApiOfferChainTemplatePlacementTypeInfo.newBuilder()
                        .setPlacementTypeId(placement.getPlacementTypeId())
                        .setThemeId(placement.getPlacementTypeThemeId())
                        .build())
                .toList();
    }

    private static ApiOfferChainMapType mapMapType(AdminOfferChainMapType mapType) {
        return ApiOfferChainMapType.newBuilder()
                .setCode(mapType.getCode())
                .putAllParams(toApiParams(mapType.getParams()))
                .build();
    }

    private static ApiOfferChainMapTheme mapMapTheme(AdminOfferChainMapTheme mapTheme) {
        return ApiOfferChainMapTheme.newBuilder()
                .setCode(mapTheme.getCode())
                .build();
    }

    public static Map<String, ApiParamValue> toApiParams(Map<String, ParamValue> params) {
        return params == null ? Collections.emptyMap()
                : params.entrySet().stream()
                        .collect(toMap(Map.Entry::getKey, entry -> toApiParamValue(entry.getValue())));
    }

    private static ApiParamValue toApiParamValue(ParamValue paramValue) {
        ApiParamValue.Builder builder = ApiParamValue.newBuilder();
        if (paramValue.getStringValue() != null) {
            builder.setStringValue(paramValue.getStringValue());
        } else if (paramValue.getListValue() != null) {
            StringList.Builder listBuilder = StringList.newBuilder();
            paramValue.getListValue().stream().filter(Objects::nonNull).forEach(listBuilder::addValues);
            builder.setListValue(listBuilder.build());
        } else if (paramValue.getIntValue() != null) {
            builder.setIntValue(paramValue.getIntValue());
        } else if (paramValue.getDoubleValue() != null) {
            builder.setDoubleValue(paramValue.getDoubleValue());
        } else if (paramValue.getBoolValue() != null) {
            builder.setBoolValue(paramValue.getBoolValue());
        } else if (paramValue.getNestedValue() != null) {
            builder.setNestedValue(NestedParams.newBuilder().putAllParams(toApiParams(paramValue.getNestedValue())).build());
        }
        return builder.build();
    }

    @SuppressWarnings("deprecation")
    private static void validateFreeSpinsBetLevel(AdminOfferChainMapItemOffer offer) {
        if (offer.getType() != AdminOfferChainMapItemOfferType.FREE_SPINS) {
            return;
        }
        if (offer.getFreeSpinsBetLevel() == null) {
            throw new IllegalArgumentException("freeSpinsBetLevel must be not null for FREE_SPINS offers");
        }
        if (offer.getFreeSpinsBetLevel() <= 0) {
            throw new IllegalArgumentException(
                String.format("freeSpinsBetLevel must be greater than 0 for FREE_SPINS offers, but was: %s",
                    offer.getFreeSpinsBetLevel()));
        }
    }
}
