package offerchain.worker;

import com.turbospaces.ups.UPSs;
import common.ImmutableUPSsCollection;
import offerchain.OfferChainProto;

public class OfferChainWorkerUPSs extends ImmutableUPSsCollection {
    {
        //
        // ~ common
        //
        add(UPSs.INFRA_CORE);
        add(UPSs.INFRA_SERVER);
        add(UPSs.REDIS);

        add(OfferChainProto.OFFERCHAIN_POSTGRES_OWNER);
        add(OfferChainProto.OFFERCHAIN_POSTGRES_APP);
        add(OfferChainProto.OFFERCHAIN_QUARTZ_APP);
        add(OfferChainProto.OFFERCHAIN_POSTGRES_SLAVE_READ_ONLY);
    }
}
