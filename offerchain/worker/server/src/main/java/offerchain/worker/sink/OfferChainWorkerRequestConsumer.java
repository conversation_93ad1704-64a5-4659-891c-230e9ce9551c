package offerchain.worker.sink;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Semaphore;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import org.apache.kafka.clients.consumer.Consumer;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.support.Acknowledgment;

import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.sink.AbstractSink;
import com.turbospaces.mdc.MdcUtil;
import com.turbospaces.metrics.MetricTags;
import com.turbospaces.metrics.Metrics;
import com.turbospaces.rpc.ApiResponseEntity;

import io.github.resilience4j.core.functions.CheckedRunnable;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import offerchain.api.OfferchainServiceApi;
import offerchain.api.v1.CreateOfferChainInstanceRequest;
import offerchain.api.v1.DeleteOfferChainInstanceByTemplateRequest;
import offerchain.worker.OfferChainWorkerProto;
import offerchain.worker.OfferChainWorkerServerProperties;
import offerchain.worker.api.v1.OfferChainBackgroundRequest;
import offerchain.worker.job.DeleteOfferChainsByTemplateJob;
import reactor.core.publisher.Flux;

@Slf4j
public class OfferChainWorkerRequestConsumer extends AbstractSink {

    private final OfferChainWorkerServerProperties offerchainProps;
    private final RetryRegistry retryRegistry;
    private final OfferchainServiceApi offerchainServiceApi;
    private final Supplier<Boolean> retryEnabled;
    private final PlatformExecutorService executor;
    private final RateLimiter chainOfferRateLimiter;
    private final Scheduler scheduler;

    @Inject
    public OfferChainWorkerRequestConsumer(
            ApplicationProperties props,
            OfferChainWorkerServerProperties offerchainProps,
            OfferchainServiceApi offerchainServiceApi,
            MeterRegistry meterRegistry,
            RetryRegistry retryRegistry,
            KafkaListenerEndpointRegistry registry,
            Property<Boolean> enabled,
            Supplier<Boolean> retryEnabled,
            RateLimiter offerChainCreateRateLimiter,
            Scheduler scheduler) {
        super(registry, enabled);
        this.offerchainProps = Objects.requireNonNull(offerchainProps);
        this.retryRegistry = retryRegistry;
        this.offerchainServiceApi = Objects.requireNonNull(offerchainServiceApi);
        this.retryEnabled = retryEnabled;
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry,
            factory -> new ThreadPoolExecutor(
                props.APP_PLATFORM_MIN_SIZE.get(), props.APP_PLATFORM_MAX_SIZE.get(),
                props.APP_PLATFORM_MAX_IDLE.get().toSeconds(), TimeUnit.SECONDS, new SynchronousQueue<>(),
                factory.build(), new ThreadPoolExecutor.CallerRunsPolicy()));
        this.chainOfferRateLimiter = offerChainCreateRateLimiter;
        this.scheduler = scheduler;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();
        executor.setBeanName(OfferChainWorkerProto.UPS_BACKGROUND_REQUEST_CONSUMER);
        executor.afterPropertiesSet();
    }

    @Override
    public void destroy() throws Exception {
        try {
            executor.destroy();
        } finally {
            super.destroy();
        }
    }

    @Override
    @Timed(value = Metrics.SINK, extraTags = { MetricTags.OPERATION, OfferChainWorkerProto.UPS_BACKGROUND_REQUEST_CONSUMER })
    public void accept(Flux<KafkaWorkUnit> flux, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
        var throttler = new Semaphore(offerchainProps.PARALLEL_DEFAULT_MAX_REQUESTS.get());
        for (KafkaWorkUnit unit : flux.collectList().block()) {
            try {
                throttler.acquire();
                executor.execute(() -> {
                    try {
                        Any any = toAny(unit);
                        AbstractSink.setMdc(unit, any);
                        logger.trace("IN ::: (t-{}):(offset-{}):(t-{}))", unit.topic(), unit.offset(), any.getTypeUrl());
                        applyRecord(any).run();
                    } catch (Throwable ex) {
                        logger.error("unable to process worker request", ex);
                    } finally {
                        MdcUtil.clearMdc(unit);
                        throttler.release();
                    }
                });
            } catch (InterruptedException err) {
                logger.warn(err.getMessage(), err);
                Thread.currentThread().interrupt();
            }
        }
        acknowledgment.acknowledge();
    }

    private CheckedRunnable applyRecord(Any any) {
        Retry retry = retryRegistry.retry(OfferChainWorkerProto.UPS_BACKGROUND_REQUEST_CONSUMER);
        return Retry.decorateCheckedRunnable(retry, () -> {
            if (any.is(OfferChainBackgroundRequest.class)) {
                var request = any.unpack(OfferChainBackgroundRequest.class);
                applyRequest(request);
            } else {
                log.warn("Worker received unknown request type: {}", any.getTypeUrl());
            }
        });
    }

    private void applyRequest(OfferChainBackgroundRequest routable)
            throws InvalidProtocolBufferException, InterruptedException, ExecutionException, SchedulerException {
        var routingKey = AsciiString.cached(routable.getRoutingKey());
        var request = routable.getRequest();
        boolean toRetry = retryEnabled.get();

        if (request.is(CreateOfferChainInstanceRequest.class)) {
            var unpacked = request.unpack(CreateOfferChainInstanceRequest.class);
            processCreateOfferChainInstance(toRetry, unpacked, routingKey);
        } else if (request.is(DeleteOfferChainInstanceByTemplateRequest.class)) {
            var msg = request.unpack(DeleteOfferChainInstanceByTemplateRequest.class);
            log.info("Worker received 'Delete OfferChain instances' request for template={}", msg.getOfferChainTemplateCode());
            DeleteOfferChainsByTemplateJob.scheduleOneShotJob(scheduler, msg.getOfferChainTemplateCode(), routable.getRoutingKey());
        } else {
            log.warn("Processing {} is not implemented yet", request.getTypeUrl());
        }
    }

    private void processCreateOfferChainInstance(boolean toRetry, CreateOfferChainInstanceRequest request, AsciiString routingKey)
            throws InterruptedException, ExecutionException {
        if (chainOfferRateLimiter.acquirePermission()) {
            logger.trace("Offer chain instance creation request has been sent. template={} account={} brand={}",
                    request.getOfferChainTemplateCode(), request.getIdentity().getByAccountId().getAccountId(), request.getBrandName());

            var resp = offerchainServiceApi.createOfferChainInstance(request, routingKey).get();
            var status = resp.status();

            if (toRetry && isErrorRequest(resp)) {
                throw new RuntimeException(status.errorText());
            } else if (isBadRequest(resp)) {
                logger.warn("Offer chain instance creation failed due: {}", status.errorText());
            }
        } else if (toRetry) {
            RateLimiter.Metrics limiterMetrics = chainOfferRateLimiter.getMetrics();
            if (logger.isDebugEnabled()) {
                logger.debug("Rate limiter of offer chains requests. available: {} waiting: {}",
                        limiterMetrics.getAvailablePermissions(), limiterMetrics.getNumberOfWaitingThreads());
            }
            throw new RuntimeException("Rate limiter of offer chain server requests exceeded.");
        } else {
            logger.warn("Skip processing due Rate limiter exceeded and Retry disabled");
        }
    }

    private static boolean isErrorRequest(ApiResponseEntity<?> resp) {
        var status = resp.status();
        return status.isSystem() || status.isTimeout();
    }

    private static boolean isBadRequest(ApiResponseEntity<?> resp) {
        var status = resp.status();
        return status.isBadRequest() || status.isNotFound();
    }

    private static Any toAny(KafkaWorkUnit unit) throws IOException {
        Any.Builder anyb = Any.newBuilder();
        try (InputStream io = unit.value().openStream()) {
            anyb.mergeFrom(io);
        }
        return anyb.build();
    }
}
