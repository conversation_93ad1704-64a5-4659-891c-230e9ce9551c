package offerchain.worker.job;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.turbospaces.cfg.ApplicationProperties;

import io.ebean.Transaction;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import offerchain.api.OfferchainServiceApi;
import offerchain.model.OfferChainInstance;
import offerchain.worker.OfferChainWorkerJpaManager;
import offerchain.worker.OfferChainWorkerScheduler;
import offerchain.worker.OfferChainWorkerServerProperties;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;

@Slf4j
@Component
@DisallowConcurrentExecution
public class DeleteOfferChainsByTemplateJob extends AbstractOfferChainWorkerJob {

    public static final String JOB_NAME = "offerchain-retract-by-template";
    public static final String TRIGGER_NAME = JOB_NAME + "-trigger";

    public static final String TEMPLATE_CODE_PARAM = "templateCode";
    public static final String BRAND_PARAM = "brand";

    private final OfferchainServiceApi offerchainServiceApi;

    public DeleteOfferChainsByTemplateJob(
            ApplicationProperties appProps,
            OfferChainWorkerServerProperties props,
            MeterRegistry meterRegistry,
            OfferChainWorkerJpaManager ebean,
            OfferchainServiceApi offerchainServiceApi) {
        super(appProps, props, meterRegistry, ebean);
        this.offerchainServiceApi = offerchainServiceApi;
    }

    @Override
    public void doExecute(JobExecutionContext context) throws Throwable {
        JobDataMap data = context.getMergedJobDataMap();
        UUID templateCode = UUID.fromString(data.getString(TEMPLATE_CODE_PARAM));
        int batchSize = props.OFFERCHAIN_INSTANCES_RETRACT_BATCH_SIZE.get();

        log.info("Starting DeleteOfferChainsByTemplateJob, templateCode={}, batchSize={}",
                templateCode, batchSize);

        var countUpdated = new AtomicInteger();

        List<Long> instanceIdsToExpire = ebean.offerChainWorkerRepo().findActiveInstancesIdsByTemplate(templateCode);
        List<List<Long>> batches = Lists.partition(instanceIdsToExpire, batchSize);
        int countTotal = instanceIdsToExpire.size();

        for (List<Long> batchIds : batches) {
            List<OfferChainInstance> batch;
            try (Transaction tx = ebean.newReadOnlyTransaction()) {
                batch = ebean.offerChainRepo().findInstancesByIds(batchIds, tx);
            }
            for (OfferChainInstance instance : batch) {
                retractInstance(instance, countUpdated);
            }
        }

        log.info("DeleteOfferChainsByTemplateJob by template={} is done. Retracted {} of {} instances",
                templateCode, countUpdated.get(), countTotal);
        if (countUpdated.get() < countTotal) {
            log.error("DeleteOfferChainsByTemplateJob failed to retract {} of {} instances, templateCode={}",
                    countTotal - countUpdated.get(), countTotal, templateCode);
        }
    }

    private void retractInstance(OfferChainInstance instance, AtomicInteger countUpdated) throws Exception {
        try {
            if (!instance.getStatus().canRetract()) {
                countUpdated.incrementAndGet();
                log.warn("ChainOffer {} in status {} cannot transition to RETRACTED", instance.getCode(), instance.getStatus());
                return;
            }

            log.debug("Created DeleteOfferChainInstance request. code={} account={}", instance.getCode(), instance.getAccount().getRemoteId());
            var responseStatus = offerchainServiceApi.deleteOfferChainInstance(
                            createInternalRequest(instance), instance.getAccount().routingKey())
                    .get().status();
            if (responseStatus.isOK()) {
                countUpdated.incrementAndGet();
                log.debug("ChainOffer has been retracted. code={} accountId={}", instance.getCode(), instance.getAccount().getRemoteId());
            } else if (responseStatus.isBadRequest()) {
                String errorText = responseStatus.errorText();
                log.warn("ChainOffer hasn't been retracted due {}. code={} accountId={} ", errorText, instance.getCode(),
                        instance.getAccount().getRemoteId());
            } else {
                String errorText = responseStatus.errorText();
                log.error("ChainOffer hasn't been retracted due : {}. code={} accountId={} ", errorText, instance.getCode(),
                        instance.getAccount().getRemoteId());
            }
        } catch (InterruptedException e) {
            log.error("Job was interrupted ChainOffer template {}", instance.getOfferChainTemplate());
            throw e;
        }
    }

    private static offerchain.api.v1.DeleteOfferChainInstanceRequest createInternalRequest(
            OfferChainInstance instance) {
        var identity = Identity.newBuilder().setByAccountId(
                IdentityByAccountId.newBuilder().setAccountId(instance.getAccount().getRemoteId()));
        var builder = offerchain.api.v1.DeleteOfferChainInstanceRequest.newBuilder();
        builder.setIdentity(identity);
        builder.setOfferChainInstanceCode(instance.getCode().toString());
        return builder.build();
    }

    public static void scheduleOneShotJob(Scheduler scheduler, String templateCode, String brand) throws SchedulerException {
        JobDetail jobDetail = JobBuilder.newJob(DeleteOfferChainsByTemplateJob.class)
                .requestRecovery(true)
                .withIdentity(JOB_NAME + "-" + templateCode, OfferChainWorkerScheduler.WORKER_GROUP)
                .usingJobData(TEMPLATE_CODE_PARAM, templateCode)
                .usingJobData(BRAND_PARAM, brand)
                .build();

        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity(TRIGGER_NAME + "-" + templateCode, OfferChainWorkerScheduler.WORKER_GROUP)
                .startNow()
                .build();

        scheduler.scheduleJob(jobDetail, trigger);
        log.info("Scheduled DeleteOfferChainsByTemplateJob for templateCode={}, brand={}", templateCode, brand);
    }
}
