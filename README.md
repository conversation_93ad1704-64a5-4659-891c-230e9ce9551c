# Creator Service

PlayFame introduces a new role for users: creator. 
Creators have access to different pages and can stream their playing activity. 
Other (regular) players can follow creators, watch their streams and play together with the creator on their own bets and custom amount of rounds.

## What is this repository for?

Creator service handles new creator role and new type of interaction (playTogetherSession) related to this role.

## Project Structure

The project is organized as a multi-module Maven project:

- **admin**: Admin-related modules
- **server-api-common**: Common API definitions
- **server-api**: Server API definitions
- **server-model**: Data models and database entities
- **server**: Core server implementation
- **server-endpoint**: Server endpoints and application starter
- **frontend**: Frontend-related modules
- **gateway**: Gateway-related modules
- **test**: Test modules
- **providers**: Provider implementations
- **coverage-report**: Test coverage reports

## Prerequisites

- Java 23
- Maven 3.8+
- PostgreSQL database
- Redis
- Kafka
- Spanner 
- Memcache

## Setup and Installation

### 1. Clone the repository

```bash
<NAME_EMAIL>:turbospaces/creator-service.git
cd creator-service
```

### 2. Build the project

```bash
mvn clean install
```

If you encounter build failures due to missing migration, start the `GenerateCreatorPostgresMigration` starter first to generate the required migration files.

### 3. Configure the environment

For detailed instructions on setting up your local environment, refer to the [How to setup local environment and use it](https://patrianna.atlassian.net/wiki/spaces/YDD/pages/819341/How+to+setup+local+environment+and+use+it) wiki page.

Start the required services using the following commands:

```bash
$ brew services start postgresql
$ brew services start zookeeper
$ brew services start kafka
$ brew services start redis
$ gcloud beta emulators spanner start
$ gcloud beta emulators pubsub start --host-port=0.0.0.0:8085
```

The application requires the following services to be available:

- PostgreSQL database
- Redis
- Kafka
- Memcache

Configuration properties are loaded from `creator-server.properties` and other property files in the respective modules.

### 4. Database Setup

You need to create a local PostgreSQL database named "creator" before running the application.

### 5. Authentication Setup

For initial user signup and signin, you need to start the following components from the CRM repository:

- AuthServerTestStarter
- UamServerTestStarter
- UamFrontendTestStarter

These starters are required for authentication. After successful authentication, you will be able to call the creators API endpoints that require authentication.

### 6. Creator Service Components Setup

To complete the setup, you need to start the following components from the creator-service repository:

- CreatorServerTestStarter
- AdminAppTestStarter
- CreatorFrontendTestStarter

These starters are required for the full functionality of the creator service.

### 7. Run the application

The main application class is `creator.CreatorServerStarter` in the server-endpoint module.

#### Running from IntelliJ IDEA:

1. Open the project in IntelliJ IDEA
2. Navigate to the `creator.CreatorServerStarter` class
3. Right-click and select "Run CreatorServerStarter"
4. Before running, configure the following:
   - Add VM option: `-XX:+AllowRedefinitionToAddDeleteMethods`
   - Enable the ebean plugin in IntelliJ IDEA settings (Settings → Plugins → search for "ebean" and install/enable it)

This configuration ensures proper hot reloading and database entity enhancement during development.

## Testing

Run the tests using:

```bash
mvn test
```

## Who do I talk to?

* Vitalii Paskar, Mykola Yaremchuk
