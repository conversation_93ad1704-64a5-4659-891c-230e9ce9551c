<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterForeignKey name="fk_accounts_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_accounts_brand_id" tableName="uam.accounts"/>
        <alterForeignKey name="fk_account_gameplay_info_id" columnNames="DROP FOREIGN KEY" tableName="uam.account_gameplay_info"/>
        <alterForeignKey name="fk_account_meta_info_account_id" columnNames="DROP FOREIGN KEY" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_first_payment_order_id" columnNames="DROP FOREIGN KEY" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_first_deposit_order_id" columnNames="DROP FOREIGN KEY" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_first_successful_payment_order_id" columnNames="DROP FOREIGN KEY" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_first_successful_deposit_order_id" columnNames="DROP FOREIGN KEY" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_last_failed_on_provider_payment_orde_6" columnNames="DROP FOREIGN KEY" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_last_payment_order_id" columnNames="DROP FOREIGN KEY" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_last_deposit_order_id" columnNames="DROP FOREIGN KEY" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_first_withdraw_id" columnNames="DROP FOREIGN KEY" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_last_withdraw_id" columnNames="DROP FOREIGN KEY" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_id" columnNames="DROP FOREIGN KEY" tableName="uam.account_meta_info"/>
        <alterForeignKey name="fk_account_offer_meta_info_offer_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_offer_meta_info_offer_id" tableName="payment.account_offer_meta_info"/>
        <alterForeignKey name="fk_account_offer_meta_info_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_offer_meta_info_account_id" tableName="payment.account_offer_meta_info"/>
        <alterForeignKey name="fk_account_payment_info_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_payment_info_account_id" tableName="payment.account_payment_info"/>
        <alterForeignKey name="fk_payment_methods_card_payment_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_methods_card_payment_method_id" tableName="payment.payment_methods"/>
        <alterForeignKey name="fk_payment_methods_ach_payment_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_methods_ach_payment_method_id" tableName="payment.payment_methods"/>
        <alterForeignKey name="fk_payment_methods_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_methods_account_id" tableName="payment.payment_methods"/>
        <alterForeignKey name="fk_payment_methods_e_transfer_payment_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_methods_e_transfer_payment_method_id" tableName="payment.payment_methods"/>
        <alterForeignKey name="fk_payment_methods_e_wallet_payment_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_methods_e_wallet_payment_method_id" tableName="payment.payment_methods"/>
        <alterForeignKey name="fk_payment_methods_crypto_payment_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_methods_crypto_payment_method_id" tableName="payment.payment_methods"/>
        <alterForeignKey name="fk_account_purchase_limit_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_purchase_limit_account_id" tableName="payment.account_purchase_limit"/>
        <alterForeignKey name="fk_account_vip_level_history_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_vip_level_history_account_id" tableName="uam.account_vip_level_history"/>
        <alterForeignKey name="fk_withdraw_methods_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_withdraw_methods_account_id" tableName="payment.withdraw_methods"/>
        <alterForeignKey name="fk_ach_payment_method_address_ach_payment_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_ach_payment_method_address_ach_payment_method_id" tableName="payment.ach_payment_method_address"/>
        <alterForeignKey name="fk_card_payment_method_meta_info_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_card_payment_method_meta_info_account_id" tableName="payment.card_payment_method_meta_info"/>
        <alterForeignKey name="fk_chargeback_history_order_id" columnNames="DROP FOREIGN KEY" indexName="ix_chargeback_history_order_id" tableName="payment.chargeback_history"/>
        <alterForeignKey name="fk_chargeback_history_chargeback_id" columnNames="DROP FOREIGN KEY" indexName="ix_chargeback_history_chargeback_id" tableName="payment.chargeback_history"/>
        <alterForeignKey name="fk_deactivate_provider_policy_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_deactivate_provider_policy_brand_id" tableName="payment.deactivate_provider_policy"/>
        <alterForeignKey name="fk_offer_templates_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_offer_templates_brand_id" tableName="payment.offer_templates"/>
        <alterForeignKey name="fk_offer_templates_modified_by_id" columnNames="DROP FOREIGN KEY" indexName="ix_offer_templates_modified_by_id" tableName="payment.offer_templates"/>
        <alterForeignKey name="fk_offer_templates_upgrade_id" columnNames="DROP FOREIGN KEY" indexName="ix_offer_templates_upgrade_id" tableName="payment.offer_templates"/>
        <alterForeignKey name="fk_payment_method_fraud_applied_rule_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_method_fraud_applied_rule_method_id" tableName="payment.payment_method_fraud_applied_rule"/>
        <alterForeignKey name="fk_payment_method_fraud_applied_rule_provider_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_method_fraud_applied_rule_provider_id" tableName="payment.payment_method_fraud_applied_rule"/>
        <alterForeignKey name="fk_payment_method_error_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_method_error_method_id" tableName="payment.payment_method_error"/>
        <alterForeignKey name="fk_payment_method_error_provider_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_method_error_provider_id" tableName="payment.payment_method_error"/>
        <alterForeignKey name="fk_payment_method_meta_info_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_method_meta_info_account_id" tableName="payment.payment_method_meta_info"/>
        <alterForeignKey name="fk_payment_method_verification_request_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_method_verification_request_account_id" tableName="payment.payment_method_verification_request"/>
        <alterForeignKey name="fk_payment_method_verification_request_meta_info_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_method_verification_request_meta_info_id" tableName="payment.payment_method_verification_request"/>
        <alterForeignKey name="fk_payment_orders_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_orders_account_id" tableName="payment.payment_orders"/>
        <alterForeignKey name="fk_payment_orders_payment_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_orders_payment_method_id" tableName="payment.payment_orders"/>
        <alterForeignKey name="fk_payment_orders_card_transaction_details_id" columnNames="DROP FOREIGN KEY" tableName="payment.payment_orders"/>
        <alterForeignKey name="fk_payment_orders_offer_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_orders_offer_id" tableName="payment.payment_orders"/>
        <alterForeignKey name="fk_payment_orders_provider_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_orders_provider_id" tableName="payment.payment_orders"/>
        <alterForeignKey name="fk_payment_orders_sca_authentication_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_orders_sca_authentication_id" tableName="payment.payment_orders"/>
        <alterForeignKey name="fk_payment_orders_crypto_purchase_detail_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_orders_crypto_purchase_detail_id" tableName="payment.payment_orders"/>
        <alterForeignKey name="fk_payment_order_meta_info_order_id" columnNames="DROP FOREIGN KEY" tableName="payment.payment_order_meta_info"/>
        <alterForeignKey name="fk_providers_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_providers_brand_id" tableName="payment.providers"/>
        <alterForeignKey name="fk_routing_member_card_token_provider_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_member_card_token_provider_id" tableName="payment.routing_member_card_token"/>
        <alterForeignKey name="fk_routing_member_card_token_payment_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_member_card_token_payment_method_id" tableName="payment.routing_member_card_token"/>
        <alterForeignKey name="fk_routing_provider_card_token_provider_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_provider_card_token_provider_id" tableName="payment.routing_provider_card_token"/>
        <alterForeignKey name="fk_routing_provider_card_token_card_payment_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_provider_card_token_card_payment_method_id" tableName="payment.routing_provider_card_token"/>
        <alterForeignKey name="fk_vip_level_purchase_templates_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_vip_level_purchase_templates_brand_id" tableName="payment.vip_level_purchase_templates"/>
        <alterForeignKey name="fk_vip_level_purchase_templates_modified_by_id" columnNames="DROP FOREIGN KEY" indexName="ix_vip_level_purchase_templates_modified_by_id" tableName="payment.vip_level_purchase_templates"/>
        <alterForeignKey name="fk_weekly_personalized_offer_aggregation_history_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_weekly_personalized_offer_aggregation_history_account_id" tableName="payment.weekly_personalized_offer_aggregation_history"/>
        <alterForeignKey name="fk_withdraw_money_requests_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_withdraw_money_requests_account_id" tableName="payment.withdraw_money_requests"/>
        <alterForeignKey name="fk_withdraw_money_requests_method_id" columnNames="DROP FOREIGN KEY" indexName="ix_withdraw_money_requests_method_id" tableName="payment.withdraw_money_requests"/>
        <alterForeignKey name="fk_withdraw_money_requests_provider_id" columnNames="DROP FOREIGN KEY" indexName="ix_withdraw_money_requests_provider_id" tableName="payment.withdraw_money_requests"/>
        <alterForeignKey name="fk_withdraw_money_requests_crypto_withdrawal_detail_id" columnNames="DROP FOREIGN KEY" indexName="ix_withdraw_money_requests_crypto_withdrawal_detail_id" tableName="payment.withdraw_money_requests"/>
        <alterForeignKey name="fk_account_payment_aggregation_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_payment_aggregation_account_id" tableName="payment.account_payment_aggregation"/>
        <alterForeignKey name="fk_account_payment_settings_account_id" columnNames="DROP FOREIGN KEY" tableName="payment.account_payment_settings"/>
        <alterForeignKey name="fk_account_provider_black_list_account_id" columnNames="DROP FOREIGN KEY" tableName="payment.account_provider_black_list"/>
        <alterForeignKey name="fk_brand_settings_brand_id" columnNames="DROP FOREIGN KEY" tableName="payment.brand_settings"/>
        <alterForeignKey name="fk_currency_rate_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_currency_rate_brand_id" tableName="payment.currency_rate"/>
        <alterForeignKey name="fk_daily_redeem_limits_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_daily_redeem_limits_brand_id" tableName="payment.daily_redeem_limits"/>
        <alterForeignKey name="fk_dynamic_secure_3d_checks_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_dynamic_secure_3d_checks_brand_id" tableName="payment.dynamic_secure_3d_checks"/>
        <alterForeignKey name="fk_payment_aggregated_meta_info_provider_id" columnNames="DROP FOREIGN KEY" indexName="ix_payment_aggregated_meta_info_provider_id" tableName="payment.payment_aggregated_meta_info"/>
        <alterForeignKey name="fk_payment_orders_bonus_abuse_order_id" columnNames="DROP FOREIGN KEY" tableName="payment.payment_orders_bonus_abuse"/>
        <alterForeignKey name="fk_provider_redeem_limit_policies_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_provider_redeem_limit_policies_brand_id" tableName="payment.provider_redeem_limit_policies"/>
        <alterForeignKey name="fk_purchase_track_event_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_purchase_track_event_account_id" tableName="payment.purchase_track_event"/>
        <alterForeignKey name="fk_refund_history_order_id" columnNames="DROP FOREIGN KEY" indexName="ix_refund_history_order_id" tableName="payment.refund_history"/>
        <alterForeignKey name="fk_rescue_purchase_providers_settings_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_rescue_purchase_providers_settings_brand_id" tableName="payment.rescue_purchase_providers_settings"/>
        <alterForeignKey name="fk_routing_error_config_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_error_config_brand_id" tableName="payment.routing_error_config"/>
        <alterForeignKey name="fk_routing_error_config_provider_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_error_config_provider_id" tableName="payment.routing_error_config"/>
        <alterForeignKey name="fk_routing_provider_ach_token_provider_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_provider_ach_token_provider_id" tableName="payment.routing_provider_ach_token"/>
        <alterForeignKey name="fk_routing_provider_ach_token_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_provider_ach_token_account_id" tableName="payment.routing_provider_ach_token"/>
        <alterForeignKey name="fk_routing_rules_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_rules_brand_id" tableName="payment.routing_rules"/>
        <alterForeignKey name="fk_routing_rule_providers_routing_rules" columnNames="DROP FOREIGN KEY" indexName="ix_routing_rule_providers_routing_rules" tableName="payment.routing_rule_providers"/>
        <alterForeignKey name="fk_routing_rule_providers_providers" columnNames="DROP FOREIGN KEY" indexName="ix_routing_rule_providers_providers" tableName="payment.routing_rule_providers"/>
        <alterForeignKey name="fk_volume_allocation_config_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_volume_allocation_config_brand_id" tableName="payment.volume_allocation_config"/>
        <alterForeignKey name="fk_withdraw_money_request_meta_info_withdraw_id" columnNames="DROP FOREIGN KEY" tableName="payment.withdraw_money_request_meta_info"/>
        <alterForeignKey name="fk_accepted_payment_terms_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_accepted_payment_terms_account_id" tableName="payment.accepted_payment_terms"/>
        <alterForeignKey name="fk_account_card_aggregation_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_card_aggregation_account_id" tableName="payment.account_card_aggregation"/>
        <alterForeignKey name="fk_account_deferred_purchase_limit_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_deferred_purchase_limit_account_id" tableName="payment.account_deferred_purchase_limit"/>
        <alterForeignKey name="fk_blacklist_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_blacklist_brand_id" tableName="payment.blacklist"/>
        <alterForeignKey name="fk_chargeback_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_chargeback_account_id" tableName="payment.chargeback"/>
        <alterForeignKey name="fk_chargeback_order_id" columnNames="DROP FOREIGN KEY" tableName="payment.chargeback"/>
        <alterForeignKey name="fk_chargeback_config_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_chargeback_config_brand_id" tableName="payment.chargeback_config"/>
        <alterForeignKey name="fk_inbox_notifications_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_inbox_notifications_account_id" tableName="payment.inbox_notifications"/>
        <alterForeignKey name="fk_inbox_notifications_crypto_payment_order_id" columnNames="DROP FOREIGN KEY" indexName="ix_inbox_notifications_crypto_payment_order_id" tableName="payment.inbox_notifications"/>
        <alterForeignKey name="fk_inbox_notifications_offer_template_id" columnNames="DROP FOREIGN KEY" indexName="ix_inbox_notifications_offer_template_id" tableName="payment.inbox_notifications"/>
        <alterForeignKey name="fk_transaction_limit_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_transaction_limit_brand_id" tableName="payment.transaction_limit"/>
        <alterForeignKey name="fk_account_offer_reward_log_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_offer_reward_log_account_id" tableName="payment.account_offer_reward_log"/>
        <alterForeignKey name="fk_account_offer_reward_log_offer_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_offer_reward_log_offer_id" tableName="payment.account_offer_reward_log"/>
        <alterForeignKey name="fk_offer_template_change_log_offer_template_id" columnNames="DROP FOREIGN KEY" indexName="ix_offer_template_change_log_offer_template_id" tableName="payment.offer_template_change_log"/>
        <alterForeignKey name="fk_offer_template_change_log_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_offer_template_change_log_brand_id" tableName="payment.offer_template_change_log"/>
        <alterForeignKey name="fk_account_fraud_info_account_id" columnNames="DROP FOREIGN KEY" tableName="fraud.account_fraud_info"/>
        <alterForeignKey name="fk_external_reward_order_id" columnNames="DROP FOREIGN KEY" indexName="ix_external_reward_order_id" tableName="payment.external_reward"/>
        <dropIndex indexName="ix_accounts_hash" tableName="uam.accounts"/>
        <dropIndex indexName="ix_accounts_admin" tableName="uam.accounts"/>
        <dropIndex indexName="ix_accounts_guest" tableName="uam.accounts" platforms="POSTGRES"/>
        <dropIndex indexName="ix_account_gameplay_info_last_gameplay" tableName="uam.account_gameplay_info"/>
        <dropIndex indexName="ix_payment_methods_account_id_remember" tableName="payment.payment_methods"/>
        <dropIndex indexName="uq_account_saved_method_code_type" tableName="payment.payment_methods" platforms="POSTGRES"/>
        <dropIndex indexName="ix_payment_methods_code" tableName="payment.payment_methods"/>
        <dropIndex indexName="ix_payment_method_meta_info_account_id_fingerprint" tableName="payment.payment_method_meta_info"/>
        <dropIndex indexName="ix_payment_orders_source_id" tableName="payment.payment_orders"/>
        <dropIndex indexName="ix_payment_orders_refunded" tableName="payment.payment_orders"/>
        <dropIndex indexName="ix_payment_orders_chargeback_status" tableName="payment.payment_orders"/>
        <dropIndex indexName="ix_payment_orders_at" tableName="payment.payment_orders"/>
        <dropIndex indexName="ix_withdraw_money_requests_at" tableName="payment.withdraw_money_requests"/>
        <dropIndex indexName="ix_withdraw_money_requests_modified_at" tableName="payment.withdraw_money_requests" platforms="POSTGRES"/>
        <dropIndex indexName="ix_total_account_aggregation" tableName="payment.account_payment_aggregation" platforms="POSTGRES"/>
        <dropIndex indexName="ix_account_payment_settings_account_id" tableName="payment.account_payment_settings"/>
        <dropIndex indexName="ix_withdraw_methods_account_id_type" tableName="payment.withdraw_methods"/>
        <dropIndex indexName="uq_account_saved_withdraw_method_type_code" tableName="payment.withdraw_methods" platforms="POSTGRES"/>
        <dropIndex indexName="ix_daily_redeem_limit_state" tableName="payment.daily_redeem_limits" platforms="POSTGRES"/>
        <dropIndex indexName="ix_daily_redeem_limit_brand_id_state" tableName="payment.daily_redeem_limits" platforms="POSTGRES"/>
        <dropIndex indexName="ix_fx_currency_rate_at" tableName="payment.fx_currency_rate"/>
        <dropIndex indexName="ix_payment_aggregated_meta_info_provider_id_at" tableName="payment.payment_aggregated_meta_info"/>
        <dropIndex indexName="ix_refund_history_at" tableName="payment.refund_history"/>
        <dropIndex indexName="ix_rescue_purchase_providers_settings_brand_id_payment_me_1" tableName="payment.rescue_purchase_providers_settings"/>
        <dropIndex indexName="ix_routing_error_config_error_code" tableName="payment.routing_error_config"/>
        <dropIndex indexName="uq_account_saved_routing_ach_token" tableName="payment.routing_provider_ach_token" platforms="POSTGRES"/>
        <dropIndex indexName="ix_sca_authentications_token" tableName="payment.sca_authentications"/>
        <dropIndex indexName="ix_withdraw_money_requests_transaction_id" tableName="payment.withdraw_money_requests"/>
        <dropIndex indexName="ix_withdraw_money_requests_modified_at_date" tableName="payment.withdraw_money_requests"/>
        <dropIndex indexName="ix_accepted_payment_terms_account_id_code" tableName="payment.accepted_payment_terms"/>
        <dropIndex indexName="ix_account_card_aggregation_fingerprint_account" tableName="payment.account_card_aggregation" platforms="POSTGRES"/>
        <dropIndex indexName="ix_card_transaction_details_auth_code" tableName="payment.card_transaction_details"/>
        <dropIndex indexName="ix_card_transaction_details_acquirer_reference_number" tableName="payment.card_transaction_details"/>
        <dropIndex indexName="ix_chargeback_chargeback_at" tableName="payment.chargeback"/>
        <dropIndex indexName="ix_integration_type_currency" tableName="payment.chargeback_config" platforms="POSTGRES"/>
        <dropIndex indexName="ix_brand_integration_type_currency" tableName="payment.chargeback_config" platforms="POSTGRES"/>
        <dropIndex indexName="ix_chargeback_history_created_at_collection_status" tableName="payment.chargeback_history"/>
        <dropIndex indexName="ix_inbox_notifications_account_id_status" tableName="payment.inbox_notifications" platforms="POSTGRES"/>
        <dropIndex indexName="ix_payment_orders_account_id_success_at" tableName="payment.payment_orders"/>
        <dropIndex indexName="ix_account_offer_reward_log" tableName="payment.account_offer_reward_log" platforms="POSTGRES"/>
        <dropIndex indexName="ix_account_offer_reward_log_status" tableName="payment.account_offer_reward_log"/>
        <dropIndex indexName="ix_payment_orders_account_id_internal_status_at" tableName="payment.payment_orders"/>
        <dropIndex indexName="ix_account_fraud_info_sign_up_fraud_score" tableName="fraud.account_fraud_info"/>
        <dropIndex indexName="ix_payment_orders_chargeback_at" tableName="payment.payment_orders"/>
        <dropIndex indexName="ix_payment_orders_tc40_received_at" tableName="payment.payment_orders"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropTable name="uam.accounts" sequenceCol="id"/>
        <dropTable name="uam.account_gameplay_info" sequenceCol="id"/>
        <dropTable name="payment.account_meta_info" sequenceCol="id"/>
        <dropTable name="uam.account_meta_info" sequenceCol="id"/>
        <dropTable name="payment.account_offer_meta_info" sequenceCol="id"/>
        <dropTable name="payment.account_payment_info" sequenceCol="id"/>
        <dropTable name="payment.payment_methods" sequenceCol="id"/>
        <dropTable name="payment.account_purchase_limit" sequenceCol="id"/>
        <dropTable name="uam.account_vip_level_history" sequenceCol="id"/>
        <dropTable name="payment.withdraw_methods" sequenceCol="id"/>
        <dropTable name="payment.ach_payment_method" sequenceCol="id"/>
        <dropTable name="payment.ach_payment_method_address" sequenceCol="id"/>
        <dropTable name="core.brands" sequenceCol="id"/>
        <dropTable name="payment.card_bin_info"/>
        <dropTable name="payment.card_payment_method" sequenceCol="id"/>
        <dropTable name="payment.card_payment_method_meta_info" sequenceCol="id"/>
        <dropTable name="payment.card_transaction_details" sequenceCol="id"/>
        <dropTable name="payment.chargeback_history" sequenceCol="id"/>
        <dropTable name="payment.deactivate_provider_policy" sequenceCol="id"/>
        <dropTable name="payment.error_mapping" sequenceCol="id"/>
        <dropTable name="payment.offer_templates" sequenceCol="id"/>
        <dropTable name="payment.payment_method_fraud_applied_rule" sequenceCol="id"/>
        <dropTable name="payment.payment_method_error" sequenceCol="id"/>
        <dropTable name="payment.payment_method_meta_info" sequenceCol="id"/>
        <dropTable name="payment.payment_method_verification_request" sequenceCol="id"/>
        <dropTable name="payment.payment_orders" sequenceCol="id"/>
        <dropTable name="payment.payment_order_meta_info" sequenceCol="id"/>
        <dropTable name="payment.providers" sequenceCol="id"/>
        <dropTable name="payment.routing_member_card_token" sequenceCol="id"/>
        <dropTable name="payment.routing_provider_card_token" sequenceCol="id"/>
        <dropTable name="payment.vip_level_purchase_templates" sequenceCol="id"/>
        <dropTable name="payment.weekly_personalized_offer_aggregation_history" sequenceCol="id"/>
        <dropTable name="payment.withdraw_money_requests" sequenceCol="id"/>
        <dropTable name="payment.account_payment_aggregation" sequenceCol="id"/>
        <dropTable name="payment.account_payment_settings" sequenceCol="id"/>
        <dropTable name="payment.account_provider_black_list" sequenceCol="id"/>
        <dropTable name="payment.brand_settings" sequenceCol="id"/>
        <dropTable name="payment.currency_rate" sequenceCol="id"/>
        <dropTable name="payment.daily_redeem_limits" sequenceCol="id"/>
        <dropTable name="payment.dynamic_secure_3d_checks" sequenceCol="id"/>
        <dropTable name="payment.e_transfer_payment_method" sequenceCol="id"/>
        <dropTable name="payment.fx_currency_rate" sequenceCol="id"/>
        <dropTable name="payment.payment_aggregated_meta_info" sequenceCol="id"/>
        <dropTable name="payment.payment_orders_bonus_abuse" sequenceCol="id"/>
        <dropTable name="payment.provider_redeem_limit_policies" sequenceCol="id"/>
        <dropTable name="payment.purchase_track_event" sequenceCol="id"/>
        <dropTable name="payment.refund_history" sequenceCol="id"/>
        <dropTable name="payment.rescue_purchase_providers_settings" sequenceCol="id"/>
        <dropTable name="payment.routing_error_config" sequenceCol="id"/>
        <dropTable name="payment.routing_provider_ach_token" sequenceCol="id"/>
        <dropTable name="payment.routing_rules" sequenceCol="id"/>
        <dropTable name="payment.routing_rule_providers"/>
        <dropTable name="payment.sca_authentications" sequenceCol="id"/>
        <dropTable name="payment.volume_allocation_config" sequenceCol="id"/>
        <dropTable name="payment.withdraw_money_request_meta_info" sequenceCol="id"/>
        <dropTable name="payment.accepted_payment_terms" sequenceCol="id"/>
        <dropTable name="payment.account_card_aggregation" sequenceCol="id"/>
        <dropTable name="payment.account_deferred_purchase_limit" sequenceCol="id"/>
        <dropTable name="payment.blacklist" sequenceCol="id"/>
        <dropTable name="payment.chargeback" sequenceCol="id"/>
        <dropTable name="payment.chargeback_config" sequenceCol="id"/>
        <dropTable name="payment.crypto_payment_methods" sequenceCol="id"/>
        <dropTable name="payment.crypto_purchase_details" sequenceCol="id"/>
        <dropTable name="payment.crypto_rates" sequenceCol="id"/>
        <dropTable name="payment.crypto_withdrawal_details" sequenceCol="id"/>
        <dropTable name="payment.debezium_signal"/>
        <dropTable name="payment.e_wallet_payment_method" sequenceCol="id"/>
        <dropTable name="payment.inbox_notifications" sequenceCol="id"/>
        <dropTable name="payment.transaction_limit" sequenceCol="id"/>
        <dropTable name="payment.account_offer_reward_log" sequenceCol="id"/>
        <dropTable name="payment.offer_template_change_log" sequenceCol="id"/>
        <dropTable name="fraud.account_fraud_info" sequenceCol="id"/>
        <dropTable name="payment.external_reward" sequenceCol="id"/>
    </changeSet>
</migration>