package org.patrianna.workflow.assertions.ui.page.registration;

import static java.util.Objects.requireNonNull;
import static org.patrianna.core.service.brand.BrandApi.brandApi;

import lombok.SneakyThrows;
import org.patrianna.core.service.brand.CrmApi;
import org.patrianna.ui.web.page.registration.RegistrationPageAbstract;
import org.patrianna.workflow.assertions.AssertsAbstract;

@SuppressWarnings("java:S2160")
public abstract class RegistrationPageAssertsAbstract extends
    AssertsAbstract<RegistrationPageAssertsAbstract, RegistrationPageAbstract> {

    private final CrmApi crmApi = brandApi().crmApi();

    protected RegistrationPageAssertsAbstract(RegistrationPageAbstract actual, Class<?> selfType) {
        super(actual, selfType);
    }

    @SneakyThrows
    public RegistrationPageAssertsAbstract invokeMethod(String method) {
        this.getClass().getMethod(method).invoke(this);

        return this;
    }

    public RegistrationPageAssertsAbstract hasAvailableStates() {
        var expectedAllowedStates = requireNonNull(crmApi.getSignupCountriesAndStates().body())
            .getCountriesAndStates()
            .getLast()
            .getAllowedStates();

        softly.assertThat(actual.getAvailableStates())
            .as("Available states")
            .isEqualTo(expectedAllowedStates);

        return this;
    }

    public RegistrationPageAssertsAbstract hasStateSelectionError() {
        softly.assertThat(actual.getStateSelectionError().textContent())
            .as("State selection error")
            .isEqualTo("You are not entitled to play in this state");

        return this;
    }

    public RegistrationPageAssertsAbstract hasEmailRequirementError() {
        softly.assertThat(actual.getEmailRequirementError().textContent())
            .as("Email requirement error")
            .isEqualTo("Please enter a valid email address");

        return this;
    }

    public RegistrationPageAssertsAbstract hasEmailFieldEmpty() {
        softly.assertThat(actual.getEmailAddressInput().textContent())
            .as("Email field empty")
            .isNullOrEmpty();

        return this;
    }

    public RegistrationPageAssertsAbstract hasEmailInvalidCharactersError() {
        softly.assertThat(actual.getEmailRequirementError().textContent())
            .as("Email invalid characters error")
            .contains("Text entered contains invalid characters");

        return this;
    }

    public RegistrationPageAssertsAbstract hasSubmissionFormButtonDisabled() {
        softly.assertThat(actual.isSubmissionFormButtonEnabled())
            .as("Submit form button disabled")
            .isFalse();

        return this;
    }

    public RegistrationPageAssertsAbstract hasSubmissionFormButtonEnabled() {
        softly.assertThat(actual.isSubmissionFormButtonEnabled())
            .as("Submit form button enabled")
            .isTrue();

        return this;
    }

    public RegistrationPageAssertsAbstract hasShortPasswordError() {
        softly.assertThat(actual.getPasswordRequirementError().textContent())
            .as("Short password error")
            .isEqualTo("Password must be at least 8 characters long");

        return this;
    }

    public RegistrationPageAssertsAbstract hasUpperCasePasswordError() {
        softly.assertThat(actual.getPasswordRequirementError().textContent())
            .as("Password without upper case error")
            .isEqualTo("Should contain at least one uppercase letter");

        return this;
    }

    public RegistrationPageAssertsAbstract hasNumberPasswordError() {
        softly.assertThat(actual.getPasswordRequirementError().textContent())
            .as("Password without number error")
            .isEqualTo("Should contain at least one digit");

        return this;
    }

    public RegistrationPageAssertsAbstract hasNoPasswordError() {
        softly.assertThat(actual.getPasswordRequirementError().isHidden())
            .as("Password error visibility")
            .isTrue();

        return this;
    }

    public RegistrationPageAssertsAbstract pageUrlIsRegistration() {
        softly.assertThat(actual.getActualUrlRelativePath())
            .as("Relative page URL matches Registration URL")
            .contains(actual.getRelativeUrl());
        return this;
    }

    public RegistrationPageAssertsAbstract emailIsPrefilledWith(String email) {
        softly.assertThat(actual.getEmailAddressInput().inputValue())
            .as("Email is prefilled with")
            .isEqualToIgnoringCase(email);
        return this;
    }
}