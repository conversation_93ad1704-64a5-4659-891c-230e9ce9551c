package org.patrianna.workflow.assertions.ui.page.transactionhistory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import lombok.EqualsAndHashCode;
import org.patrianna.ui.web.page.transactionhistory.B2BTransactionHistoryPage;

@EqualsAndHashCode(callSuper = false)
public class B2BTransactionHistoryPageAsserts extends
    TransactionHistoryAssertsAbstract {

    private final B2BTransactionHistoryPage b2BTransactionHistoryPage;

    private static final int EXPECTED_MAX_ORDERS = 10;
    private static final int ORDER_DATE_LENGTH = 19;
    private static final int SIX = 6;

    public B2BTransactionHistoryPageAsserts(B2BTransactionHistoryPage b2BTransactionHistoryPage) {
        super(b2BTransactionHistoryPage, B2BTransactionHistoryPageAsserts.class);
        this.b2BTransactionHistoryPage = b2BTransactionHistoryPage;
    }

    public B2BTransactionHistoryPageAsserts hasAllMandatoryElementsPresent() {
        pageTitleIsVisible();
        purchasesTabIsVisible();
        redemptionsTabIsVisible();
        return this;
    }

    public void redemptionsTabIsVisible() {
        softly.assertThat(b2BTransactionHistoryPage.isRedemptionsTabVisible())
            .as("Redemptions tab presence")
            .isTrue();
    }

    public void purchasesTabIsVisible() {
        softly.assertThat(b2BTransactionHistoryPage.isPurchasesTabVisible())
            .as("Purchase tab presence")
            .isTrue();
    }

    public void pageTitleIsVisible() {
        softly.assertThat(b2BTransactionHistoryPage.isPageTitleVisible())
            .as("Page title presence")
            .isTrue();
    }

    public B2BTransactionHistoryPageAsserts hasNotMoreThanTenOrders() {
        softly.assertThat(b2BTransactionHistoryPage.getOrdersAmountOnPage())
            .as("Amount of orders on the page")
            .isLessThanOrEqualTo(EXPECTED_MAX_ORDERS);
        return this;
    }

    public B2BTransactionHistoryPageAsserts hasOrderStatus(int orderNumber, String expectedStatus) {
        var actualStatus = b2BTransactionHistoryPage.getOrderStatusText(orderNumber);

        softly.assertThat(actualStatus)
            .as("Order status for order #" + orderNumber)
            .isEqualTo(expectedStatus);

        return this;
    }

    public B2BTransactionHistoryPageAsserts hasDateOrder(int orderNumber) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM, yyyy", Locale.ENGLISH);
        String expectedDate = LocalDateTime.now().format(formatter);

        var actualDate = b2BTransactionHistoryPage.getOrderDateText(orderNumber);
        var actualDateTrimmed = actualDate.substring(SIX, ORDER_DATE_LENGTH);

        softly.assertThat(actualDateTrimmed)
            .as("Order date for order #%s" + orderNumber)
            .contains(expectedDate);

        return this;
    }

    public B2BTransactionHistoryPageAsserts hasDateNewerThanPreviousOrder(int firstOrder, int secondOrder) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM, yyyy 'at' hh:mm a", Locale.ENGLISH);

        String firstOrderDateText = extractDateText(firstOrder);
        String secondOrderDateText = extractDateText(secondOrder);

        LocalDateTime firstOrderDate = LocalDateTime.parse(firstOrderDateText, formatter);
        LocalDateTime secondOrderDate = LocalDateTime.parse(secondOrderDateText, formatter);

        softly.assertThat(firstOrderDate)
            .as("1st order date should be after 2nd order date")
            .isAfterOrEqualTo(secondOrderDate);

        return this;
    }

    public B2BTransactionHistoryPageAsserts hasOrderPaymentMethod(int orderNumber, String expectedMethod) {
        var actualPaymentMethod = b2BTransactionHistoryPage.getOrderMethodText(orderNumber);

        softly.assertThat(actualPaymentMethod)
            .as("Order payment method")
            .isNotNull()
            .contains(expectedMethod);

        return this;
    }

    public B2BTransactionHistoryPageAsserts hasOrderScAmount(int orderNumber, String expectedScAmount) {
        var actualScAmount = parseFloatFromString(b2BTransactionHistoryPage.getOrderScAmountText(orderNumber));
        var expectedScAmountFloat = parseFloatFromString(expectedScAmount);

        softly.assertThat(actualScAmount)
            .as("Order SC amount")
            .isEqualTo(expectedScAmountFloat);

        return this;
    }

    public B2BTransactionHistoryPageAsserts hasOrderGcAmount(int orderNumber, String expectedGcAmount) {
        var actualGcAmount = parseFloatFromString(b2BTransactionHistoryPage.getOrderGcAmountText(orderNumber));
        var expectedGcAmountFloat = parseFloatFromString(expectedGcAmount);

        softly.assertThat(actualGcAmount)
            .as("Order GC amount")
            .isEqualTo(expectedGcAmountFloat);

        return this;
    }

    public B2BTransactionHistoryPageAsserts hasActivePage(String expectedActivePage) {
        var currentActivePage = b2BTransactionHistoryPage.getCurrentActivePageNumber();

        softly.assertThat(currentActivePage)
            .as("Active page")
            .isEqualTo(expectedActivePage);

        return this;
    }
}