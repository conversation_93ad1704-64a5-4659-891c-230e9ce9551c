package org.patrianna.workflow.assertions.ui.page.login;

import org.patrianna.ui.web.page.login.LoginPageAbstract;
import org.patrianna.workflow.assertions.AssertsAbstract;

public abstract class LoginPageAssertsAbstract<T extends LoginPageAbstract> extends
    AssertsAbstract<LoginPageAssertsAbstract<T>, T> {

    protected LoginPageAssertsAbstract(T actual, Class<?> selfType) {
        super(actual, selfType);
    }

    public LoginPageAssertsAbstract<T> userExistsModalForEmailShownWithMessage() {
        softly.assertThat(actual.getUserExistsModalMessage())
            .as("Email: User exists modal message")
            .isEqualTo(actual.getExpectedUserExistsModalMessage());
        return this;
    }

    public LoginPageAssertsAbstract<T> userExistsModalForGoogleShownWithMessage() {
        softly.assertThat(actual.getUserExistsModalMessage())
            .as("Google: User exists modal message")
            .isEqualTo("Hello again! We can see you already have an account with us, "
                       + "please login to your existing account via Google");
        return this;
    }

    public LoginPageAssertsAbstract<T> userExistsModalForFacebookShownWithMessage() {
        softly.assertThat(actual.getUserExistsModalMessage())
            .as("User exists modal message")
            .isEqualTo("Our records show that you already have an account with us, "
                + "please login to your existing account via Facebook");
        return this;
    }

    public LoginPageAssertsAbstract<T> incorrectEmailFormatErrorShown() {
        softly.assertThat(actual.getEmailErrorMessage())
            .as("Email error message")
            .isEqualTo("Please enter a valid email address");
        return this;
    }

    public LoginPageAssertsAbstract<T> passwordMismatchErrorShown() {
        errorShown("Your Email address and Password do not match. Please try again");
        return this;
    }

    public LoginPageAssertsAbstract<T> requiredFieldErrorShown() {
        errorShown("Required field");
        return this;
    }

    public LoginPageAssertsAbstract<T> errorShown(String error) {
        softly.assertThat(actual.getPasswordErrorMessage())
            .as("Error message")
            .isEqualTo(error);
        return this;
    }

    public LoginPageAssertsAbstract<T> pageUrlIsLogin() {
        String expectedPath = actual.getRelativeUrl().replaceAll("\\?.*$", "").trim();

        softly.assertThat(actual.getActualUrlRelativePath())
            .as("Relative page URL matches Login URL")
            .contains(expectedPath);
        return this;
    }
}
