package org.patrianna.workflow.assertions.ui.page.store;

import org.patrianna.ui.web.page.store.StorePageAbstract;
import org.patrianna.workflow.assertions.AssertsAbstract;

public abstract class StorePageAssertsAbstract extends
    AssertsAbstract<StorePageAssertsAbstract, StorePageAbstract> {

    protected StorePageAssertsAbstract(StorePageAbstract actual, Class<?> selfType) {
        super(actual, selfType);
    }

    public StorePageAssertsAbstract hasAllMandatoryElementsPresent() {
        offerItemsAreVisible();
        return this;
    }

    public void offerItemsAreVisible() {
        softly.assertThat(actual.getOfferItem().all())
            .as("Offers presence")
            .isNotEmpty();
    }

    public StorePageAssertsAbstract offerShownWithoutSc() {
        softly.assertThat(actual.visibleScOffers().findAny())
            .as("Sweepstakes offer list")
            .isEmpty();
        return this;
    }

    public StorePageAssertsAbstract offerIsVisible(String offerCode) {
        softly.assertThat(actual.isOfferVisible(offerCode))
            .as("Offer with code %s presence".formatted(offerCode))
            .isTrue();
        return this;
    }

    public StorePageAssertsAbstract sweepstakesRulesIsAbsent() {
        softly.assertThat(actual.getSweepstakesRulesLink().isVisible())
            .as("Sweepstakes rules link presence")
            .isFalse();
        return this;
    }

    public StorePageAssertsAbstract snackBarHasMessage(String expectedMessage) {
        softly.assertThat(actual.getSnackbarModal().getMessageText())
            .as("Snackbar message")
            .isEqualTo(expectedMessage);
        return this;
    }

    public StorePageAssertsAbstract liveChatIconIsVisible() {
        softly.assertThat(actual.getLiveChatIcon().isVisible())
            .as("Live Chat icon visibility")
            .isTrue();
        return this;
    }

    public StorePageAssertsAbstract blockWithOfferPresence() {
        softly.assertThat(actual.getOffersBlock().first().isVisible())
            .as("Offer block presence")
            .isTrue();
        return this;
    }

    public StorePageAssertsAbstract blockReferAFriendPresence() {
        softly.assertThat(actual.getReferAFriendBlock().isVisible())
            .as("Refer a friend block presence")
            .isTrue();
        return this;
    }

    public StorePageAssertsAbstract blockDailyBonusPresence() {
        softly.assertThat(actual.getDailyBonusBlock().isVisible())
            .as("Daily bonus block presence")
            .isTrue();
        return this;
    }

}
