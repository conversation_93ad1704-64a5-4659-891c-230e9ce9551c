package org.patrianna.workflow.assertions.ui.page.game;

import org.patrianna.common.data.dto.ui.payment.Balance;
import org.patrianna.ui.web.page.game.GamePageAbstract;
import org.patrianna.workflow.assertions.AssertsAbstract;

public abstract class GamePageAssertsAbstract extends
    AssertsAbstract<GamePageAssertsAbstract, GamePageAbstract> {

    protected GamePageAssertsAbstract(GamePageAbstract actual, Class<?> selfType) {
        super(actual, selfType);
    }

    public GamePageAssertsAbstract hasAllMandatoryElementsPresent() {
        lowOnCoinsModalIsVisible();
        return this;
    }

    public GamePageAssertsAbstract lowOnCoinsModalIsVisible() {
        softly.assertThat(actual.isLowOnCoinsModalVisible())
            .as("Low on coins modal presence")
            .isTrue();
        return this;
    }

    public GamePageAssertsAbstract pageUrlHasGcGamePath() {
        softly.assertThat(actual.getActualUrlRelativePath())
            .as("Relative page URL matches Gold Coin URL")
            .contains(actual.getGcGamePlayUrl());
        return this;
    }

    public GamePageAssertsAbstract scBalanceUpdated(Balance expectedBalance, double actualScBalance) {
        softly.assertThat(actualScBalance)
            .as("SC balance")
            .isEqualTo(expectedBalance.getSc());
        return this;
    }

    public GamePageAssertsAbstract gcBalanceUpdated(Balance expectedBalance, double actualGcBalance) {
        softly.assertThat(actualGcBalance)
            .as("GC balance")
            .isEqualTo(expectedBalance.getGc());
        return this;
    }
}