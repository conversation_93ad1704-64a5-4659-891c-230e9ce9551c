package org.patrianna.workflow.assertions.ui.page.home;

import org.patrianna.common.data.dto.ui.payment.Balance;
import org.patrianna.common.enums.Visibility;
import org.patrianna.ui.web.page.home.HomePageAbstract;
import org.patrianna.workflow.assertions.AssertsAbstract;

public abstract class HomePageAssertsAbstract<T extends HomePageAbstract> extends
    AssertsAbstract<HomePageAssertsAbstract<T>, T> {

    protected HomePageAssertsAbstract(T actual, Class<?> selfType) {
        super(actual, selfType);
    }

    public abstract HomePageAssertsAbstract<T> hasAllMandatoryElementsPresent();

    public abstract HomePageAssertsAbstract<T> hasLogout();

    public abstract HomePageAssertsAbstract<T> balanceWidgetIsVisible();

    public HomePageAssertsAbstract<T> balanceUpdated(Balance expectedBalance, Balance actualBalance) {
        gcBalanceUpdated(expectedBalance.getGc(), actualBalance);
        scBalanceUpdated(expectedBalance.getSc(), actualBalance);
        return this;
    }

    private HomePageAssertsAbstract<T> gcBalanceUpdated(double expectedBalance, Balance actualBalance) {
        softly.assertThat(actualBalance.getGc())
            .as("GC balance")
            .isEqualTo(expectedBalance);
        return this;
    }

    private HomePageAssertsAbstract<T> scBalanceUpdated(double expectedBalance, Balance actualBalance) {
        softly.assertThat(actualBalance.getSc())
            .as("SC balance")
            .isEqualTo(expectedBalance);
        return this;
    }

    public HomePageAssertsAbstract<T> chatBotShouldBe(Visibility visibility) {
        softly.assertThat(actual.isChatBotVisible())
            .as("Chatbot presence")
            .isEqualTo(visibility.getStatus());
        return this;
    }

    public HomePageAssertsAbstract<T> unlockedOfferModalIsVisible() {
        softly.assertThat(actual.unlockedOfferModalIsVisible())
            .as("Unlock new offer modal visibility")
            .isTrue();
        return this;
    }

    public HomePageAssertsAbstract<T> pageUrlIsHome() {
        softly.assertThat(actual.getActualUrlRelativePath())
            .as("Relative page URL matches Home URL")
            .contains(actual.getRelativeUrl());
        return this;
    }
}
