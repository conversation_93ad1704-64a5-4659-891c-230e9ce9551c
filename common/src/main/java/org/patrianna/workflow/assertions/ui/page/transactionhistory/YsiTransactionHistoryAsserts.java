package org.patrianna.workflow.assertions.ui.page.transactionhistory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.Objects;
import lombok.EqualsAndHashCode;
import org.patrianna.ui.web.page.transactionhistory.YsiTransactionHistoryPage;

@EqualsAndHashCode(callSuper = false)
public class YsiTransactionHistoryAsserts extends TransactionHistoryAssertsAbstract {

    private final YsiTransactionHistoryPage ysiTransactionHistoryPage;

    private static final int SIX = 6;
    private static final int EXPECTED_MAX_ORDERS = 10;
    private static final int NINETEEN = 19;

    public YsiTransactionHistoryAsserts(YsiTransactionHistoryPage ysiTransactionHistoryPage) {
        super(ysiTransactionHistoryPage, YsiTransactionHistoryAsserts.class);
        this.ysiTransactionHistoryPage = ysiTransactionHistoryPage;
    }

    @Override
    public YsiTransactionHistoryAsserts hasAllMandatoryElementsPresent() {
        redemptionsTabIsVisible();
        purchasesTabIsVisible();
        pageTitleIsVisible();

        return this;
    }

    public void redemptionsTabIsVisible() {
        softly.assertThat(ysiTransactionHistoryPage.isRedemptionsTabVisible())
            .as("Redemptions tab presence")
            .isTrue();
    }

    public void purchasesTabIsVisible() {
        softly.assertThat(ysiTransactionHistoryPage.isPurchasesTabVisible())
            .as("Purchase tab presence")
            .isTrue();
    }

    public void pageTitleIsVisible() {
        softly.assertThat(ysiTransactionHistoryPage.isPageTitleVisible())
            .as("Page title presence")
            .isTrue();
    }

    public YsiTransactionHistoryAsserts hasPagePaginationIsVisible() {
        var isVisible = ysiTransactionHistoryPage.getPaginationPanel().isVisible();
        if (!isVisible) {
            failWithMessage("Expected Pagination PAnel to be visible");
        }

        return this;
    }

    @Override
    public YsiTransactionHistoryAsserts hasNotMoreThanTenOrders() {
        softly.assertThat(ysiTransactionHistoryPage.getOrdersAmountOnPage())
            .as("Amount of orders on the page")
            .isLessThanOrEqualTo(EXPECTED_MAX_ORDERS);

        return this;
    }

    @Override
    public YsiTransactionHistoryAsserts hasOrderStatus(int orderNumber, String expectedStatus) {
        var actualStatus = ysiTransactionHistoryPage.getOrderStatusText(orderNumber);

        softly.assertThat(actualStatus)
            .as("Order status for order #" + orderNumber)
            .isEqualTo(expectedStatus);

        return this;
    }

    @Override
    public YsiTransactionHistoryAsserts hasDateOrder(int orderNumber) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM, yyyy", Locale.ENGLISH);
        String expectedDate = LocalDateTime.now().format(formatter);

        var actualDate = ysiTransactionHistoryPage.getOrderDateText(orderNumber);
        var actualDateTrimmed = actualDate.substring(SIX, NINETEEN);

        softly.assertThat(actualDateTrimmed)
            .as("Order date for order #" + orderNumber)
            .contains(expectedDate);

        return this;
    }

    @Override
    public YsiTransactionHistoryAsserts hasDateNewerThanPreviousOrder(int firstOrder, int secondOrder) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM, yyyy 'at' hh:mm a", Locale.ENGLISH);

        String firstOrderDateText = extractDateText(firstOrder);
        String secondOrderDateText = extractDateText(secondOrder);

        LocalDateTime firstOrderDate = LocalDateTime.parse(firstOrderDateText, formatter);
        LocalDateTime secondOrderDate = LocalDateTime.parse(secondOrderDateText, formatter);

        softly.assertThat(firstOrderDate)
            .as("1st order date should be after 2nd order date")
            .isAfterOrEqualTo(secondOrderDate);

        return this;
    }

    @Override
    public YsiTransactionHistoryAsserts hasOrderPaymentMethod(int orderNumber, String expectedMethod) {
        var actualPaymentMethod = ysiTransactionHistoryPage.getOrderMethodText(orderNumber);

        softly.assertThat(actualPaymentMethod)
            .as("Order payment method")
            .isNotNull()
            .contains(expectedMethod);

        return this;
    }

    @Override
    public YsiTransactionHistoryAsserts hasOrderScAmount(int orderNumber, String expectedScAmount) {
        var actualScAmount = parseFloatFromString(ysiTransactionHistoryPage.getOrderScAmountText(orderNumber));
        var expectedScAmountFloat = parseFloatFromString(expectedScAmount);

        softly.assertThat(actualScAmount)
            .as("Order SC amount")
            .isEqualTo(expectedScAmountFloat);

        return this;
    }

    @Override
    public YsiTransactionHistoryAsserts hasOrderGcAmount(int orderNumber, String expectedGcAmount) {
        var actualGcAmount = parseFloatFromString(ysiTransactionHistoryPage.getOrderGcAmountText(orderNumber));
        var expectedGcAmountFloat = parseFloatFromString(expectedGcAmount);

        softly.assertThat(actualGcAmount)
            .as("Order GC amount")
            .isEqualTo(expectedGcAmountFloat);

        return this;
    }

    @Override
    public YsiTransactionHistoryAsserts hasActivePage(String expectedActivePage) {
        var currentActivePage = ysiTransactionHistoryPage.getCurrentActivePageNumber();
        if (!Objects.equals(currentActivePage, expectedActivePage)) {
            failWithMessage("Expected active page to be <%s> but was <%s>", expectedActivePage,
                currentActivePage);
        }
        return this;
    }
}