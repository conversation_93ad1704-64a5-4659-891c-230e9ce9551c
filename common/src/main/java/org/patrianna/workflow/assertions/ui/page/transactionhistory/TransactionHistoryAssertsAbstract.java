package org.patrianna.workflow.assertions.ui.page.transactionhistory;

import static org.patrianna.ui.factory.PortalActions.homeActions;
import static org.patrianna.utils.ui.WaitsUtil.waitUntilAsserted;

import java.util.stream.IntStream;
import lombok.EqualsAndHashCode;
import org.patrianna.ui.web.page.transactionhistory.TransactionHistoryPageAbstract;
import org.patrianna.utils.common.NumberExtractor;
import org.patrianna.workflow.assertions.AssertsAbstract;

@EqualsAndHashCode(callSuper = false)
public abstract class TransactionHistoryAssertsAbstract
    extends AssertsAbstract<TransactionHistoryAssertsAbstract, TransactionHistoryPageAbstract> {

    private static final int EXPECTED_MAX_ORDERS = 10;
    private static final int FIRST_OFFER = 1;
    protected TransactionHistoryPageAbstract actualTransactionHistoryPage;

    protected TransactionHistoryAssertsAbstract(TransactionHistoryPageAbstract actual, Class<?> selfType) {
        super(actual, selfType);
        actualTransactionHistoryPage = actual;
    }

    public abstract TransactionHistoryAssertsAbstract hasAllMandatoryElementsPresent();

    public abstract TransactionHistoryAssertsAbstract hasNotMoreThanTenOrders();

    public abstract TransactionHistoryAssertsAbstract hasOrderStatus(int orderNumber, String expectedStatus);

    public abstract TransactionHistoryAssertsAbstract hasDateOrder(int orderNumber);

    public abstract TransactionHistoryAssertsAbstract hasDateNewerThanPreviousOrder(int firstOrder, int secondOrder);

    public abstract TransactionHistoryAssertsAbstract hasOrderPaymentMethod(int orderNumber, String expectedMethod);

    public abstract TransactionHistoryAssertsAbstract hasOrderScAmount(int orderNumber, String expectedScAmount);

    public abstract TransactionHistoryAssertsAbstract hasOrderGcAmount(int orderNumber, String expectedGcAmount);

    public abstract TransactionHistoryAssertsAbstract hasActivePage(String expectedActivePage);

    // remove lower case conversion after the fix of PAY-7673
    public TransactionHistoryAssertsAbstract hasOrderTitle(int orderNumber, String expectedTitle) {
        softly.assertThat(actualTransactionHistoryPage.getTransactionItemTitleText(orderNumber).toLowerCase())
            .as("Order title for order #" + orderNumber)
            .contains(expectedTitle.toLowerCase());
        return this;
    }

    public TransactionHistoryAssertsAbstract hasOrdersTitles(String expectedTitle, Integer... orderNumbers) {
        for (Integer orderNumber : orderNumbers) {
            softly.assertThat(actualTransactionHistoryPage.getTransactionItemTitleText(orderNumber))
                .as("Order title for order #" + orderNumber)
                .isEqualTo(expectedTitle);
        }
        return this;
    }


    public TransactionHistoryAssertsAbstract hasRedeemStatus(int orderNumber, String expectedStatus) {
        var actualStatus = actualTransactionHistoryPage.getRedeemStatusTextByLocator(orderNumber);

        softly.assertThat(actualStatus)
            .as("Redeem status for order #" + orderNumber)
            .isEqualTo(expectedStatus);
        return this;
    }

    public TransactionHistoryAssertsAbstract hasRedeemTitleForAllRedeems(String expectedProvider) {
        int amountOfRedeems = actualTransactionHistoryPage.getOrdersAmountOnPage();

        IntStream.range(0, amountOfRedeems)
            .forEach(orderNumber -> hasOrderTitle(orderNumber, expectedProvider));
        return this;
    }

    public TransactionHistoryAssertsAbstract hasCancelButtonForOrder(int orderNumber) {
        softly.assertThat(actualTransactionHistoryPage.isCancelButtonVisible(orderNumber))
            .as("Cancel button visibility for order #" + orderNumber)
            .isTrue();
        return this;
    }

    public TransactionHistoryAssertsAbstract hasNoCancelButtonForOrder(int orderNumber) {
        softly.assertThat(actualTransactionHistoryPage.isCancelButtonVisible(orderNumber))
            .as("Cancel button visibility for order #" + orderNumber)
            .isFalse();
        return this;
    }

    public TransactionHistoryAssertsAbstract hasTimestampForOrder(int orderNumber) {
        softly.assertThat(actualTransactionHistoryPage.isTransactionItemTimestampVisible(orderNumber))
            .as("Timestamp visibility for order #" + orderNumber)
            .isTrue();
        return this;
    }

    public TransactionHistoryAssertsAbstract hasOrderCount(int expectedCount) {
        softly.assertThat(actualTransactionHistoryPage.getOrdersAmountOnPage())
            .as("Orders count")
            .isEqualTo(expectedCount);
        return this;
    }

    public TransactionHistoryAssertsAbstract ordersSortedDescendingByDate() {
        int firstOfferIndex = FIRST_OFFER - 1;
        int lastOfferIndex = EXPECTED_MAX_ORDERS - FIRST_OFFER - 1;
        for (int currentOrder = firstOfferIndex; currentOrder <= lastOfferIndex; currentOrder++) {
            int nextOrder = currentOrder + 1;
            hasDateNewerThanPreviousOrder(currentOrder, nextOrder);
        }

        return this;
    }

    public double parseFloatFromString(String text) {
        return NumberExtractor.extractDecimalFromString(text);
    }

    public TransactionHistoryAssertsAbstract currentScBalanceIs(double expectedBalance) {
        softly.assertThat(homeActions().getCurrentBalance().getSc())
            .as("Current balance")
            .isEqualTo(expectedBalance);

        return this;
    }

    public TransactionHistoryAssertsAbstract verifyOrderStatus(int orderNumber, String expectedStatus) {
        waitUntilAsserted(() -> this
            .hasNoCancelButtonForOrder(orderNumber)
            .hasOrderStatus(orderNumber, expectedStatus)
            .assertNow()
        );
        return this;
    }

    public TransactionHistoryAssertsAbstract snackbarModalVisible() {
        softly.assertThat(actual.getSnackbarModal().isVisible())
            .as("Snackbar modal presence")
            .isTrue();
        return this;
    }

    public String extractDateText(int orderNumber) {
        return actualTransactionHistoryPage
            .getOrderDateText(orderNumber)
            .replaceFirst("^.{6}", "");
    }
}