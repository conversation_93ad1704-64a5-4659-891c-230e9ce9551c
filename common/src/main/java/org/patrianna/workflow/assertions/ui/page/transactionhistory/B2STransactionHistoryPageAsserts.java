package org.patrianna.workflow.assertions.ui.page.transactionhistory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import lombok.EqualsAndHashCode;
import org.patrianna.ui.web.page.transactionhistory.B2STransactionHistoryPage;

@EqualsAndHashCode(callSuper = false)
public class B2STransactionHistoryPageAsserts extends
    TransactionHistoryAssertsAbstract {

    private final B2STransactionHistoryPage transactionHistoryPage;

    private static final int SIX = 6;
    private static final int EXPECTED_MAX_ORDERS = 10;
    private static final int NINETEEN = 19;

    public B2STransactionHistoryPageAsserts(B2STransactionHistoryPage transactionHistoryPage) {
        super(transactionHistoryPage, B2STransactionHistoryPageAsserts.class);
        this.transactionHistoryPage = transactionHistoryPage;
    }

    public B2STransactionHistoryPageAsserts hasAllMandatoryElementsPresent() {
        redemptionsTabIsVisible();
        purchasesTabIsVisible();
        pageTitleIsVisible();

        return this;
    }

    public void redemptionsTabIsVisible() {
        softly.assertThat(transactionHistoryPage.isRedemptionsTabVisible())
            .as("Redemptions tab presence")
            .isTrue();
    }

    public void purchasesTabIsVisible() {
        softly.assertThat(transactionHistoryPage.isPurchasesTabVisible())
            .as("Purchase tab presence")
            .isTrue();
    }

    public void pageTitleIsVisible() {
        softly.assertThat(transactionHistoryPage.isPageTitleVisible())
            .as("Page title presence")
            .isTrue();
    }

    public B2STransactionHistoryPageAsserts hasNotMoreThanTenOrders() {
        softly.assertThat(transactionHistoryPage.getOrdersAmountOnPage())
            .as("Amount of orders on the page")
            .isLessThanOrEqualTo(EXPECTED_MAX_ORDERS);

        return this;
    }

    public B2STransactionHistoryPageAsserts hasOrderStatus(int orderNumber, String expectedStatus) {
        var actualStatus = transactionHistoryPage.getOrderStatusText(orderNumber);

        softly.assertThat(actualStatus)
            .as("Order status for order #" + orderNumber)
            .isEqualTo(expectedStatus);

        return this;
    }

    public B2STransactionHistoryPageAsserts hasDateOrder(int orderNumber) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM, yyyy", Locale.ENGLISH);
        String expectedDate = LocalDateTime.now().format(formatter);

        var actualDate = transactionHistoryPage.getOrderDateText(orderNumber);
        var actualDateTrimmed = actualDate.substring(0, NINETEEN);

        softly.assertThat(actualDateTrimmed)
            .as("Order date for order #" + orderNumber)
            .contains(expectedDate);

        return this;
    }

    public B2STransactionHistoryPageAsserts hasDateNewerThanPreviousOrder(int firstOrder, int secondOrder) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd MMMM, yyyy 'at' hh:mm a", Locale.ENGLISH);

        String firstOrderDateText = extractDateText(firstOrder);
        String secondOrderDateText = extractDateText(secondOrder);

        LocalDateTime firstOrderDate = LocalDateTime.parse(firstOrderDateText, formatter);
        LocalDateTime secondOrderDate = LocalDateTime.parse(secondOrderDateText, formatter);

        softly.assertThat(firstOrderDate)
            .as("1st order date should be after 2nd order date")
            .isAfterOrEqualTo(secondOrderDate);

        return this;
    }

    public B2STransactionHistoryPageAsserts hasOrderPaymentMethod(int orderNumber, String expectedMethod) {
        var actualPaymentMethod = transactionHistoryPage.getOrderMethodText(orderNumber);

        softly.assertThat(actualPaymentMethod)
            .as("Order payment method")
            .isNotNull()
            .contains(expectedMethod);

        return this;
    }

    public B2STransactionHistoryPageAsserts hasOrderScAmount(int orderNumber, String expectedScAmount) {
        var actualScAmount = parseFloatFromString(transactionHistoryPage.getOrderScAmountText(orderNumber));
        var expectedScAmountFloat = parseFloatFromString(expectedScAmount);

        softly.assertThat(actualScAmount)
            .as("Order SC amount")
            .isEqualTo(expectedScAmountFloat);

        return this;
    }

    public B2STransactionHistoryPageAsserts hasOrderGcAmount(int orderNumber, String expectedGcAmount) {
        var actualGcAmount = parseFloatFromString(transactionHistoryPage.getOrderGcAmountText(orderNumber));
        var expectedGcAmountFloat = parseFloatFromString(expectedGcAmount);

        softly.assertThat(actualGcAmount)
            .as("Order GC amount")
            .isEqualTo(expectedGcAmountFloat);

        return this;
    }

    public B2STransactionHistoryPageAsserts hasActivePage(String expectedActivePage) {
        var currentActivePage = transactionHistoryPage.getCurrentActivePageNumber();

        softly.assertThat(currentActivePage)
            .as("Active page")
            .isEqualTo(expectedActivePage);

        return this;
    }
}