package org.patrianna.workflow.flow.dataactivation;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;
import static org.patrianna.ui.factory.PortalActions.completePurchaseActions;
import static org.patrianna.ui.factory.PortalActions.homeActions;
import static org.patrianna.ui.factory.PortalActions.loginActions;
import static org.patrianna.ui.factory.PortalActions.storeActions;
import static org.patrianna.ui.factory.PortalActions.ysiHomeActions;

import com.microsoft.playwright.Page;
import com.microsoft.playwright.Response;
import com.microsoft.playwright.options.Cookie;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitUntilState;
import java.util.Arrays;
import java.util.List;
import org.patrianna.common.data.dto.dataactivation.DataActivationFlowParams;
import org.patrianna.common.data.dto.ui.payment.CreditCardDetails;
import org.patrianna.common.enums.payment.PaymentMethods;
import org.patrianna.ui.constant.CookieCategory;
import org.patrianna.ui.constant.ThirdPartyServicesUrls;
import org.patrianna.ui.web.modal.completepurchase.YsiCompletePurchaseModal;
import org.patrianna.ui.web.modal.successfulpurchase.YsiSuccessfulPurchaseModal;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.home.YsiHomePage;
import org.patrianna.ui.web.page.unauthorized.landing.YsiLandingPage;
import org.patrianna.utils.common.UserData;
import org.patrianna.workflow.assertions.ui.dataactivation.AssertNavigation;


public class DataActivationFlow {

    private final String baseUrl;
    private final YsiHomePage homePage;
    private final YsiLandingPage landingPage;
    private YsiSuccessfulPurchaseModal<? extends SitePageAbstract>
        successfulPurchaseModal;
    private final YsiCompletePurchaseModal<? extends SitePageAbstract>
        completePurchaseModal;

    private DataActivationFlow(DataActivationFlowParams params) {
        this.baseUrl = params.getBaseUrl();
        this.homePage = new YsiHomePage();
        this.landingPage = new YsiLandingPage();
        this.completePurchaseModal = new YsiCompletePurchaseModal<>(homePage);
    }

    public static DataActivationFlow dataActivationFlow(DataActivationFlowParams params) {
        return new DataActivationFlow(params);
    }

    public DataActivationFlow openLandingPage() {
        landingPage.open(baseUrl, new Page.NavigateOptions().setWaitUntil(WaitUntilState.COMMIT));
        return this;
    }

    public DataActivationFlow disableAllCookies() {
        landingPage
            .getCookiesConsentModal()
            .customisePreferences()
            .disableFunctionalCookies()
            .disableTargetingCookies()
            .disablePerformanceCookies()
            .confirmMyChoices();
        return this;
    }

    public DataActivationFlow enableCookiesOnlyCategory(CookieCategory... cookieCategory) {
        landingPage
            .getCookiesConsentModal()
            .customisePreferences()
            .enableCookiesOnlyCategory(cookieCategory)
            .confirmMyChoices();
        return this;
    }

    public DataActivationFlow enableFunctionalOnlyCookies() {
        landingPage
            .getCookiesConsentModal()
            .customisePreferences()
            .disablePerformanceCookies()
            .disableTargetingCookies()
            .confirmMyChoices();
        return this;
    }

    public DataActivationFlow enableTargetOnlyCookies() {
        landingPage
            .getCookiesConsentModal()
            .customisePreferences()
            .disableFunctionalCookies()
            .disablePerformanceCookies()
            .confirmMyChoices();
        return this;
    }

    public DataActivationFlow enablePerformanceOnlyCookies() {
        landingPage
            .getCookiesConsentModal()
            .customisePreferences()
            .disableFunctionalCookies()
            .disableTargetingCookies()
            .confirmMyChoices();
        return this;
    }

    public YsiHomePage homePage() {
        return new YsiHomePage();
    }

    public AssertNavigation navigateTo(String path) {
        String fullUrl = String.format("%s%s", baseUrl, path);
        Page.NavigateOptions options = new Page.NavigateOptions().setWaitUntil(WaitUntilState.DOMCONTENTLOADED);
        Response response = getPlaywrightPage().navigate(fullUrl, options);
        return new AssertNavigation(response, fullUrl);
    }

    public DataActivationFlow checkCookiesPromptVisible() {
        landingPage.waitForCookiesConsentModal();
        return this;
    }

    public DataActivationFlow trustAllCookies() {
        homePage
            .getCookiesConsentModal()
            .waitUntilAcceptAllButtonVisible()
            .acceptAll();
        return this;
    }

    public List<Cookie> getContextCookies() {
        getPlaywrightPage().waitForLoadState(LoadState.LOAD);
        return getPlaywrightPage().context().cookies();
    }

    public DataActivationFlow performRegistration(UserData user) {
        landingPage.clickRegisterButton();
        loginActions().performRegistration(user);
        return this;
    }

    public DataActivationFlow navigateToGoldOfferPage() {
        homePage
            .clickGotItButton()
            .closeSubscriptionIfPresent();

        homeActions()
                .navigateToStorePage()
                .waitPageLoaded();
        return this;
    }

    public DataActivationFlow purchaseAnyOfferWithUserDetails(UserData user) {
        storeActions()
            .clickOnFirstOffer()
            .selectPaymentMethod(PaymentMethods.BUY_WITH_CARD);

        completePurchaseActions()
            .fillInCreditCardDetails(CreditCardDetails.validCardDetails2223())
            .fillInPersonalDetails(user);

        successfulPurchaseModal =
            (YsiSuccessfulPurchaseModal<? extends SitePageAbstract>) completePurchaseModal
                .getAddNewCardModal()
                .clickOnPayButtonWithSuccess();
        return this;
    }

    public DataActivationFlow waitSuccessPurchaseModal() {
        successfulPurchaseModal.waitUntilModalIsVisible();
        return this;
    }

    public DataActivationFlow closeSuccessPurchaseModal() {
        successfulPurchaseModal.close();
        return this;
    }

    public void enableDarkTheme() {
        ysiHomeActions().enableDarkTheme();
    }

    public DataActivationFlow openThirdPartyUrlsInOtherTabs() {
        Arrays.stream(ThirdPartyServicesUrls.values()).forEach(element -> getPlaywrightPage()
            .context()
            .newPage()
            .navigate(element.getUrl()));

        getPlaywrightPage().context().pages().getFirst().bringToFront();
        return this;
    }
}
