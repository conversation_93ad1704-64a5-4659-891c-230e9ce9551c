package org.patrianna.workflow.flow;

import static org.patrianna.workflow.flow.CrmFlow.crmFlow;

import admin.models.SetInternalPurchaseLimitRequest;
import admin.models.redeem.policy.UpdateRedeemPolicyRequest;
import api.v1.ForcementModeSpec;
import java.util.List;
import org.patrianna.common.data.dto.crm.accountinfo.AccountInfoDto;
import org.patrianna.common.data.dto.crm.accountinfo.GetAccountInfoResponseDto;
import org.patrianna.common.data.dto.crm.accountinfo.SetAccountInfoResponseDto;
import org.patrianna.common.enums.fraud.KycStatus;
import org.patrianna.core.service.retool.RetoolApiManager;
import org.patrianna.core.service.retool.dto.OfferApprovalRequestDto;
import org.patrianna.core.service.retool.dto.SaveOfferTemplateRequestDto;
import org.patrianna.core.service.retool.dto.SetOfferActiveStatusRequestDto;
import org.patrianna.utils.common.UserData;
import retrofit2.Response;

public class RetoolFlow {

    private final UserData user;
    private final RetoolApiManager retoolApiManager;
    private Response<?> lastResponse;

    public RetoolFlow(UserData user) {
        this.user = user;
        retoolApiManager = RetoolApiManager.retoolApiManager();
    }

    public static RetoolFlow retoolFlow(UserData user) {
        return new RetoolFlow(user);
    }

    @SuppressWarnings("unchecked")
    public <T> Response<T> getLastResponse() {
        return (Response<T>) lastResponse;
    }

    public RetoolFlow makeUserAdmin(Response<SetAccountInfoResponseDto> response) {
        retoolApiManager.accountRetoolApi().setUserAsAdmin(response.body());
        return this;
    }

    public RetoolFlow setSoftKycWithDefaultValues(AccountInfoDto accountInfoResponse) {
        retoolApiManager.accountRetoolApi().setSoftKycWithDefaultValues(accountInfoResponse);
        return this;
    }

    public RetoolFlow createKycIdVerification() {
        retoolApiManager.fraudRetoolApi().createKycIdVerification(user);
        return this;
    }

    public RetoolFlow createKycPoaVerification() {
        retoolApiManager.fraudRetoolApi().createKycPoaVerification(user);
        return this;
    }

    public RetoolFlow createKycPoaVerificationStatus(KycStatus status) {
        retoolApiManager.fraudRetoolApi().createKycPoaVerificationStatus(user, status);
        return this;
    }

    public RetoolFlow setInternalPurchaseLimitRequest(SetInternalPurchaseLimitRequest setInternalPurchaseLimitRequest) {
        retoolApiManager.paymentRetoolApi().setInternalPurchaseLimitRequest(setInternalPurchaseLimitRequest, user);
        return this;
    }

    public RetoolFlow saveOfferTemplate(SaveOfferTemplateRequestDto saveOfferTemplateRequestDto) {
        retoolApiManager.paymentRetoolApi().saveOfferTemplate(saveOfferTemplateRequestDto, user);
        return this;
    }

    public RetoolFlow updateRedeemPolicy(UpdateRedeemPolicyRequest updateRedeemPolicyRequest) {
        retoolApiManager.paymentRetoolApi().updateRedeemPolicy(updateRedeemPolicyRequest, user);
        return this;
    }

    public RetoolFlow updateOfferTemplate(SaveOfferTemplateRequestDto saveOfferTemplateRequestDto) {
        retoolApiManager.paymentRetoolApi().updateOfferTemplate(saveOfferTemplateRequestDto, user);
        return this;
    }

    public RetoolFlow setOfferApproval(String... code) {
        retoolApiManager.paymentRetoolApi()
            .setOfferApproval(OfferApprovalRequestDto.builder().offerCodes(List.of(code)).build(), user);
        return this;
    }

    public RetoolFlow setOfferActiveStatus(String code, boolean inactive) {
        retoolApiManager.paymentRetoolApi()
            .setOfferActiveStatus(SetOfferActiveStatusRequestDto.builder()
                .offerCode(code)
                .inactive(inactive).build(), user);
        return this;
    }

    public RetoolFlow confirmUserEmailWithScMode() {
        Response<GetAccountInfoResponseDto> response = crmFlow(user)
            .signInManual()
            .getAccountInfo()
            .getLastResponse();

        var accountRetool = retoolApiManager.accountRetoolApi();

        accountRetool.confirmUserEmailWithMode(response.body(), ForcementModeSpec.SWEEPSTAKE);
        return this;
    }

    public RetoolFlow confirmUserEmailWithGcMode() {
        Response<GetAccountInfoResponseDto> response = crmFlow(user)
            .signInManual()
            .getAccountInfo()
            .getLastResponse();

        var accountRetool = retoolApiManager.accountRetoolApi();

        accountRetool.confirmUserEmailWithMode(response.body(), ForcementModeSpec.GOLD);
        return this;
    }
}