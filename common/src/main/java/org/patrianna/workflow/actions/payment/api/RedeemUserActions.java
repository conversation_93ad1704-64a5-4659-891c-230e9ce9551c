package org.patrianna.workflow.actions.payment.api;

import static org.patrianna.core.service.brand.BrandApi.brandApi;
import static org.patrianna.core.service.payment.component.PaymentApiManager.paymentApiManager;
import static org.patrianna.core.service.retool.RetoolApiManager.retoolApiManager;
import static org.patrianna.ui.factory.PortalActions.homeActions;
import static org.patrianna.workflow.assertions.ui.factory.AssertionFactoryAbstract.assertThat;
import static org.springframework.beans.support.PagedListHolder.DEFAULT_PAGE_SIZE;

import admin.models.UpdateRedeemMoneyStatusRequest.RedeemStatusAction;
import admin.models.risk.status.RedeemRiskStatusAction;
import api.v1.PlatformSpec;
import fe.api.payment.model.Currency;
import fe.api.payment.model.RedeemItem;
import fe.api.payment.model.RedeemStatus;
import fe.api.payment.model.RedeemUserStatus;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.SneakyThrows;
import org.patrianna.common.data.dto.brandconfig.UserCredentialsDto;
import org.patrianna.core.service.payment.component.dto.redeem.GetRedeemsPageRequestDto;
import org.patrianna.core.service.payment.component.dto.redeem.GetRedeemsRequestDto;
import org.patrianna.core.service.payment.component.dto.redeem.GetRedeemsResponseDto;
import org.patrianna.core.service.payment.component.dto.redeem.GetRedeemsResponseDto.PaginationDto;
import org.patrianna.core.service.payment.component.dto.redeem.GetRedeemsResponseDto.RedeemItemDto;
import org.patrianna.core.service.payment.component.endpoints.RedeemsApi;
import org.patrianna.utils.common.DateUtil;
import org.patrianna.utils.common.UserData;
import payment.model.RedeemStatusSpec;
import payment.type.RedeemProviderSpec;
import retrofit2.Response;

public class RedeemUserActions {
    private final String email;

    public RedeemUserActions(UserData user) {
        this.email = user.getEmail();
    }

    public RedeemUserActions(UserCredentialsDto user) {
        this.email = user.getEmail();
    }

    public RedeemUserActions lockRedeemAndCheckSuccess(String redeemId) {
        List<Long> redeemIds = List.of(Long.parseLong(redeemId));
        approveRedeem(redeemId,  brandApi().crmApi().getAccountId());
        var response = retoolApiManager().paymentRetoolApi().updateRedeemMoneyStatus(
            email,
            redeemIds,
            RedeemStatusSpec.PRE_AUTHORIZED,
            RedeemStatusAction.LOCK
        ).body();

        assertThat(response)
            .hasValidProcessedIds(redeemIds)
            .hasEmptyProcessedIdsList();
        return this;
    }

    public RedeemUserActions confirmRedeemAndCheckSuccess(String redeemId) {
        List<Long> redeemIds = List.of(Long.parseLong(redeemId));
        var response = retoolApiManager().paymentRetoolApi().updateRedeemMoneyStatus(
            email,
            redeemIds,
            RedeemStatusSpec.LOCKED,
            RedeemStatusAction.CONFIRM
        ).body();

        assertThat(response)
            .hasValidProcessedIds(redeemIds)
            .hasEmptyProcessedIdsList();
        return this;
    }

    public RedeemUserActions unlockRedeemAndCheckSuccess(String redeemId) {
        List<Long> redeemIds = List.of(Long.parseLong(redeemId));
        var response = retoolApiManager().paymentRetoolApi().updateRedeemMoneyStatus(
            email,
            redeemIds,
            RedeemStatusSpec.LOCKED,
            RedeemStatusAction.UNLOCK
        ).body();

        assertThat(response)
            .hasValidProcessedIds(redeemIds)
            .hasEmptyProcessedIdsList();
        return this;
    }

    public RedeemUserActions declineRedeemAndCheckSuccess(String redeemId) {
        List<Long> redeemIds = List.of(Long.parseLong(redeemId));
        var response = retoolApiManager().paymentRetoolApi().updateRedeemMoneyStatus(
            email,
            redeemIds,
            RedeemStatusSpec.LOCKED,
            RedeemStatusAction.DECLINE
        ).body();

        assertThat(response)
            .hasValidProcessedIds(redeemIds)
            .hasEmptyProcessedIdsList();
        return this;
    }

    public void confirmRedeemWithManualMassConfirm(String redeemId) {
        manualMassConfirmRedeem(Long.parseLong(redeemId));
    }

    public RedeemUserActions approveRedeem(String redeemId, Long accountId) {
        retoolApiManager().paymentRetoolApi()
            .updateRedeemMoneyRiskStatus(email, accountId, List.of(Long.parseLong(redeemId)),
                RedeemRiskStatusAction.IN_REVIEW);
        retoolApiManager().paymentRetoolApi()
            .updateRedeemMoneyRiskStatus(email, accountId, List.of(Long.parseLong(redeemId)),
                RedeemRiskStatusAction.APPROVE);
        return this;
    }

    public RedeemUserActions manualMassConfirmRedeem(Long redeemId) {
        retoolApiManager().paymentRetoolApi().manualMassConfirmRedeem(
            email,
            List.of(redeemId)
        );

        return this;
    }

    public RedeemUserActions updateStandardAchProviderAndCheckSuccess(Long redeemId, RedeemProviderSpec provider) {
        List<Long> redeemIds = List.of(redeemId);
        var response = retoolApiManager().paymentRetoolApi().updateStandardAchProviderRequest(
            email,
            List.of(redeemId),
            provider
        ).body();

        assertThat(response)
            .isSuccess()
            .hasValidProcessedIds(redeemIds);

        return this;
    }

    @SneakyThrows
    public RedeemItem mapRedeemItem(GetRedeemsResponseDto responseDto) {
        RedeemItem redeemItem = null;

        if (responseDto != null && !responseDto.getRedeems().isEmpty()) {
            var dto = responseDto.getRedeems().getFirst();
            redeemItem = mapRedeemItemDto(dto);
        }

        return redeemItem;
    }

    @SneakyThrows
    public RedeemItem mapRedeemItem(RedeemItemDto responseDto) {
        RedeemItem redeemItem = null;

        if (responseDto != null) {
            redeemItem = mapRedeemItemDto(responseDto);
        }

        return redeemItem;
    }

    public RedeemItemDto getRedeemItemFromResponse(Response<GetRedeemsResponseDto> response,
        Integer redeemId) {
        return response.body() != null && response.body().getRedeems() != null
            ? response
            .body()
            .getRedeems()
            .stream()
            .filter(redeem -> redeem.getId().toString().equals(redeemId.toString()))
            .findFirst()
            .orElse(new RedeemItemDto())
            : new RedeemItemDto();
    }

    public Response<GetRedeemsResponseDto> getRedeemHistory() {
        RedeemsApi redeemsApi = paymentApiManager().redeemsApi();
        List<RedeemItemDto> allRedeems = new ArrayList<>();

        int pageSize = DEFAULT_PAGE_SIZE;
        int totalPages = getTotalPages(redeemsApi, pageSize);

        for (int page = 1; page <= totalPages; page++) {
            Response<GetRedeemsResponseDto> pageResponse = fetchPage(redeemsApi, page, pageSize);
            if (pageResponse.body() != null && pageResponse.body().getRedeems() != null) {
                allRedeems.addAll(pageResponse.body().getRedeems());
            }
        }

        GetRedeemsResponseDto fullHistory = GetRedeemsResponseDto.builder()
            .redeems(allRedeems)
            .build();

        return Response.success(fullHistory);
    }

    public void performRedeemProcess(UserData user, int redeemAmount) {
        retoolApiManager()
            .accountRetoolApi()
            .setAccountGrantRewardsSC(user, BigDecimal.valueOf(redeemAmount));

        makeRedeemWithStandardAch(redeemAmount);
    }

    public void makeRedeemWithStandardAch(int redeemAmount) {
        homeActions()
            .openHomePage()
            .navigateToRedeems()
            .clickOnRedeemAsCash()
            .redemptionWithStandardAch(String.valueOf(redeemAmount));
    }

    private int getTotalPages(RedeemsApi api, int pageSize) {
        Response<GetRedeemsResponseDto> firstResponse = fetchPage(api, 1, pageSize);

        return Optional.ofNullable(firstResponse.body())
            .map(GetRedeemsResponseDto::getPagination)
            .map(PaginationDto::getTotalPages)
            .orElse(1);
    }

    private RedeemItem mapRedeemItemDto(RedeemItemDto redeemItemDto) {
        return RedeemItem.builder()
            .amount(redeemItemDto.getAmount())
            .createdAt(DateUtil.convertToDateIfNotNull(redeemItemDto.getCreatedAt()))
            .lockedAt(DateUtil.convertToDateIfNotNull(redeemItemDto.getLockedAt()))
            .preConfirmedAt(DateUtil.convertToDateIfNotNull(redeemItemDto.getPreConfirmedAt()))
            .currency(Currency.USD)
            .id(redeemItemDto.getId())
            .provider(redeemItemDto.getProvider())
            .status(RedeemStatus.fromString(redeemItemDto.getStatus()))
            .redeemStatus(RedeemUserStatus.fromString(redeemItemDto.getRedeemStatus().code()))
            .paymentDetails(redeemItemDto.getPaymentDetails())
            .build();
    }

    private Response<GetRedeemsResponseDto> fetchPage(RedeemsApi api, int page, int size) {
        GetRedeemsPageRequestDto pageRequest = GetRedeemsPageRequestDto.builder()
            .pageNumber(page)
            .pageSize(size)
            .build();

        GetRedeemsRequestDto requestDto = GetRedeemsRequestDto.builder()
            .pageRequest(pageRequest)
            .build();

        Response<GetRedeemsResponseDto> response = api.getRedeems(PlatformSpec.WEB, requestDto);

        if (response == null || !response.isSuccessful() || response.body() == null) {
            throw new IllegalStateException("Failed to fetch redeem history on page " + page);
        }

        return response;
    }
}
