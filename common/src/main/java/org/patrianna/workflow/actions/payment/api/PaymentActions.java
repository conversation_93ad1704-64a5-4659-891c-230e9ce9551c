package org.patrianna.workflow.actions.payment.api;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.patrianna.common.enums.ErrorMessage.OFFER_NOT_FOUND;
import static org.patrianna.core.service.payment.admin.PaymentRetoolApiClient.retoolApi;
import static org.patrianna.utils.api.dbmanager.DatabaseManager.TEST_BRAND_CODE;
import static org.patrianna.utils.api.dbmanager.DatabaseManager.dbManager;
import static org.patrianna.workflow.actions.payment.api.OrderAction.orderAction;
import static org.patrianna.workflow.actions.payment.api.ProviderSetup.updateProviderRequest;
import static org.patrianna.workflow.flow.payment.PaymentIntegrationFlow.paymentFlow;

import api.v1.AccountStatusSpec;
import api.v1.KYCStatusSpec;
import bots.payment.ResponseWrapper;
import com.epam.reportportal.annotations.Step;
import com.google.common.net.HttpHeaders;
import com.spreedly.sdk.gateway.SpreedlyPurchaseService;
import com.spreedly.sdk.gateway.model.RootSpreedlyTransactionResponse;
import com.spreedly.sdk.gateway.model.SpreedlyPaymentMethod;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ups.PlainServiceInfo;
import common.CoreConstraints;
import fe.api.payment.model.BillingAddress;
import fe.api.payment.model.Capture3DsPaymentOrderRequestBody;
import fe.api.payment.model.Capture3DsPaymentOrderResponseBody;
import fe.api.payment.model.CreateOrderRequestBody;
import fe.api.payment.model.CreateOrderResponseBody;
import fe.api.payment.model.GetInboxNotificationsResponseBody;
import fe.api.payment.model.GetOffersResponseBody;
import fe.api.payment.model.GetPaymentMetaInfoRequestBody;
import fe.api.payment.model.GetPaymentMetaInfoResponseBody;
import fe.api.payment.model.OfferInfo;
import fe.api.payment.model.SpreedlyScaAuthenticateRequest;
import fe.api.payment.model.UpdateInboxNotificationRequestBody.StatusEnum;
import fraud.worker.FraudWorkerMasterEbeanJpaManager;
import io.github.resilience4j.retry.Retry;
import it.DefaultBootRuleContext;
import it.EmbeddedCompositeBot;
import it.EmbeddedPaymentBot;
import it.MinimalCrmBootRule;
import it.MinimalFraudBootRule;
import it.MinimalPaymentBootRule;
import it.PaymentBootRuleContext;
import it.PaymentTestDataOperations;
import it.mock.MockChallengeScaProvider;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.Callable;
import lombok.SneakyThrows;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.jboss.resteasy.util.BasicAuthHelper;
import org.patrianna.common.enums.ErrorCode;
import org.patrianna.common.enums.OfferTypeSpec;
import org.patrianna.common.enums.payment.PaymentProvider;
import org.patrianna.common.exceptions.payment.CreateOrderRequestException;
import org.patrianna.core.parameters.payment.CreateOrderRequestParams;
import org.patrianna.core.service.payment.admin.endpoints.SetAccount3dsActionClient;
import org.patrianna.dataproviders.testdata.payment.CardService;
import org.patrianna.dataproviders.testdata.payment.PaymentProviderUtils;
import org.patrianna.utils.api.retry.RetryService.RetryServiceBuilder;
import org.patrianna.workflow.actions.payment.api.routing.RoutingActions;
import payment.PaymentEbeanJpaManager;
import payment.PaymentProto;
import payment.PaymentTopics;
import payment.card.RoutingProviderCardToken;
import payment.context.order.SimpleGenericPaymentContext;
import payment.model.BlackList;
import payment.model.CardBinInfo;
import payment.model.immutable.ImmutableBrand;
import payment.services.sca.ScaProviders;
import payment.spreedly.SpreedlyThreeDsContext;
import payment.type.BlackListTypeSpec;
import payment.type.PurchaseProviderSpec;
import uam.UamEbeanJpaManager;


public class PaymentActions {

    private static final int HTTP_STATUS_OK = 200;
    private static final int TEN_SECONDS = 10;
    public static final long ID = 45L;
    public static final int CARD_BIN_LENGTH = 8;
    private static final String BROWSER_INFO = "eyJ3aWR0aCI6MjU2MCwiaGVpZ2h0IjoxNDQwLCJkZXB0aCI6MjQsInRpbWV6b25lIjotM"
        + "TIwLCJ1c2VyX2FnZW50IjoiTW96aWxsYS81LjAgKE1hY2ludG9zaDsgSW50ZWwgTWFjIE9TIFggMTBfMTVfNykgQXBwbGVXZWJLaXQvNTM"
        + "3LjM2IChLSFRNTCwgbGlrZSBHZWNrbykgQ2hyb21lLzEyMS4wLjAuMCBTYWZhcmkvNTM3LjM2IiwiamF2YSI6ZmFsc2UsImxhbmd1YWdlI"
        + "joiZW4tVVMiLCJicm93c2VyX3NpemUiOiIwNSIsImFjY2VwdF9oZWFkZXIiOiJ0ZXh0L2h0bWwsYXBwbGljYXRpb24veGh0bWwreG1sO3E"
        + "9MC45LCovKjtxPTAuOCJ9";
    private final EmbeddedPaymentBot paymentBot;
    private final EmbeddedCompositeBot compositeBot;

    public PaymentActions(EmbeddedCompositeBot compositeBot) {
        this.compositeBot = compositeBot;
        this.paymentBot = compositeBot.getPaymentBot();
    }

    public RedeemActions redeemActions() {
        return new RedeemActions(compositeBot);
    }

    public RoutingActions routingActions(PaymentBootRuleContext paymentCtx) {
        return new RoutingActions(paymentCtx);
    }

    @Step("Get offer by code")
    public OfferInfo getOfferByCode(String offerCode) throws Exception {
        return paymentBot.getOffers().getOffers().stream()
            .filter(o -> o.getCode().equals(offerCode)).findAny()
            .orElseThrow(() -> new AssertionError(OFFER_NOT_FOUND.getMessage()));
    }

    @Step("Get offer by index")
    public OfferInfo getOfferByIndex(int index) throws Exception {
        return paymentBot.getOffers().getOffers().get(index);
    }

    @Step("Retrieve offer information for user")
    public GetOffersResponseBody sendGetOffersRequest() throws Exception {
        return paymentBot.getOffers();
    }

    @SneakyThrows
    @Step("Retrieve inbox notifications information for user")
    public GetInboxNotificationsResponseBody sendInboxNotificationsRequest() {
        return paymentBot.getInboxNotifications();
    }

    @SneakyThrows
    @Step("Update offer information for inbox notifications information for user")
    public void updateInboxNotificationsRequest(UUID token, StatusEnum status) {
        paymentBot.updateInboxNotification(token, status);
    }

    @Step("Tokenize card using Spreedly purchase service")
    public String tokenizeCard(PaymentBootRuleContext payment, SpreedlyPaymentMethod method) {
        var ps = payment.getPaymentServer().getCtx().getBean(SpreedlyPurchaseService.class);
        try (var httpResp = ps.addPaymentMethod(method,
            new SimpleGenericPaymentContext(null, null, null),
            PaymentProto.UPS_SPREEDLY)) {
            httpResp.bufferEntity();
            return httpResp.readEntity(RootSpreedlyTransactionResponse.class).getTransaction().getPaymentMethod()
                .getToken();
        }
    }

    @Step("Connect to payment bot")
    public PaymentActions connect() throws Exception {
        paymentBot.connect();
        return this;
    }

    @Step("Find offer by generated code")
    public OfferInfo findOfferByCode(PaymentTestDataOperations paymentUtils) throws Exception {
        String code = paymentFlow().offerSetup(paymentUtils).generateDefaultOffer();
        return paymentBot.getOffers().getOffers().stream().filter(o -> o.getCode().equals(code))
            .findAny().orElseThrow(() -> new AssertionError(OFFER_NOT_FOUND.getMessage()));
    }

    @Step("Find offer by generated code")
    public OfferInfo generateOfferWithType(PaymentTestDataOperations paymentUtils, OfferTypeSpec offerTypeSpec)
        throws Exception {
        String code = paymentFlow().offerSetup(paymentUtils).generateOfferWithType(offerTypeSpec);
        return paymentBot.getOffers().getOffers().stream().filter(o -> o.getCode().equals(code))
            .findAny().orElseThrow(() -> new AssertionError(OFFER_NOT_FOUND.getMessage()));
    }

    @SneakyThrows
    @Step("Find generated offer with capacity per player")
    public OfferInfo generateOfferWithTypeAndCapacity(PaymentTestDataOperations paymentUtils,
        OfferTypeSpec offerTypeSpec, Integer capacityPerPlayer) {
        String code = paymentFlow()
            .offerSetup(paymentUtils)
            .generateOfferWithCapacityPerPlayer(offerTypeSpec, capacityPerPlayer);
        return paymentBot
            .getOffers()
            .getOffers()
            .stream()
            .filter(offer -> offer.getCode().equals(code))
            .findAny()
            .orElseThrow(() -> new AssertionError(OFFER_NOT_FOUND.getMessage()));
    }

    @Step("Find offer by generated code")
    public OfferInfo generateOfferWithPrice(PaymentTestDataOperations paymentUtils, BigDecimal offerPrice)
        throws Exception {
        String code = paymentFlow().offerSetup(paymentUtils).generateOffer(offerPrice);
        return paymentBot.getOffers().getOffers().stream().filter(o -> o.getCode().equals(code))
            .findAny().orElseThrow(() -> new AssertionError(OFFER_NOT_FOUND));
    }

    @Step("Generate offer with unique code and upgrade code")
    public OfferInfo generateOfferWithUpgradeOfferCode(PaymentTestDataOperations paymentUtils,
        OfferTypeSpec offerTypeSpec, String code, String upgradeCode) throws Exception {
        String offerCode = paymentFlow()
            .offerSetup(paymentUtils)
            .generateOfferWithUpgrade(offerTypeSpec, code, upgradeCode);
        return paymentBot
            .getOffers()
            .getOffers()
            .stream()
            .filter(offer -> offer.getCode().equals(offerCode))
            .findAny()
            .orElseThrow(() -> new AssertionError(OFFER_NOT_FOUND));
    }

    @Step("Make gold purchase with notification")
    public CreateOrderResponseBody makeGoldPurchaseWithNtf(OfferInfo offerInfo) throws Exception {
        ResponseWrapper<CreateOrderResponseBody> resp = paymentBot.createOrder(offerInfo.getCode());
        resp.assertNtf();
        return resp.getResp();
    }

    @Step("Make gold purchase")
    public ResponseWrapper<CreateOrderResponseBody> makeGoldPurchase(String offerCode) throws Exception {
        return paymentBot.createOrder(offerCode);
    }

    @Step("Fiat deposit")
    public void fiatDeposit(BigDecimal amount) throws Exception {
        amount = amount.setScale(CoreConstraints.MONEY_SCALE, RoundingMode.DOWN);
        paymentBot.deposit(amount, CreateOrderRequestBody.builder()).assertNtf();
    }

    @Step("Send create order request with billing address")
    public ResponseWrapper<CreateOrderResponseBody> sendCreateOrderRequestWithBillingAddress(
        CreateOrderRequestParams params) throws Exception {
        return paymentBot.send(buildCreateOrderBillingAddressCvv(params), CreateOrderResponseBody.class);
    }

    @Step("Send create order request and return response for further validation")
    public ResponseWrapper<CreateOrderResponseBody> sendCreateOrderWithProvider(
        String offerCode, String cardToken, PaymentProvider provider) throws Exception {
        var transactionId = PlatformUtil.randomUUID();
        var params = createOrderParams(offerCode, cardToken, transactionId, provider.getCode());
        CreateOrderRequestBody requestBody = buildCreateOrderRequestBody(params);
        return paymentBot.send(requestBody, CreateOrderResponseBody.class);
    }

    @Step("Send create order request and return response for further validation")
    public ResponseWrapper<CreateOrderResponseBody> sendCreateOrderWithProvider(
        String offerCode, String cardToken, UUID transactionId, PaymentProvider provider) throws Exception {
        var params = createOrderParams(offerCode, cardToken, transactionId, provider.getCode());
        CreateOrderRequestBody requestBody = buildCreateOrderRequestBody(params);
        return paymentBot.send(requestBody, CreateOrderResponseBody.class);
    }

    @Step("Send create order request and return response for further validation")
    public ResponseWrapper<CreateOrderResponseBody> sendCreateOrderRequest(CreateOrderRequestParams params)
        throws Exception {
        CreateOrderRequestBody requestBody = buildCreateOrderRequestBody(params);
        return paymentBot.send(requestBody, CreateOrderResponseBody.class);
    }

    @Step("Send create order request with custom transaction ID")
    public ResponseWrapper<CreateOrderResponseBody> sendCreateOrderRequest(String offerCode, String cardToken,
        UUID transactionId) throws Exception {
        OrderAction orderAction = orderAction(offerCode, cardToken, transactionId);
        CreateOrderRequestParams params = orderAction.createOrderWithCvvParams();
        CreateOrderRequestBody requestBody = buildCreateOrderRequestBody(params);
        return paymentBot.send(requestBody, CreateOrderResponseBody.class);
    }

    @Step("Send basic create order request")
    public ResponseWrapper<CreateOrderResponseBody> sendCreateOrderRequest(String offerCode, String cardToken)
        throws Exception {
        var transactionId = PlatformUtil.randomUUID();
        return sendCreateOrderRequest(offerCode, cardToken, transactionId);
    }

    @Step("Send create order request with retry mechanism")
    public ResponseWrapper<CreateOrderResponseBody> sendCreateOrderRequestWithRetry(String offerCode, String cardToken)
        throws Exception {

        Callable<ResponseWrapper<CreateOrderResponseBody>> retryableSupplier = () -> {
            UUID transactionId = UUID.randomUUID();
            return sendCreateOrderRequest(offerCode, cardToken, transactionId);
        };

        Retry retry = new RetryServiceBuilder<ResponseWrapper<CreateOrderResponseBody>>()
            .withName("sendCreateOrderRetry")
            .withWaitDuration(Duration.ofSeconds(TEN_SECONDS))
            .retryOnResult(response -> Objects.equals(response.getErrorCode(), ErrorCode.ERR_TIMEOUT.getCode()))
            .build();

        Callable<ResponseWrapper<CreateOrderResponseBody>> decoratedSupplier = Retry.decorateCallable(retry,
            retryableSupplier);

        try {
            return decoratedSupplier.call();
        } catch (Exception e) {
            throw new CreateOrderRequestException("Failed to send create order request after retries.", e);
        }
    }

    @Step("Send create order request without CVV entered")
    public ResponseWrapper<CreateOrderResponseBody> sendCreateOrderRequestWithoutCvv(String offerCode, String cardToken,
        UUID transactionId) throws Exception {
        OrderAction orderAction = orderAction(offerCode, cardToken, transactionId);
        CreateOrderRequestParams params = orderAction.createOrderWithoutCvvParameters();
        CreateOrderRequestBody requestBody = buildCreateOrderRequestBody(params);
        return paymentBot.send(requestBody, CreateOrderResponseBody.class);
    }

    @Step("Send create order request without CVV entered")
    public ResponseWrapper<CreateOrderResponseBody> sendCreateOrderRequestWithoutCvv(String offerCode,
        String cardToken) throws Exception {
        UUID transactionId = UUID.randomUUID();
        return sendCreateOrderRequestWithoutCvv(offerCode, cardToken, transactionId);
    }

    @Step("Send create order request and return response for further validation")
    public ResponseWrapper<CreateOrderResponseBody> sendOrderRequestWithBrowserInfo(CreateOrderRequestParams params)
        throws Exception {
        CreateOrderRequestBody requestBody = buildCreateOrderRequestBody(params)
            .scaAuthenticateData(SpreedlyScaAuthenticateRequest.builder()
                .browserInfo(BROWSER_INFO)
                .build());
        return paymentBot.send(requestBody, CreateOrderResponseBody.class);
    }

    @Step("Complete 3Ds challenge by sending a request to Spreedly")
    public PaymentActions complete3DsChallenge(String txId, boolean success) throws IOException {
        PlainServiceInfo serviceInfo = PaymentProviderUtils.SPREEDLY;
        String authHeader = BasicAuthHelper.createHeader(serviceInfo.getUserName(), serviceInfo.getPassword());

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(String.format("%s://%s", serviceInfo.getScheme(), serviceInfo.getHost())
                + "/transaction/" + txId + "/three_ds_automated_complete");

            httpPost.addHeader("Content-Type", "multipart/form-data");
            httpPost.addHeader(HttpHeaders.AUTHORIZATION, authHeader);

            var result = success ? List.of(
                new BasicNameValuePair("auth_result", "succeeded"),
                new BasicNameValuePair("commit", "Allow")
            ) : List.of(
                new BasicNameValuePair("auth_result", "failed"),
                new BasicNameValuePair("commit", "Deny")
            );

            httpPost.setEntity(new UrlEncodedFormEntity(result));
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                verifyResponseStatus(response);
                return this;
            }
        }
    }

    @Step("Send create order request and return response for further validation")
    public ResponseWrapper<Capture3DsPaymentOrderResponseBody> capture3DsRequest(EmbeddedCompositeBot compositeBot,
        UUID transactionId) throws Exception {
        Capture3DsPaymentOrderRequestBody requestBody = Capture3DsPaymentOrderRequestBody.builder()
            .orderTransactionId(transactionId)
            .cvvEntered(true)
            .session("")
            .build();

        return compositeBot.getPaymentBot().send(requestBody, Capture3DsPaymentOrderResponseBody.class);
    }

    @Step("Generate {purchaseCount} purchases via SKRILL payment provider")
    public PaymentActions generatePurchases(int purchaseCount, String offerCode) throws Exception {
        for (int i = 0; i < purchaseCount; i++) {
            var order = paymentBot.createOrder(offerCode, b -> b.provider(PaymentProvider.SKRILL.name())).getResp();
            paymentBot.confirmOrder(order.getTransactionId(), PlatformUtil.randomUUID().toString(),
                PaymentProvider.SKRILL.name());
        }
        return this;
    }

    @Step("Generate purchase via SKRILL payment provider")
    @SneakyThrows
    public UUID generatePurchase(String offerCode) {
        var order = paymentBot.createOrder(offerCode, b -> b.provider(PaymentProvider.SKRILL.name())).getResp();
        paymentBot.confirmOrder(order.getTransactionId(), PlatformUtil.randomUUID().toString(),
            PaymentProvider.SKRILL.name());
        return order.getTransactionId();
    }

    @SneakyThrows
    @Step("Set secure 3D action to FORCE")
    public PaymentActions add3dsSettings(MinimalPaymentBootRule rule, EmbeddedCompositeBot bot) {
        try (SetAccount3dsActionClient client = retoolApi(rule).setAccount3dsAction()) {
            client.setAccount3dsActionForce(bot);
        }
        return this;
    }

    @Step("Create mocked order request")
    public ResponseWrapper<CreateOrderResponseBody> createMockedOrderRequest(String code,
        ScaProviders<SpreedlyThreeDsContext> scaProviders) throws Exception {
        doReturn(new MockChallengeScaProvider()).when(scaProviders).resolveProvider(any());

        return paymentBot.createOrder(
            code,
            b -> b.token("any")
                .provider(PurchaseProviderSpec.SPREEDLY.name().toLowerCase())
                .scaAuthenticateData(SpreedlyScaAuthenticateRequest.builder()
                    .browserInfo("browserInfo")
                    .build())
        );
    }

    @Step("Send GetPaymentMetaInfoRequest and get player purchase/redeem activity")
    @SneakyThrows
    public GetPaymentMetaInfoResponseBody sendGetPaymentMetaInfoRequest() {
        return paymentBot.send(new GetPaymentMetaInfoRequestBody(), GetPaymentMetaInfoResponseBody.class).getResp();
    }

    public void updateProviderToInactiveStatus(DefaultBootRuleContext commonRule, String providerCode)
        throws Exception {
        commonRule.sendMessageToKafka(PaymentTopics.PAYMENT_REQ, updateProviderRequest(providerCode, Boolean.FALSE));
    }

    public void updateProviderToActiveStatus(DefaultBootRuleContext commonRule, String providerCode)
        throws Exception {
        commonRule.sendMessageToKafka(PaymentTopics.PAYMENT_REQ, updateProviderRequest(providerCode, Boolean.TRUE));
    }

    public CreateOrderRequestParams createOrderParams(String offerCode, String cardToken, UUID transactionId,
        String providerCode) {
        OrderAction orderAction = orderAction(offerCode, cardToken, transactionId);
        return orderAction.createOrderWithProviderParams(providerCode);
    }

    private void verifyResponseStatus(CloseableHttpResponse response) throws IOException {
        if (response.getStatusLine().getStatusCode() != HTTP_STATUS_OK) {
            throw new IOException(
                "Expected successful HTTP status code 200, but got " + response.getStatusLine().getStatusCode());
        }
    }

    private CreateOrderRequestBody buildCreateOrderRequestBody(CreateOrderRequestParams params) throws Exception {
        var offer = getOfferByCode(params.getOfferCode());
        var cardToken = params.getCardToken();
        var transactionId = params.getTransactionId();
        var cvvEntered = params.isCvvEntered();
        var provider =
            params.getProvider() != null ? params.getProvider() : PaymentProvider.SPREEDLY.name().toLowerCase();

        return CreateOrderRequestBody.builder()
            .offer(offer.getCode())
            .provider(provider)
            .token(cardToken)
            .cvvEntered(cvvEntered) // Use the parameter
            .transactionId(transactionId)
            .build();
    }

    private CreateOrderRequestBody buildCreateOrderBillingAddressCvv(CreateOrderRequestParams params) throws Exception {
        var offer = getOfferByCode(params.getOfferCode());
        var cardToken = params.getCardToken();
        var transactionId = params.getTransactionId();
        var response = compositeBot.getAccountInfo();
        return CreateOrderRequestBody.builder()
            .offer(offer.getCode())
            .provider(PaymentProvider.SPREEDLY.name().toLowerCase())
            .token(cardToken)
            .billingAddress(new BillingAddress()
                .stateOrProvince(response.location.state)
                .street(response.personalInfo.address)
                .dateOfBirth(response.personalInfo.birthDate)
                .firstName(response.firstName)
                .lastName(response.lastName)
                .postalCode(response.personalInfo.zip)
                .city(response.location.city)
                .country(response.country))
            .cvvEntered(true) // Use the parameter
            .transactionId(transactionId)
            .build();
    }

    @Step("Build black list")
    public BlackList buildBlackListForTheCard(BlackListTypeSpec type, String excludedValue) {
        var now = new Date();
        return BlackList.builder()
            .id(ID)
            .brand(new ImmutableBrand(TEST_BRAND_CODE))
            .country(CardService.SPREEDLY_4200.country)
            .type(type)
            .excludedValue(excludedValue)
            .inactive(false)
            .createdBy("admin")
            .createdAt(now)
            .modifiedAt(now)
            .build();
    }

    @Step("Build card bin info")
    public CardBinInfo buildCardBinInfo(String cardNumber) {
        return new CardBinInfo(
            cardNumber.substring(0, CARD_BIN_LENGTH),
            "CIAGROUP",
            "DEBIT",
            "PREPAID",
            "US",
            1,
            new Date()
        );
    }

    @Step("Update account status to default")
    @SneakyThrows
    public PaymentActions setAccountStatus(MinimalCrmBootRule crmBootRule, AccountStatusSpec status) {
        var crmEbean = crmBootRule.getCrmServer().getCtx().getBean(UamEbeanJpaManager.class);
        dbManager().uam(crmEbean).accountAction(compositeBot).setAccountStatus(status);

        return this;
    }

    @Step("Update account kyc to {kycStatus}")
    @SneakyThrows
    public void setAccountKycStatus(MinimalFraudBootRule fraudBootRule, KYCStatusSpec kycStatus) {
        var fraudWorkerJpaManager = fraudBootRule.getFraudWorker().getCtx()
            .getBean(FraudWorkerMasterEbeanJpaManager.class);
        dbManager().fraud(fraudWorkerJpaManager).accountManager(compositeBot).setAccountKyc(kycStatus);
    }

    @Step("Update exclude_in_same_tokenization for provider {provider} to {isExcluded}")
    @SneakyThrows
    public PaymentActions setExcludeInSameTokenization(
        MinimalPaymentBootRule paymentBootRule, PurchaseProviderSpec provider, Boolean isExcluded) {
        var paymentEbean = paymentBootRule.getPaymentServer().getCtx().getBean(PaymentEbeanJpaManager.class);
        dbManager().payment(paymentEbean).setExcludeInSameTokenization(provider.code(), isExcluded);

        return this;
    }

    @Step("Get exclude_in_same_tokenization for provider {provider}")
    @SneakyThrows
    public RoutingProviderCardToken getRoutingProviderCardToken(
        MinimalPaymentBootRule paymentBootRule, PurchaseProviderSpec provider) {
        var paymentEbean = paymentBootRule.getPaymentServer().getCtx().getBean(PaymentEbeanJpaManager.class);
        return dbManager().payment(paymentEbean).getRoutingProviderCardToken(provider.code());
    }
}
