package org.patrianna.ui.action.login;

import static com.microsoft.playwright.options.WaitUntilState.DOMCONTENTLOADED;
import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;
import static org.patrianna.ui.factory.PortalActions.homeActions;
import static org.patrianna.ui.web.page.login.LoginPageAbstract.DISABLE_CAPTCHA_URL;
import static org.patrianna.utils.ui.WaitsUtil.waitForLocator;
import static org.patrianna.utils.ui.WaitsUtil.waitUntil;
import static org.patrianna.workflow.flow.CrmFlow.crmFlow;
import static uam.CrmProto.PATH_SIGN_IN_MANUAL;

import com.microsoft.playwright.Page.NavigateOptions;
import com.microsoft.playwright.Request;
import com.microsoft.playwright.Route;
import lombok.Getter;
import org.patrianna.common.data.dto.brandconfig.UserCredentialsDto;
import org.patrianna.ui.action.home.HomeActionsAbstract;
import org.patrianna.ui.factory.PortalAbstract;
import org.patrianna.ui.web.page.game.GamePageAbstract;
import org.patrianna.ui.web.page.home.HomePageAbstract;
import org.patrianna.ui.web.page.locationverification.LocationVerificationPage;
import org.patrianna.ui.web.page.login.LoginPageAbstract;
import org.patrianna.ui.web.page.otp.OtpPageAbstract;
import org.patrianna.ui.web.page.profile.MyProfilePageAbstract;
import org.patrianna.ui.web.page.registration.RegistrationPageAbstract;
import org.patrianna.ui.web.page.resetpassword.ResetPasswordPageAbstract;
import org.patrianna.ui.web.page.unauthorized.home.UnauthorizedHomePageAbstract;
import org.patrianna.ui.web.page.unauthorized.landing.LandingPageAbstract;
import org.patrianna.utils.common.UserData;
import org.patrianna.utils.ui.NetworkUtil;

@Getter
public abstract class LoginActionsAbstract {

    // Increased timeout to accommodate potential delays in SEON response
    public static final long OTP_PAGE_LOAD_TIMEOUT = 60_000L;

    protected final PortalAbstract portal;

    protected LoginActionsAbstract(PortalAbstract portal) {
        this.portal = portal;
    }

    public LoginPageAbstract getLoginPage() {
        return portal.getLoginPage();
    }

    public UnauthorizedHomePageAbstract getUnauthorizedPage() {
        return portal.getUnauthorizedPage();
    }

    public LandingPageAbstract getLandingPage() {
        return portal.getLandingPage();
    }

    public HomePageAbstract getHomePage() {
        return portal.getHomePage();
    }

    public RegistrationPageAbstract getRegistrationPage() {
        return portal.getRegistrationPage();
    }

    public ResetPasswordPageAbstract getResetPasswordPage() {
        return portal.getResetPasswordPage();
    }

    public MyProfilePageAbstract getMyProfilePage() {
        return portal.getMyProfilePage();
    }

    public GamePageAbstract getGamePage() {
        return portal.getGamePage();
    }

    public LoginActionsAbstract openLoginPage() {
        getLoginPage().open().waitUntilNavigated(getLoginPage()).getRidOfModals();
        return this;
    }

    public LoginActionsAbstract openLandingPage() {
        getLandingPage().open().getRidOfModals();
        return this;
    }

    public HomeActionsAbstract loginAndWaitHomePage(String email, String password) {
        this.login(email, password);
        getLoginPage().waitUntilNavigated(getHomePage());
        getHomePage().getTermsOfServiceModal().agreeOnTermsOfUseIfPresent();
        return homeActions();
    }

    public HomeActionsAbstract loginAndWaitHomePage(UserData user) {
        return loginAndWaitHomePage(user.getEmail(), user.getPassword());
    }

    public HomeActionsAbstract loginAndWaitHomePage(UserCredentialsDto user) {
        return loginAndWaitHomePage(user.getEmail(), user.getPassword());
    }

    public OtpPageAbstract loginAndWaitOtp(UserCredentialsDto user) {
        return loginAndWaitOtp(user.getEmail(), user.getPassword());
    }

    public OtpPageAbstract loginAndWaitOtp(UserData user) {
        return loginAndWaitOtp(user.getEmail(), user.getPassword());
    }

    private OtpPageAbstract loginAndWaitOtp(String email, String password) {
        this.login(email, password);
        return getLoginPage().waitUntilNavigated(getOtpPage(), OTP_PAGE_LOAD_TIMEOUT);
    }

    public LocationVerificationPage loginAndWaitLocationVerification(UserCredentialsDto user) {
        return loginAndWaitLocationVerification(user.getEmail(), user.getPassword());
    }

    public LocationVerificationPage loginAndWaitLocationVerification(UserData user) {
        return loginAndWaitLocationVerification(user.getEmail(), user.getPassword());
    }

    private LocationVerificationPage loginAndWaitLocationVerification(String email, String password) {
        this.login(email, password);
        return getLoginPage().waitUntilNavigated(getLocationVerificationPage());
    }

    public Request getLoginRequest(String email, String password) {
        return NetworkUtil.getRequest(PATH_SIGN_IN_MANUAL,
            () -> this.loginWithReCaptcha(email, password));
    }

    public Request getLoginRequest(UserCredentialsDto user) {
        return getLoginRequest(user.getEmail(), user.getPassword());
    }

    public LoginActionsAbstract openResetPasswordPage(String url) {
        getPlaywrightPage().navigate(url, new NavigateOptions().setWaitUntil(DOMCONTENTLOADED));
        return this;
    }

    public LoginActionsAbstract resetPassword(String newPassword) {
        getResetPasswordPage()
            .fillNewPasswordField(newPassword)
            .fillConfirmPasswordField(newPassword)
            .clickChangePasswordButton();
        return this;
    }

    public LoginActionsAbstract fillUserData(String email, String password) {
        getLoginPage().fillEmail(email);
        getLoginPage().fillPassword(password);
        return this;
    }

    public LoginActionsAbstract forceLogin(String email, String password) {
        this.fillUserData(email, password);
        getLoginPage().forceClickLoginButton();
        return this;
    }

    public abstract LoginActionsAbstract registerWithEmail(UserData user);

    public abstract LoginActionsAbstract registerWithEmailWithoutNavigation(UserData user);

    public abstract LoginActionsAbstract performRegistration(UserData user);

    public abstract LoginActionsAbstract navigateToStateSelection(UserData user);

    public abstract LoginActionsAbstract navigateToRegistrationPage();

    public abstract LoginActionsAbstract registerWithEmailAndRestrictedState(UserData user);

    public abstract LoginActionsAbstract continueRegistrationWithRestrictedState(UserData user);

    public abstract LoginActionsAbstract fillEverythingExceptEmailField(UserData user);

    public abstract LoginActionsAbstract fillEmail(String email);

    public abstract LoginActionsAbstract fillEverythingExceptPasswordField(UserData user);

    public abstract LoginActionsAbstract fillPassword(String password);

    public abstract LoginActionsAbstract sendAgain();

    public LoginActionsAbstract reloadLoginPageWithDisableCaptcha() {
        return reloadLoginPageWithDisableCaptcha("&");
    }

    protected LoginActionsAbstract reloadLoginPageWithDisableCaptcha(String divider) {
        waitForLocator(getLoginPage().getEmailAddressInput());
        String currentUrl = getPlaywrightPage().url();
        String disableCaptchaUrl = "%s%s%s".formatted(currentUrl, divider, DISABLE_CAPTCHA_URL);
        getPlaywrightPage().navigate(disableCaptchaUrl);
        waitUntil(() -> getLoginPage().isFakeReCaptchaButtonVisible());
        return this;
    }

    /**
     * Blocks requests that match the given request name.
     *
     * <p>This method sets up a route handler that aborts network requests matching the specified request name.
     *
     * @param request the partial name or path of the request to block
     * @return the current {@code LoginActionsAbstract} instance for chaining
     */
    public LoginActionsAbstract blockRequest(String request) {
        String pattern = String.format("**/%s**", request);
        getPlaywrightPage().route(pattern, Route::abort);
        return this;
    }

    /**
     * Unblocks previously blocked requests that match the given request name.
     *
     * <p>Removes the route handler created by {@link #blockRequest(String)},
     * allowing the network request to proceed normally.
     *
     * @param request the partial name or path of the previously blocked request
     * @return the current {@code LoginActionsAbstract} instance for chaining
     */
    public LoginActionsAbstract unblockRequest(String request) {
        String pattern = String.format("**/%s**", request);
        getPlaywrightPage().unroute(pattern);
        return this;
    }

    public LoginActionsAbstract login(UserData user) {
        return login(user.getEmail(), user.getPassword());
    }

    public abstract LoginActionsAbstract login(String email, String password);

    public abstract LoginActionsAbstract login(UserCredentialsDto user);

    public abstract LoginActionsAbstract loginWithReCaptcha(String email, String password);

    public abstract OtpPageAbstract getOtpPage();

    public LocationVerificationPage getLocationVerificationPage() {
        return new LocationVerificationPage();
    }

    public void reTriggerGetAccountInfo(UserData user) {
        crmFlow(user.getEmail()).signInManual();
    }
}