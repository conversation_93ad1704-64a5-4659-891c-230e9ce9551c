package org.patrianna.ui.action.home;

import static org.patrianna.utils.ui.WaitsUtil.WAIT_3000_MS;
import static org.patrianna.utils.ui.WaitsUtil.waitTimeoutMS;

import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.Page;
import com.patrianna.aqa.ui.playwright.dto.device.DeviceDescriptorDto;
import org.patrianna.common.data.dto.ui.payment.Balance;
import org.patrianna.ui.factory.portal.B2SPortal;
import org.patrianna.utils.ui.SiteConfigUtil;

public class SportsmillionsHomeActions<P extends B2SPortal> extends B2SHomeActions {

    private final P sportsMillionsPortal;

    public SportsmillionsHomeActions(P portal) {
        super(portal);
        this.sportsMillionsPortal = portal;
    }

    @Override
    public B2SHomeActions clearModals() {
        sportsMillionsPortal.getHomePage().getEnableNotificationsModal().closeIfVisible();
        sportsMillionsPortal.getHomePage().getRidOfModals();
        return this;
    }

    @Override
    public void performGetCoinsButtonClicksDuringNavigation(Page currentPage, BrowserContext currentContext,
        DeviceDescriptorDto currentDeviceConfig) {
        if (SiteConfigUtil.isDesktop()) {
            super.performGetCoinsButtonClicksDuringNavigation(currentPage, currentContext, currentDeviceConfig);
        }
        // This step is not valid for SM mobile view
    }

    @Override
    public HomeActionsAbstract logout() {
        sportsMillionsPortal.getHomePage().navigationMenuItemsComponent()
            .clickLogoutButton();

        return this;
    }

    @Override
    public Balance getCurrentBalanceWithAnimationHandling() {
        sportsMillionsPortal.getHomePage().navigationMenuItemsComponent().clickMenuButton();
        waitTimeoutMS(WAIT_3000_MS); // Wait till animation for balance update is finished
        return getCurrentBalance();
    }
}
