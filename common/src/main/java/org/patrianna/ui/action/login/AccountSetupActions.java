package org.patrianna.ui.action.login;

import static org.patrianna.common.enums.LegalRulesEnum.PRIVACY_POLICY;
import static org.patrianna.common.enums.LegalRulesEnum.SWEEPSTAKE_RULES;
import static org.patrianna.common.enums.LegalRulesEnum.TERMS_OF_CONDITION;
import static org.patrianna.core.service.brand.CrmSessionManager.crmSessionManager;
import static org.patrianna.utils.common.StringUtil.generateValidUsPhoneNumber;

import api.v1.AccountStatusSpec;
import java.math.BigDecimal;
import java.util.List;
import lombok.Getter;
import org.patrianna.common.config.TestUsers;
import org.patrianna.common.data.dto.brandconfig.UserCredentialsDto;
import org.patrianna.common.data.dto.crm.accountinfo.GetAccountInfoResponseDto;
import org.patrianna.core.service.mailinator.MailinatorService;
import org.patrianna.core.service.retool.RetoolApiManager;
import org.patrianna.utils.common.UserData;
import org.patrianna.workflow.flow.CrmFlow;
import org.patrianna.workflow.flow.RetoolFlow;
import payment.model.Secure3dAction;
import retrofit2.Response;

public class AccountSetupActions {

    public static final Integer REDEEMABLE_SWEEPSTAKES_AMOUNT = 1000;

    public static AccountSetupActions accountSetupActions() {
        return new AccountSetupActions();
    }

    public void loginWithMainUser() {
        var user = TestUsers.getMainUser();
        crmSessionManager(user).signInManualWithCookiesSession();
    }

    public void loginWithPaymentUser() {
        var user = TestUsers.getPaymentUser();
        crmSessionManager(user).signInManualWithCookiesSession();
    }

    public static class AccountSetupBuilder {

        private final RetoolApiManager retoolApiManager;
        private final UserData user;
        private final CrmFlow crmFlow;
        private final RetoolFlow retoolFlow;
        @Getter
        private Response<GetAccountInfoResponseDto> accountInfoResponse;

        public AccountSetupBuilder(UserData user) {
            this.user = user;
            this.crmFlow = CrmFlow.crmFlow(user);
            this.retoolFlow = RetoolFlow.retoolFlow(user);
            this.retoolApiManager = RetoolApiManager.retoolApiManager();
        }

        public static AccountSetupBuilder accountSetup(UserData user) {
            return new AccountSetupBuilder(user);
        }

        public static AccountSetupBuilder accountSetup(UserCredentialsDto userCredentialsDto) {
            var mappedUser = UserData.builder()
                .email(userCredentialsDto.getEmail())
                .password(userCredentialsDto.getPassword()).build();

            return new AccountSetupBuilder(mappedUser);
        }

        public AccountSetupBuilder signUp() {
            crmSessionManager(user).signUpManualWithCookieSession();
            return this;
        }

        public AccountSetupBuilder withAcceptedRules() {
            crmFlow.getCrmApi().acceptRules(List.of(
                SWEEPSTAKE_RULES.getRule(),
                TERMS_OF_CONDITION.getRule(),
                PRIVACY_POLICY.getRule()
            ));
            return this;
        }

        public AccountSetupBuilder withConfirmedEmail(MailinatorService mailinatorService) {
            crmFlow.confirmEmail(mailinatorService);
            return this;
        }

        public AccountSetupBuilder withScModeConfirmedEmail() {
            retoolFlow.confirmUserEmailWithScMode();
            return this;
        }

        public AccountSetupBuilder withGcModeConfirmedEmail() {
            retoolFlow.confirmUserEmailWithGcMode();
            return this;
        }

        public AccountSetupBuilder withAccountInfo() {
            accountInfoResponse = crmFlow.setAccountInfo().getLastResponse();
            return this;
        }

        public AccountSetupBuilder withAdminRights() {
            if (accountInfoResponse == null || accountInfoResponse.body() == null) {
                throw new IllegalStateException("The account information is missing. "
                    + "Please ensure that 'withAccountInfo()' is called before 'withAdminRights()'.");
            }
            retoolApiManager.accountRetoolApi().setUserAsAdmin(accountInfoResponse.body());
            return this;
        }

        public void withGrantRewards(BigDecimal amount) {
            retoolApiManager.accountRetoolApi().setAccountGrantRewards(user, amount, BigDecimal.ZERO);
        }

        public void withGrantRewards(BigDecimal amount, BigDecimal fiatAmount) {
            retoolApiManager.accountRetoolApi().setAccountGrantRewards(user, amount, fiatAmount);
        }

        public void withGrantDefaultRewards() {
            retoolApiManager.accountRetoolApi().setAccountGrantRewards(user,
                BigDecimal.valueOf(REDEEMABLE_SWEEPSTAKES_AMOUNT), BigDecimal.ZERO);
        }

        public AccountSetupBuilder with3DsAction(Secure3dAction secure3dAction) {
            retoolApiManager.paymentRetoolApi().setAccount3dsAction(user, secure3dAction);
            return this;
        }

        public AccountSetupBuilder withValidPhoneNumber() {
            retoolApiManager.accountRetoolApi().setValidPhoneNumber(user, generateValidUsPhoneNumber());
            return this;
        }

        public void withAccountStatus(AccountStatusSpec userStatus) {
            retoolApiManager.accountRetoolApi().setStatus(user, userStatus);
        }

        public AccountSetupBuilder withSoftKyc() {
            if (accountInfoResponse == null || accountInfoResponse.body() == null) {
                throw new IllegalStateException("Account info response is null");
            }
            retoolFlow
                .setSoftKycWithDefaultValues(accountInfoResponse.body());
            return this;
        }

        public AccountSetupBuilder withKycVerification() {
            retoolFlow
                .createKycIdVerification()
                .createKycPoaVerification();
            return this;
        }

        public UserData signIn() {
            crmSessionManager(user).signInManualWithCookiesSession();
            return user;
        }
    }

}