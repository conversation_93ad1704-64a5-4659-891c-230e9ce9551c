package org.patrianna.ui.factory;

import lombok.Getter;
import org.patrianna.ui.web.page.game.GamePageAbstract;
import org.patrianna.ui.web.page.gameinfo.GameInfoPageAbstract;
import org.patrianna.ui.web.page.home.HomePageAbstract;
import org.patrianna.ui.web.page.login.LoginPageAbstract;
import org.patrianna.ui.web.page.otp.OtpPageAbstract;
import org.patrianna.ui.web.page.profile.MyProfilePageAbstract;
import org.patrianna.ui.web.page.registration.RegistrationPageAbstract;
import org.patrianna.ui.web.page.registration.emailregistration.confirmemail.ConfirmEmailPageAbstract;
import org.patrianna.ui.web.page.resetpassword.ResetPasswordPageAbstract;
import org.patrianna.ui.web.page.slots.SlotsPageAbstract;
import org.patrianna.ui.web.page.store.StorePageAbstract;
import org.patrianna.ui.web.page.transactionhistory.TransactionHistoryPageAbstract;
import org.patrianna.ui.web.page.unauthorized.home.UnauthorizedHomePageAbstract;
import org.patrianna.ui.web.page.unauthorized.landing.LandingPageAbstract;
import org.patrianna.ui.web.page.verification.VerificationPageAbstract;

@Getter
public abstract class PortalAbstract {

    private final HomePageAbstract homePage;
    private final LoginPageAbstract loginPage;
    private final LandingPageAbstract landingPage;
    private final UnauthorizedHomePageAbstract unauthorizedPage;
    private final ConfirmEmailPageAbstract confirmEmailPage;
    private final OtpPageAbstract otpPage;
    private final ResetPasswordPageAbstract resetPasswordPage;
    private final StorePageAbstract storePage;
    private final TransactionHistoryPageAbstract transactionHistoryPage;
    private final VerificationPageAbstract verificationsPage;
    private final RegistrationPageAbstract registrationPage;
    private final SlotsPageAbstract slotsPage;
    private final MyProfilePageAbstract myProfilePage;
    private final GamePageAbstract gamePage;
    private final GameInfoPageAbstract gameInfoPage;

    protected PortalAbstract() {
        this.homePage = createHomePage();
        this.loginPage = createLoginPage();
        this.landingPage = createLandingPage();
        this.unauthorizedPage = createUnauthorizedPage();
        this.confirmEmailPage = createConfirmEmailPage();
        this.otpPage = createOtpPage();
        this.resetPasswordPage = createResetPasswordPage();
        this.storePage = createStorePage();
        this.transactionHistoryPage = createTransactionHistoryPage();
        this.verificationsPage = createVerificationsPage();
        this.registrationPage = createRegistrationPage();
        this.slotsPage = createSlotsPage();
        this.myProfilePage = createMyProfilePage();
        this.gamePage = createGamePage();
        this.gameInfoPage = createGameInfoPage();
    }

    protected abstract HomePageAbstract createHomePage();

    protected abstract LoginPageAbstract createLoginPage();

    protected abstract LandingPageAbstract createLandingPage();

    protected abstract UnauthorizedHomePageAbstract createUnauthorizedPage();

    protected abstract ConfirmEmailPageAbstract createConfirmEmailPage();

    protected abstract OtpPageAbstract createOtpPage();

    protected abstract ResetPasswordPageAbstract createResetPasswordPage();

    protected abstract StorePageAbstract createStorePage();

    protected abstract TransactionHistoryPageAbstract createTransactionHistoryPage();

    protected abstract VerificationPageAbstract createVerificationsPage();

    protected abstract RegistrationPageAbstract createRegistrationPage();

    protected abstract GamePageAbstract createGamePage();

    protected abstract GameInfoPageAbstract createGameInfoPage();

    protected abstract SlotsPageAbstract createSlotsPage();

    protected abstract MyProfilePageAbstract createMyProfilePage();
}