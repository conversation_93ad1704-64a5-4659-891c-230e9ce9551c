package org.patrianna.ui.web.modal.logout;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;

import com.epam.reportportal.annotations.Step;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.options.WaitForSelectorState;
import java.util.List;
import lombok.Getter;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.SitePageModalAbstract;

@Getter
public class B2SLogoutModal extends SitePageModalAbstract<SitePageAbstract> {

    private final Locator modalRoot = getPlaywrightPage().locator("[data-test='common-modal']");
    private final Locator title = modalRoot.locator("[class*='AreYouSureWantLogoutDialog_title__']");
    private final Locator subTitle = modalRoot.locator("[class*='AreYouSureWantLogoutDialog_subTitle__']");
    private final Locator closeButton = modalRoot.locator(
        "[data-test='close-modal-button'], [data-testid='close-modal-button']");
    private final Locator cancelButton = modalRoot.locator("[data-test='log-out-modal-cancel']");
    protected Locator logoutButton = modalRoot
        .locator("[data-test*='log-out-modal-logout'], [data-testid='log-out-modal-logout']");

    public B2SLogoutModal(SitePageAbstract sitePage) {
        super(sitePage);
    }

    @Override
    public List<Locator> getMandatoryElements() {
        return List.of(title, subTitle, logoutButton, cancelButton);
    }

    /**
     * Wait until the modal is visible (both title and logoutButton).
     */
    public void waitForModalVisible() {
        modalRoot.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));
        // Also ensure the logout button is visible
        logoutButton.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE));
    }

    /**
     * Gets the text of the modal title.
     *
     * @return The text of the modal title.
     */
    public String getTitleText() {
        return title.innerText();
    }

    /**
     * Gets the text of the modal subtitle.
     *
     * @return The text of the modal subtitle.
     */
    public String getSubTitleText() {
        return subTitle.innerText();
    }

    /**
     * User interaction methods.
     */

    @Step("Confirm logout by clicking the logout button")
    public void confirmLogout() {
        logoutButton.click();
    }

    @Step("Cancel logout by clicking the cancel button")
    public void cancelLogout() {
        cancelButton.click();
    }

    @Step("Close logout modal by clicking the close button")
    @Override
    public void close() {
        closeButton.click();
    }

    public boolean isTitleVisible() {
        return title.isVisible();
    }

    public boolean isSubTitleVisible() {
        return subTitle.isVisible();
    }

    public boolean isLogoutButtonVisible() {
        return logoutButton.isVisible();
    }

    public boolean isCancelButtonVisible() {
        return cancelButton.isVisible();
    }
}
