package org.patrianna.ui.web.page.home;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;
import static org.patrianna.utils.ui.WaitsUtil.waitForLocator;

import com.microsoft.playwright.Locator;
import org.patrianna.ui.web.component.navigationmenu.SportsmillionsNavigationMenuItemsComponentMobile;
import org.patrianna.ui.web.modal.dailyreward.SportmillionsDailyRewardsModal;
import org.patrianna.ui.web.modal.kyc.verificationrequired.SportmillionsKycVerificationRequiredModal;
import org.patrianna.ui.web.modal.offer.SportmillionsMegaOfferModal;
import org.patrianna.ui.web.modal.redeem.SportmillionsRedeemModalMobile;
import org.patrianna.ui.web.modal.referfriend.SportmillionsReferFriendModal;
import org.patrianna.ui.web.modal.unlockedoffer.SportsmillionsUnlockedNewOfferModalMobile;
import org.patrianna.ui.web.modal.welcome.SportmillionsWelcomeModal;
import org.patrianna.ui.web.page.SitePageAbstract;

public class SportmillionsHomePageMobile extends B2SHomePageMobile {

    private final SportmillionsHomePage delegate;
    private final Locator sidePanel = getPlaywrightPage().locator("div[class*='styles_menuDesktop']");

    public SportmillionsHomePageMobile(SportmillionsHomePage delegate) {
        super(delegate);
        this.delegate = delegate;
        this.startPlayingButton = getPlaywrightPage().locator("button[class*='startPlayingPutton']");
        // --Navigation menu locators--
        this.balanceWidget = getPlaywrightPage()
            .locator("div[class*='socialBalance'], [data-test='cash-balance-header']");
        this.getCoinsButton = getPlaywrightPage().locator("[data-test='common-header-buy-button']").last();
    }

    @Override
    public String getRelativeUrl() {
        return "/pick-em";
    }

    @Override
    public SportsmillionsNavigationMenuItemsComponentMobile<B2SHomePage> navigationMenuItemsComponent() {
        return new SportsmillionsNavigationMenuItemsComponentMobile<>(this);
    }

    @Override
    public SportmillionsMegaOfferModal<B2SHomePage> getMegaOfferModal() {
        return delegate.getMegaOfferModal();
    }

    @Override
    public SportmillionsDailyRewardsModal<SitePageAbstract> getDailyRewardsModal() {
        return delegate.getDailyRewardsModal();
    }

    @Override
    public SportmillionsReferFriendModal<B2SHomePage> getReferFriendModal() {
        return delegate.getReferFriendModal();
    }

    @Override
    public SportmillionsRedeemModalMobile<SitePageAbstract> getRedeemModal() {
        return new SportmillionsRedeemModalMobile<>(delegate.getRedeemModal());
    }

    @Override
    public SportmillionsHomePageMobile clickStartPlayingButton() {
        startPlayingButton.click();
        return this;
    }

    @Override
    public boolean isBalanceWidgetVisible() {
        openSidePanelIfNotVisible();
        return waitForLocator(getBalanceWidget()).isVisible();
    }

    @Override
    public boolean isGetCoinsButtonVisible() {
        openSidePanelIfNotVisible();
        return waitForLocator(getGetCoinsButton()).isVisible();
    }

    private void openSidePanelIfNotVisible() {
        if (!sidePanel.isVisible()) {
            navigationMenuItemsComponent().getBurgerMenu().click();
        }
    }

    @Override
    public SportmillionsKycVerificationRequiredModal<SitePageAbstract> getKycVerificationRequiredModal() {
        return delegate.getKycVerificationRequiredModal();
    }

    @Override
    public SportmillionsWelcomeModal<B2SHomePage> getWelcomeModal() {
        return new SportmillionsWelcomeModal<>(getPageObject());
    }

    @Override
    public Locator getGetCoinsButton() {
        openSidePanelIfNotVisible();
        return delegate.getCoinsButton;
    }

    @Override
    public SportmillionsHomePageMobile clickWithdrawButton() {
        if (!delegate.getWithdrawButton().isVisible()) {
            burgerMenu.click();
        }
        delegate.getWithdrawButton().click();
        return this;
    }

    @Override
    public SportsmillionsUnlockedNewOfferModalMobile<SitePageAbstract> getUnlockedNewOfferModal() {
        return new SportsmillionsUnlockedNewOfferModalMobile<>(getPageObject());
    }
}
