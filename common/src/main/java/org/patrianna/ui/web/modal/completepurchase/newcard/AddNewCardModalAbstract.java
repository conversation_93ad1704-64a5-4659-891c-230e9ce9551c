package org.patrianna.ui.web.modal.completepurchase.newcard;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;
import static org.patrianna.utils.ui.VisibilityCheck.isVisibleWithWait;
import static org.patrianna.utils.ui.WaitsUtil.WAIT_5000_MS;
import static org.patrianna.utils.ui.WaitsUtil.WAIT_500_MS;

import com.epam.reportportal.annotations.Step;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Locator.ClickOptions;
import com.microsoft.playwright.Locator.WaitForOptions;
import com.microsoft.playwright.PlaywrightException;
import com.microsoft.playwright.options.SelectOption;
import com.microsoft.playwright.options.WaitForSelectorState;
import java.util.List;
import lombok.Getter;
import org.patrianna.common.enums.playwright.Attribute;
import org.patrianna.ui.web.component.forms.billingdetails.BillingDetailsComponentAbstract;
import org.patrianna.ui.web.component.forms.carddetails.CardDetailsComponentAbstract;
import org.patrianna.ui.web.component.forms.personaldetails.PersonalDetailsComponentAbstract;
import org.patrianna.ui.web.modal.successfulpurchase.SuccessfulPurchaseModalAbstract;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.SitePageModalAbstract;

@Getter
public abstract class AddNewCardModalAbstract<P extends SitePageAbstract>
    extends SitePageModalAbstract<P> {

    protected final Locator modalRoot = getPlaywrightPage().locator("[data-test='common-modal']");
    protected final Locator closeButton = modalRoot
        .locator("[data-test='close-modal-button'], [data-testid='close-modal-button']");
    protected Locator payNowButton = modalRoot.locator("button[type='submit']").last();
    private final Locator addCardAndPayNowButton = modalRoot
        .locator("button[type='submit']");
    protected Locator lastUsedTitle = getPlaywrightPage().locator("[data-type = 'last-used-title']");
    private final Locator enterAddressManually = getPlaywrightPage().locator(
        "//button[text() = 'Enter Address Manually']");
    private final Locator addressOptional = getPlaywrightPage().locator("input[name='address2']");
    private final Locator cityField = getPlaywrightPage().locator("[name='city']");
    private final Locator zipField = getPlaywrightPage().locator("[name='zip']");
    private final Locator stateDropdown = getPlaywrightPage().locator("[name='state']");
    private final Locator autocompleteResult = getPlaywrightPage().locator("[class*='styles_location']");
    private final Locator backButton = modalRoot.locator(".mt-modal-header button[aria-label='back button']");
    private final Locator sweepstakesRules = modalRoot.locator("a[href='/sweepstakes-rules']");
    protected Locator title = modalRoot.locator("h6[class*='styles_title'], " 
        + "[class*='PurchaseBlock_title'], span:has-text('Quick Purchase'), [class*='modal-title']").last();
    protected Locator address1Field = getPlaywrightPage().locator("input[name='address1']");
    protected Locator buyButton = getPlaywrightPage().locator("button:has-text('Add Card & Pay Now'),"
        + "button:has-text('Add Card & Deposit')");

    protected AddNewCardModalAbstract(P pageObject) {
        super(pageObject);
    }

    @Override
    public List<Locator> getMandatoryElements() {
        return List.of(getTitle(), getPayNowButton());
    }

    public boolean isTitleVisible() {
        return getTitle().isVisible();
    }

    public boolean isPayNowButtonVisible() {
        return getPayNowButton().isVisible();
    }

    public boolean isEnterAddressButtonVisible() {
        return isVisibleWithWait(getEnterAddressManually(), WAIT_500_MS);
    }

    public List<String> getStatesVisible() {
        Locator options = stateDropdown.locator("option");

        return options.all()
            .stream()
            .map(option -> option.getAttribute(Attribute.VALUE.getValue()))
            .filter(value -> value != null && !value.isEmpty())
            .toList();
    }

    public String getFirstNameInputValue() {
        return getPersonalDetailsForm().getFirstNameInput().inputValue();
    }

    public String getLastNameInputValue() {
        return getPersonalDetailsForm().getLastNameInput().inputValue();
    }

    public String getNameOnCardInputValue() {
        return getCardDetailsForm().getNameOnCardInput().inputValue();
    }

    @Step("Pay now with credit/debit card by clicking the Pay Now button")
    public AddNewCardModalAbstract<P> addCardAndPayNow() {
        getAddCardAndPayNowButton().click();
        return this;
    }

    @Step("Close purchase modal by clicking the close button")
    public void close() {
        getCloseButton().click();
    }


    @Step("Click on modal window")
    public AddNewCardModalAbstract<P> clickOnModal() {
        title.click();
        return this;
    }

    @Step("Click on 'Enter address manually' button")
    public AddNewCardModalAbstract<P> clickOnEnterAddressManually() {
        enterAddressManually.hover();
        enterAddressManually.click();
        return this;
    }

    @Step("Select state from dropdown")
    public AddNewCardModalAbstract<P> selectState(String state) {
        stateDropdown.selectOption(state);
        return this;
    }

    @Step("Get state options")
    public List<String> getStateOptions() {
        return stateDropdown.locator("option").allInnerTexts();
    }


    @Step("Check if last used card title is displayed")
    public boolean isLastUsedTitleDisplayed() {
        boolean isVisible;
        try {
            getLastUsedTitle().waitFor(new WaitForOptions()
                .setState(WaitForSelectorState.VISIBLE)
                .setTimeout(WAIT_5000_MS));
            isVisible = true;
        } catch (PlaywrightException exception) {
            isVisible = false;
        }
        return isVisible;
    }

    @Step("Click on buy button")
    public AddNewCardModalAbstract<P> clickOnBuyButton() {
        buyButton.scrollIntoViewIfNeeded();
        buyButton.focus();
        if (buyButton.isEnabled()) {
            buyButton.click();
            if (buyButton.isVisible()) {
                buyButton.click(new ClickOptions().setForce(true));
            }
        }

        return this;
    }

    @Step("Fill billibng state")
    public void fillBillingState(String state) {
        getBillingDetailsForm()
            .getBillingStateDropDown()
            .selectOption(new SelectOption().setValue(state));
    }

    public boolean isBuyButtonEnabled() {
        return buyButton.isEnabled();
    }

    public abstract SuccessfulPurchaseModalAbstract<SitePageAbstract> clickOnPayButtonWithSuccess();

    public abstract CardDetailsComponentAbstract<SitePageAbstract> getCardDetailsForm();

    public abstract PersonalDetailsComponentAbstract<SitePageAbstract> getPersonalDetailsForm();

    public abstract BillingDetailsComponentAbstract<SitePageAbstract> getBillingDetailsForm();

    public abstract boolean isSweepstakesMentionedOnModal();
}
