package org.patrianna.ui.web.component.navigationmenu;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;

import com.epam.reportportal.annotations.Step;
import com.microsoft.playwright.Locator;
import org.patrianna.ui.web.modal.livechat.B2SLiveChatModal;
import org.patrianna.ui.web.modal.logout.B2SLogoutModal;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.profile.B2SMyProfilePage;
import org.patrianna.ui.web.page.profile.B2SMyProfilePageMobile;
import org.patrianna.ui.web.page.store.SportmillionsStorePage;
import org.patrianna.ui.web.page.transactionhistory.B2STransactionHistoryPage;
import org.patrianna.ui.web.page.transactionhistory.SportmillionsTransactionHistoryPage;

public class SportsmillionsNavigationMenuItemsComponentMobile<P extends SitePageAbstract>
    extends B2SNavigationMenuItemsComponentMobile<P> {

    private final Locator getLastCoinsButton =
        getPlaywrightPage().locator("[data-test='common-header-buy-button']").last();

    public SportsmillionsNavigationMenuItemsComponentMobile(P decoratedPage) {
        super(decoratedPage);
        super.logoutButton = getPlaywrightPage().getByText("Logout");
        super.burgerMenu = getPlaywrightPage().locator("button[class*=MenuButton]");
        super.balanceWidget = getPlaywrightPage()
            .locator("[data-test='cash-balance-header'], div[class*='socialBalance']");
        super.scBalance = balanceWidget.locator("span:has-text('SC')");
        super.sidePanel = getPlaywrightPage().locator("div[class*='menuItems']");
        super.scInfoButton = getPlaywrightPage().locator("[class*='info']").first();
        super.notificationsButton = getPlaywrightPage()
            .locator("a[href='/my-inbox']")
            .last();
    }

    @Override
    @Step("Click on the Profile button and navigate to the My Profile page")
    public B2SMyProfilePageMobile clickProfileButton() {
        clickMenuButton();
        getProfileButton().click();

        B2SMyProfilePage myProfilePage = new B2SMyProfilePage();
        return getPageObject().waitUntilNavigated(new B2SMyProfilePageMobile(myProfilePage));
    }

    @Override
    @Step("Click on the Logout button and open the Logout Modal")
    public B2SLogoutModal clickLogoutButton() {
        clickMenuButton();
        logoutButton.click();
        return new B2SLogoutModal(getPageObject());
    }

    @Override
    @Step("Click on the Get Coins button")
    public void clickGetCoins() {
        openSmSidePanelIfClosed();
        getLastCoinsButton.click();
    }

    @Override
    @Step("Click on the Get Coins button and navigate to the Select Coin Package page")
    public SportmillionsStorePage navigateToStorePageViaGetCoinsButton() {
        openSmSidePanelIfClosed();
        getLastCoinsButton.click();
        return new SportmillionsStorePage();
    }

    @Override
    public boolean isLiveChatButtonVisible() {
        clickMenuButton();
        return liveChatButton.isVisible();
    }

    @Override
    @Step("Click on the Live Chat button and navigate to the Live Chat Modal")
    public B2SLiveChatModal<P> clickLiveChatButton() {
        clickMenuButton();
        getLiveChatButton().click();
        return new B2SLiveChatModal<>(getPageObject());
    }

    @Override
    @Step("Click on the Transaction History button")
    public B2STransactionHistoryPage clickTransactionHistoryMenuOption() {
        openSmSidePanelIfClosed();
        getTransactionHistoryButton().click();
        return new SportmillionsTransactionHistoryPage();
    }

    @Override
    public String getGcBalance() {
        openSmSidePanelIfClosed();
        return getGoldenCoinsBalance().innerText();
    }

    @Override
    public String getScBalance() {
        openSmSidePanelIfClosed();
        return scBalance.innerText();
    }

    private void openSmSidePanelIfClosed() {
        if (!sidePanel.isVisible()) {
            clickMenuButton();
        }
    }
}
