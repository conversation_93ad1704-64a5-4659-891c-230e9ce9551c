package org.patrianna.ui.web.component.navigationmenu;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;

import com.epam.reportportal.annotations.Step;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.home.MegabonanzaHomePage;
import org.patrianna.ui.web.page.store.MegabonanzaStorePage;
import org.patrianna.ui.web.page.transactionhistory.MegabonanzaTransactionHistoryPage;

public class MegabonanzaNavigationMenuItemsComponent<P extends SitePageAbstract> extends
    B2BNavigationMenuItemsComponent<P>  {

    private final B2BNavigationMenuItemsComponent<P> delegate;


    public MegabonanzaNavigationMenuItemsComponent(P decoratedPage) {
        super(decoratedPage);
        this.delegate = new B2BNavigationMenuItemsComponent<>(decoratedPage);
        this.notificationsButton = getPlaywrightPage()
            .locator("//img[contains(@alt, 'notifications')]/parent::button");
    }

    @Override
    @Step("Click on the Logout button and open the Logout Modal")
    public MegabonanzaHomePage clickLogoutButton() {
        logoutButton.click();
        return getPageObject().waitUntilNavigated(new MegabonanzaHomePage());
    }

    @Override
    @Step("Click on the Transaction History button")
    public MegabonanzaTransactionHistoryPage clickTransactionHistoryMenuOption() {
        delegate.clickTransactionHistoryButton();
        return new MegabonanzaTransactionHistoryPage();
    }

    @Override
    @Step("Click on the Get Coins button and navigate to the Select Coin Package page")
    public MegabonanzaStorePage navigateToStorePageViaGetCoinsButton() {
        getCoinsButton.click();
        return new MegabonanzaStorePage();
    }
}
