package org.patrianna.ui.web.component.navigationmenu;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;

import com.epam.reportportal.annotations.Step;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.home.MegabonanzaHomePage;
import org.patrianna.ui.web.page.home.MegabonanzaHomePageMobile;
import org.patrianna.ui.web.page.store.MegabonanzaStorePage;
import org.patrianna.ui.web.page.store.MegabonanzaStorePageMobile;

public class MegabonanzaNavigationMenuItemsComponentMobile<P extends SitePageAbstract> extends
    B2BNavigationMenuItemsComponentMobile<P> {

    private final B2BNavigationMenuItemsComponentMobile<P> delegate;

    public MegabonanzaNavigationMenuItemsComponentMobile(P decoratedPage) {
        super(decoratedPage);
        this.delegate = new B2BNavigationMenuItemsComponentMobile<>(decoratedPage);
        this.closeMenuButton = getPlaywrightPage()
            .locator("div[class*='menuActions'] button").last();
    }

    @Override
    @Step("Click on the Logout button and open the Logout Modal")
    public MegabonanzaHomePageMobile clickLogoutButton() {
        delegate.clickMenuButton();
        logoutButton.click();
        return getPageObject().waitUntilNavigated(new MegabonanzaHomePageMobile(new MegabonanzaHomePage()));
    }

    @Override
    @Step("Click on the Get Coins button and navigate to the Select Coin Package page")
    public MegabonanzaStorePageMobile navigateToStorePageViaGetCoinsButton() {
        getCoinsButton.click();
        return new MegabonanzaStorePageMobile(new MegabonanzaStorePage());
    }
}
