package org.patrianna.ui.web.component.navigationmenu;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;

import com.epam.reportportal.annotations.Step;
import com.microsoft.playwright.Locator;
import java.util.List;
import org.patrianna.ui.web.modal.livechat.B2BLiveChatModal;
import org.patrianna.ui.web.modal.redeem.B2BRedeemModal;
import org.patrianna.ui.web.modal.redeem.RedeemModalAbstract;
import org.patrianna.ui.web.modal.sweepstakesinfo.SweepstakesInfoModal;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.home.B2BHomePage;
import org.patrianna.ui.web.page.profile.B2BMyProfilePage;
import org.patrianna.ui.web.page.promotions.B2BPromotionsPage;
import org.patrianna.ui.web.page.store.B2BStorePage;
import org.patrianna.ui.web.page.transactionhistory.B2BTransactionHistoryPage;

public class B2BNavigationMenuItemsComponent<P extends SitePageAbstract>
    extends NavigationMenuItemsComponentAbstract<P> {

    protected final Locator getCoinsButton =
        getPlaywrightPage().locator("[data-test='common-header-buy-button']").first();
    protected final Locator logoutButton = getPlaywrightPage().locator("//img[contains(@src,'logout')]");
    private final Locator transactionHistoryButton = getPlaywrightPage().locator("[href='/transactions']");
    private final Locator profileButton = getPlaywrightPage().locator("[href='/my-profile']");
    private final Locator promotionsButton = getPlaywrightPage().locator("[href='/promotions']");
    protected Locator liveChatButton = getPlaywrightPage().locator("button:has-text('Live Chat')");
    private final Locator balanceWidget = getPlaywrightPage().locator("[data-test='cash-balance-header']").first();
    private final Locator goldenCoinsBalanceWidget = balanceWidget.locator("span:has-text('GC')").first();
    private final Locator scValueLocator = balanceWidget.locator("span:has-text('SC')").first();
    protected Locator notificationsButton = getPlaywrightPage()
        .locator("button[aria-label='Notifications']");

    public B2BNavigationMenuItemsComponent(P page) {
        super(page);
        this.redeemButton = getPlaywrightPage().locator("button:has-text('Redeem')").first();
        this.menuRoot = getPlaywrightPage().locator("div[class*='Layout_menuDesktop_']");
        this.sweepstakesCoinsSections = menuRoot.locator("[alt='sc icon']");
        this.burgerMenu = getPlaywrightPage().locator("[data-testid='bottom-navigation-action']").last();
    }

    @Override
    public List<Locator> getMandatoryElements() {
        return List.of(logoutButton);
    }

    /**
     * User interaction methods.
     */
    @Step("Click on the Logout button and open the Logout Modal")
    public B2BHomePage clickLogoutButton() {
        logoutButton.click();
        return getPageObject().waitUntilNavigated(new B2BHomePage());
    }

    @Step("Click on the Transaction History button")
    public B2BTransactionHistoryPage clickTransactionHistoryMenuOption() {
        transactionHistoryButton.click();
        return new B2BTransactionHistoryPage();
    }

    @Step("Click on the Promotions  button")
    public B2BPromotionsPage clickPromotionsMenuOption() {
        promotionsButton.click();
        return new B2BPromotionsPage();
    }

    @Step("Click on the Get Coins button and navigate to the Select Coin Package page")
    public B2BStorePage navigateToStorePageViaGetCoinsButton() {
        clickGetCoins();
        return getPageObject().waitUntilNavigated(new B2BStorePage());
    }

    @Step("Click on the Live Chat button and navigate to the Live Chat Modal")
    public B2BLiveChatModal<P> clickLiveChatButton() {
        liveChatButton.click();
        return new B2BLiveChatModal<>(getPageObject());
    }

    @Step("Click on the Profile button")
    public B2BMyProfilePage clickProfileButton() {
        profileButton.click();
        return getPageObject().waitUntilNavigated(new B2BMyProfilePage());
    }

    @Step("Click on the Get Coins button")
    public void clickGetCoins() {
        getCoinsButton.click();
    }

    @Override
    @Step("Click on the Redeem button")
    public RedeemModalAbstract<P> openRedeemPage() {
        redeemButton.click();
        return new B2BRedeemModal<>(getPageObject());
    }

    protected void clickMenuButton() {
        burgerMenu.click();
    }

    protected void clickTransactionHistoryButton() {
        transactionHistoryButton.click();
    }

    public String getGcBalance() {
        return goldenCoinsBalanceWidget.innerText();
    }

    public String getScBalance() {
        return scValueLocator.innerText();
    }

    public boolean isLiveChatButtonVisible() {
        return liveChatButton.isVisible();
    }

    @Override
    public SweepstakesInfoModal<P> clickSweepstakesInfoButton() {
        scInfoButton.click();
        return new SweepstakesInfoModal<>(getPageObject());
    }

    @Override
    public void clickOnInboxNotificationsButton() {
        notificationsButton.click();
    }
}