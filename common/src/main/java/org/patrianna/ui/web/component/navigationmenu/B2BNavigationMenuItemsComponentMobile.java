package org.patrianna.ui.web.component.navigationmenu;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;

import com.epam.reportportal.annotations.Step;
import org.patrianna.ui.web.modal.livechat.B2BLiveChatModal;
import org.patrianna.ui.web.modal.redeem.B2BRedeemModal;
import org.patrianna.ui.web.modal.redeem.B2BRedeemModalMobile;
import org.patrianna.ui.web.modal.redeem.RedeemModalAbstract;
import org.patrianna.ui.web.modal.sweepstakesinfo.SweepstakesInfoModal;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.home.B2BHomePage;
import org.patrianna.ui.web.page.home.B2BHomePageMobile;
import org.patrianna.ui.web.page.profile.B2BMyProfilePage;
import org.patrianna.ui.web.page.promotions.B2BPromotionsPage;
import org.patrianna.ui.web.page.store.B2BStorePage;
import org.patrianna.ui.web.page.transactionhistory.B2BTransactionHistoryPage;

public class B2BNavigationMenuItemsComponentMobile<P extends SitePageAbstract>
    extends B2BNavigationMenuItemsComponent<P> {

    private final B2BNavigationMenuItemsComponent<P> delegate;

    public B2BNavigationMenuItemsComponentMobile(P b2bPage) {
        super(b2bPage);
        this.delegate = new B2BNavigationMenuItemsComponent<>(b2bPage);
        this.notificationsButton = getPlaywrightPage()
            .locator("button[class*='NotificationsButton']")
            .first();
    }

    @Override
    @Step("Click on the Transaction History button")
    public B2BTransactionHistoryPage clickTransactionHistoryMenuOption() {
        openSidePanelIfClosed();
        delegate.clickTransactionHistoryButton();
        return new B2BTransactionHistoryPage();
    }

    @Override
    @Step("Click on the Get Coins button and navigate to the Select Coin Package page")
    public B2BStorePage navigateToStorePageViaGetCoinsButton() {
        delegate.clickGetCoins();
        return new B2BStorePage();
    }

    @Override
    @Step("Click on the Profile button")
    public B2BMyProfilePage clickProfileButton() {
        delegate.clickMenuButton();
        delegate.clickProfileButton();

        return getPageObject().waitUntilNavigated(new B2BMyProfilePage());
    }

    @Override
    @SuppressWarnings("unchecked")
    @Step("Click on the Redeem button and open the Redeem Modal")
    public RedeemModalAbstract<P> openRedeemPage() {
        clickRedeemButton();
        var redeemModal = new B2BRedeemModalMobile<>(new B2BRedeemModal<>(getPageObject()));

        return (RedeemModalAbstract<P>) redeemModal;
    }

    @Override
    @Step("Click on the Redeem button and open the Redeem Modal")
    public void clickRedeemButton() {
        if (!redeemButton.isVisible()) {
            super.clickMenuButton();
        }
        redeemButton.click();
    }

    @Override
    @Step("Click on the Live Chat button and navigate to the Live Chat Modal")
    public B2BLiveChatModal<P> clickLiveChatButton() {
        delegate.clickMenuButton();
        delegate.clickLiveChatButton();
        return new B2BLiveChatModal<>(getPageObject());
    }

    @Override
    public boolean isLiveChatButtonVisible() {
        delegate.clickMenuButton();
        return liveChatButton.isVisible();
    }

    @Override
    public SweepstakesInfoModal<P> clickSweepstakesInfoButton() {
        super.clickMenuButton();
        scInfoButton.click();
        return new SweepstakesInfoModal<>(getPageObject());
    }

    @Override
    @Step("Click on the Logout button and open the Logout Modal")
    public B2BHomePageMobile clickLogoutButton() {
        delegate.clickMenuButton();
        logoutButton.click();
        return getPageObject().waitUntilNavigated(new B2BHomePageMobile(new B2BHomePage()));
    }

    @Override
    @Step("Click on the Promotions button")
    public B2BPromotionsPage clickPromotionsMenuOption() {
        openSidePanelIfClosed();
        super.clickPromotionsMenuOption();

        return new B2BPromotionsPage();
    }

}