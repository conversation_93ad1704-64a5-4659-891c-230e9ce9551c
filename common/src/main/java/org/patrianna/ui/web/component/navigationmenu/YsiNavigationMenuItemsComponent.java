package org.patrianna.ui.web.component.navigationmenu;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;
import static org.patrianna.utils.ui.WaitsUtil.waitForLocator;

import com.epam.reportportal.annotations.Step;
import com.microsoft.playwright.Locator;
import java.util.List;
import lombok.Getter;
import org.patrianna.ui.web.modal.livechat.LiveChatModalAbstract;
import org.patrianna.ui.web.modal.redeem.RedeemModalAbstract;
import org.patrianna.ui.web.modal.redeem.YsiRedeemModal;
import org.patrianna.ui.web.modal.sweepstakesinfo.SweepstakesInfoModal;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.home.YsiHomePage;
import org.patrianna.ui.web.page.profile.YsiMyAccountPage;
import org.patrianna.ui.web.page.store.YsiStorePage;
import org.patrianna.ui.web.page.transactionhistory.YsiTransactionHistoryPage;
import org.patrianna.utils.ui.WaitsUtil;

@Getter
public class YsiNavigationMenuItemsComponent<P extends SitePageAbstract>
    extends NavigationMenuItemsComponentAbstract<P> {

    protected Locator logoutButton = getPlaywrightPage().locator("[data-test='menu-link-Logout']");
    private final Locator transactionHistoryButton = getPlaywrightPage()
        .locator("[data-test='menu-link-Transaction History']");
    private final Locator homeButton = getPlaywrightPage().locator("[data-test='menu-link-home']");
    protected Locator getCoinsButton = getPlaywrightPage().locator("[data-test='menu-get-coins-button']");
    private final Locator getCoinsHeaderButton = getPlaywrightPage()
        .locator("button[data-test='common-header-buy-button']");
    private final Locator myAccountButton = getPlaywrightPage().locator("[data-test='menu-link-My Account']");
    private final Locator darkThemeSwitcher = getPlaywrightPage().locator("[value='darkTheme']");
    private final Locator balanceWidget = getPlaywrightPage().locator("[data-test='cash-balance-header']")
        .first();
    protected Locator goldenCoinsBalanceWidget = getPlaywrightPage()
        .locator("[data-test='menu-account-info'] span:has-text('GC')");
    protected Locator scValueLocator = getPlaywrightPage()
        .locator("[data-test='menu-account-info'] span:has-text('SC')");
    private final Locator promotionsButton = getPlaywrightPage().locator("[href='/promotions']").last();

    public YsiNavigationMenuItemsComponent(P page) {
        super(page);
        this.redeemButton = getPlaywrightPage().locator("[data-test='menu-redeem-button']");
        this.menuRoot = getPlaywrightPage().locator("div[class*='styles_menuDesktop']");
        this.sweepstakesCoinsSections = menuRoot.locator("span:has-text('Sweepstakes Coins')");
    }

    @Override
    public List<Locator> getMandatoryElements() {
        return List.of(
            logoutButton,
            homeButton
        );
    }

    @Step("Go to Transaction History page")
    public YsiTransactionHistoryPage clickTransactionHistoryMenuOption() {
        transactionHistoryButton.click();
        return new YsiTransactionHistoryPage();
    }

    public YsiHomePage clickLogoutButton() {
        waitForLocator(logoutButton).click();
        return new YsiHomePage();
    }

    @Override
    public YsiMyAccountPage clickMyAccountButton() {
        waitForLocator(myAccountButton).click();
        return new YsiMyAccountPage();
    }

    @Override
    public SweepstakesInfoModal<P> clickSweepstakesInfoButton() {
        throw new UnsupportedOperationException("Sweepstakes info button is not clickable for YSI brands");
    }

    public YsiStorePage navigateToStorePageViaGetCoinsButton() {
        waitForLocator(getCoinsHeaderButton);
        getCoinsHeaderButton.click();
        return getPageObject().waitUntilNavigated(new YsiStorePage());
    }

    public YsiStorePage clickGetCoinsMenuOption() {
        getCoinsButton.click();
        return new YsiStorePage();
    }

    public RedeemModalAbstract<P> openRedeemPage() {
        waitForLocator(redeemButton).click();
        var redeemModal = new YsiRedeemModal<>(getPageObject());
        WaitsUtil.waitUntil(WaitsUtil.WAIT_10000_MS, WaitsUtil.WAIT_500_MS, redeemModal::isVisible);
        return redeemModal;
    }

    public YsiHomePage clickOnHome() {
        waitForLocator(homeButton).click();
        return new YsiHomePage();
    }

    @Override
    public LiveChatModalAbstract<P> clickLiveChatButton() {
        throw new UnsupportedOperationException("Live Chat is not supported for YSI brand");
    }

    @Override
    public void clickGetCoins() {
        getCoinsButton.click();
    }

    @Override
    public boolean isLiveChatButtonVisible() {
        throw new UnsupportedOperationException("Live Chat is not supported for YSI brand");
    }

    @Override
    public void clickOnInboxNotificationsButton() {
        throw new UnsupportedOperationException("Inbox notifications button is not supported for YSI brand");
    }

    public void enableDarkTheme() {
        darkThemeSwitcher.setChecked(true);
    }


    public String getGcBalance() {
        return goldenCoinsBalanceWidget.innerText();
    }

    public String getScBalance() {
        return scValueLocator.innerText();
    }
}
