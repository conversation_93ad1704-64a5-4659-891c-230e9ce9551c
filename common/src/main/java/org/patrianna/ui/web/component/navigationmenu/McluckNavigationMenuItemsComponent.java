package org.patrianna.ui.web.component.navigationmenu;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;

import com.epam.reportportal.annotations.Step;
import lombok.Getter;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.store.McluckStorePage;

@Getter
public class McluckNavigationMenuItemsComponent<P extends SitePageAbstract> extends
    B2SNavigationMenuItemsComponent<P> {

    public McluckNavigationMenuItemsComponent(P decoratedHeaderComponent) {
        super(decoratedHeaderComponent);
        super.burgerMenu = getPlaywrightPage().locator("[aria-label='Open navigation']").last();
    }

    @Override
    @Step("Click on the Get Coins button and navigate to the Select Coin Package page")
    public McluckStorePage navigateToStorePageViaGetCoinsButton() {
        super.clickGetCoins();
        return new McluckStorePage();
    }
}
