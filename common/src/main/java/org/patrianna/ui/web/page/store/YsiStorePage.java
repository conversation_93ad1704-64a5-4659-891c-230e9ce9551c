package org.patrianna.ui.web.page.store;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;

import com.epam.reportportal.annotations.Step;
import java.util.List;
import lombok.Getter;
import org.patrianna.ui.brand.common.web.modal.purchaselimit.YsiPurchaseLimitModal;
import org.patrianna.ui.web.component.footer.FooterComponentAbstract;
import org.patrianna.ui.web.component.footer.YsiFooterComponent;
import org.patrianna.ui.web.modal.completepurchase.CompletePurchaseModalAbstract;
import org.patrianna.ui.web.modal.completepurchase.YsiCompletePurchaseModal;
import org.patrianna.ui.web.modal.cookiesconsent.YsiCookiesConsentModal;
import org.patrianna.ui.web.modal.dailyreward.DailyRewardsModalAbstract;
import org.patrianna.ui.web.modal.dailyreward.YsiDailyRewardsModal;
import org.patrianna.ui.web.modal.snackbar.YsiSnackbarModal;
import org.patrianna.ui.web.modal.successfulpurchase.SuccessfulPurchaseModalAbstract;
import org.patrianna.ui.web.modal.successfulpurchase.YsiSuccessfulPurchaseModal;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.SitePageModalAbstract;
import org.patrianna.utils.ui.ModalHandler;

@Getter
public class YsiStorePage extends StorePageAbstract {

    private static final String BUY_BUTTON_LOCATOR = "[data-test*='pay-button']:has-text('%s')";

    public YsiStorePage() {
        super();
        this.offerItem = getPlaywrightPage().locator("[data-ab-test*='store-'] div[class*=mt-paper]");
        this.offerBuyButton = "[data-test*='pay-button']";
        this.scPackageValue = "div[class*=largeStyles_sweepstakeBonus] span";
        this.gcPackageValue = "p[class*=largeStyles_newCount] b";
        this.sweepstakesRulesLink = getPlaywrightPage()
            .locator("main#ui-layout-content-node a[href='/sweepstakes-rules']");
    }

    @Override
    public YsiStorePage getPageObject() {
        return this;
    }

    @Override
    public String getRelativeUrl() {
        return "/store";
    }

    @Override
    public List<SitePageModalAbstract<SitePageAbstract>> getObscureModals() {
        return List.of(getCookiesConsentModal());
    }

    public YsiCookiesConsentModal<YsiStorePage> getCookiesConsentModal() {
        return new YsiCookiesConsentModal<>(getPageObject());
    }

    @Override
    public YsiStorePage getRidOfModals() {
        ModalHandler.getRidOfModals(this, null);
        return this;
    }

    @Override
    public FooterComponentAbstract<SitePageAbstract> footerComponent() {
        return new YsiFooterComponent<>(getPageObject());
    }

    public YsiCompletePurchaseModal<SitePageAbstract> getCompletePurchaseModal() {
        return new YsiCompletePurchaseModal<>(getPageObject());
    }

    public SuccessfulPurchaseModalAbstract<SitePageAbstract> getSuccessfulPurchaseModal() {
        return new YsiSuccessfulPurchaseModal<>(this);
    }

    public YsiPurchaseLimitModal<SitePageAbstract> getPurchaseLimitModal() {
        return new YsiPurchaseLimitModal<>(this);
    }

    @Override
    public YsiSnackbarModal<StorePageAbstract> createSnackbarModal() {
        return new YsiSnackbarModal<>(getPageObject());
    }

    @Override
    @Step("Click on the firstOffer and open Complete Purchase Modal")
    public CompletePurchaseModalAbstract<SitePageAbstract> clickOnFirstOffer() {
        offerItem.first().locator(offerBuyButton).click();
        return getCompletePurchaseModal();
    }

    @Override
    @Step("Click on the Buy Offer  button for the offer package: {offerIndex}")
    public CompletePurchaseModalAbstract<SitePageAbstract> clickBuyOfferButton(int offerIndex) {
        offerItem.nth(offerIndex).locator(offerBuyButton).click();
        return getCompletePurchaseModal();
    }

    @Override
    public CompletePurchaseModalAbstract<SitePageAbstract> clickBuyButtonByPrice(Double price) {
        getPlaywrightPage()
            .locator(BUY_BUTTON_LOCATOR.formatted(price))
            .first()
            .click();

        return getCompletePurchaseModal();
    }

    @Override
    public DailyRewardsModalAbstract<SitePageAbstract> createDailyRewardsModal() {
        return new YsiDailyRewardsModal<>(getPageObject());
    }

    @Override
    public Boolean isOfferVisible(String offerCode) {
        return offerItem.locator("[data-test*='%s']".formatted(offerCode)).isVisible();
    }
}