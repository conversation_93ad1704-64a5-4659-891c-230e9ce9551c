package org.patrianna.ui.web.component.navigationmenu;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;

import com.epam.reportportal.annotations.Step;
import com.microsoft.playwright.Locator;
import java.util.List;
import lombok.Getter;
import org.patrianna.ui.web.modal.livechat.B2SLiveChatModal;
import org.patrianna.ui.web.modal.logout.B2SLogoutModal;
import org.patrianna.ui.web.modal.redeem.B2SRedeemModal;
import org.patrianna.ui.web.modal.redeem.RedeemModalAbstract;
import org.patrianna.ui.web.modal.sweepstakesinfo.SweepstakesInfoModal;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.profile.B2SMyProfilePage;
import org.patrianna.ui.web.page.promotions.B2SPromotionsPage;
import org.patrianna.ui.web.page.store.B2SStorePage;
import org.patrianna.ui.web.page.transactionhistory.B2STransactionHistoryPage;
import org.patrianna.utils.ui.WaitsUtil;

@Getter
public class B2SNavigationMenuItemsComponent<P extends SitePageAbstract>
    extends NavigationMenuItemsComponentAbstract<P> {

    // --- Navigation Buttons ---
    private final Locator profileButton = getPlaywrightPage().locator("[href='/my-profile']");
    protected final Locator transactionHistoryButton = getPlaywrightPage()
        .locator("[href='/transactions'], [href='/pick-em/transactions']").last();
    private final Locator lobbyButton = getPlaywrightPage().locator("[data-test='menu-link-lobby']");
    private final Locator promotionsButton = getPlaywrightPage().locator("[href='/promotions']").last();
    protected Locator logoutButton = getPlaywrightPage()
        .locator("[data-test='menu-link-log-out'], [data-testid='menu-link-log-out']");

    // --- Action Buttons ---
    protected Locator liveChatButton = getPlaywrightPage().locator("button:has-text('Live Chat')");
    protected Locator getCoinsButton = getPlaywrightPage()
        .locator(":is([data-test='common-header-buy-button'], [data-test='header-store-btn']), "
            + "[data-testid='menu-items-get-coins-button']").first();
    protected Locator notificationsButton = getPlaywrightPage()
        .locator("//div[contains(@class, 'menuDesktop')]//span[text() = 'Notifications']/ancestor::button")
        .first();
    private final Locator lastCoinsButton = getPlaywrightPage()
        .locator("[data-test='common-header-buy-button']").last();

    // --- Widgets / Containers ---
    private final Locator navigationMenuContainer = getPlaywrightPage().locator("[class*='Layout_menuDesktop']");
    private final Locator currencySwitcher = getPlaywrightPage().locator("//div[contains(@class, 'CurrencySwitcher')]");

    // --- Balance Elements ---
    protected Locator balanceWidget = getPlaywrightPage()
        .locator("[data-test='cash-balance-header'], div[class*='socialBalance']").first();
    private final Locator goldenCoinsBalance = balanceWidget.locator("span:has-text('GC')").first();
    private final Locator pickemBalance = getPlaywrightPage().locator("div[class*='styles_usdAmount'] span");
    protected Locator scBalance = balanceWidget.locator("span:has-text('SC')").first();

    public B2SNavigationMenuItemsComponent(P page) {
        super(page);
        super.redeemButton = getPlaywrightPage().locator("[data-test='common-header-redeem-button']")
            .last();
        this.menuRoot = getPlaywrightPage().locator("div[class*='Layout_menuDesktop_']");
        this.sweepstakesCoinsSections = menuRoot.locator("[data-test='sc-balance']");
        this.burgerMenu = getPlaywrightPage()
            .locator("[data-test*='footer-button'], [data-testid='bottom-navigation-menu']").last();
    }

    @Override
    public List<Locator> getMandatoryElements() {
        return List.of(logoutButton, lobbyButton);
    }

    public String getGcBalance() {
        return goldenCoinsBalance.innerText();
    }

    public String getPickemBalance() {
        return pickemBalance.innerText();
    }

    public String getScBalance() {
        return scBalance.innerText();
    }

    /**
     * User interaction methods.
     */
    @Step("Click on the Logout button and open the Logout Modal")
    public B2SLogoutModal clickLogoutButton() {
        logoutButton.click();
        return new B2SLogoutModal(getPageObject());
    }

    @Step("Click on the Profile button and navigate to the My Profile page")
    public B2SMyProfilePage clickProfileButton() {
        profileButton.click();
        return getPageObject().waitUntilNavigated(new B2SMyProfilePage());
    }

    @Step("Click on the Transaction History button")
    public B2STransactionHistoryPage clickTransactionHistoryMenuOption() {
        transactionHistoryButton.scrollIntoViewIfNeeded();
        transactionHistoryButton.click();
        return new B2STransactionHistoryPage();
    }

    @Step("Click on the Promotions button")
    public B2SPromotionsPage clickPromotionsMenuOption() {
        promotionsButton.click();
        return new B2SPromotionsPage();
    }

    @Step("Click on the Live Chat button and navigate to the Live Chat Modal")
    public B2SLiveChatModal<P> clickLiveChatButton() {
        liveChatButton.click();
        return new B2SLiveChatModal<>(getPageObject());
    }

    @Step("Click on the Get Coins button")
    public void clickGetCoins() {
        getCoinsButton.click();
    }

    @Override
    @Step("Click on the Redeem button")
    public RedeemModalAbstract<P> openRedeemPage() {
        redeemButton.click();
        var redeemModal = new B2SRedeemModal<>(getPageObject());
        WaitsUtil.waitUntil(WaitsUtil.WAIT_20000_MS, WaitsUtil.WAIT_500_MS, redeemModal::isVisible);
        return redeemModal;
    }

    @Step("Navigate to the Select Coin Package page via Get Coins button")
    public B2SStorePage navigateToStorePageViaGetCoinsButton() {
        clickGetCoins();
        return getPageObject().waitUntilNavigated(new B2SStorePage());
    }

    public void clickTransactionHistoryButton() {
        transactionHistoryButton.click();
    }

    @Step("Click on the burger menu button")
    public void clickMenuButton() {
        burgerMenu.click();
    }

    @Step("Check if the Live Chat button is visible")
    public boolean isLiveChatButtonVisible() {
        return liveChatButton.isVisible();
    }

    @Override
    public SweepstakesInfoModal<P> clickSweepstakesInfoButton() {
        scInfoButton.click();
        return new SweepstakesInfoModal<>(getPageObject());
    }

    @Step("Click on Notifications button")
    @Override
    public void clickOnInboxNotificationsButton() {
        notificationsButton.click();
    }
}