package org.patrianna.ui.web.component.navigationmenu;

import static com.patrianna.aqa.ui.PlaywrightManager.getPlaywrightPage;

import com.epam.reportportal.annotations.Step;
import com.microsoft.playwright.Locator;
import org.patrianna.ui.web.page.SitePageAbstract;
import org.patrianna.ui.web.page.profile.SpinBlitzMyProfilePage;
import org.patrianna.ui.web.page.profile.SpinBlitzMyProfilePageMobile;
import org.patrianna.ui.web.page.store.SpinBlitzStorePage;
import org.patrianna.ui.web.page.store.SpinBlitzStorePageMobile;
import org.patrianna.ui.web.page.transactionhistory.SpinBlitzTransactionHistoryPage;

public class SpinBlitzNavigationMenuItemsComponentMobile<P extends SitePageAbstract>
    extends B2SNavigationMenuItemsComponentMobile<P> {

    private final Locator mobileGetCoinsButton = getPlaywrightPage().locator("[data-test='header-store-btn']").first();

    public SpinBlitzNavigationMenuItemsComponentMobile(P decoratedPage) {
        super(decoratedPage);
        this.sidePanel = getPlaywrightPage().locator("div[class*='More_drawer']").last();
        this.notificationsButton = getPlaywrightPage()
            .locator("button[class*='NotificationButton']")
            .first();
    }

    @Override
    @Step("Click on the Get Coins button")
    public void clickGetCoins() {
        clickMenuButton();
        mobileGetCoinsButton.click();
    }

    @Override
    @Step("Click on the Profile button and navigate to the My Profile page")
    public SpinBlitzMyProfilePageMobile clickProfileButton() {
        clickMenuButton();
        getProfileButton().click();

        return getPageObject().waitUntilNavigated(
            new SpinBlitzMyProfilePageMobile(new SpinBlitzMyProfilePage())
        );
    }

    @Override
    @Step("Click on the Transaction History button")
    public SpinBlitzTransactionHistoryPage clickTransactionHistoryMenuOption() {
        openSidePanelIfClosed();
        getTransactionHistoryButton().click();
        return new SpinBlitzTransactionHistoryPage();
    }

    @Override
    @Step("Click on the Get Coins button and navigate to the Select Coin Package page")
    public SpinBlitzStorePageMobile navigateToStorePageViaGetCoinsButton() {
        getGetCoinsButton().click();
        return new SpinBlitzStorePageMobile(new SpinBlitzStorePage());
    }
}
