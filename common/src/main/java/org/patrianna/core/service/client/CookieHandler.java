package org.patrianna.core.service.client;

import com.microsoft.playwright.options.Cookie;
import com.microsoft.playwright.options.SameSiteAttribute;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * Utility class for handling HTTP cookies.
 * This class provides methods for parsing and manipulating cookies from HTTP headers.
 */
public class CookieHandler {

    /**
     * Parses Set-Cookie headers into a list of Playwright Cookie objects.
     *
     * @param setCookieHeaders List of Set-Cookie header values
     * @return List of Playwright Cookie objects
     */
    public static List<Cookie> parseSetCookieHeaders(List<String> setCookieHeaders) {
        return setCookieHeaders.stream()
            .map(CookieHandler::parseCookie)
            .filter(Objects::nonNull)
            .toList();
    }

    /**
     * Parses a single Set-Cookie header into a Playwright Cookie object.
     *
     * @param header Set-Cookie header value
     * @return Playwright Cookie object, or null if parsing fails
     */
    private static Cookie parseCookie(String header) {
        Cookie result = null;

        String[] parts = header.split(";");
        if (parts.length > 0) {
            String[] nameValue = parts[0].split("=", 2);
            if (nameValue.length == 2) {
                String name = nameValue[0].trim();
                String value = nameValue[1].trim();
                String domain = extractDomain(parts);
                Double expiration = extractExpiration(parts);

                result = new Cookie(name, value);
                result.setPath("/");
                result.setExpires(expiration);
                result.setDomain(domain);
                result.setSecure(true);
                result.setSameSite(SameSiteAttribute.NONE);
            }
        }

        return result;
    }

    /**
     * Extracts the domain from cookie parts.
     *
     * @param parts Cookie parts from the Set-Cookie header
     * @return Domain string, or null if not found
     */
    private static String extractDomain(String[] parts) {
        String prefix = "domain=";
        String rawDomain = getDomainPart(parts, prefix);

        return "." + rawDomain;
    }

    private static String getDomainPart(String[] parts, String prefix) {
        String domainPart = null;

        for (String part : parts) {
            String trimmed = part.trim().toLowerCase();
            if (trimmed.startsWith(prefix)) {
                domainPart = part.trim().substring(prefix.length());
                break;
            }
        }

        return domainPart;
    }

    /**
     * Extracts the expiration time from cookie parts.
     *
     * @param parts Cookie parts from the Set-Cookie header
     * @return Expiration time as a Double (epoch seconds), or null if not found
     */
    private static Double extractExpiration(String[] parts) {
        String expires = null;

        for (String part : parts) {
            String trimmed = part.trim();
            if (trimmed.toLowerCase().startsWith("expires=")) {
                expires = trimmed.substring("expires=".length());
                break;
            }
        }

        assert expires != null;
        expires = expires.replace("-", " ");
        DateTimeFormatter formatter = DateTimeFormatter.RFC_1123_DATE_TIME;

        ZonedDateTime zdt = ZonedDateTime.parse(expires, formatter);
        return (double) zdt.toEpochSecond();
    }

    private CookieHandler() {
        // Hide implicit public constructor
    }
}