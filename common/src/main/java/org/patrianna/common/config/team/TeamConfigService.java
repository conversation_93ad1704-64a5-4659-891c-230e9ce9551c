package org.patrianna.common.config.team;

import java.util.Map;
import org.patrianna.common.data.dto.team.TeamConfigDto;

/**
 * Service class for handling team configuration operations.
 */
public class TeamConfigService {

    /**
     * Get URL for a specific service and brand.
     *
     * @param teamConfigDto The team configuration data
     * @param service       The service name
     * @param brand         The brand name
     * @return The URL for the specified service and brand
     * @throws IllegalArgumentException if service or brand not found
     */
    public String getUrl(TeamConfigDto teamConfigDto, String service, String brand) {
        TeamConfigDto.TeamConfig teamConfig = getServiceConfig(teamConfigDto, service);
        if (teamConfig == null || teamConfig.getBrands() == null) {
            throw new IllegalArgumentException("Service not found: " + service);
        }

        String url = teamConfig.getBrands().get(brand);
        if (url == null) {
            throw new IllegalArgumentException("Brand not found for service " + service + ": " + brand);
        }

        return url;
    }

    /**
     * Get the team configuration for a specific service.
     *
     * @param teamConfigDto The team configuration data
     * @param service       The service name
     * @return The team configuration for the specified service
     */
    private TeamConfigDto.TeamConfig getServiceConfig(TeamConfigDto teamConfigDto, String service) {
        Map<String, TeamConfigDto.TeamConfig> serviceConfigMap = buildServiceConfigMap(teamConfigDto);
        return serviceConfigMap.get(service);
    }

    /**
     * Build a map of service names to team configurations.
     *
     * @param teamConfigDto The team configuration data
     * @return A map of service names to team configurations
     */
    private Map<String, TeamConfigDto.TeamConfig> buildServiceConfigMap(TeamConfigDto teamConfigDto) {
        return Map.of(
            "payments", teamConfigDto.getPayments(),
            "dataActivation", teamConfigDto.getDataActivation(),
            "cms", teamConfigDto.getCms(),
            "crm", teamConfigDto.getCrm(),
            "engagement", teamConfigDto.getEngagement(),
            "conversion", teamConfigDto.getConversion(),
            "account", teamConfigDto.getAccount()
        );
    }
}