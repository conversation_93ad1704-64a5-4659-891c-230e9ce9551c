package it.aeropay;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import bots.payment.ResponseWrapper;
import fe.api.payment.model.AeroPayPaymentMethod;
import fe.api.payment.model.AeroPayWithdrawMethod;
import fe.api.payment.model.CreateNewAeroPayMethodResponseBody;
import fe.api.payment.model.WithdrawMethodObject;
import fe.api.payment.model.WithdrawMethodType;
import it.EmbeddedCompositeBot;
import payment.card.AchPaymentMethod;
import payment.model.AccountPaymentMethod;
import payment.model.AccountWithdrawMethod;
import payment.model.AchAccountTypeSpec;
import payment.model.WithdrawMethodSpec;
import payment.model.immutable.ImmutableAccount;
import payment.type.PaymentMethodTypeSpec;
import payment.withdraws.AeroPayWithdrawMethodInfo;

class CreateNewAeroPayMethodRequestHandlerIntegrationTest extends AeroPayBaseIntegrationTest {

    private static final String PHONE = "+***********";
    private static final String EMAIL = "<EMAIL>";
    private static final String FULL_NAME = "Chau Duong";

    @BeforeEach
    void setUp() {
        addAeroPayUps();
        resetMocks();
    }

    @ParameterizedTest
    @CsvSource({
            "1293137, 929412, 5801, 8890f409da7d3313c24239f7d417a8ad, false",
            "1293138, 929414, 5366, 2d10ce8e99b892230ec40d8bf096177d, false",
            "1293139, 932831, 1629, 5ca3ade840d4e3196837eb84b4bcee7d, true",
            "1293140, 944892, 0860, c13084f716866981cb280ee2ce7ceec0, true",
    })
    void addNewMethods(String givenAeroPayUserId, String givenBankAccountId, String last4, String expectedWithdrawMethodCode, boolean previouslySaved) throws Throwable {
        String expectedBankAccountNumber = "************" + last4;
        WithdrawMethodObject expectedWithdrawMethod = WithdrawMethodObject.builder()
                .code(expectedWithdrawMethodCode)
                .withdrawMethod(AeroPayWithdrawMethod.builder()
                        .phone(PHONE)
                        .bankAccountName(AEROPAY_ACCOUNT_NAME)
                        .bankName(BANK_NAME)
                        .bankAccountNumber(expectedBankAccountNumber)
                        .accountId(givenBankAccountId)
                        .type(WithdrawMethodType.AEROPAY)
                        .build())
                .build();
        AeroPayPaymentMethod expectedPaymentMethod = AeroPayPaymentMethod.builder()
                .bankAccountName(AEROPAY_ACCOUNT_NAME)
                .bankName(BANK_NAME)
                .bankAccountNumber(expectedBankAccountNumber)
                .accountId(givenBankAccountId)
                .phone(PHONE)
                .type("AeroPayPaymentMethod")
                .build();
        CreateNewAeroPayMethodResponseBody expectedResponse = CreateNewAeroPayMethodResponseBody.builder()
                .withdrawMethod(expectedWithdrawMethod)
                .paymentMethod(expectedPaymentMethod)
                .build();
        try (EmbeddedCompositeBot bot = createBot()) {
            ImmutableAccount account = signUp(bot);
            saveAccountPaymentInfoToDataBase(account, givenAeroPayUserId);
            if (previouslySaved) {
                savePaymentAndWithdrawMethods(account, expectedBankAccountNumber, givenBankAccountId, givenAeroPayUserId);
            }

            mockUserAuthResponse(givenAeroPayUserId);
            String expectationId = mockUserDetailsWithMultipleBanksResponse(givenAeroPayUserId);

            ResponseWrapper<CreateNewAeroPayMethodResponseBody> response = bot.createNewAeroPayMethod(givenBankAccountId);

            assertEquals("err_ok", response.getErrorCode());
            assertEquals(expectedResponse, response.getResp());
            mockHttp.verify(expectationId);
            verifySavedMethodsInDataBase(givenAeroPayUserId, givenBankAccountId, expectedBankAccountNumber, expectedWithdrawMethodCode);
        }
    }

    @Test
    void newMethodsShouldNotBeAddedForIncorrectBankAccountId() throws Throwable {
        String givenAeroPayUserId = "1293141";
        String givenBankAccountId = "123456"; // Incorrect bank account ID
        try (EmbeddedCompositeBot bot = createBot()) {
            ImmutableAccount account = signUp(bot);
            saveAccountPaymentInfoToDataBase(account, givenAeroPayUserId);

            mockUserAuthResponse(givenAeroPayUserId);
            String expectationId = mockUserDetailsWithMultipleBanksResponse(givenAeroPayUserId);

            ResponseWrapper<CreateNewAeroPayMethodResponseBody> response = bot.createNewAeroPayMethod(givenBankAccountId);

            assertEquals("err_not_found", response.getErrorCode());
            assertEquals("Bank account not found", response.getErrorText());
            mockHttp.verify(expectationId);
            List<AccountPaymentMethod> savedPaymentMethods = ebean.find(AccountPaymentMethod.class).findList();
            List<AccountWithdrawMethod> savedWithdrawMethods = ebean.find(AccountWithdrawMethod.class).findList();
            assertEquals(0, savedPaymentMethods.size());
            assertEquals(0, savedWithdrawMethods.size());
        }
    }

    @Test
    void newMethodsShouldNotBeAddedForIncorrectAuth() throws Throwable {
        String givenAeroPayUserId = "1293142";
        String givenBankAccountId = "123456";
        try (EmbeddedCompositeBot bot = createBot()) {
            ImmutableAccount account = signUp(bot);
            saveAccountPaymentInfoToDataBase(account, givenAeroPayUserId);

            mockUserAuthFailedResponse(givenAeroPayUserId);

            ResponseWrapper<CreateNewAeroPayMethodResponseBody> response = bot.createNewAeroPayMethod(givenBankAccountId);

            assertEquals("err_denied", response.getErrorCode());
            assertEquals("Account was not confirmed", response.getErrorText());
            List<AccountPaymentMethod> savedPaymentMethods = ebean.find(AccountPaymentMethod.class).findList();
            List<AccountWithdrawMethod> savedWithdrawMethods = ebean.find(AccountWithdrawMethod.class).findList();
            assertEquals(0, savedPaymentMethods.size());
            assertEquals(0, savedWithdrawMethods.size());
        }
    }

    @Test
    void newMethodsShouldNotBeAddedWhenNoAccountPaymentInfo() throws Throwable {
        String givenBankAccountId = "123456";
        try (EmbeddedCompositeBot bot = createBot()) {
            signUp(bot);

            ResponseWrapper<CreateNewAeroPayMethodResponseBody> response = bot.createNewAeroPayMethod(givenBankAccountId);

            assertEquals("err_not_found", response.getErrorCode());
            assertEquals("Payment account was not found for user", response.getErrorText());
            List<AccountPaymentMethod> savedPaymentMethods = ebean.find(AccountPaymentMethod.class).findList();
            List<AccountWithdrawMethod> savedWithdrawMethods = ebean.find(AccountWithdrawMethod.class).findList();
            assertEquals(0, savedPaymentMethods.size());
            assertEquals(0, savedWithdrawMethods.size());
        }
    }

    private void savePaymentAndWithdrawMethods(ImmutableAccount account, String bankAccountNumber, String bankAccountId, String aeroPayId) {
        AeroPayWithdrawMethodInfo withdrawMethodInfo = buildWithdrawMethodInfo(aeroPayId, bankAccountId, bankAccountNumber);
        AchPaymentMethod achPaymentMethod = new AchPaymentMethod();
        achPaymentMethod.setUsernames(List.of(FULL_NAME));
        achPaymentMethod.setEmails(List.of(EMAIL));
        achPaymentMethod.setAccountName(AEROPAY_ACCOUNT_NAME);
        achPaymentMethod.setAccountNumber(bankAccountNumber);
        achPaymentMethod.setAccountType(AchAccountTypeSpec.CHECKING);
        achPaymentMethod.setBankName(BANK_NAME);
        achPaymentMethod.setPhones(List.of(PHONE));
        AccountPaymentMethod paymentMethod = new AccountPaymentMethod(account);
        paymentMethod.setAchPaymentMethod(achPaymentMethod);
        paymentMethod.setCode(bankAccountId);
        paymentMethod.setFingerprint(aeroPayId + bankAccountId);
        paymentMethod.setType(PaymentMethodTypeSpec.AEROPAY.code());
        paymentMethod.setRemember(false);
        paymentMethod.setAchPaymentMethod(achPaymentMethod);
        AccountWithdrawMethod accountWithdrawMethod = new AccountWithdrawMethod(account, false);
        accountWithdrawMethod.setType(WithdrawMethodSpec.AEROPAY);
        accountWithdrawMethod.setCode(withdrawMethodInfo.hash().toString());
        accountWithdrawMethod.setMethod(withdrawMethodInfo);
        try (var tx = ebean.beginTransaction()) {
            ebean.save(achPaymentMethod, tx);
            ebean.save(paymentMethod, tx);
            ebean.save(accountWithdrawMethod, tx);
            tx.commit();
        }
    }

    private void verifySavedMethodsInDataBase(String givenAeroPayUserId,
                                              String givenBankAccountId,
                                              String expectedBankAccountNumber,
                                              String expectedWithdrawMethodCode) {
        AeroPayWithdrawMethodInfo expectedWithdrawMethodInfo = buildWithdrawMethodInfo(givenAeroPayUserId, givenBankAccountId, expectedBankAccountNumber);
        List<AccountPaymentMethod> savedPaymentMethods = ebean.find(AccountPaymentMethod.class)
                .fetch("achPaymentMethod")
                .findList();
        List<AccountWithdrawMethod> savedWithdrawMethods = ebean.find(AccountWithdrawMethod.class)
                .findList();
        assertEquals(1, savedPaymentMethods.size());
        assertEquals(1, savedWithdrawMethods.size());
        AccountPaymentMethod savedPaymentMethod = savedPaymentMethods.getFirst();
        AccountWithdrawMethod savedWithdrawMethod = savedWithdrawMethods.getFirst();
        AchPaymentMethod savedAchPaymentMethod = savedPaymentMethod.getAchPaymentMethod();
        assertTrue(savedPaymentMethod.isRemember());
        assertEquals(givenBankAccountId, savedPaymentMethod.getCode());
        assertEquals(PaymentMethodTypeSpec.AEROPAY.code(), savedPaymentMethod.getType());
        assertEquals(givenAeroPayUserId + givenBankAccountId, savedPaymentMethod.getFingerprint());
        assertNotNull(savedAchPaymentMethod);
        assertEquals(List.of(EMAIL), savedAchPaymentMethod.getEmails());
        assertEquals(List.of(PHONE), savedAchPaymentMethod.getPhones());
        assertEquals(List.of(FULL_NAME), savedAchPaymentMethod.getUsernames());
        assertEquals(AEROPAY_ACCOUNT_NAME, savedAchPaymentMethod.getAccountName());
        assertEquals(expectedBankAccountNumber, savedAchPaymentMethod.getAccountNumber());
        assertEquals(AchAccountTypeSpec.CHECKING, savedAchPaymentMethod.getAccountType());
        assertEquals(BANK_NAME, savedAchPaymentMethod.getBankName());
        assertTrue(savedWithdrawMethod.isRemember());
        assertEquals(expectedWithdrawMethodCode, savedWithdrawMethod.getCode());
        assertEquals(WithdrawMethodSpec.AEROPAY, savedWithdrawMethod.getType());
        assertEquals(expectedWithdrawMethodInfo, savedWithdrawMethod.getMethod());
    }

    private AeroPayWithdrawMethodInfo buildWithdrawMethodInfo(String givenAeroPayUserId, String givenBankAccountId, String expectedBankAccountNumber) {
        return AeroPayWithdrawMethodInfo.builder()
                .userId(givenAeroPayUserId)
                .bankAccountId(givenBankAccountId)
                .bankAccountName(AEROPAY_ACCOUNT_NAME)
                .bankAccountNumber(expectedBankAccountNumber)
                .bankName(BANK_NAME)
                .phoneNumber(PHONE)
                .build();
    }
}
