package it;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Arrays;
import java.util.List;

import api.v1.ApplicationException;
import fe.api.payment.model.Crypto;
import fe.api.payment.model.Payper;
import org.junit.jupiter.api.Test;
import org.springframework.cloud.DynamicCloud;

import com.github.robtimus.obfuscation.Obfuscator;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ups.PlainServiceInfo;

import fe.api.payment.model.AeroPay;
import fe.api.payment.model.AndroidInApp;
import fe.api.payment.model.AppleInApp;
import fe.api.payment.model.CurrencyType;
import fe.api.payment.model.Fiserv;
import fe.api.payment.model.FiservApplePay;
import fe.api.payment.model.FiservGooglePay;
import fe.api.payment.model.GetPaymentServiceInfoResponseBody;
import fe.api.payment.model.GetPaymentServiceInfoResponseBodyPurchaseProviders;
import fe.api.payment.model.GetPaymentServiceInfoResponseBodyWithdrawProviders;
import fe.api.payment.model.Nuvei;
import fe.api.payment.model.Prizeout;
import fe.api.payment.model.Skrill;
import fe.api.payment.model.Spreedly;
import fe.api.payment.model.SpreedlyApplePay;
import fe.api.payment.model.SpreedlyGooglePay;
import framework.listeners.PaymentWorkerProviderDataGenerator;
import gateway.api.SignUpManualRequest;
import gateway.api.SignUpResponse;
import io.ebean.Transaction;
import payment.model.immutable.ImmutableAccount;
import payment.type.PurchaseProviderSpec;
import payment.type.RedeemProviderSpec;
import payment.worker.PaymentWorkerJpaManager;
import uam.CrmProto;

class GetPaymentServiceInfoRequestHandlerIntegrationTest extends AbstractIntegrationTest {

    private final PaymentWorkerJpaManager ebean = paymentRule.getPaymentWorkerServer().getCtx().getBean(PaymentWorkerJpaManager.class);
    private final PaymentServerResource paymentServer = paymentRule.getPaymentServer();
    private final DynamicCloud cloud  = paymentRule.getPaymentServer().getCloud();
    private final int mockPort  = paymentRule.getPaymentServer().getRule().getMockHttp().getPort();

    @Test
    void getPaymentServiceInfoRequestShouldReturnExpectedProviders() throws Throwable {
        try (Transaction tx = ebean.createTransaction()) {
            Arrays.stream(PurchaseProviderSpec.values()).forEach(provider -> setUpPurchaseProvider(provider, tx));
            Arrays.stream(RedeemProviderSpec.values()).forEach(provider -> setUpWithdrawProvider(provider, tx));
            tx.commit();
        }
        GetPaymentServiceInfoResponseBody expected = getGetPaymentServiceInfoResponseBody();

        try (EmbeddedCompositeBot bot = createBot()) {
            // set sc mode
            SignUpManualRequest.SignUpManualRequestBuilder<?, ?> builder = SignUpManualRequest.builder();
            builder.password(Obfuscator.all().obfuscateObject(uamRule.getCrmServer().getProperties().genPassword()));
            builder.query(CrmProto.FIRST_UTM_CAMPAIGN + "=set_sc");
            SignUpResponse response = bot.signUpManual(builder);
            setupProperties(response);
            bot.connect();

            GetPaymentServiceInfoResponseBody actual = bot.serviceInfoRequest();

            assertEquals(expected, actual);
        }
    }

    private void setupProperties(SignUpResponse response) throws ApplicationException {
        long accountId = Long.parseLong(response.id.substring(response.id.lastIndexOf("/") + 1));

        ImmutableAccount account;
        try (Transaction tx = ebean.createTransaction()) {
            account = ebean.paymentAccountRepo().requiredAccount(accountId, tx);
        }

        String brandName = account.getBrand().getName();
        paymentServer.setProperty(brandName + ".crypto.users-applied.to", 100);
        paymentServer.setProperty(brandName + ".aeropay.users-applied.to", 100);
    }

    private GetPaymentServiceInfoResponseBody getGetPaymentServiceInfoResponseBody() {
        GetPaymentServiceInfoResponseBodyPurchaseProviders expectedPurchaseProviders = new GetPaymentServiceInfoResponseBodyPurchaseProviders();
        expectedPurchaseProviders.setAppleInApp((AppleInApp) new AppleInApp().providerName("APPLE_IN_APP").currencyType(CurrencyType.FIAT));
        expectedPurchaseProviders.setAndroidInApp((AndroidInApp) new AndroidInApp().providerName("ANDROID_IN_APP").currencyType(CurrencyType.FIAT));
        expectedPurchaseProviders.setFiserv((Fiserv) new Fiserv().id("userFISERV").providerName("FISERV").currencyType(CurrencyType.FIAT));
        expectedPurchaseProviders.setAeropay((AeroPay) new AeroPay().providerName("AEROPAY").currencyType(CurrencyType.FIAT));
        expectedPurchaseProviders.setFiservApplePay((FiservApplePay) new FiservApplePay()
                .merchantId("fiserv_apple_pay")
                .providerName("FISERV_APPLE_PAY")
                .currencyType(CurrencyType.FIAT)
        );
        expectedPurchaseProviders.setFiservGooglePay(
                (FiservGooglePay) new FiservGooglePay()
                        .gateway("FISERV_GOOGLE_PAY")
                        .merchant("FISERV_GOOGLE_PAY")
                        .merchantId("fiserv_google_pay")
                        .merchantName("FISERV_GOOGLE_PAY")
                        .providerName("FISERV_GOOGLE_PAY")
                        .currencyType(CurrencyType.FIAT)
        );
        expectedPurchaseProviders.setNuvei((Nuvei) new Nuvei().id("userNUVEI_MAZOOMA_ACH").providerName("NUVEI_MAZOOMA_ACH").currencyType(CurrencyType.FIAT));
        expectedPurchaseProviders.setSkrill((Skrill) new Skrill().providerName("SKRILL").currencyType(CurrencyType.FIAT));
        expectedPurchaseProviders.setSpreedly((Spreedly) new Spreedly().id("userSPREEDLY").providerName("SPREEDLY").currencyType(CurrencyType.FIAT));
        expectedPurchaseProviders.setSpreedlyApplePay((SpreedlyApplePay) new SpreedlyApplePay()
                .merchantId("spreedly_apple_pay")
                .providerName("SPREEDLY_APPLE_PAY")
                .currencyType(CurrencyType.FIAT)
        );
        expectedPurchaseProviders.setSpreedlyGooglePay((SpreedlyGooglePay) new SpreedlyGooglePay()
                .gateway("SPREEDLY_GOOGLE_PAY")
                .merchant("SPREEDLY_GOOGLE_PAY")
                .merchantId("spreedly_google_pay")
                .merchantName("SPREEDLY_GOOGLE_PAY")
                .providerName("SPREEDLY_GOOGLE_PAY")
                .currencyType(CurrencyType.FIAT)
        );
        expectedPurchaseProviders.setPayper(Payper.builder()
                .providerName("PAYPER")
                .currencyType(CurrencyType.FIAT)
                .build()
        );
        expectedPurchaseProviders.setCrypto(Crypto.builder()
                .providerName("CRYPTO")
                .currencyType(CurrencyType.CRYPTO)
                .build()
        );
        GetPaymentServiceInfoResponseBodyWithdrawProviders expectedWithdrawProviders = new GetPaymentServiceInfoResponseBodyWithdrawProviders();
        expectedWithdrawProviders.setNuvei((Nuvei) new Nuvei().id("userNUVEI_MAZOOMA_ACH").providerName("NUVEI_MAZOOMA_ACH_REDEEM").currencyType(CurrencyType.FIAT));
        expectedWithdrawProviders.setPrizeout((Prizeout) new Prizeout().id("userPRIZEOUT").key("passPRIZEOUT").providerName("PRIZEOUT").currencyType(CurrencyType.FIAT));
        expectedWithdrawProviders.setSkrill((Skrill) new Skrill().providerName("SKRILL_REDEEM").currencyType(CurrencyType.FIAT));
        expectedWithdrawProviders.setAeropay((AeroPay) new AeroPay().providerName("AEROPAY").currencyType(CurrencyType.FIAT));
        expectedWithdrawProviders.setPayper(Payper.builder()
                .providerName("PAYPER")
                .currencyType(CurrencyType.FIAT)
                .build()
        );
        GetPaymentServiceInfoResponseBody expected = new GetPaymentServiceInfoResponseBody();
        expected.setThirdPartyCheckEnabled(false);
        expected.setSoftKycRequiredEnabled(false);
        expected.setCardRegistrationModify(false);
        expected.setSoftKycRequiredEnabled(false);
        expected.setSoftKycAutoCompleteAddress(false);
        expected.setSupportedCurrencies(List.of(CurrencyType.CRYPTO, CurrencyType.FIAT));
        expected.setPurchaseProviders(expectedPurchaseProviders);
        expected.setWithdrawProviders(expectedWithdrawProviders);
        return expected;
    }

    private void setUpPurchaseProvider(PurchaseProviderSpec provider, Transaction tx) {
        PaymentWorkerProviderDataGenerator.genPurchaseProvider(ebean, provider, provider.getIntegrationTypeSpec(), tx);
        if (provider.getSecret() != null) {
            cloud.addUps(new PlainServiceInfo(provider.getSecret(),
                    "http://user%s:pass%s@localhost:%d?gateway_token=%s&store_id=%s&merchantId=%s&merchant=%s&gateway=%s&merchantName=%s"
                            .formatted(provider, provider, mockPort, PlatformUtil.randomUUID(), provider.code(), provider.code(), provider, provider, provider)));
        }
    }

    private void setUpWithdrawProvider(RedeemProviderSpec provider, Transaction tx) {
        PaymentWorkerProviderDataGenerator.genWithdrawProvider(ebean, provider, provider.getIntegrationType(), tx);
        if (provider.getSecret() != null) {
            cloud.addUps(new PlainServiceInfo(provider.getSecret(), "http://user%s:pass%s@localhost:%d".formatted(provider, provider, mockPort)));
        }
    }
}
