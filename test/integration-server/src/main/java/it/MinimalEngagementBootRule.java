package it;

import java.util.List;

import org.apache.commons.lang3.time.StopWatch;
import org.junit.jupiter.api.extension.ExtensionContext;


import io.ebean.event.ShutdownManager;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public class MinimalEngagementBootRule extends AbstractMinimalConfigurableBootRule {
    public static final String CONTEXT_CORE = "engagement";
    @Delegate
    private EngagementBootRuleContext ctx;
    private EngagementTestDataOperations operations;

    @Override
    public void beforeAll(ExtensionContext context) throws Exception {
        super.beforeAll(context);
        var globalNs = context.getRoot().getStore(ExtensionContext.Namespace.GLOBAL);
        var commonRule = globalNs.getOrComputeIfAbsent(DefaultBootRuleContext.class);
        operations = new EngagementTestDataOperations(commonRule);
        globalNs.getOrComputeIfAbsent(CONTEXT_CORE, t -> {
            log.debug("about to init: {}", t);
            StopWatch stopWatch = StopWatch.createStarted();

            try {
                ctx = toContext(context, commonRule);
                return ctx;
            } catch (Throwable err) {
                log.error(err.getMessage(), err);
                throw new RuntimeException(err);
            } finally {
                ShutdownManager.deregisterShutdownHook();
                log.debug("completed init: {} in {}", t, stopWatch);
            }
        });
    }

    private EngagementBootRuleContext toContext(ExtensionContext context, DefaultBootRuleContext commonRule) throws Throwable {
        EngagementBootRuleContext bootRule = new EngagementBootRuleContext();
        bootRule.setServer(startServer(commonRule, context));
        bootRule.setGatewayServer(startGateway(commonRule, context));
        bootRule.setWorkerServer(startWorkerServer(commonRule, context));
        bootRule.setFrontendServer(startFrontend(commonRule, context));
        bootRule.setAdminServer(startAdmin(commonRule, context));
        bootRule.accept(operations);
        return bootRule;
    }

    private EngagementWorkerServerResource startWorkerServer(DefaultBootRuleContext commonRule, ExtensionContext context) throws Exception {
        var store = context.getRoot().getStore(ExtensionContext.Namespace.GLOBAL);

        var workerServerResource = new EngagementWorkerServerResource(newProps().cfg(), commonRule);
        workerServerResource.beforeAll(context);
        store.put(EngagementWorkerServerResource.class, workerServerResource);

        return workerServerResource;
    }

    protected EngagementServerResource startServer(DefaultBootRuleContext commonRule, ExtensionContext context) throws Exception {
        var store = context.getRoot().getStore(ExtensionContext.Namespace.GLOBAL);

        var server = new EngagementServerResource(newProps().cfg(), commonRule);
        server.beforeAll(context);
        store.put(EngagementServerResource.class, server);

        return server;
    }

    protected EngagementGatewayResource startGateway(DefaultBootRuleContext commonRule, ExtensionContext context) throws Exception {
        var store = context.getRoot().getStore(ExtensionContext.Namespace.GLOBAL);

        var gatewayResource = new EngagementGatewayResource(newProps().cfg(), commonRule);
        gatewayResource.beforeAll(context);
        store.put(EngagementGatewayResource.class, gatewayResource);

        return gatewayResource;
    }

    protected EngagementFrontendResource startFrontend(DefaultBootRuleContext commonRule, ExtensionContext context) throws Exception {
        var store = context.getRoot().getStore(ExtensionContext.Namespace.GLOBAL);

        var frontend = new EngagementFrontendResource(newProps().cfg(), commonRule);
        frontend.beforeAll(context);
        store.put(EngagementFrontendResource.class, frontend);

        return frontend;
    }

    protected EngagementAdminResource startAdmin(DefaultBootRuleContext commonRule, ExtensionContext context) throws Exception {
        var store = context.getRoot().getStore(ExtensionContext.Namespace.GLOBAL);

        var server = new EngagementAdminResource(newProps().cfg(), commonRule);
        server.beforeAll(context);
        store.put(EngagementAdminResource.class, server);

        return server;
    }

    @Override
    public List<AbstractConfigurableServerResource<?, ?>> serverResources() {
        return List.of(
                getServer(),
                getFrontendServer(),
                getGatewayServer(),
                getAdminServer(),
                getWorkerServer()
        );
    }

}
