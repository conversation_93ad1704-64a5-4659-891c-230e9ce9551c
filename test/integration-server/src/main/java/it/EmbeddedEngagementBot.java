package it;

import api.v1.PlatformSpec;
import bots.EngagementFrontendBot;
import bots.crm.impl.DefaultCrmBot;
import com.turbospaces.json.CommonObjectMapper;

public class EmbeddedEngagementBot extends EngagementFrontendBot {

    public EmbeddedEngagementBot(DefaultCrmBot crmBot, EngagementBootRuleContext engagementCtx, PlatformSpec platform) throws Throwable {
        this(crmBot, engagementCtx.getServer(), platform);
    }

    public EmbeddedEngagementBot(DefaultCrmBot crmBot, AbstractServerResource<?, ?> frontend, PlatformSpec platform) throws Throwable {
        super(
                crmBot,
                frontend.toHttpUrl(),
                frontend.toSecondaryHttpUrl(),
                TestDataOperations.BRAND_NAME,
                platform,
                frontend.getCtx().getBean(CommonObjectMapper.class)
        );
    }
}
