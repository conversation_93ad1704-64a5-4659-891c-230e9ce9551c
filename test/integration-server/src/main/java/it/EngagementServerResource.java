package it;

import static loyalty.LoyaltyProto.LOYALTY_POSTGRES_APP;
import static loyalty.LoyaltyProto.LOYALTY_POSTGRES_OWNER;
import static offerchain.OfferChainProto.OFFERCHAIN_POSTGRES_APP;
import static offerchain.OfferChainProto.OFFERCHAIN_POSTGRES_OWNER;
import static org.mockito.Mockito.spy;
import static quest.QuestProto.QUEST_POSTGRES_APP;
import static quest.QuestProto.QUEST_POSTGRES_OWNER;
import static randomreward.RandomRewardProto.RANDOM_REWARD_POSTGRES_APP;
import static randomreward.RandomRewardProto.RANDOM_REWARD_POSTGRES_OWNER;
import static randomreward.RandomRewardProto.RANDOM_REWARD_QUARTZ_APP;
import static reward.RewardProto.REWARD_POSTGRES_APP;
import static reward.RewardProto.REWARD_POSTGRES_OWNER;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.flywaydb.core.api.configuration.FluentConfiguration;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.util.ResourceUtils;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.ebean.DataGeneratorUtil;
import com.turbospaces.plugins.FlywayBootstrapInitializer;
import com.turbospaces.ups.RawServiceInfo;

import engagement.EngagementServerProperties;
import engagement.EngagementServerStarter;
import engagement.QuestEntities;
import engagement.di.EngagementServerDiModule;
import engagement.model.quest.QuestlineBrand;
import gamehub.GameHubServiceApi;

import jakarta.persistence.Table;

import lombok.Getter;
import loyalty.LoyaltyEntities;
import loyalty.LoyaltyServerProperties;
import loyalty.di.LoyaltyEmbeddedServerDiModule;
import loyalty.model.LoyaltyBrand;
import model.Schemas;
import offerchain.OfferChainEbeanJpaManager;
import offerchain.OfferChainEntities;
import offerchain.OfferChainServerProperties;
import offerchain.di.OfferChainEmbeddedServerDiModule;
import offerchain.model.OfferChainBrand;
import quest.QuestServerProperties;
import quest.di.QuestEmbeddedServerDiModule;
import randomreward.RandomRewardEntities;
import randomreward.RandomRewardServerProperties;
import randomreward.di.RandomRewardServerDiModule;
import reward.RewardEntities;
import reward.RewardServerProperties;
import reward.di.RewardServerDiModule;
import reward.model.RewardBrand;
import uam.model.BackofficeEvent;

@Getter
public class EngagementServerResource extends AbstractConfigurableServerResource<EngagementServerProperties, SimpleBootstrap> {
    public static final String BRAND_NAME = TestDataOperations.BRAND_NAME;
    private final DefaultBootRuleContext rule;
    private final Class<?>[] mainClasses;
    private DynamicCloud cloud;
    private EngagementServerProperties properties;

    private static final List<Class<?>> IGNORE_TRUNCATE = List.of(OfferChainBrand.class, RewardBrand.class, QuestlineBrand.class, LoyaltyBrand.class);

    public EngagementServerResource(ApplicationConfig cfg, DefaultBootRuleContext rule) throws IOException {
        this(cfg, rule, TestConfiguration.class);
    }

    public EngagementServerResource(ApplicationConfig cfg, DefaultBootRuleContext rule, Class<?>... mainClasses) throws IOException {
        super(cfg);
        this.rule = Objects.requireNonNull(rule);
        this.mainClasses = Objects.requireNonNull(mainClasses);
        cfg.loadDefaultPropsFromResource(ResourceUtils.getURL("classpath:engagement-server.properties"));
    }

    @Override
    public void beforeAll(ExtensionContext extensionContext) throws Exception {
        try {
            var props = new EngagementServerProperties(cfg.factory());

            cfg.setLocalProperty(props.APP_JMX_DOMAIN.getKey(), "engagement-server");
            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");
            cfg.setLocalProperty(props.offerChainServerProperties.OFFERCHAIN_BRANDS_AUTOCONFIGURATION_LIST.getKey(), BRAND_NAME);
            cfg.setLocalProperty(props.questServerProperties.BRANDS_AUTOCONFIGURATION_LIST.getKey(), BRAND_NAME);
            cfg.setLocalProperty(props.GAME_ROUND_EVENT_LISTENING_ENABLED.getKey(), true);
            cfg.setLocalProperty(props.KAFKA_BATCH_FETCH_MAX_WAIT.getKey(), Duration.ofMillis(1));

            setBootstrap(new SimpleBootstrap(props, mainClasses));

            // ~ common UPSs
            bootstrap.addUps(new PostgresqlServiceInfo(LOYALTY_POSTGRES_APP, rule.getPasi().getUri()));
            bootstrap.addUps(new PostgresqlServiceInfo(LOYALTY_POSTGRES_OWNER, rule.getPasi().getUri()));

            bootstrap.addUps(new PostgresqlServiceInfo(REWARD_POSTGRES_APP, rule.getPasi().getUri()));
            bootstrap.addUps(new PostgresqlServiceInfo(REWARD_POSTGRES_OWNER, rule.getPasi().getUri()));

            bootstrap.addUps(new PostgresqlServiceInfo(OFFERCHAIN_POSTGRES_APP, rule.getPasi().getUri()));
            bootstrap.addUps(new PostgresqlServiceInfo(OFFERCHAIN_POSTGRES_OWNER, rule.getPasi().getUri()));

            bootstrap.addUps(new PostgresqlServiceInfo(QUEST_POSTGRES_APP, rule.getPasi().getUri()));
            bootstrap.addUps(new PostgresqlServiceInfo(QUEST_POSTGRES_OWNER, rule.getPasi().getUri()));

            bootstrap.addUps(new PostgresqlServiceInfo(RANDOM_REWARD_POSTGRES_APP, rule.getPasi().getUri()));
            bootstrap.addUps(new PostgresqlServiceInfo(RANDOM_REWARD_POSTGRES_OWNER, rule.getPasi().getUri()));

            bootstrap.addUps(new PostgresqlServiceInfo(RANDOM_REWARD_QUARTZ_APP, rule.getPosi().getUri()));
            bootstrap.addUps(rule.getKsi());
            bootstrap.addUps(rule.getRsi());
            bootstrap.addUps(new RawServiceInfo("jwt", rule.getJwtSecret().getBytes()));

            bootstrap.addBootstrapRegistryInitializer(
                    new FlywayBootstrapInitializer(props, () -> "db/offerchain-migration", rule.getPosi(), new OfferChainEntities(), Schemas.OFFER_CHAIN) {
                        @Override
                        protected void configureMigration(FluentConfiguration config) {
                            super.configureMigration(config);
                            var ocp = new OfferChainServerProperties(cfg.factory());
                            ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                            m.put(ocp.OFFERCHAIN_PARTITIONING_DEFAULT_PRECISION.getKey(), ocp.OFFERCHAIN_PARTITIONING_DEFAULT_PRECISION.get());
                            m.put(ocp.OFFERCHAIN_PARTITIONING_DEFAULT_SCALE.getKey(), Integer.toString(ocp.OFFERCHAIN_PARTITIONING_DEFAULT_SCALE.get()));
                            config.placeholders(m.build());
                            config.baselineOnMigrate(true);
                            config.baselineVersion("0");
                        }
                    });
            bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, () -> "db/loyalty-migration", rule.getPosi(), new LoyaltyEntities(), Schemas.LOYALTY) {
                @Override
                protected void configureMigration(FluentConfiguration config) {
                    super.configureMigration(config);
                    var lp = new LoyaltyServerProperties(cfg.factory());
                    ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                    m.put(lp.PARTITIONING_DEFAULT_LOYALTY_PRECISION.getKey(), lp.PARTITIONING_DEFAULT_LOYALTY_PRECISION.get());
                    m.put(lp.PARTITIONING_DEFAULT_LOYALTY_SCALE.getKey(), Integer.toString(lp.PARTITIONING_DEFAULT_LOYALTY_SCALE.get()));

                    config.placeholders(m.build());
                }
            });
            bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, () -> "db/reward-migration", rule.getPosi(), new RewardEntities(), Schemas.REWARD) {
                @Override
                protected void configureMigration(FluentConfiguration config) {
                    super.configureMigration(config);
                    var rw = new RewardServerProperties(cfg.factory());
                    ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                    m.put(rw.PARTITIONING_DEFAULT_REWARD_PRECISION.getKey(), rw.PARTITIONING_DEFAULT_REWARD_PRECISION.get());
                    m.put(rw.PARTITIONING_DEFAULT_REWARD_SCALE.getKey(), Integer.toString(rw.PARTITIONING_DEFAULT_REWARD_SCALE.get()));

                    config.placeholders(m.build());
                    config.baselineOnMigrate(true);
                    config.baselineVersion("0");
                }
            });
            bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, () -> "db/questline-migration", rule.getPosi(), new QuestEntities(), Schemas.QUESTLINE) {
                @Override
                protected void configureMigration(FluentConfiguration config) {
                    super.configureMigration(config);
                    var rw = new QuestServerProperties(cfg.factory());
                    ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                    m.put(rw.PARTITIONING_DEFAULT_QUEST_PRECISION.getKey(), rw.PARTITIONING_DEFAULT_QUEST_PRECISION.get());
                    m.put(rw.PARTITIONING_DEFAULT_QUEST_SCALE.getKey(), Integer.toString(rw.PARTITIONING_DEFAULT_QUEST_SCALE.get()));

                    config.placeholders(m.build());
                    config.baselineOnMigrate(true);
                    config.baselineVersion("0");
                }
            });
            bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, () -> "db/random-reward-migration", rule.getPosi(), new RandomRewardEntities(), Schemas.RANDOM_REWARD) {
                @Override
                protected void configureMigration(FluentConfiguration config) {
                    super.configureMigration(config);

                    var rrprops = new RandomRewardServerProperties(cfg.factory());
                    ImmutableMap.Builder<String, String> m = ImmutableMap.builder();
                    m.put(rrprops.PARTITIONING_DEFAULT_RANDOM_REWARD_PRECISION.getKey(), rrprops.PARTITIONING_DEFAULT_RANDOM_REWARD_PRECISION.get());
                    m.put(rrprops.PARTITIONING_DEFAULT_RANDOM_REWARD_SCALE.getKey(), Integer.toString(rrprops.PARTITIONING_DEFAULT_RANDOM_REWARD_SCALE.get()));

                    config.placeholders(m.build());
                    config.baselineOnMigrate(true);
                    config.baselineVersion("0");
                }
            });
            EngagementServerStarter.registerHealthchecks(bootstrap, EngagementServerStarter.createTopics(props), rule.getRsi());
            ctx = bootstrap.run();
            cloud = bootstrap.cloud();
            properties = props;
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    @Override
    public void beforeEach(ExtensionContext extensionContext) throws Exception {
        super.beforeEach(extensionContext);
        var list = ImmutableList.<Class<?>> builder()
                .addAll(new OfferChainEntities())
                .addAll(new LoyaltyEntities())
                .addAll(new QuestEntities())
                .addAll(new RewardEntities())
                .addAll(new RandomRewardEntities())
                .build()
                .stream()
                .filter(c -> {
                    Table table = c.getAnnotation(Table.class);
                    return table != null && (table.schema().equals(Schemas.OFFER_CHAIN)
                            || table.schema().equals(Schemas.REWARD)
                            || table.schema().equals(Schemas.QUESTLINE)
                            || table.schema().equals(Schemas.LOYALTY));
                }).filter(c -> !IGNORE_TRUNCATE.contains(c)).collect(Collectors.toCollection(ArrayList::new));
        list.add(BackofficeEvent.class);

        retry(() -> DataGeneratorUtil.clearPostgresDb(getCtx().getBean(OfferChainEbeanJpaManager.class), list));
    }

    @Configuration
    @Import({
            EngagementServerDiModule.class,
            RewardServerDiModule.class,
            LoyaltyEmbeddedServerDiModule.class,
            OfferChainEmbeddedServerDiModule.class,
            QuestEmbeddedServerDiModule.class,
            RandomRewardServerDiModule.class
    })
    public static class TestConfiguration {
        @Primary
        @Bean
        public GameHubServiceApi primaryGameHubServiceApi(GameHubServiceApi gameHubServiceApi) {
            return spy(gameHubServiceApi);
        }
    }
}
