package it;

import java.math.BigDecimal;
import java.security.SecureRandom;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

import com.turbospaces.common.DisposableCountdownLatch;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;

import admin.loyalty.model.ActionType;
import admin.loyalty.model.SetAccountLoyaltySystemXpAdminRequest;
import api.v1.PlatformSpec;
import api.v1.ProviderSpec;
import bots.QuestAdminBot;
import bots.RandomRewardAdminBot;
import bots.RewardAdminBot;
import bots.RewardFrontendBot;
import bots.auth.AuthBot;
import bots.crm.CrmBot;
import bots.loyalty.LoyaltyAdminBot;
import bots.loyalty.LoyaltyFrontendBot;
import bots.loyalty.LoyaltyGatewayBot;
import bots.loyalty.VariantAdminBot;
import bots.offerchain.OfferChainAdminBot;
import bots.offerchain.OfferChainFrontendBot;
import bots.offerchain.OfferChainGatewayBot;
import bots.payment.BasicPaymentBot;
import lombok.Getter;
import lombok.experimental.Delegate;
import loyalty.gateway.dto.SetAccountXpLoyaltySystemRequest;
import payment.type.OfferPlatformSpec;
import uam.UamServerProperties;

@Getter
public class EmbeddedCompositeBot implements AutoCloseable {

    private final OfferChainAdminBot offerchainAdminBot;
    private final OfferChainFrontendBot offerchainFrontendBot;
    private final OfferChainGatewayBot offerchainGatewayBot;

    private final QuestAdminBot questAdminBot;

    private final RewardAdminBot rewardAdminBot;
    private final RandomRewardAdminBot randomRewardAdminBot;
    private final RewardFrontendBot rewardFrontendBot;

    private final EmbeddedEngagementBot engagementBot;

    private final LoyaltyAdminBot loyaltyAdminBot;
    private final VariantAdminBot variantAdminBot;
    private final LoyaltyGatewayBot loyaltyGatewayBot;
    private final LoyaltyFrontendBot loyaltyFrontendBot;
    @Delegate(types = { CrmBot.class })
    private final EmbeddedCrmBot crmBot;
    @Delegate(types = { AuthBot.class })
    private final EmbeddedAuthBot authBot;
    @Delegate(types = { BasicPaymentBot.class })
    private final EmbeddedPaymentBot paymentBot;

    // Add these fields for XP update tracking
    private final AtomicBoolean receivedXpUpdate = new AtomicBoolean(false);
    private DisposableCountdownLatch xpUpdateLatch;

    public EmbeddedCompositeBot(
            DefaultBootRuleContext commonRule,
            EngagementBootRuleContext engagementCtx,
            CrmBootRuleContext crmCtx,
            AuthBootRuleContext authCtx,
            PaymentBootRuleContext paymentCtx,
            PlatformSpec platform) throws Throwable {

        var props = crmCtx.getCrmServer().getCtx().getBean(UamServerProperties.class);
        var frontend = engagementCtx.getFrontendServer();
        props.cfg().setLocalProperty(props.GAME_ROUND_EVENT_POPULATE_TRANSACTIONS.getKey(), true);

        crmBot = new EmbeddedCrmBot(crmCtx.getGatewayServer(), crmCtx.getFrontendServer(), platform);
        authBot = new EmbeddedAuthBot(crmBot, commonRule, authCtx, platform);
        paymentBot = new EmbeddedPaymentBot(crmBot, authBot, paymentCtx, platform);

        engagementBot = new EmbeddedEngagementBot(crmBot, engagementCtx.getFrontendServer(), platform);

        offerchainAdminBot = new OfferChainAdminBot(engagementCtx.getAdminServer().toHttpUrl(),
                engagementCtx.getAdminServer().getCtx().getBean(CommonObjectMapper.class));
        offerchainFrontendBot = new OfferChainFrontendBot(crmBot, frontend.toHttpUrl(), frontend.toSecondaryHttpUrl(),
                TestDataOperations.BRAND_NAME, platform, frontend.getCtx().getBean(CommonObjectMapper.class));
        offerchainGatewayBot = new OfferChainGatewayBot(
                engagementCtx.getGatewayServer().toHttpUrl(),
                engagementCtx.getGatewayServer().getCtx().getBean(CommonObjectMapper.class));

        rewardFrontendBot = new RewardFrontendBot(crmBot, frontend.toHttpUrl(), frontend.toSecondaryHttpUrl(),
                TestDataOperations.BRAND_NAME, platform, frontend.getCtx().getBean(CommonObjectMapper.class));
        loyaltyFrontendBot = new LoyaltyFrontendBot(crmBot, frontend.toHttpUrl(), frontend.toSecondaryHttpUrl(),
                TestDataOperations.BRAND_NAME, platform, frontend.getCtx().getBean(CommonObjectMapper.class));
        rewardAdminBot = new RewardAdminBot(engagementCtx.getAdminServer().toHttpUrl(),
                engagementCtx.getAdminServer().getCtx().getBean(CommonObjectMapper.class));
        randomRewardAdminBot = new RandomRewardAdminBot(engagementCtx.getAdminServer().toHttpUrl(),
                engagementCtx.getAdminServer().getCtx().getBean(CommonObjectMapper.class));

        questAdminBot = new QuestAdminBot(engagementCtx.getAdminServer().toHttpUrl(),
                engagementCtx.getAdminServer().getCtx().getBean(CommonObjectMapper.class));

        loyaltyAdminBot = new LoyaltyAdminBot(engagementCtx.getAdminServer().toHttpUrl(),
                engagementCtx.getAdminServer().getCtx().getBean(CommonObjectMapper.class));
        variantAdminBot = new VariantAdminBot(engagementCtx.getAdminServer().toHttpUrl(),
                engagementCtx.getAdminServer().getCtx().getBean(CommonObjectMapper.class));
        loyaltyGatewayBot = new LoyaltyGatewayBot(
                engagementCtx.getGatewayServer().toHttpUrl(),
                engagementCtx.getGatewayServer().getCtx().getBean(CommonObjectMapper.class));
    }

    public void connect() throws Exception {
        crmBot.connect();

        authBot.attachCrmInfo(crmBot);
        authBot.connect();

        paymentBot.attachCrmInfo(crmBot);
        paymentBot.connect();

        engagementBot.attachCrmInfo(crmBot);
        engagementBot.connect();

        offerchainFrontendBot.attachCrmInfo(crmBot);
        offerchainFrontendBot.connect();

        rewardFrontendBot.attachCrmInfo(crmBot);
        rewardFrontendBot.connect();

        loyaltyFrontendBot.attachCrmInfo(crmBot);
        loyaltyFrontendBot.connect();

        paymentBot.attachCrmInfo(crmBot);
        paymentBot.connect();
    }

    @Override
    public void close() throws Exception {
        if (xpUpdateLatch != null) {
            xpUpdateLatch.dispose();
        }
        crmBot.close();
        engagementBot.close();
        offerchainFrontendBot.close();
        rewardFrontendBot.close();
        loyaltyFrontendBot.close();
        authBot.close();
        paymentBot.close();
    }

    public double gcBalance() {
        return crmBot.balance(crmBot.goldCurrency()).amount.doubleValue();
    }

    public double scBalance() {
        return crmBot.balance(crmBot.sweepstakeCurrency()).amount.doubleValue();
    }

    public void acceptOffer(PaymentTestDataOperations paymentUtil, BigDecimal amount) throws Exception {
        var offerCode = paymentUtil.genOffer(amount, OfferPlatformSpec.WEB);
        paymentBot.createOrder(offerCode).assertNtf();
    }

    public void doGamePlay(ProviderSpec productSpec) throws Exception {
        var product = anySlot(productSpec);
        var tempToken = getTempToken(productSpec.code(), product, sweepstakeCurrency());
        var verifyTokenResponse = verifyToken(tempToken.token);
        String permToken = verifyTokenResponse.getCashierToken();
        String customerId = verifyTokenResponse.getCustomerId();

        var roundId = PlatformUtil.randomUUID();
        relaxDebit(product, customerId, permToken, roundId, new SecureRandom().nextInt(Integer.MAX_VALUE - 1), BigDecimal.TEN);
        relaxCredit(product, customerId, permToken, roundId, new SecureRandom().nextInt(Integer.MAX_VALUE - 1), BigDecimal.TEN);
        relaxDebit(product, customerId, permToken, roundId, new SecureRandom().nextInt(Integer.MAX_VALUE - 1), BigDecimal.TEN);
        relaxDebit(product, customerId, permToken, roundId, new SecureRandom().nextInt(Integer.MAX_VALUE - 1), BigDecimal.ONE);
    }

    /**
     * Simulates earning loyalty XP for the user through admin API
     */
    public void earnLoyaltyXp(String xpVariantCode, BigDecimal xpAmount) throws Exception {
        var request = new SetAccountLoyaltySystemXpAdminRequest();
        request.accountId = flatten().getAccountId();
        request.variantCode = xpVariantCode;
        request.amount = xpAmount;
        request.type = ActionType.ADD;
        request.date = java.time.LocalDate.now();

        loyaltyAdminBot.setAccountXp(request);
    }

    /**
     * Alternative method to simulate XP earning via gateway (Bloomreach pattern)
     */
    public void earnLoyaltyXpViaGateway(String xpVariantCode, BigDecimal xpAmount) throws Exception {
        var gatewayRequest = new SetAccountXpLoyaltySystemRequest();
        gatewayRequest.uuids = Set.of(flatten().writeExternal());
        gatewayRequest.variantCode = xpVariantCode;
        gatewayRequest.amount = xpAmount;
        gatewayRequest.type = loyalty.gateway.dto.ActionType.ADD;
        gatewayRequest.brand = TestDataOperations.BRAND_NAME;
        gatewayRequest.requestId = PlatformUtil.randomUUID().toString();
        gatewayRequest.date = java.time.LocalDate.now();

        loyaltyGatewayBot.setAccountXp(gatewayRequest);
    }

    /**
     * Helper method to simulate earning XP from a non-quest source
     */
    public void earnXpFromNonQuestSource(String xpVariantCode, BigDecimal xpAmount) throws Exception {
        // Use the admin API for simplicity
        earnLoyaltyXp(xpVariantCode, xpAmount);
    }

    /**
     * Gets current XP balance for a specific variant (using frontend API)
     */
    public BigDecimal getXpBalance(String xpVariantCode) throws Exception {
        var variants = loyaltyFrontendBot.getAccountVariants();
        return variants.getVariants().stream()
                .filter(variant -> xpVariantCode.equals(variant.getVariantCode()))
                .findFirst()
                .map(variant -> variant.getPoints())
                .orElse(BigDecimal.ZERO);
    }
}