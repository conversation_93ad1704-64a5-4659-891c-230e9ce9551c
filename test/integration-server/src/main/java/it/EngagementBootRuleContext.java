package it;

import engagement.repo.QuestEbeanJpaManager;
import io.vavr.CheckedConsumer;
import lombok.Getter;
import lombok.Setter;
import loyalty.LoyaltyEbeanJpaManager;
import offerchain.OfferChainEbeanJpaManager;
import randomreward.RandomRewardJpaManager;
import reward.RewardEbeanJpaManager;

@Getter
@Setter
public class EngagementBootRuleContext implements CheckedConsumer<EngagementTestDataOperations> {
    private EngagementServerResource server;
    private EngagementFrontendResource frontendServer;
    private EngagementGatewayResource gatewayServer;
    private EngagementWorkerServerResource workerServer;
    private EngagementAdminResource adminServer;

    @Override
    public void accept(EngagementTestDataOperations engagementTestDataOperations) {
        //
        // ~ settings
        //
    }

    public OfferChainEbeanJpaManager offerChainEbean() {
        return server.getCtx().getBean(OfferChainEbeanJpaManager.class);
    }

    public RewardEbeanJpaManager rewardEbean() {
        return server.getCtx().getBean(RewardEbeanJpaManager.class);
    }

    public RandomRewardJpaManager randomRewardEbean() {
        return server.getCtx().getBean(RandomRewardJpaManager.class);
    }

    public LoyaltyEbeanJpaManager loyaltyEbean() {
        return server.getCtx().getBean(LoyaltyEbeanJpaManager.class);
    }

    public QuestEbeanJpaManager questEbean() {
        return server.getCtx().getBean(QuestEbeanJpaManager.class);
    }

}
