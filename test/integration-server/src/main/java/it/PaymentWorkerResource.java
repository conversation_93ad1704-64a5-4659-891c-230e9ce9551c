package it;

import java.time.Duration;
import java.util.Objects;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.mockito.Mockito;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.util.ResourceUtils;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.ebean.DataGeneratorUtil;
import com.turbospaces.plugins.FlywayBootstrapInitializer;
import com.turbospaces.ups.PlainServiceInfo;

import jakarta.persistence.Table;
import lombok.Getter;
import lombok.SneakyThrows;
import model.Schemas;
import payment.PaymentProto;
import payment.bi.BiApiJaxRsClient;
import payment.worker.PaymentWorkerEbeanJpaManager;
import payment.worker.PaymentWorkerOnlyEntities;
import payment.worker.PaymentWorkerProto;
import payment.worker.PaymentWorkerServerProperties;
import payment.worker.di.PaymentWorkerServerDiModule;
import payment.worker.job.AbstractPaymentWorkerJob;
import uam.api.UamServiceApi;

@Getter
public class PaymentWorkerResource extends AbstractConfigurableServerResource<PaymentWorkerServerProperties, SimpleBootstrap> {
    private final DefaultBootRuleContext rule;
    private final Class<?>[] mainClasses;
    private DynamicCloud cloud;

    public PaymentWorkerResource(ApplicationConfig cfg, DefaultBootRuleContext rule) {
        this(cfg, rule, TestConfiguration.class);
    }

    @SneakyThrows
    public PaymentWorkerResource(ApplicationConfig cfg, DefaultBootRuleContext rule, Class<?>... mainClasses) {
        super(cfg);
        this.rule = Objects.requireNonNull(rule);
        this.mainClasses = Objects.requireNonNull(mainClasses);
        cfg.loadDefaultPropsFromResource(ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "payment-worker-server.properties"));
    }

    @Override
    public void beforeAll(ExtensionContext context) throws Exception {
        try {
            PaymentWorkerServerProperties props = new PaymentWorkerServerProperties(cfg.factory());

            cfg.setLocalProperty(props.APP_JMX_DOMAIN.getKey(), "payment-worker-server");
            cfg.setLocalProperty(props.QUARTZ_JOBSTORE_IS_CLUSTERED.getKey(), true);
            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");
            cfg.setLocalProperty(props.SYNC_WITHDRAWALS_JOB_ENABLED.getKey(), false);
            cfg.setLocalProperty(props.KAFKA_BATCH_FETCH_MAX_WAIT.getKey(), Duration.ofMillis(1));
            cfg.setLocalProperty("event-streaming.clickhouse.enabled", true);

            //
            cfg.setLocalProperty(props.QUARTZ_JOBSTORE_IS_CLUSTERED.getKey(), true);
            cfg.setLocalProperty(props.APP_DB_MIGRATION_PATH.getKey(), "db/payment-worker-migration");

            setBootstrap(new SimpleBootstrap(props, mainClasses));

            // ~ common UPSs
            bootstrap.addUps(rule.getPosi());
            bootstrap.addUps(rule.getPasi());
            bootstrap.addUps(rule.getKsi());
            bootstrap.addUps(rule.getRsi());
            bootstrap.addUps(rule.getClsi());
            bootstrap.addUps(rule.getZksi());
            bootstrap.addUps(rule.getTmprlsi());

            bootstrap.addUps(new PlainServiceInfo(PaymentProto.UPS_BI_API, "fake-api"));
            bootstrap.addUps(new PostgresqlServiceInfo(PaymentWorkerProto.UPS_POSTGRES_SLAVE_READ_ONLY, rule.getPosi().getUri()));
            bootstrap.addBootstrapRegistryInitializer(new FlywayBootstrapInitializer(props, props.APP_DB_MIGRATION_PATH, rule.getPosi(), new PaymentWorkerOnlyEntities(), Schemas.PAYMENT_WORKER));
            ctx = bootstrap.run();
            cloud = bootstrap.cloud();
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    public PaymentWorkerServerProperties getProperties() {
        return getCtx().getBean(PaymentWorkerServerProperties.class);
    }

    @Override
    public void beforeEach(ExtensionContext extensionContext) throws Exception {
        super.beforeEach(extensionContext);
        retry(() -> DataGeneratorUtil.clearPostgresDb(
                getCtx().getBean(PaymentWorkerEbeanJpaManager.class),
                new PaymentWorkerOnlyEntities().stream()
                        .filter(c -> {
                            Table table = c.getAnnotation(Table.class);
                            return table != null && table.schema().equals(Schemas.PAYMENT_WORKER);
                        }).toList()
        ));
    }

    @Configuration
    @Import({PaymentWorkerServerDiModule.class})
    @ComponentScan(basePackageClasses = {
            AbstractPaymentWorkerJob.class
    })
    public static class TestConfiguration {
        @Bean
        @Primary
        public BiApiJaxRsClient spyBiApi(BiApiJaxRsClient biApi) {
            return Mockito.spy(biApi);
        }

        @Bean
        @Primary
        public UamServiceApi spyUamServiceApi(UamServiceApi uamServiceApi) {
            return Mockito.spy(uamServiceApi);
        }
    }
}
