package it;

import java.util.Objects;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;

import fe.EngagementFrontendServerProperties;
import fe.EngagementFrontendStarter;
import fe.EngagementFrontendTopics;
import fe.di.EngagementFrontendServerDiModule;
import fe.di.FrontendAcceptorsDiModule;
import fe.di.LoyaltyFrontendServerDiModule;
import fe.di.QuestFrontendEmbeddedDiModule;
import fe.di.RandomRewardFrontendServerDiModule;
import fe.di.RewardFrontendEndpointsDiModule;
import fe.services.mock.MockOtpService;
import fe.services.otp.OtpService;
import identity.GeoLocationDiModule;
import offerchain.fe.di.OfferChainFrontendEmbeddedDiModule;

public class EngagementFrontendResource extends AbstractConfigurableServerResource<EngagementFrontendServerProperties, SimpleBootstrap> {
    protected final DefaultBootRuleContext rule;

    public EngagementFrontendResource(ApplicationConfig cfg, DefaultBootRuleContext rule) {
        super(cfg);
        this.rule = Objects.requireNonNull(rule);
    }

    @Override
    public void beforeAll(ExtensionContext context) throws Exception {
        try {
            var props = new EngagementFrontendServerProperties(cfg.factory());

            cfg.setLocalProperty(props.CLOUD_APP_ID.getKey(), "engagement-frontend");
            cfg.setLocalProperty(props.CLOUD_APP_NAME.getKey(), "com.patrianna:engagement-frontend:latest");
            cfg.setLocalProperty(props.APP_JMX_DOMAIN.getKey(), "frontend");
            cfg.setLocalProperty(props.APP_DB_MIGRATION_PATH.getKey(), "db/spanner-migration");
            cfg.setLocalProperty(props.KAFKA_AUTO_OFFSET_RESET.getKey(), "earliest");
            cfg.setLocalProperty(props.REQUEST_REPLY_TOPIC.getKey(), EngagementFrontendTopics.RESP.name().toString());

            setBootstrap(new SimpleBootstrap(props, RootConfiguration.class));

            bootstrap.addUps(rule.getKsi());
            bootstrap.addUps(rule.getRsi());
            bootstrap.addUps(rule.getMsi());

            EngagementFrontendStarter.registerHealthchecks(bootstrap, EngagementFrontendStarter.createTopics(props));
            this.ctx = bootstrap.run();
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    @Import({
            FrontendAcceptorsDiModule.class,
            GeoLocationDiModule.class,
            EngagementFrontendServerDiModule.class,
            LoyaltyFrontendServerDiModule.class,
            RewardFrontendEndpointsDiModule.class,
            OfferChainFrontendEmbeddedDiModule.class,
            QuestFrontendEmbeddedDiModule.class,
            RandomRewardFrontendServerDiModule.class
    })
    @Configuration
    public static class RootConfiguration {
        @Bean
        public OtpService skipOtpService() {
            return new MockOtpService();
        }
    }
}