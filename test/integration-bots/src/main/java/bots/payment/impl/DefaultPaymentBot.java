package bots.payment.impl;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

import fe.api.payment.model.ConfirmPaymentUserAccountRequestBody;
import fe.api.payment.model.ConfirmPaymentUserAccountResponseBody;
import fe.api.payment.model.CreateNewAeroPayMethodRequestBody;
import fe.api.payment.model.CreateNewAeroPayMethodResponseBody;
import fe.api.payment.model.GetPaymentServiceBankDetailsRequestBody;
import fe.api.payment.model.GetPaymentServiceBankDetailsResponseBody;
import fe.api.payment.model.GetWidgetRequestBody;
import fe.api.payment.model.GetWidgetResponseBody;
import fe.api.payment.model.GetWithdrawSettingsRequestBody;
import fe.api.payment.model.GetWithdrawSettingsResponseBody;
import fe.api.payment.model.LinkBankAccountRequestBody;
import fe.api.payment.model.LinkBankAccountResponseBody;
import fe.api.payment.model.RefreshOrderRequestBody;
import fe.api.payment.model.RefreshOrderResponseBody;
import fe.api.payment.model.RegisterPaymentUserAccountRequestBody;
import fe.api.payment.model.RegisterPaymentUserAccountResponseBody;
import fe.api.payment.model.RewardOfferUpdateNotification;
import org.apache.commons.lang3.function.Consumers;
import org.apache.commons.lang3.mutable.MutableObject;

import com.turbospaces.common.DisposableCountdownLatch;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.HttpProto;
import com.turbospaces.http.UnexpectedHttpClientException;
import com.turbospaces.json.CommonObjectMapper;

import api.v1.Code;
import api.v1.JsonSignMethodSpec;
import api.v1.PlatformSpec;
import bots.auth.impl.DefaultAuthBot;
import bots.crm.impl.DefaultCrmBot;
import bots.payment.BasicPaymentBot;
import bots.payment.ResponseWrapper;
import fe.api.payment.CaptureRedeemOptionRequest;
import fe.api.payment.model.Capture3DsPaymentOrderRequestBody;
import fe.api.payment.model.Capture3DsPaymentOrderResponseBody;
import fe.api.payment.model.CaptureRedeemOptionResponseBody;
import fe.api.payment.model.ConfirmOrderRequestBody;
import fe.api.payment.model.ConfirmOrderResponseBody;
import fe.api.payment.model.CreateOrderRequestBody;
import fe.api.payment.model.CreateOrderResponseBody;
import fe.api.payment.model.DeletePaymentMethodRequestBody;
import fe.api.payment.model.DeletePaymentMethodResponseBody;
import fe.api.payment.model.GetInboxNotificationsRequestBody;
import fe.api.payment.model.GetInboxNotificationsResponseBody;
import fe.api.payment.model.GetOffersRequestBody;
import fe.api.payment.model.GetOffersResponseBody;
import fe.api.payment.model.GetPaymentMethodsRequestBody;
import fe.api.payment.model.GetPaymentMethodsResponseBody;
import fe.api.payment.model.GetPaymentOrderRequestBody;
import fe.api.payment.model.GetPaymentOrderResponseBody;
import fe.api.payment.model.GetPaymentServiceInfoRequestBody;
import fe.api.payment.model.GetPaymentServiceInfoResponseBody;
import fe.api.payment.model.GetPurchaseHistoryRequestBody;
import fe.api.payment.model.GetPurchaseHistoryResponseBody;
import fe.api.payment.model.GetPurchaseLimitsRequestBody;
import fe.api.payment.model.GetPurchaseLimitsResponseBody;
import fe.api.payment.model.GetRedeemMoneyHistoryRequestBody;
import fe.api.payment.model.GetRedeemMoneyHistoryResponseBody;
import fe.api.payment.model.GetRedeemsRequestBody;
import fe.api.payment.model.GetRedeemsResponseBody;
import fe.api.payment.model.GetWithdrawMethodsRequestBody;
import fe.api.payment.model.GetWithdrawMethodsResponseBody;
import fe.api.payment.model.InboxNotificationsRefreshedNotification;
import fe.api.payment.model.OfferDeclineNotification;
import fe.api.payment.model.OfferInfo;
import fe.api.payment.model.OfferPurchaseNotification;
import fe.api.payment.model.PageRequest;
import fe.api.payment.model.PurchaseSuccessNotification;
import fe.api.payment.model.RedeemMoneyRequestBody;
import fe.api.payment.model.RedeemMoneyResponseBody;
import fe.api.payment.model.ResetPurchaseLimitsRequestBody;
import fe.api.payment.model.ResetPurchaseLimitsResponseBody;
import fe.api.payment.model.SetPurchaseLimitRequestBody;
import fe.api.payment.model.SetPurchaseLimitResponseBody;
import fe.api.payment.model.UpdateInboxNotificationRequestBody;
import fe.api.payment.model.UpdateInboxNotificationResponseBody;
import fe.api.payment.model.UpdatePaymentMethodsNotification;
import gateway.api.RequestBody;
import gateway.api.ResponseOrNotification;
import io.vavr.CheckedConsumer;
import it.bot.AbstractFrontendBot;
import it.bot.security.HmacInfo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import payment.type.OfferTypeSpec;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import uam.api.v1.PaymentProvider;

@Slf4j
public class DefaultPaymentBot extends AbstractFrontendBot implements BasicPaymentBot {
    public static final Duration PUSH_TIMEOUT = Duration.ofSeconds(10);
    //
    // ~ core
    //
    private final Sinks.Many<PurchaseSuccessNotification> purchaseChangesProcessor = Sinks.many().multicast().directBestEffort();
    private final Flux<PurchaseSuccessNotification> purchaseNtnChange = purchaseChangesProcessor.asFlux();
    private final Sinks.Many<OfferPurchaseNotification> offerPurchaseChangesProcessor = Sinks.many().multicast().directBestEffort();
    private final Flux<OfferPurchaseNotification> purchaseChange = offerPurchaseChangesProcessor.asFlux();
    private final Sinks.Many<OfferDeclineNotification> offerDeclinesProcessor = Sinks.many().multicast().directBestEffort();
    private final Sinks.Many<RewardOfferUpdateNotification> rewardOfferUpdateProcessor = Sinks.many().multicast().directBestEffort();
    private final Sinks.Many<UpdatePaymentMethodsNotification> updatePaymentMethodsProcessor = Sinks.many().multicast().directBestEffort();
    private final Sinks.Many<InboxNotificationsRefreshedNotification> inboxNotificationsProcessor = Sinks.many().multicast().directBestEffort();
    private final Flux<UpdatePaymentMethodsNotification> updatePaymentMethodsNotifications = updatePaymentMethodsProcessor.asFlux();
    private final Flux<InboxNotificationsRefreshedNotification> inboxNotificationsUpdatedNotifications = inboxNotificationsProcessor.asFlux();
    public final Flux<OfferDeclineNotification> offerDeclines = offerDeclinesProcessor.asFlux();
    public final Flux<RewardOfferUpdateNotification> rewardOffersUpdate = rewardOfferUpdateProcessor.asFlux();
    private final DefaultCrmBot crmBot;
    private final DefaultAuthBot authBot;

    protected DefaultPaymentBot(
            DefaultCrmBot crmBot,
            DefaultAuthBot authBot,
            HttpUrl feinfo,
            HttpUrl wsinfo,
            String brand,
            PlatformSpec platform,
            CommonObjectMapper mapper) throws Throwable {
        super(feinfo, wsinfo, brand, platform, mapper);
        this.crmBot = crmBot;
        this.authBot = authBot;
        attachCrmInfo(crmBot);
    }

    public void attachCrmInfo(DefaultCrmBot bot) throws Exception {
        if (bot.isStrictMode()) {
            var info = bot.getHmacInfo();
            setHmacInfo(new HmacInfo(info.key, JsonSignMethodSpec.valueOf(info.signMethod.toUpperCase()), info.alg));
        }
        if (bot.cookie() != null) {
            setCookie(bot.cookie());
        }
    }

    @Override
    public RedeemMoneyResponseBody redeemMoney(String currency, BigDecimal amount) throws Exception {
        RedeemMoneyRequestBody req = new RedeemMoneyRequestBody();
        req.setCurrency(currency);
        req.setAmount(amount);
        req.setEmail(authBot.getEmail());

        return send(req, RedeemMoneyResponseBody.class).getResp();
    }

    @Override
    public GetOffersResponseBody getOffers() throws Exception {
        return send(new GetOffersRequestBody(), GetOffersResponseBody.class).getResp();
    }

    @Override
    public GetInboxNotificationsResponseBody getInboxNotifications() throws Exception {
        return send(new GetInboxNotificationsRequestBody(), GetInboxNotificationsResponseBody.class).getResp();
    }

    @Override
    public UpdateInboxNotificationResponseBody updateInboxNotification(UUID token, UpdateInboxNotificationRequestBody.StatusEnum newStatus) throws Exception {
        var req = new UpdateInboxNotificationRequestBody();
        req.setNotificationToken(token.toString());
        req.status(newStatus);
        return send(req, UpdateInboxNotificationResponseBody.class).getResp();
    }

    @Override
    public DeletePaymentMethodResponseBody deletePaymentMethod(String code) throws Exception {
        DeletePaymentMethodRequestBody req = new DeletePaymentMethodRequestBody();
        req.setCode(code);

        return send(req, DeletePaymentMethodResponseBody.class).getResp();
    }

    @Override
    public GetPaymentMethodsResponseBody getPaymentMethods() throws Exception {
        return send(new GetPaymentMethodsRequestBody(), GetPaymentMethodsResponseBody.class).getResp();
    }

    @Override
    public GetWithdrawMethodsResponseBody getWithdrawMethods() throws Exception {
        return send(new GetWithdrawMethodsRequestBody(), GetWithdrawMethodsResponseBody.class).getResp();
    }

    @Override
    public GetPaymentServiceInfoResponseBody serviceInfoRequest() throws Exception {
        return send(new GetPaymentServiceInfoRequestBody(), GetPaymentServiceInfoResponseBody.class).getResp();
    }

    @Override
    public GetWithdrawSettingsResponseBody getWithdrawSettings() throws Exception {
        return send(new GetWithdrawSettingsRequestBody(), GetWithdrawSettingsResponseBody.class).getResp();
    }

    @Override
    public GetPurchaseHistoryResponseBody getPurchaseHistory(GetPurchaseHistoryRequestBody request) throws Exception {
        return send(request, GetPurchaseHistoryResponseBody.class).getResp();
    }
    
    @Override
    public SetPurchaseLimitResponseBody setPurchaseLimit(SetPurchaseLimitRequestBody request) throws Exception {
        return send(request, SetPurchaseLimitResponseBody.class).getResp();
    }
    
    @Override
    public GetPurchaseLimitsResponseBody getPurchaseLimits(GetPurchaseLimitsRequestBody request) throws Exception {
        return send(request, GetPurchaseLimitsResponseBody.class).getResp();
    }
    
    @Override
    public ResetPurchaseLimitsResponseBody resetPurchaseLimits(ResetPurchaseLimitsRequestBody request) throws Exception {
        return send(request, ResetPurchaseLimitsResponseBody.class).getResp();
    }

    @Override
    public RegisterPaymentUserAccountResponseBody registerPaymentUserAccount(String phoneNumber) throws Exception {
        RegisterPaymentUserAccountRequestBody request = RegisterPaymentUserAccountRequestBody.builder()
                .phone(phoneNumber)
                .build();
        return send(request, RegisterPaymentUserAccountResponseBody.class).getResp();
    }

    @Override
    public LinkBankAccountResponseBody linkBankAccount(LinkBankAccountRequestBody request) throws Exception {
        return send(request, LinkBankAccountResponseBody.class).getResp();
    }

    @Override
    public GetPaymentServiceBankDetailsResponseBody getPaymentServiceBankDetails() throws Exception {
        return send(new GetPaymentServiceBankDetailsRequestBody(), GetPaymentServiceBankDetailsResponseBody.class).getResp();
    }

    @Override
    public ConfirmPaymentUserAccountResponseBody confirmPaymentUserAccount(String code) throws Exception {
        return send(new ConfirmPaymentUserAccountRequestBody().code(code), ConfirmPaymentUserAccountResponseBody.class).getResp();
    }

    @Override
    public ResponseWrapper<GetWidgetResponseBody> getWidget() throws Exception {
        return send(new GetWidgetRequestBody(), GetWidgetResponseBody.class);
    }

    @Override
    public ResponseWrapper<CreateNewAeroPayMethodResponseBody> createNewAeroPayMethod(String bankAccountId) throws Exception {
        return send(new CreateNewAeroPayMethodRequestBody().accountId(bankAccountId), CreateNewAeroPayMethodResponseBody.class);
    }

    @Override
    public ResponseWrapper<CreateOrderResponseBody> deposit(BigDecimal amount, CreateOrderRequestBody.CreateOrderRequestBodyBuilder builder, UUID transactionId) throws Exception {
        return makeDeposit(PaymentProvider.SPREEDLY_TEST_GATEWAY, amount, builder, transactionId);

    }

    @Override
    public ResponseWrapper<CreateOrderResponseBody> createOrder(String offer, Consumer<CreateOrderRequestBody.CreateOrderRequestBodyBuilder> builder) throws Exception {
        return acceptOffer(PaymentProvider.SPREEDLY_TEST_GATEWAY, offer, builder);
    }

    @Override
    public ResponseWrapper<CreateOrderResponseBody> createOrder(PaymentProvider provider, String offer, Consumer<CreateOrderRequestBody.CreateOrderRequestBodyBuilder> builder) throws Exception {
        return acceptOffer(provider, offer, builder);
    }

    @Override
    public GetPaymentOrderResponseBody orderDetails(UUID trxId) throws Exception {
        return send(new GetPaymentOrderRequestBody().transactionId(trxId), GetPaymentOrderResponseBody.class).getResp();
    }

    @Override
    public ResponseWrapper<Capture3DsPaymentOrderResponseBody> capture3DsOffer(UUID orderTxId) throws Exception {
        DisposableCountdownLatch subscribe = crmBot.subscribeBalanceChange();
        DisposableCountdownLatch subscribePurchaseChange = subscribePurchaseChange();
        var builder = Capture3DsPaymentOrderRequestBody.builder();
        builder.orderTransactionId(orderTxId);
        builder.session("session");
        var resp = send(builder.build(), Capture3DsPaymentOrderResponseBody.class);
        resp.setNtfCallback(t -> {
            try {
                return subscribe.await(t, TimeUnit.SECONDS) && subscribePurchaseChange.await(t, TimeUnit.SECONDS);
            } finally {
                subscribe.dispose();
            }
        });
        return resp;
    }

    @Override
    public ResponseWrapper<RefreshOrderResponseBody> refreshOrder(UUID orderTxId, String token, String provider) throws Exception {
        RefreshOrderRequestBody req = new RefreshOrderRequestBody();
        req.setTransactionId(orderTxId);
        req.setProvider(provider.toLowerCase());
        if (token != null) {
            req.setToken(token);
        }
        return send(req, RefreshOrderResponseBody.class);
    }

    @Override
    public ConfirmOrderResponseBody confirmOrder(UUID trxId, String tempToken, String provider) throws Exception {
        return confirmOffer(trxId, tempToken, provider);
    }

    @Override
    public ResponseWrapper<RedeemMoneyResponseBody> redeemMoney(RedeemMoneyRequestBody.RedeemMoneyRequestBodyBuilder builder) throws Exception {
        DisposableCountdownLatch subscribe = crmBot.subscribeBalanceChange();
        var resp = send(builder.build(), RedeemMoneyResponseBody.class);
        resp.setNtfCallback((t) -> {
            try {
                return subscribe.await(t, TimeUnit.SECONDS);
            } finally {
                subscribe.dispose();
            }
        });
        return resp;
    }

    @Override
    public ResponseWrapper<CaptureRedeemOptionResponseBody> captureRedeemOption(CaptureRedeemOptionRequest request) throws Exception {
        return send(request, CaptureRedeemOptionResponseBody.class);
    }

    @Override
    public GetRedeemMoneyHistoryResponseBody getRedeemHistory(GetRedeemMoneyHistoryRequestBody.GetRedeemMoneyHistoryRequestBodyBuilder req) throws Exception {
        return send(req.firstResult(0).maxResults(100).build(), GetRedeemMoneyHistoryResponseBody.class).getResp();
    }

    @Override
    public GetRedeemsResponseBody getRedeems(GetRedeemsRequestBody.GetRedeemsRequestBodyBuilder req) throws Exception {
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPageNumber(1);
        pageRequest.setPageSize(100);
        return send(req.pageRequest(pageRequest).build(), GetRedeemsResponseBody.class).getResp();
    }

    @Override
    public <REQ, RESP> ResponseWrapper<RESP> doKafkaRedeemCall(REQ req, Function<REQ, RESP> action) throws Exception {
        DisposableCountdownLatch subscribe = crmBot.subscribeBalanceChange();
        var resp = action.apply(req);
        return new ResponseWrapper<>(resp, null, null, (t) -> {
            try {
                return subscribe.await(t, TimeUnit.SECONDS);
            } finally {
                subscribe.dispose();
            }
        });
    }

    @Override
    public DisposableCountdownLatch subscribeRewardOfferUpdate(Predicate<RewardOfferUpdateNotification> predicate) {
        CountDownLatch latch = new CountDownLatch(1);
        Disposable subscribe = rewardOffersUpdate.subscribe(notification -> {
            if (predicate.test(notification)) {
                latch.countDown();
            }
        });
        return new DisposableCountdownLatch(subscribe, latch, new MutableObject<>());
    }

    @Override
    public DisposableCountdownLatch subscribeOfferDecline(Predicate<OfferDeclineNotification> predicate) {
        CountDownLatch latch = new CountDownLatch(1);
        Disposable subscribe = offerDeclines.subscribe(notification -> {
            if (predicate.test(notification)) {
                latch.countDown();
            }
        });
        return new DisposableCountdownLatch(subscribe, latch, new MutableObject<>());
    }

    @Override
    public DisposableCountdownLatch subscribeUpdatePaymentMethodsNotification(Predicate<UpdatePaymentMethodsNotification> predicate) {
        CountDownLatch latch = new CountDownLatch(1);
        Disposable subscribe = updatePaymentMethodsNotifications.subscribe(notification -> {
            if (predicate.test(notification)) {
                latch.countDown();
            }
        });
        return new DisposableCountdownLatch(subscribe, latch, new MutableObject<>());
    }

    @Override
    public DisposableCountdownLatch subscribeInboxNotificationsRefreshedNotification(Predicate<InboxNotificationsRefreshedNotification> predicate) {
        CountDownLatch latch = new CountDownLatch(1);
        Disposable subscribe = inboxNotificationsUpdatedNotifications.subscribe(notification -> {
            if (predicate.test(notification)) {
                latch.countDown();
            }
        });
        return new DisposableCountdownLatch(subscribe, latch, new MutableObject<>());
    }

    @Override
    public DisposableCountdownLatch subscribeOfferSuccess(Predicate<OfferPurchaseNotification> predicate) {
        CountDownLatch latch = new CountDownLatch(1);
        Disposable subscribe = purchaseChange.subscribe(notification -> {
            if (predicate.test(notification)) {
                latch.countDown();
            }
        });
        return new DisposableCountdownLatch(subscribe, latch, new MutableObject<>());
    }

    protected DisposableCountdownLatch subscribePurchaseChange() {
        CountDownLatch latch = new CountDownLatch(1);
        Disposable subscribe = purchaseChange.subscribe(notification -> latch.countDown());
        return new DisposableCountdownLatch(subscribe, latch, new MutableObject<>());
    }

    protected DisposableCountdownLatch subscribePurchaseNtfChange() {
        CountDownLatch latch = new CountDownLatch(1);
        Disposable subscribe = purchaseNtnChange.subscribe(notification -> latch.countDown());
        return new DisposableCountdownLatch(subscribe, latch, new MutableObject<>());
    }

    @Override
    public void onPush(ResponseOrNotification body) {
        if (body instanceof OfferPurchaseNotification) {
            offerPurchaseChangesProcessor.tryEmitNext((OfferPurchaseNotification) body);
        } else if (body instanceof OfferDeclineNotification) {
            offerDeclinesProcessor.tryEmitNext((OfferDeclineNotification) body);
        } else if (body instanceof UpdatePaymentMethodsNotification) {
            updatePaymentMethodsProcessor.tryEmitNext((UpdatePaymentMethodsNotification) body);
        } else if (body instanceof PurchaseSuccessNotification) {
            purchaseChangesProcessor.tryEmitNext((PurchaseSuccessNotification) body);
        } else if (body instanceof InboxNotificationsRefreshedNotification) {
            inboxNotificationsProcessor.tryEmitNext((InboxNotificationsRefreshedNotification) body);
        } else if (body instanceof RewardOfferUpdateNotification) {
            rewardOfferUpdateProcessor.tryEmitNext((RewardOfferUpdateNotification) body);
        }
    }

    protected ResponseWrapper<CreateOrderResponseBody> acceptOffer(PaymentProvider provider, String offer) throws Exception {
        return acceptOffer(provider, offer, Consumers.nop());
    }

    protected ResponseWrapper<CreateOrderResponseBody> acceptOffer(
            PaymentProvider provider,
            String code,
            Consumer<CreateOrderRequestBody.CreateOrderRequestBodyBuilder> enhancer) throws Exception {
        CreateOrderRequestBody.CreateOrderRequestBodyBuilder builder = CreateOrderRequestBody.builder();
        builder.offer(code);
        builder.provider(provider.name().toLowerCase());
        builder.transactionId(PlatformUtil.randomUUID());
        enhancer.accept(builder);

        DisposableCountdownLatch subscribe = crmBot.subscribeBalanceChange();
        DisposableCountdownLatch subscribePurchaseChange = subscribePurchaseChange();
        var resp = send(builder.build(), CreateOrderResponseBody.class);
        resp.setNtfCallback(t -> {
            try {
                return subscribe.await(t, TimeUnit.SECONDS) && subscribePurchaseChange.await(t, TimeUnit.SECONDS);
            } finally {
                subscribe.dispose();
            }
        });
        return resp;
    }

    @Override
    public ResponseWrapper<CreateOrderResponseBody> makeDeposit(PaymentProvider provider, BigDecimal amount,
                                                                   CreateOrderRequestBody.CreateOrderRequestBodyBuilder builder,
                                                                   UUID transactionId) throws Exception {
        builder.amount(amount);
        builder.transactionId(transactionId);
        builder.provider(provider.name().toLowerCase());

        DisposableCountdownLatch subscribe = crmBot.subscribeBalanceChange();
        DisposableCountdownLatch subscribePurchaseChange = subscribePurchaseNtfChange();
        var resp = send(builder.build(), CreateOrderResponseBody.class);
        resp.setNtfCallback(t -> {
            try {
                return subscribe.await(t, TimeUnit.SECONDS) && subscribePurchaseChange.await(t, TimeUnit.SECONDS);
            } finally {
                subscribe.dispose();
            }
        });
        return resp;
    }

    protected CreateOrderResponseBody acceptOfferNotConfirmed(
            PaymentProvider provider,
            String offer,
            UUID transactionId,
            CreateOrderRequestBody.CreateOrderRequestBodyBuilder builder) throws Exception {
        CreateOrderRequestBody req = builder.build();
        req.setOffer(offer);
        req.setTransactionId(transactionId);
        req.setProvider(provider.name().toLowerCase());

        return send(req, CreateOrderResponseBody.class).getResp();
    }

    protected ConfirmOrderResponseBody confirmOffer(UUID transactionId, String token, String provider) throws Exception {
        ConfirmOrderRequestBody req = new ConfirmOrderRequestBody();
        req.setTransactionId(transactionId);
        req.setToken(token);
        req.setProvider(provider.toLowerCase());

        DisposableCountdownLatch subscribe = crmBot.subscribeBalanceChange();

        try {
            ConfirmOrderResponseBody resp = send(req, ConfirmOrderResponseBody.class).getResp();
            if (subscribe.await((int) PUSH_TIMEOUT.toSeconds(), TimeUnit.SECONDS)) {
                log.warn("no offer confirm balance change: " + req.getTransactionId());
            }
            return resp;
        } finally {
            subscribe.dispose();
        }
    }

    protected Map<String, BigDecimal> acceptAllOffers(PaymentProvider provider, CheckedConsumer<String> action) throws Throwable {
        var map = new HashMap<String, BigDecimal>();
        var sumGold = BigDecimal.ZERO;
        var sumSweepstake = BigDecimal.ZERO;

        for (OfferInfo info : getOffers().getOffers()) {
            if (OfferTypeSpec.fromString(info.getOfferType().toString()).isOneTimeOrPermanent()) {
                sumGold = sumGold.add(info.getGoldMoney());
                sumSweepstake = sumSweepstake.add(info.getSweepstakeMoney());
                action.accept(info.getCode());
            }
        }

        map.put(crmBot.goldCurrency(), sumGold);
        map.put(crmBot.sweepstakeCurrency(), sumSweepstake);
        map.put(crmBot.fiatCurrency(), BigDecimal.ZERO);

        log.info("accepted all offer via {}: {}", provider.name().toLowerCase(), map);
        return map;
    }

    public  <T> ResponseWrapper<T> send(RequestBody req, Class<T> resp) throws Exception {
        var respWrapper = new ResponseWrapper<T>();
        respWrapper.setErrorCode(Code.ERR_OK.name().toLowerCase());
        respWrapper.setErrorText("");
        try {
            T respBody = sendOut(req, resp);
            respWrapper.setResp(respBody);
        } catch (UnexpectedHttpClientException ex) {
            ex.getFirstHeaderValue(HttpProto.HEADER_X_STATUS).ifPresent(respWrapper::setErrorCode);
            ex.getFirstHeaderValue(HttpProto.HEADER_X_STATUS_TEXT).ifPresent(respWrapper::setErrorText);
            try {
                respWrapper.setResp(mapper.readValue(ex.getEntity(), resp));
            } catch (Exception e) {
                log.warn("Unable to parse response: {}", ex.getEntity());
            }
        }
        return respWrapper;
    }
}
