package bots;

import static admin.api.QuestApiEndpoint.QUESTLINE_CREATE;
import static admin.api.QuestApiEndpoint.QUESTLINE_FIND;
import static admin.api.QuestApiEndpoint.QUESTLINE_INSTANCE_CREATE_FOR_ACCOUNT;
import static admin.api.QuestApiEndpoint.QUESTLINE_INSTANCE_RETRACT_BY_INSTANCE_CODE;
import static admin.api.QuestApiEndpoint.QUESTLINE_INSTANCE_RETRACT_BY_TEMPLATE;
import static admin.api.QuestApiEndpoint.QUESTLINE_UPDATE;

import admin.api.QuestApiEndpoint;
import admin.models.quest.delete.RetractQuestlineInstanceByCodeRequest;
import com.turbospaces.json.CommonObjectMapper;

import admin.api.AdminOkResponse;
import admin.models.quest.create.CreateOrUpdateQuestlineTemplateAdminRequest;
import admin.models.quest.create.CreateOrUpdateQuestlineTemplateAdminResponse;
import admin.models.quest.create.CreateOrUpdateQuestlineTemplateAdminWrapper;
import admin.models.quest.create.CreateQuestlineInstanceAdminRequest;
import admin.models.quest.create.CreateQuestlineInstanceAdminResponse;
import admin.models.quest.delete.RetractQuestlineInstancesByTemplateRequest;
import admin.models.quest.get.GetQuestlineTemplateByCodeAdminRequest;
import admin.models.quest.get.GetQuestlineTemplateByCodeAdminResponse;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;

@Slf4j
public class QuestAdminBot extends AbstractAdminBot {
    public QuestAdminBot(HttpUrl adminInfo, CommonObjectMapper mapper) {
        super(adminInfo, mapper);
    }

    public QuestAdminBot(HttpUrl adminInfo, CommonObjectMapper mapper, String secret, String brand) {
        super(adminInfo, mapper, secret, brand);
    }

    public CreateOrUpdateQuestlineTemplateAdminResponse createQuestlineTemplate(CreateOrUpdateQuestlineTemplateAdminRequest req)
            throws Exception {
        var createOrUpdateQuestlineTemplateAdminWrapper = new CreateOrUpdateQuestlineTemplateAdminWrapper();
        createOrUpdateQuestlineTemplateAdminWrapper.setQuestlineTemplate(req);
        return sendRequest(
                createOrUpdateQuestlineTemplateAdminWrapper,
                QuestApiEndpoint.V1_ADMIN_QUEST + QUESTLINE_CREATE,
                CreateOrUpdateQuestlineTemplateAdminResponse.class);
    }

    public CreateOrUpdateQuestlineTemplateAdminResponse updateQuestlineTemplate(CreateOrUpdateQuestlineTemplateAdminRequest req)
            throws Exception {
        var createOrUpdateQuestlineTemplateAdminWrapper = new CreateOrUpdateQuestlineTemplateAdminWrapper();
        createOrUpdateQuestlineTemplateAdminWrapper.setQuestlineTemplate(req);
        return sendRequest(
                createOrUpdateQuestlineTemplateAdminWrapper,
                QuestApiEndpoint.V1_ADMIN_QUEST + QUESTLINE_UPDATE,
                CreateOrUpdateQuestlineTemplateAdminResponse.class);
    }

    public CreateQuestlineInstanceAdminResponse createQuestlineInstance(CreateQuestlineInstanceAdminRequest req)
            throws Exception {
        return sendRequest(
                req,
                QuestApiEndpoint.V1_ADMIN_QUEST + QUESTLINE_INSTANCE_CREATE_FOR_ACCOUNT,
                CreateQuestlineInstanceAdminResponse.class);
    }

    public GetQuestlineTemplateByCodeAdminResponse getQuestlineTemplate(GetQuestlineTemplateByCodeAdminRequest req) throws Exception {
        return sendRequest(
                req,
                QuestApiEndpoint.V1_ADMIN_QUEST + QUESTLINE_FIND,
                GetQuestlineTemplateByCodeAdminResponse.class);

    }

    public AdminOkResponse retractAllQuestlineInstancesByTemplate(
            RetractQuestlineInstancesByTemplateRequest req) throws Exception {
        return sendRequest(
                req,
                QuestApiEndpoint.V1_ADMIN_QUEST + QUESTLINE_INSTANCE_RETRACT_BY_TEMPLATE,
                AdminOkResponse.class);
    }


    public AdminOkResponse retractOneQuestlineInstanceByCode(
            RetractQuestlineInstanceByCodeRequest req) throws Exception {
        return sendRequest(
                req,
                QuestApiEndpoint.V1_ADMIN_QUEST + QUESTLINE_INSTANCE_RETRACT_BY_INSTANCE_CODE,
                AdminOkResponse.class);
    }
}
