package bots;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.function.Predicate;

import org.apache.commons.lang3.mutable.MutableObject;

import com.turbospaces.common.DisposableCountdownLatch;
import com.turbospaces.json.CommonObjectMapper;

import api.v1.JsonSignMethodSpec;
import api.v1.PlatformSpec;
import bots.crm.impl.DefaultCrmBot;
import fe.api.quest.GetQuestlinesRequest;
import fe.api.questline.model.ClaimMilestoneRewardRequestBody;
import fe.api.questline.model.ClaimMilestoneRewardResponseBody;
import fe.api.questline.model.GetQuestlinesResponseBody;
import fe.api.questline.model.QuestlineProgressUpdateNotification;
import fe.handlers.quest.QuestFrontendServerConstants;
import gateway.api.ResponseOrNotification;
import it.bot.AbstractFrontendBot;
import it.bot.security.HmacInfo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

@Slf4j
public class EngagementFrontendBot extends AbstractFrontendBot implements QuestBot {
    //
    // ~ Notfications
    //
    private final Sinks.Many<QuestlineProgressUpdateNotification> questlineProgressUpdateProcessor = Sinks.many().multicast().directBestEffort();
    private final Flux<QuestlineProgressUpdateNotification> questlineProgressUpdateNtnChange = questlineProgressUpdateProcessor.asFlux();

    @Getter
    private final List<ResponseOrNotification> notifications = new ArrayList<>();

    public EngagementFrontendBot(
            HttpUrl feinfo,
            HttpUrl wsinfo,
            String brand,
            PlatformSpec platform,
            CommonObjectMapper mapper) {
        super(feinfo, wsinfo, brand, platform, mapper);
        // for now notifications only in quest and soon services will be decoupled
        ntfChannel = QuestFrontendServerConstants.NTF_CHANNEL.toString();
    }

    public EngagementFrontendBot(
            DefaultCrmBot crmBot,
            HttpUrl feinfo,
            HttpUrl wsinfo,
            String brand,
            PlatformSpec platform,
            CommonObjectMapper mapper) throws Throwable {
        this(feinfo, wsinfo, brand, platform, mapper);
        attachCrmInfo(crmBot);
    }

    public void attachCrmInfo(DefaultCrmBot crmBot) throws Exception {
        if (crmBot.isStrictMode()) {
            var hmacInfo = crmBot.getHmacInfo();
            setHmacInfo(new HmacInfo(hmacInfo.key, JsonSignMethodSpec.valueOf(hmacInfo.signMethod.toUpperCase()), hmacInfo.alg));
        }
        if (crmBot.cookie() != null) {
            setCookie(crmBot.cookie());
        }
    }

    @Override
    public void onPush(ResponseOrNotification body) {
        if (body instanceof QuestlineProgressUpdateNotification) {
            questlineProgressUpdateProcessor.tryEmitNext((QuestlineProgressUpdateNotification) body);
        }
        notifications.add(body);
    }

    @Override
    public DisposableCountdownLatch subscribeQuestlineProgressUpdate(Predicate<QuestlineProgressUpdateNotification> predicate) {
        CountDownLatch latch = new CountDownLatch(1);
        Disposable subscribe = questlineProgressUpdateNtnChange.subscribe(notification -> {
            if (predicate.test(notification)) {
                latch.countDown();
            }
        });
        return new DisposableCountdownLatch(subscribe, latch, new MutableObject<>());
    }
    @Override
    public ClaimMilestoneRewardResponseBody claimMilestoneReward(String claimCode) throws Exception {
        var req = ClaimMilestoneRewardRequestBody.builder().code(claimCode).build();
        return sendOut("/v1/questline/milestone/reward/claim", req, ClaimMilestoneRewardResponseBody.class);
    }

    @Override
    public GetQuestlinesResponseBody getActiveQuestlines() throws Exception {
        var req = new GetQuestlinesRequest();
        return sendOut("/v1/questline/list", req, GetQuestlinesResponseBody.class);
    }
}
