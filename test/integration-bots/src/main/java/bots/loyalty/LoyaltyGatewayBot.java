package bots.loyalty;

import bots.AbstractGatewayBot;
import com.turbospaces.json.CommonObjectMapper;
import loyalty.gateway.dto.AssignLoyaltySystemRequest;
import loyalty.gateway.dto.SetAccountLoyaltySystemStatusRequest;
import loyalty.gateway.dto.SetAccountXpLoyaltySystemRequest;
import okhttp3.HttpUrl;

import static loyalty.gateway.endpoints.LoyaltyApiEndpoint.V1_LOYALTY_PATH;

public class LoyaltyGatewayBot extends AbstractGatewayBot {

    public LoyaltyGatewayBot(HttpUrl gatewayInfo, CommonObjectMapper mapper) {
        super(gatewayInfo, mapper);
    }

    public void assignLoyaltySystem(AssignLoyaltySystemRequest req, String apikey) throws Exception {
        sendApiRequest(req, V1_LOYALTY_PATH + "/assign", apikey);
    }

    public void assignLoyaltySystem(AssignLoyaltySystemRequest req) throws Exception {
        sendBloomreachRequest(req, V1_LOYALTY_PATH + "/assign");
    }

    public void setAccountXp(SetAccountXpLoyaltySystemRequest req) throws Exception {
        sendBloomreachRequest(req, V1_LOYALTY_PATH + "/setAccountXp");
    }

    public void setAccountStatus(SetAccountLoyaltySystemStatusRequest req) throws Exception {
        sendBloomreachRequest(req, V1_LOYALTY_PATH + "/setAccountLoyaltySystemStatus");
    }

    public void setAllAccountLoyaltySystemStatus(SetAccountLoyaltySystemStatusRequest req) throws Exception {
        sendBloomreachRequest(req, V1_LOYALTY_PATH + "/setAllAccountLoyaltySystemStatus");
    }

}
