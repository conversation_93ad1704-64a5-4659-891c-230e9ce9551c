package randomreward.testutils;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.test.context.TestContext;
import org.springframework.test.context.TestExecutionListener;

import com.google.common.hash.Hashing;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.DataGeneratorUtil;
import com.turbospaces.ebean.EbeanJpaManager;
import com.turbospaces.ebean.JpaManager;

import io.ebean.Transaction;
import model.Schemas;
import randomreward.RandomRewardJpaManager;
import randomreward.model.RandomRewardAccount;
import randomreward.model.RandomRewardBrand;
import randomreward.model.RandomRewardInstance;
import randomreward.model.RandomRewardInstanceSnapshot;
import randomreward.model.RandomRewardMiniGameType;
import randomreward.model.RandomRewardPlacementType;
import randomreward.model.RandomRewardPrizeTable;
import randomreward.model.RandomRewardTemplate;
import randomreward.model.RandomRewardTemplatePlacementType;

public class RandomRewardTestDataGenerator implements TestExecutionListener {

    public static final String TEST_BRAND = "bluedream";

    private RandomRewardTestDataGenerator() {
    }

    @Override
    public void beforeTestMethod(TestContext testContext) {
        EbeanJpaManager ebean = getEbean(testContext);
        try (Transaction tx = ebean.newTransaction()) {
            ebean.save(new RandomRewardBrand(TEST_BRAND), tx);
            tx.commit();
        } catch (Throwable err) {
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    @Override
    public void afterTestMethod(TestContext testContext) {
        DataGeneratorUtil.clearH2Db(getEbean(testContext), new String[]{Schemas.CORE, Schemas.RANDOM_REWARD});
    }

    public static RandomRewardAccount createAccount(RandomRewardJpaManager ebean, RandomRewardBrand brand) {
        var account = new RandomRewardAccount(brand);
        account.setCode(PlatformUtil.randomUUID());
        long remoteId = generateAccountRemoteId();
        account.setRemoteId(remoteId);
        account.setHash(generateAccountHash(remoteId));
        ebean.save(account);
        return account;
    }

    public static RandomRewardBrand getBrand(RandomRewardJpaManager ebean) {
        return ebean.find(RandomRewardBrand.class).where().eq("name", TEST_BRAND).findOne();
    }

    private static EbeanJpaManager getEbean(TestContext testContext) {
        return testContext.getApplicationContext().getBean(EbeanJpaManager.class);
    }

    public static RandomRewardPrizeTable buildPrizeTable(
            JpaManager ebean,
            RandomRewardBrand brand) {
        var prizeTable = new RandomRewardPrizeTable();
        prizeTable.setName("My Prize Table");
        prizeTable.setBrand(brand);
        prizeTable.setCode(PlatformUtil.randomUUID());
        prizeTable.setCreatedAt(new Date());
        prizeTable.setModifiedAt(new Date());

        try (Transaction tx = ebean.beginTransaction()) {
            ebean.save(prizeTable);
            tx.commit();
        }
        return prizeTable;
    }

    public static RandomRewardMiniGameType buildMiniGameType(JpaManager ebean) {
        var miniGameType = new RandomRewardMiniGameType();
        miniGameType.setName("Wheel of Fortune");
        miniGameType.setFeatureKey("New Year Wheel of Fortune");
        miniGameType.setCode(PlatformUtil.randomUUID());
        miniGameType.setCreatedAt(new Date());
        miniGameType.setModifiedAt(new Date());

        try (Transaction tx = ebean.beginTransaction()) {
            ebean.save(miniGameType, tx);
            tx.commit();
        }
        return miniGameType;
    }

    public static RandomRewardPlacementType buildPlacementType(JpaManager ebean) {
        var placementType = new RandomRewardPlacementType();
        placementType.setName("PopUp");
        placementType.setFeatureKey("New Year PopUp");
        placementType.setCode(PlatformUtil.randomUUID());
        placementType.setCreatedAt(new Date());
        placementType.setModifiedAt(new Date());

        try (Transaction tx = ebean.beginTransaction()) {
            ebean.save(placementType, tx);
            tx.commit();
        }
        return placementType;
    }

    public static RandomRewardTemplate buildRewardTemplate(
            JpaManager ebean,
            RandomRewardBrand brand) {
        return buildRewardTemplate(ebean, brand, null, null, null, null, true);
    }

    public static RandomRewardTemplate buildRewardTemplate(
            JpaManager ebean,
            RandomRewardBrand brand,
            Long limitPerDay,
            Long issuedPerDay,
            Long limitTotal,
            Long issuedTotal,
            boolean available) {
        var randomRewardTemplate = new RandomRewardTemplate();
        randomRewardTemplate.setDisplayName("Wheel of Fortune Template");
        randomRewardTemplate.setDisplayNameShort("WOF Fortune Temp");
        randomRewardTemplate.setBrand(brand);
        randomRewardTemplate.setName(PlatformUtil.randomAlphabetic(7));
        randomRewardTemplate.setActive(true);
        randomRewardTemplate.setTest(false);
        randomRewardTemplate.setAvailable(true);
        randomRewardTemplate.setPrizeTable(buildPrizeTable(ebean, brand));
        randomRewardTemplate.setMiniGameType(buildMiniGameType(ebean));
        randomRewardTemplate.setIconSmallClosed("https://example.com/icon_small_closed.png");
        randomRewardTemplate.setIconLargeClosed("https://example.com/icon_large_closed.png");
        randomRewardTemplate.setIconSmallOpen("https://example.com/icon_small_open.png");
        randomRewardTemplate.setIconLargeOpen("https://example.com/icon_large_open.png");
        randomRewardTemplate.setShopIconSmall("https://example.com/shop_icon_small.png");
        randomRewardTemplate.setShopIconLarge("https://example.com/shop_icon_large.png");
        randomRewardTemplate.setTimeLimited(false);
        randomRewardTemplate.setExpiresAfterDays(30);
        randomRewardTemplate.setTermUrl("https://example.com/terms");
        randomRewardTemplate.setDisplayTagline("Limited Offer");
        randomRewardTemplate.setCode(PlatformUtil.randomUUID());
        randomRewardTemplate.setLimitPerDay(limitPerDay);
        randomRewardTemplate.setIssuedPerDay(issuedPerDay);
        randomRewardTemplate.setLimitTotal(limitTotal);
        randomRewardTemplate.setIssuedTotal(issuedTotal);

        var templatePlacementType = new RandomRewardTemplatePlacementType();
        templatePlacementType.setRewardTemplate(randomRewardTemplate);
        templatePlacementType.setPlacementType(buildPlacementType(ebean));
        templatePlacementType.setCode(PlatformUtil.randomUUID());
        randomRewardTemplate.setPlacements(List.of(templatePlacementType));

        try (Transaction tx = ebean.beginTransaction()) {
            ebean.save(randomRewardTemplate, tx);
            tx.commit();
        }

        return randomRewardTemplate;
    }

    private static List<RandomRewardInstanceSnapshot.PrizeTableItem> buildPrizeTableItems() {
        return List.of(
                new RandomRewardInstanceSnapshot.PrizeTableItem(
                        PlatformUtil.randomUUID(),
                        "SC + FS",
                        1,
                        new BigDecimal("0.5"),
                        List.of(PlatformUtil.randomUUID(),
                                PlatformUtil.randomUUID())),
                new RandomRewardInstanceSnapshot.PrizeTableItem(
                        PlatformUtil.randomUUID(),
                        "PO",
                        2,
                        new BigDecimal("0.5"),
                        List.of(PlatformUtil.randomUUID()))
        );
    }

    private static List<RandomRewardInstanceSnapshot.PlacementType> buildPlacementTypes() {
        return List.of(
                new RandomRewardInstanceSnapshot.PlacementType(PlatformUtil.randomUUID(), "popup"),
                new RandomRewardInstanceSnapshot.PlacementType(PlatformUtil.randomUUID(), "modal"));
    }

    private static RandomRewardInstanceSnapshot.GameType buildGameType() {
        return new RandomRewardInstanceSnapshot.GameType(PlatformUtil.randomUUID(), "wheel");
    }

    private static RandomRewardInstanceSnapshot.PrizeTable buildPrizeTable() {
        return new RandomRewardInstanceSnapshot.PrizeTable(PlatformUtil.randomUUID(), buildPrizeTableItems());
    }

    private static RandomRewardInstanceSnapshot buildSnapshot() {
        return new RandomRewardInstanceSnapshot(
                "display name example",
                "short display name example",
                buildPlacementTypes(),
                buildGameType(),
                buildPrizeTable(),
                1
        );
    }

    public static RandomRewardInstance buildRandomRewardRewardInstance(
            JpaManager ebean,
            RandomRewardBrand brand,
            RandomRewardAccount account,
            String source,
            String sourceReference) {
        return buildRandomRewardRewardInstance(ebean, brand, account, buildRewardTemplate(ebean, brand), source, sourceReference);
    }

    public static RandomRewardInstance buildRandomRewardRewardInstance(
            JpaManager ebean,
            RandomRewardBrand brand,
            RandomRewardAccount account) {
        return buildRandomRewardRewardInstance(ebean, brand, account, buildRewardTemplate(ebean, brand));
    }

    public static RandomRewardInstance buildRandomRewardRewardInstance(
            JpaManager ebean,
            RandomRewardBrand brand,
            RandomRewardAccount account,
            RandomRewardTemplate template) {
        return buildRandomRewardRewardInstance(ebean, brand, account, template, null, null);
    }

    public static RandomRewardInstance buildRandomRewardRewardInstance(
            JpaManager ebean,
            RandomRewardBrand brand,
            RandomRewardAccount account,
            RandomRewardTemplate template,
            String source,
            String sourceReference) {
        RandomRewardInstance rewardRewardInstance = new RandomRewardInstance();
        rewardRewardInstance.setCode(PlatformUtil.randomUUID());
        rewardRewardInstance.setBrand(brand);
        rewardRewardInstance.setAccount(account);
        rewardRewardInstance.setRewardTemplate(template);
        rewardRewardInstance.setStatus(RandomRewardInstance.Status.ASSIGNED);
        rewardRewardInstance.setExpiresAt(DateUtils.addDays(new Date(), 7));
        rewardRewardInstance.setSnapshot(buildSnapshot());
        rewardRewardInstance.setRequestId(PlatformUtil.randomNumeric(7));
        rewardRewardInstance.setAt(LocalDate.now(ZoneOffset.UTC));
        rewardRewardInstance.setSource(StringUtils.isEmpty(source) ? null : RandomRewardInstance.Source.valueOf(source));
        rewardRewardInstance.setSourceReference(sourceReference);

        try (Transaction tx = ebean.beginTransaction()) {
            ebean.save(rewardRewardInstance);
            tx.commit();
        }

        return rewardRewardInstance;
    }

    private static long generateAccountRemoteId() {
        return PlatformUtil.RANDOM.nextLong(0, Long.MAX_VALUE);
    }

    private static String generateAccountHash(Long remoteId) {
        return Hashing.murmur3_32_fixed().hashBytes(remoteId.toString().getBytes(StandardCharsets.UTF_8)).toString();
    }
}