package randomreward.model;

import java.util.List;
import java.util.UUID;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(name = "random_reward_prize_table", schema = Schemas.RANDOM_REWARD)
public class RandomRewardPrizeTable extends BasicModel {

    @Column(nullable = false, length = RandomRewardModelConstraints.NAME)
    private String name;

    @ManyToOne(optional = false)
    private RandomRewardBrand brand;

    @Column(nullable = false, unique = true)
    private UUID code;

    @OneToMany(mappedBy = "prizeTable", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<RandomRewardPrizeTableItem> items;

}