package randomreward.model;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import io.ebean.annotation.DbArray;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(
        name = "random_reward_prize_table_item",
        schema = Schemas.RANDOM_REWARD,
        uniqueConstraints = @UniqueConstraint(columnNames = {"prize_table_id", "rank"})
)
public class RandomRewardPrizeTableItem extends BasicModel {

    @ManyToOne(optional = false)
    private RandomRewardPrizeTable prizeTable;

    @Column(nullable = false, length = RandomRewardModelConstraints.NAME)
    private String name;

    @Column(nullable = false)
    private Integer rank;

    @Column(nullable = false)
    private BigDecimal probability;

    @Column(nullable = false, unique = true)
    private UUID code;

    @DbArray
    private List<UUID> rewardCodes;
}
