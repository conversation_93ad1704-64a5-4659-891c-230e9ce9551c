package randomreward.model;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonCreator;

import io.ebean.annotation.DbEnumValue;
import io.ebean.annotation.DbJsonB;
import io.ebean.annotation.DbPartition;
import io.ebean.annotation.History;
import io.ebean.annotation.Index;
import io.ebean.annotation.PartitionMode;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(
        name = "random_reward_instance",
        schema = Schemas.RANDOM_REWARD,
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"code", "at"}),
                @UniqueConstraint(columnNames = {"request_id", "at"})
        })
@History
@Index(name = "idx_random_reward_instance_status_expires_at", columnNames = {"status", "expires_at"})
@Index(columnNames = {"source", "source_reference"})
@DbPartition(mode = PartitionMode.MONTH, property = "at")
public class RandomRewardInstance extends BasicModel {

    @Column(nullable = false)
    private UUID code;

    @ManyToOne(optional = false)
    private RandomRewardBrand brand;

    @ManyToOne(optional = false)
    private RandomRewardAccount account;

    @ManyToOne(optional = false)
    private RandomRewardTemplate rewardTemplate;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Status status;

    @Column(nullable = false)
    private Date expiresAt;

    @Column(nullable = false, updatable = false)
    private LocalDate at = LocalDate.now(ZoneOffset.UTC);

    @DbJsonB
    @Column(name = "snapshot", columnDefinition = "jsonb")
    private RandomRewardInstanceSnapshot snapshot;

    @Column(nullable = false)
    private String requestId;

    @Column
    private Source source;

    @Column
    private String sourceReference;

    public enum Status {
        ASSIGNED,
        COMPLETED,
        EXPIRED,
        RETRACTED;

        private final String code = this.name().toLowerCase().intern();

        @JsonCreator
        public static Status fromCode(String code) {
            for (Status status : Status.values()) {
                if (status.code.equalsIgnoreCase(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status: " + code);
        }

        @DbEnumValue
        public String code() {
            return this.code;
        }
    }

    public enum Source {
        BACKOFFICE,
        BLOOMREACH,
        PAYMENT_OFFER,
        OFFER_CHAIN,
        QUESTS;

        private final String code = this.name().toLowerCase().intern();

        @JsonCreator
        public static Source fromCode(String code) {
            for (Source source : Source.values()) {
                if (source.code.equalsIgnoreCase(code)) {
                    return source;
                }
            }
            throw new IllegalArgumentException("Unknown source: " + code);
        }

        @DbEnumValue
        public String code() {
            return this.code;
        }
    }

}