package randomreward.model;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RandomRewardInstanceSnapshot {
    private String displayName;
    private String shortDisplayName;
    private List<PlacementType> placementTypes;
    private GameType gameType;
    private PrizeTable prizeTable;
    private Integer winningItemIndex;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PlacementType {
        private UUID code;
        private String featureKey;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GameType {
        private UUID code;
        private String featureKey;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrizeTable {
        private UUID code;
        private List<PrizeTableItem> items;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrizeTableItem {
        private UUID code;
        private String name;
        private int rank;
        private BigDecimal probability;
        private List<UUID> rewardCodes;
    }

}