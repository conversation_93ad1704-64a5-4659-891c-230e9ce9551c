package randomreward.model;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Deprecated
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RandomRewardData {
    private BigDecimal gcAmount;
    private BigDecimal scAmount;
    private FreeSpinsPrizeInfo freeSpinsPrizeInfo;
    private PurchaseOfferInfo purchaseOfferInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FreeSpinsPrizeInfo {
        private String bonusCode;
        private Integer count;

        private String supplierCode;
        private String productCode;
        private BigDecimal betValue;
        private Integer betLevel;

        private String supplierCodeFallbackOne;
        private String productCodeFallbackOne;
        private BigDecimal betValueFallbackOne;
        private Integer betLevelFallbackOne;

        private String supplierCodeFallbackTwo;
        private String productCodeFallbackTwo;
        private BigDecimal betValueFallbackTwo;
        private Integer betLevelFallbackTwo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PurchaseOfferInfo {
        private String offerTemplateCode;
        private String purchaseOfferSegment;
        private List<String> purchaseOfferTags;
        private Integer discount;
    }
}