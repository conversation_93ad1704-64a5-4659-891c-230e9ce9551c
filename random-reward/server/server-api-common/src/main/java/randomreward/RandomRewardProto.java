package randomreward;

import lombok.experimental.UtilityClass;

@UtilityClass
public class RandomRewardProto {
    public static final String RANDOM_REWARD_POSTGRES_OWNER = "random-reward-postgres-owner";
    public static final String RANDOM_REWARD_POSTGRES_APP = "random-reward-postgres-app";
    public static final String RANDOM_REWARD_QUARTZ_APP = "random-reward-quartz-app";
    public static final String RANDOM_REWARD_POSTGRES_SLAVE_READ_ONLY = "random-reward-postgres-slave-read-only";

    public static final String UPS_RANDOM_REWARD_LIMITS = "random_reward_limits";
}
