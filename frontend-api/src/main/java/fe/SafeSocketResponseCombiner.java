package fe;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.mutable.MutableBoolean;
import org.apache.commons.lang3.mutable.MutableObject;

import com.google.common.collect.Maps;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.rpc.ApiResponse;
import com.turbospaces.rpc.ApiResponseEntity;

import fe.api.RequestAck;
import fe.api.RestEasyClient;
import gateway.api.GenericInboundWrapper;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import io.vavr.CheckedFunction1;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class SafeSocketResponseCombiner implements Function<Long, FluentFuture<GenericOutboundWrapper>> {
    private final Map<ListenableFuture<? extends ApiResponseEntity<?>>, CheckedFunction1<ResponseWrapperFacade, ResponseStatusFacade>> callbacks = Maps.newLinkedHashMap();
    private final RequestMessageWrapper reqw;
    private final RestEasyClient client;
    private final RequestAck ack;

    public SafeSocketResponseCombiner( RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) {
        this.reqw = Objects.requireNonNull(reqw);
        this.client = Objects.requireNonNull(client);
        this.ack = Objects.requireNonNull(ack);
    }
    public SafeSocketResponseCombiner addCallback(ApiResponse<?> post, CheckedFunction1<ResponseWrapperFacade, ResponseStatusFacade> callback) {
        callbacks.putIfAbsent(post, callback);
        return this;
    }
    public SafeSocketResponseCombiner addCallback(ListenableFuture<? extends ApiResponseEntity<?>> post,
            CheckedFunction1<ResponseWrapperFacade, ResponseStatusFacade> callback) {
        callbacks.putIfAbsent(post, callback);
        return this;
    }
    @Override
    public FluentFuture<GenericOutboundWrapper> apply(Long when) {
        SettableFuture<GenericOutboundWrapper> toReturn = SettableFuture.create();
        GenericOutboundWrapper reply = (GenericOutboundWrapper) reqw.toReply();
        Futures.whenAllComplete(callbacks.keySet()).run(new Runnable() {
            @Override
            public void run() {
                if (ack.isAckRequested()) {
                    ack.sendAckData();
                }

                try {
                    var counter = 1;
                    var terminate = new MutableBoolean();
                    var unrecoverable = new MutableObject<Throwable>();
                    var error = new MutableObject<ResponseStatusFacade>();

                    for (var entry : callbacks.entrySet()) {
                        var post = entry.getKey();
                        var callback = entry.getValue();
                        var name = "callback#" + counter;

                        if (terminate.isFalse()) {
                            var consumer = new SafeSocketResponseConsumer(post, client, reqw, ack) {
                                @Override
                                public void accept(ResponseWrapperFacade t) throws Throwable {
                                    ResponseStatusFacade status = null;
                                    try {
                                        status = callback.apply(t);
                                    } catch (Throwable err) {
                                        onFailure(err);
                                    }

                                    if (Objects.nonNull(status)) {
                                        if (status.isOK()) {
                                            if (Objects.isNull(error.get())) {
                                                error.setValue(status);
                                            }
                                            log.debug("callback: {} returned OK", name);
                                        } else {
                                            log.warn("callback: {} returned : {}", name, status.errorCode().toString().toLowerCase());

                                            terminate.setTrue();
                                            error.setValue(status);
                                        }
                                    }
                                }
                                @Override
                                public void onFailure(Throwable t) {
                                    terminate.setTrue();
                                    unrecoverable.setValue(t);
                                }
                            };

                            consumer.run();
                            counter++;
                        }
                    }

                    //
                    // ~ set meta information
                    //
                    reply.getHeaders().took = System.currentTimeMillis() - when;

                    if (terminate.isFalse()) {
                        toReturn.set(reply);
                    } else {
                        Throwable err = unrecoverable.get();
                        if (Objects.isNull(err)) {
                            if (error.get().isOK()) {
                                log.debug("all {} callback(s) returned OK", callbacks.values().size());
                            } else {
                                ResponseStatusFacade rsf = error.get();
                                String code = rsf.errorCode().toString().toLowerCase().intern();

                                reply.getStatus().errorCode = code;
                                reply.getStatus().errorReasonCode = rsf.errorReason().getNumber();
                                if (StringUtils.isNotEmpty(rsf.errorText())) {
                                    reply.getStatus().errorText = rsf.errorText();
                                }
                                Optional.ofNullable(rsf.errorDetails()).ifPresent(new Consumer<>() {
                                    @Override
                                    public void accept(Map<String, String> map) {
                                        reply.getStatus().errorDetails = map;
                                    }
                                });

                                log.debug("one of {} callback(s) returned non-OK status: {}", callbacks.values().size(), code);
                            }

                            toReturn.set(reply);
                        } else {
                            Throwable rootCause = ExceptionUtils.getRootCause(err);
                            if (Objects.isNull(rootCause)) {
                                rootCause = err;
                            }
                            log.error(rootCause.getMessage(), rootCause); // ~ raise sentry alert at least

                            client.setFault((GenericInboundWrapper) reqw, rootCause);
                            GenericOutboundWrapper toSend = (GenericOutboundWrapper) reqw.toReply();
                            toReturn.set(toSend);
                        }
                    }
                } catch (Throwable t) {
                    toReturn.setException(t);
                }
            }
        }, MoreExecutors.directExecutor())
                .addListener(new Runnable() {
                    @Override
                    public void run() {

                    }
                }, MoreExecutors.directExecutor());
        return FluentFuture.from(toReturn);
    }
}
